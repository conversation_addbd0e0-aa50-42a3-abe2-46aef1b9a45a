#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
from datetime import datetime, timedelta
import random

def send_test_webhook(webhook_url, project_key, project_name, quality_gate_status="OK"):
    """发送测试webhook数据"""
    
    # 生成测试数据
    test_data = {
        "taskId": f"test-task-{random.randint(1000, 9999)}",
        "status": "SUCCESS" if quality_gate_status == "OK" else "FAILED",
        "analysedAt": datetime.now().isoformat() + "+0000",
        "project": {
            "key": project_key,
            "name": project_name
        },
        "qualityGate": {
            "status": quality_gate_status,
            "conditions": [
                {
                    "metricKey": "coverage",
                    "operator": "LT",
                    "status": "OK" if random.random() > 0.3 else "ERROR",
                    "actualValue": str(round(random.uniform(60, 95), 1)),
                    "threshold": "80.0"
                },
                {
                    "metricKey": "bugs",
                    "operator": "GT",
                    "status": "OK" if random.random() > 0.2 else "ERROR",
                    "actualValue": str(random.randint(0, 10)),
                    "threshold": "5"
                },
                {
                    "metricKey": "vulnerabilities",
                    "operator": "GT",
                    "status": "OK" if random.random() > 0.1 else "ERROR",
                    "actualValue": str(random.randint(0, 3)),
                    "threshold": "2"
                },
                {
                    "metricKey": "code_smells",
                    "operator": "GT",
                    "status": "OK" if random.random() > 0.4 else "ERROR",
                    "actualValue": str(random.randint(5, 50)),
                    "threshold": "20"
                },
                {
                    "metricKey": "duplicated_lines_density",
                    "operator": "GT",
                    "status": "OK" if random.random() > 0.2 else "ERROR",
                    "actualValue": str(round(random.uniform(0, 10), 1)),
                    "threshold": "5.0"
                }
            ]
        }
    }
    
    try:
        response = requests.post(
            webhook_url,
            headers={'Content-Type': 'application/json'},
            data=json.dumps(test_data),
            timeout=10
        )
        
        if response.status_code == 200:
            print(f"✅ 成功发送测试数据: {project_name} ({quality_gate_status})")
            return True
        else:
            print(f"❌ 发送失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 发送测试数据时出错: {e}")
        return False

def main():
    print("🧪 SonarQube InfluxDB Webhook 测试工具")
    print("=" * 50)
    
    webhook_url = "http://localhost:8080/webhook/sonarqube"
    
    # 测试项目列表
    test_projects = [
        {"key": "test-project-1", "name": "Test Project 1"},
        {"key": "test-project-2", "name": "Test Project 2"},
        {"key": "demo-app", "name": "Demo Application"},
        {"key": "sample-service", "name": "Sample Service"},
        {"key": "quality-test", "name": "Quality Test Project"}
    ]
    
    print(f"📡 Webhook URL: {webhook_url}")
    print(f"📊 将发送 {len(test_projects)} 个测试项目的数据\n")
    
    success_count = 0
    
    for i, project in enumerate(test_projects, 1):
        print(f"{i}. 发送项目: {project['name']}")
        
        # 随机生成质量门状态
        quality_gate_status = "OK" if random.random() > 0.3 else "ERROR"
        
        success = send_test_webhook(
            webhook_url,
            project['key'],
            project['name'],
            quality_gate_status
        )
        
        if success:
            success_count += 1
    
    print(f"\n📈 测试结果: {success_count}/{len(test_projects)} 成功")
    
    if success_count > 0:
        print("\n✅ 测试数据发送完成!")
        print("\n📝 下一步操作:")
        print("1. 检查webhook服务状态: curl http://localhost:8080/health")
        print("2. 查看统计信息: curl http://localhost:8080/stats")
        print("3. 访问Grafana查看图表: http://localhost:3000")
        print("4. 访问InfluxDB UI: http://localhost:8086")
    else:
        print("\n❌ 所有测试都失败了，请检查服务状态")

def send_historical_data():
    """发送历史数据用于测试趋势图"""
    print("\n📊 发送历史数据...")
    
    webhook_url = "http://localhost:8080/webhook/sonarqube"
    project = {"key": "trend-test", "name": "Trend Test Project"}
    
    # 发送过去7天的数据
    for days_ago in range(7, 0, -1):
        timestamp = datetime.now() - timedelta(days=days_ago)
        
        test_data = {
            "taskId": f"historical-task-{days_ago}",
            "status": "SUCCESS",
            "analysedAt": timestamp.isoformat() + "+0000",
            "project": project,
            "qualityGate": {
                "status": "OK" if random.random() > 0.2 else "ERROR",
                "conditions": [
                    {
                        "metricKey": "coverage",
                        "operator": "LT",
                        "status": "OK",
                        "actualValue": str(round(75 + days_ago * 2 + random.uniform(-5, 5), 1)),
                        "threshold": "80.0"
                    },
                    {
                        "metricKey": "bugs",
                        "operator": "GT",
                        "status": "OK",
                        "actualValue": str(max(0, 10 - days_ago + random.randint(-2, 2))),
                        "threshold": "5"
                    }
                ]
            }
        }
        
        try:
            response = requests.post(
                webhook_url,
                headers={'Content-Type': 'application/json'},
                data=json.dumps(test_data),
                timeout=10
            )
            
            if response.status_code == 200:
                print(f"✅ 历史数据 {days_ago}天前: 成功")
            else:
                print(f"❌ 历史数据 {days_ago}天前: 失败")
                
        except Exception as e:
            print(f"❌ 发送历史数据时出错: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--historical":
        send_historical_data()
    else:
        main()
        
        # 询问是否发送历史数据
        response = input("\n🤔 是否发送历史数据用于测试趋势图? (y/N): ")
        if response.lower() in ['y', 'yes']:
            send_historical_data()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
清理数据库中的重复数据
用于Windows环境下清理quality_data.db中的重复记录
"""

import os
import sqlite3
import logging
from datetime import datetime

# 配置日志
log_filename = 'clean_duplicates_{0}.log'.format(
    datetime.now().strftime("%Y%m%d_%H%M%S")
)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(log_filename)
    ]
)
logger = logging.getLogger('Clean_Duplicates')

def get_duplicate_dates(cursor):
    """获取有重复记录的日期列表"""
    cursor.execute("""
    SELECT date, COUNT(*) as count
    FROM metrics
    GROUP BY date
    HAVING count > 1
    ORDER BY date
    """)
    return cursor.fetchall()

def show_records_by_date(cursor, date):
    """显示指定日期的所有记录"""
    cursor.execute("""
    SELECT id, date, bugs, vulnerabilities, code_smells, 
           blocker_issues, major_issues, duplications_percentage,
           comment_percentage
    FROM metrics
    WHERE date = ?
    ORDER BY id
    """, (date,))
    
    records = cursor.fetchall()
    logger.info("\n日期 {0} 的记录:".format(date))
    for record in records:
        logger.info("""
ID: {0}
- 日期: {1}
- Bugs: {2}
- 漏洞: {3}
- 异味: {4}
- 阻断: {5}
- 主要: {6}
- 重复率: {7}%
- 注释率: {8}%
        """.strip().format(
            record[0], record[1], record[2], record[3],
            record[4], record[5], record[6], record[7], record[8]
        ))
    return records

def clean_duplicates(db_path):
    """清理重复数据"""
    logger.info("开始清理数据库: {0}".format(db_path))
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 获取重复记录的日期
    duplicates = get_duplicate_dates(cursor)
    if not duplicates:
        logger.info("没有发现重复数据")
        conn.close()
        return
    
    logger.info("发现 {0} 个日期有重复记录".format(len(duplicates)))
    
    # 处理每个重复的日期
    for date, count in duplicates:
        logger.info("\n处理日期 {0} 的重复记录 (共 {1} 条)".format(date, count))
        
        # 显示该日期的所有记录
        records = show_records_by_date(cursor, date)
        
        # 保留最新的记录（ID最大的）
        latest_id = max(record[0] for record in records)
        
        # 删除其他记录
        cursor.execute("""
        DELETE FROM metrics
        WHERE date = ? AND id != ?
        """, (date, latest_id))
        
        deleted_count = cursor.rowcount
        logger.info("已删除 {0} 条重复记录，保留ID为 {1} 的记录".format(
            deleted_count, latest_id))
    
    conn.commit()
    
    # 验证清理结果
    duplicates_after = get_duplicate_dates(cursor)
    if not duplicates_after:
        logger.info("\n所有重复记录已清理完成")
    else:
        logger.warning("\n仍有 {0} 个日期存在重复记录".format(len(duplicates_after)))
    
    conn.close()

def main():
    """主函数"""
    try:
        # 获取脚本所在目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        db_path = os.path.join(script_dir, "quality_data.db")
        
        if not os.path.exists(db_path):
            logger.error("数据库文件不存在: {0}".format(db_path))
            return 1
        
        # 清理重复数据
        clean_duplicates(db_path)
        
        logger.info("操作完成!")
        return 0
        
    except Exception as e:
        logger.error("执行过程中发生错误: {0}".format(str(e)))
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit(main()) 
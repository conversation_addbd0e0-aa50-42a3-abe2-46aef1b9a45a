-- 更新视图脚本
-- 这个脚本用于重新创建所有视图，应用精度优化

-- 删除现有视图
DROP VIEW IF EXISTS daily_commit_stats;
DROP VIEW IF EXISTS author_contribution_stats;
DROP VIEW IF EXISTS project_quality_stats;
DROP VIEW IF EXISTS change_size_distribution;
DROP VIEW IF EXISTS quality_level_distribution;
DROP VIEW IF EXISTS monthly_trend_stats;
DROP VIEW IF EXISTS reviewer_stats;
DROP VIEW IF EXISTS branch_stats;

-- 重新创建视图（使用优化后的精度）

-- 1. 每日提交统计视图
CREATE OR REPLACE VIEW daily_commit_stats AS
SELECT 
    commit_date,
    COUNT(*) as total_commits,
    COUNT(DISTINCT author) as unique_authors,
    COUNT(DISTINCT gerrit_project) as unique_projects,
    SUM(changed_lines) as total_changed_lines,
    SUM(insertions) as total_insertions,
    SUM(deletions) as total_deletions,
    ROUND(AVG(changed_lines)::numeric, 2) as avg_changed_lines,
    SUM(total_issues) as total_issues,
    SUM(critical_issues) as total_critical_issues,
    ROUND(AVG(issue_density)::numeric, 2) as avg_issue_density
FROM commit_metrics
GROUP BY commit_date
ORDER BY commit_date DESC;

-- 2. 作者贡献统计视图
CREATE OR REPLACE VIEW author_contribution_stats AS
SELECT 
    author,
    COUNT(*) as total_commits,
    SUM(changed_lines) as total_changed_lines,
    ROUND(AVG(changed_lines)::numeric, 2) as avg_changed_lines,
    SUM(total_issues) as total_issues,
    SUM(critical_issues) as total_critical_issues,
    ROUND(AVG(issue_density)::numeric, 2) as avg_issue_density,
    COUNT(CASE WHEN quality_level = 'clean' THEN 1 END) as clean_commits,
    COUNT(CASE WHEN quality_level = 'critical' THEN 1 END) as critical_commits,
    ROUND((COUNT(CASE WHEN quality_level = 'clean' THEN 1 END)::float / COUNT(*) * 100)::numeric, 2) as clean_rate_percent
FROM commit_metrics
GROUP BY author
ORDER BY total_commits DESC;

-- 3. 项目质量统计视图
CREATE OR REPLACE VIEW project_quality_stats AS
SELECT 
    gerrit_project,
    COUNT(*) as total_commits,
    SUM(changed_lines) as total_changed_lines,
    SUM(total_issues) as total_issues,
    SUM(critical_issues) as total_critical_issues,
    ROUND(AVG(issue_density)::numeric, 2) as avg_issue_density,
    ROUND(AVG(comment_lines_density)::numeric, 2) as avg_comment_density,
    ROUND(AVG(duplicated_lines_density)::numeric, 2) as avg_duplication_density,
    ROUND(AVG(complexity)::numeric, 2) as avg_complexity,
    COUNT(CASE WHEN quality_level = 'clean' THEN 1 END) as clean_commits,
    COUNT(CASE WHEN quality_level = 'critical' THEN 1 END) as critical_commits,
    ROUND((COUNT(CASE WHEN quality_level = 'clean' THEN 1 END)::float / COUNT(*) * 100)::numeric, 2) as clean_rate_percent
FROM commit_metrics
GROUP BY gerrit_project
ORDER BY total_commits DESC;

-- 4. 变更大小分布视图
CREATE OR REPLACE VIEW change_size_distribution AS
SELECT 
    change_size_category,
    COUNT(*) as commit_count,
    ROUND((COUNT(*)::float / (SELECT COUNT(*) FROM commit_metrics) * 100)::numeric, 2) as percentage,
    ROUND(AVG(changed_lines)::numeric, 2) as avg_changed_lines,
    ROUND(AVG(total_issues)::numeric, 2) as avg_issues,
    ROUND(AVG(issue_density)::numeric, 2) as avg_issue_density
FROM commit_metrics
GROUP BY change_size_category
ORDER BY 
    CASE change_size_category 
        WHEN 'small' THEN 1 
        WHEN 'medium' THEN 2 
        WHEN 'large' THEN 3 
        WHEN 'huge' THEN 4 
    END;

-- 5. 质量等级分布视图
CREATE OR REPLACE VIEW quality_level_distribution AS
SELECT 
    quality_level,
    COUNT(*) as commit_count,
    ROUND((COUNT(*)::float / (SELECT COUNT(*) FROM commit_metrics) * 100)::numeric, 2) as percentage,
    ROUND(AVG(changed_lines)::numeric, 2) as avg_changed_lines,
    ROUND(AVG(total_issues)::numeric, 2) as avg_issues
FROM commit_metrics
GROUP BY quality_level
ORDER BY 
    CASE quality_level 
        WHEN 'clean' THEN 1 
        WHEN 'minor' THEN 2 
        WHEN 'major' THEN 3 
        WHEN 'critical' THEN 4 
    END;

-- 6. 月度趋势统计视图
CREATE OR REPLACE VIEW monthly_trend_stats AS
SELECT 
    commit_year,
    commit_month,
    TO_DATE(commit_year::text || '-' || LPAD(commit_month::text, 2, '0') || '-01', 'YYYY-MM-DD') as month_start,
    COUNT(*) as total_commits,
    COUNT(DISTINCT author) as unique_authors,
    COUNT(DISTINCT gerrit_project) as unique_projects,
    SUM(changed_lines) as total_changed_lines,
    SUM(total_issues) as total_issues,
    SUM(critical_issues) as total_critical_issues,
    ROUND(AVG(issue_density)::numeric, 2) as avg_issue_density,
    COUNT(CASE WHEN quality_level = 'clean' THEN 1 END) as clean_commits,
    ROUND((COUNT(CASE WHEN quality_level = 'clean' THEN 1 END)::float / COUNT(*) * 100)::numeric, 2) as clean_rate_percent
FROM commit_metrics
GROUP BY commit_year, commit_month
ORDER BY commit_year DESC, commit_month DESC;

-- 7. 评审人统计视图
CREATE OR REPLACE VIEW reviewer_stats AS
SELECT 
    reviewer,
    COUNT(*) as review_count,
    COUNT(DISTINCT author) as unique_authors_reviewed,
    COUNT(DISTINCT gerrit_project) as unique_projects_reviewed,
    ROUND(AVG(changed_lines)::numeric, 2) as avg_changed_lines_reviewed,
    ROUND(AVG(total_issues)::numeric, 2) as avg_issues_reviewed,
    ROUND(AVG(issue_density)::numeric, 2) as avg_issue_density_reviewed
FROM commit_metrics,
LATERAL unnest(reviewers) as reviewer
GROUP BY reviewer
ORDER BY review_count DESC;

-- 8. 分支统计视图
CREATE OR REPLACE VIEW branch_stats AS
SELECT 
    branch,
    COUNT(*) as total_commits,
    COUNT(DISTINCT author) as unique_authors,
    SUM(changed_lines) as total_changed_lines,
    SUM(total_issues) as total_issues,
    SUM(critical_issues) as total_critical_issues,
    ROUND(AVG(issue_density)::numeric, 2) as avg_issue_density,
    COUNT(CASE WHEN quality_level = 'clean' THEN 1 END) as clean_commits,
    ROUND((COUNT(CASE WHEN quality_level = 'clean' THEN 1 END)::float / COUNT(*) * 100)::numeric, 2) as clean_rate_percent
FROM commit_metrics
GROUP BY branch
ORDER BY total_commits DESC;

-- 验证视图创建成功
SELECT 'Views updated successfully!' as status;

-- 显示所有视图
SELECT table_name 
FROM information_schema.views 
WHERE table_schema = 'public' 
AND table_name LIKE '%stats%' OR table_name LIKE '%distribution%'
ORDER BY table_name; 
#!/bin/bash
#alias jq=D:/ruanjian/Git/mingw64/bin/jq-win64.exe
# eide_incDirs_temp.json incList libList defineList
# builder_v2.params incDirs libDirs defines

# 生成 incDirs linDirs defines临时文件
cat eide.json | jq-win64.exe .targets | jq-win64.exe .[] | jq-win64.exe .custom_dep > eide_incDirs_temp.json

# 生产sourceList临时文件
cat eide.json | jq-win64.exe .virtualFolder | grep path > path.xml

# 通过读取eide_incDirs_temp.json文件,将里面的incList,libList,defineList内容,写入builder_v2.params文件中的incDirs,libDirs,defines文件
jq-win64.exe -r '.incList' eide_incDirs_temp.json > incList.json
jq-win64.exe -r '.libList' eide_incDirs_temp.json > libList.json
jq-win64.exe -r '.defineList' eide_incDirs_temp.json > defineList.json
# 将incList.json文件内容开头空8格，文件内容不变
rm -rf incList2.json
cp incList.json incList2.json
perl -pi -e 's/^/      /' incList2.json
# 去掉incList2.json文件中'[',']'行前面的空格'
perl -pi -e 's/^\s+(\[|\])/$1/' incList2.json


# libList.json文件内容开头空8格，文件内容不变
rm -rf libList2.json
cp libList.json libList2.json
perl -pi -e 's/^/      /' libList2.json
# libList2.json文件中'[',']'行前面的空格'
perl -pi -e 's/^\s+(\[|\])/$1/' libList2.json


# defineList.json文件内容开头空8格，文件内容不变
rm -rf defineList2.json
cp defineList.json defineList2.json
perl -pi -e 's/^/      /' defineList2.json
# defineList2.json文件中'[',']'行前面的空格'
perl -pi -e 's/^\s+(\[|\])/$1/' defineList2.json


# 将path2.xml文件的所有行前面对齐空8格
rm -rf path2.xml
rm -rf path3.xml
cp path.xml path2.xml

sed '$!s/$/,/' path2.xml > path3.xml
awk '{gsub(/^ *"path": /, ""); printf "        %s\n", $0}' path3.xml > path4.xml

rm -rf builder_v2.params
cp builder_v2.params.bak builder_v2.params
# 读取incList.json文件,将里面的内容替换入builder_v2.params文件中的 "replace_incList",并且前面空5格子
perl -pi -e "s|replace_incList|$(cat incList2.json)|g" builder_v2.params
perl -pi -e "s|replace_libList|$(cat libList2.json)|g" builder_v2.params
perl -pi -e "s|replace_defineList|$(cat defineList2.json)|g" builder_v2.params
perl -pi -e "s|replace_sourceList|$(cat path4.xml)|g" builder_v2.params

# 特殊处理，"SIFLI_BUILD="000000"" 替换为"SIFLI_BUILD=\"000000\"",
sed -i 's/"SIFLI_BUILD="000000""/"SIFLI_BUILD=\\"000000\\""/g' builder_v2.params



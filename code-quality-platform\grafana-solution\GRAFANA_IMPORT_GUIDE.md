# Grafana 仪表板导入指南

## 🎯 快速导入步骤

### 方法1: Web界面导入（推荐）

1. **登录Grafana**
   - 访问您的Grafana地址
   - 使用管理员账号登录

2. **导入仪表板**
   - 点击左侧菜单 **"+"** → **"Import"**
   - 点击 **"Upload JSON file"**
   - 选择 `code-quality-overview.json` 文件
   - 点击 **"Load"**

3. **配置数据源**
   - 在 **"PostgreSQL-CodeQuality"** 下拉框中选择您的PostgreSQL数据源
   - 如果没有看到数据源，请先配置PostgreSQL数据源
   - 点击 **"Import"**

### 方法2: 复制JSON内容导入

1. **打开导入页面**
   - 在Grafana中点击 **"+"** → **"Import"**

2. **粘贴JSON**
   - 选择 **"Import via panel json"**
   - 复制 `code-quality-overview.json` 文件的全部内容
   - 粘贴到文本框中
   - 点击 **"Load"**

3. **配置并导入**
   - 选择正确的数据源
   - 点击 **"Import"**

## 🔧 数据源配置

如果您还没有配置PostgreSQL数据源，请按以下步骤操作：

### 添加PostgreSQL数据源

1. **进入数据源配置**
   - 点击左侧菜单 **"Configuration"** → **"Data sources"**
   - 点击 **"Add data source"**
   - 选择 **"PostgreSQL"**

2. **配置连接信息**
   ```
   Name: PostgreSQL-CodeQuality
   Host: **********:5434
   Database: mydatabase
   User: admin
   Password: admin123
   SSL Mode: disable
   ```

3. **测试连接**
   - 点击 **"Save & test"**
   - 确保显示 "Database Connection OK"

## 📊 仪表板功能说明

### 面板1: 关键指标概览
- 显示最近30天的核心指标
- 包括：总提交数、活跃开发者、活跃项目、总问题数、平均问题密度、清洁代码率

### 面板2: 每日提交趋势
- 时间序列图表显示每日提交数、问题数、严重问题数的趋势
- 可以观察代码活动和质量问题的变化

### 面板3: 代码质量等级分布
- 饼图显示不同质量等级的提交分布
- 颜色编码：绿色(clean)、黄色(minor)、橙色(major)、红色(critical)

### 面板4: 项目质量排名
- 表格显示项目按清洁代码率排序的Top 10
- 包含提交数、问题数、清洁代码率、问题密度等指标

### 面板5: 开发者贡献排名
- 表格显示开发者按提交数排序的Top 10
- 包含提交数、代码行数、清洁代码率、问题密度等指标

## 🛠️ 故障排除

### 问题1: 数据源连接失败
**解决方案**:
- 检查数据库地址、端口、用户名、密码是否正确
- 确认网络连通性
- 检查PostgreSQL服务是否运行

### 问题2: 查询返回空数据
**解决方案**:
- 确认 `commit_metrics` 表中有数据
- 检查视图是否已创建：
  ```sql
  SELECT * FROM daily_commit_stats LIMIT 5;
  SELECT * FROM quality_level_distribution;
  SELECT * FROM project_quality_stats LIMIT 5;
  SELECT * FROM author_contribution_stats LIMIT 5;
  ```

### 问题3: 面板显示错误
**解决方案**:
- 检查SQL查询语法
- 确认表结构与查询匹配
- 查看Grafana日志获取详细错误信息

### 问题4: 时间范围问题
**解决方案**:
- 调整仪表板右上角的时间范围
- 确认数据的时间字段格式正确
- 检查时区设置

## 📝 自定义建议

### 修改时间范围
- 在仪表板右上角可以调整时间范围
- 默认显示最近30天的数据

### 添加过滤器
- 可以添加项目、开发者等变量进行过滤
- 在仪表板设置中添加模板变量

### 调整刷新频率
- 默认5分钟自动刷新
- 可以在右上角调整刷新间隔

### 修改阈值
- 在面板编辑中可以调整颜色阈值
- 根据团队标准设置质量门限

## 🎨 扩展仪表板

您可以基于现有数据创建更多专门的仪表板：

1. **趋势分析仪表板** - 专注于时间序列分析
2. **开发者个人仪表板** - 个人代码质量报告
3. **项目对比仪表板** - 多项目质量对比
4. **风险监控仪表板** - 高风险提交告警

## 📞 技术支持

如果遇到问题，请检查：
1. Grafana版本兼容性
2. PostgreSQL连接权限
3. 数据表结构完整性
4. 查询语句正确性

---

🎉 **导入成功后，您就可以开始使用代码质量效能分析仪表板了！**

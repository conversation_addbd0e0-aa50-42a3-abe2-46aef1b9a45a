{"dashboard": {"id": null, "title": "SonarQube Quality Gate Dashboard", "tags": ["sonarqube", "quality"], "timezone": "browser", "panels": [{"id": 1, "title": "Quality Gate Status", "type": "stat", "targets": [{"expr": "sonarqube_quality_gate_status", "legendFormat": "{{project_name}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}, "mappings": [{"options": {"0": {"text": "FAILED"}, "1": {"text": "PASSED"}}, "type": "value"}]}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Analysis Completion Rate", "type": "timeseries", "targets": [{"expr": "rate(sonarqube_analysis_completed[5m])", "legendFormat": "{{project_name}}", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Quality Gate Conditions", "type": "table", "targets": [{"expr": "sonarqube_condition_value", "legendFormat": "{{project_name}} - {{metric_key}}", "refId": "A", "format": "table"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "30s"}}
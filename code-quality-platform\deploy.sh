#!/bin/bash

echo "🚀 代码质量效能分析平台部署脚本"
echo "=" * 50

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查Docker
check_docker() {
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker未安装，请先安装Docker${NC}"
        exit 1
    fi
    
    if ! docker info > /dev/null 2>&1; then
        echo -e "${RED}❌ Docker未运行，请启动Docker${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Docker环境检查通过${NC}"
}

# 检查Python
check_python() {
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}❌ Python3未安装，请先安装Python3${NC}"
        exit 1
    fi
    
    if ! command -v pip &> /dev/null; then
        echo -e "${RED}❌ pip未安装，请先安装pip${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Python环境检查通过${NC}"
}

# 测试数据库连接
test_database() {
    echo -e "${BLUE}🔍 测试数据库连接...${NC}"
    
    python3 -c "
import psycopg2
try:
    conn = psycopg2.connect(
        host='**********',
        port='5434', 
        database='mydatabase',
        user='admin',
        password='admin123'
    )
    cursor = conn.cursor()
    cursor.execute('SELECT COUNT(*) FROM commit_metrics')
    count = cursor.fetchone()[0]
    print(f'✅ 数据库连接成功，找到 {count} 条记录')
    conn.close()
except Exception as e:
    print(f'❌ 数据库连接失败: {e}')
    print('请检查数据库配置和网络连接')
    exit(1)
" 2>/dev/null || {
    echo -e "${YELLOW}⚠️  无法测试数据库连接，请确保psycopg2已安装${NC}"
    echo "可以运行: pip install psycopg2-binary"
}
}

# 部署Grafana方案
deploy_grafana() {
    echo -e "${BLUE}📊 部署Grafana方案...${NC}"
    
    cd grafana-solution
    
    # 检查配置文件
    if [ ! -f "grafana/provisioning/datasources/datasources.yml" ]; then
        echo -e "${RED}❌ Grafana配置文件不存在${NC}"
        exit 1
    fi
    
    # 启动服务
    docker-compose up -d
    
    # 等待服务启动
    echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
    sleep 30
    
    # 检查服务状态
    if curl -f http://localhost:3001/api/health > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Grafana启动成功${NC}"
        echo -e "${BLUE}🌐 访问地址: http://localhost:3001${NC}"
        echo -e "${BLUE}👤 用户名: admin${NC}"
        echo -e "${BLUE}🔑 密码: admin123${NC}"
    else
        echo -e "${RED}❌ Grafana启动失败${NC}"
        docker-compose logs grafana
    fi
    
    cd ..
}

# 部署Streamlit方案
deploy_streamlit() {
    echo -e "${BLUE}🎨 部署Streamlit方案...${NC}"
    
    cd streamlit-solution
    
    # 安装依赖
    echo -e "${YELLOW}📦 安装Python依赖...${NC}"
    pip install -r requirements.txt
    
    # 启动应用
    echo -e "${BLUE}🌐 启动Streamlit应用...${NC}"
    echo -e "${BLUE}访问地址: http://localhost:8501${NC}"
    echo -e "${YELLOW}按 Ctrl+C 停止服务${NC}"
    
    streamlit run app.py --server.port 8501 --server.address 0.0.0.0
    
    cd ..
}

# 显示菜单
show_menu() {
    echo ""
    echo -e "${BLUE}请选择要部署的方案:${NC}"
    echo "1) Grafana方案 (专业监控仪表板)"
    echo "2) Streamlit方案 (交互式数据分析)"
    echo "3) 同时部署两个方案"
    echo "4) 仅测试数据库连接"
    echo "5) 退出"
    echo ""
}

# 主函数
main() {
    echo -e "${GREEN}🔍 环境检查...${NC}"
    
    while true; do
        show_menu
        read -p "请输入选择 (1-5): " choice
        
        case $choice in
            1)
                check_docker
                test_database
                deploy_grafana
                break
                ;;
            2)
                check_python
                test_database
                deploy_streamlit
                break
                ;;
            3)
                check_docker
                check_python
                test_database
                
                echo -e "${BLUE}🚀 部署Grafana方案...${NC}"
                deploy_grafana
                
                echo ""
                echo -e "${BLUE}🚀 部署Streamlit方案...${NC}"
                echo -e "${YELLOW}注意: Streamlit将在前台运行，请在新终端中访问Grafana${NC}"
                sleep 3
                deploy_streamlit
                break
                ;;
            4)
                check_python
                test_database
                echo -e "${GREEN}✅ 数据库连接测试完成${NC}"
                ;;
            5)
                echo -e "${GREEN}👋 再见！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ 无效选择，请重新输入${NC}"
                ;;
        esac
    done
}

# 运行主函数
main

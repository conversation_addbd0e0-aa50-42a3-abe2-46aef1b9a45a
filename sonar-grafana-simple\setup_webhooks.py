#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def setup_sonarqube_webhook(sonar_url, token, webhook_url, project_key=None):
    """为SonarQube设置webhook"""
    
    session = requests.Session()
    session.auth = (token, '')
    
    # 如果指定了项目，为单个项目创建webhook
    if project_key:
        webhook_name = f"Grafana Webhook - {project_key}"
        data = {
            'project': project_key,
            'name': webhook_name,
            'url': webhook_url
        }
    else:
        # 创建全局webhook
        webhook_name = "Grafana Global Webhook"
        data = {
            'name': webhook_name,
            'url': webhook_url
        }
    
    try:
        url = f"{sonar_url}/api/webhooks/create"
        response = session.post(url, data=data)
        
        if response.status_code == 200:
            webhook_data = response.json()
            print(f"✓ 成功创建webhook: {webhook_name}")
            print(f"  URL: {webhook_data['webhook']['url']}")
            return True
        else:
            print(f"✗ 创建webhook失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ 创建webhook时出错: {e}")
        return False

def main():
    # 配置您的SonarQube服务器
    sonar_configs = [
        {
            'name': 'SonarQube Community',
            'url': 'http://********:9000',
            'token': '0da72b170a333b2aa5d1a8e48cc8ba14ce3cb1fd'
        },
        {
            'name': 'SonarQube Enterprise', 
            'url': 'http://*********:9001',
            'token': 'squ_a60fc304111367163786cc06084df9b8d96e5ff1'
        }
    ]
    
    # Webhook接收地址（请根据实际情况修改IP地址）
    webhook_url = "http://*********:8080/webhook/sonarqube"
    
    print("=== SonarQube Webhook 配置工具 ===\n")
    
    for config in sonar_configs:
        print(f"配置 {config['name']} ({config['url']})...")
        
        # 创建全局webhook（推荐）
        success = setup_sonarqube_webhook(
            config['url'], 
            config['token'], 
            webhook_url
        )
        
        if success:
            print(f"✓ {config['name']} webhook配置完成\n")
        else:
            print(f"✗ {config['name']} webhook配置失败\n")

if __name__ == "__main__":
    main()

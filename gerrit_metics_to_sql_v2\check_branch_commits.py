#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
直接通过commit_id获取Gerrit信息
"""

import os
import sys
import json
import logging
import configparser
import subprocess
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 读取配置文件
config = configparser.ConfigParser()
config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config.ini')
config.read(config_path)

# Gerrit配置
GERRIT_HOST = config.get('Gerrit', 'host')
GERRIT_PORT = config.get('Gerrit', 'port')
GERRIT_USER = config.get('Gerrit', 'user')

def get_gerrit_change_by_commit(commit_id):
    """直接通过commit_id获取Gerrit变更详情"""
    try:
        # 直接通过commit_id查询
        cmd = f'ssh -p {GERRIT_PORT} {GERRIT_USER}@{GERRIT_HOST} gerrit query --current-patch-set --all-approvals --files --comments --format=JSON commit:{commit_id}'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error(f"获取Gerrit变更详情失败: {result.stderr}")
            return None
        
        lines = result.stdout.strip().split('\n')
        if not lines:
            logger.error("Gerrit查询返回空结果")
            return None
        
        change_details = None
        for line in lines:
            try:
                obj = json.loads(line)
                if obj.get("type") == "stats":
                    continue
                change_details = obj
            except:
                continue
        
        return change_details
    except Exception as e:
        logger.error(f"获取Gerrit变更详情异常: {e}")
        return None

def check_commit_info(commit_id):
    """检查指定commit_id的详细信息"""
    try:
        logger.info(f"=== 检查Commit ID: {commit_id} ===")
        
        # 直接通过commit_id获取变更详情
        change_details = get_gerrit_change_by_commit(commit_id)
        
        if not change_details:
            logger.error("未找到相关的变更")
            return False
        
        # 提取关键信息
        change_id = change_details.get("id", "")
        branch = change_details.get("branch", "")
        project = change_details.get("project", "")
        status = change_details.get("status", "")
        subject = change_details.get("subject", "")
        
        # 获取当前patch set的commit id
        current_patch_set = change_details.get("currentPatchSet", {})
        actual_commit_id = current_patch_set.get("revision", "")
        patch_set_number = current_patch_set.get("number", "")
        
        logger.info(f"变更详情:")
        logger.info(f"  Change ID: {change_id}")
        logger.info(f"  Branch: {branch}")
        logger.info(f"  Project: {project}")
        logger.info(f"  Status: {status}")
        logger.info(f"  Subject: {subject}")
        logger.info(f"  Commit ID: {actual_commit_id}")
        logger.info(f"  Patch Set: {patch_set_number}")
        
        # 检查是否是wr02_release分支
        if branch == "wr02_release":
            logger.info("  ⭐ 这是wr02_release分支！")
        else:
            logger.warning(f"  ⚠️  这不是wr02_release分支，实际分支是: {branch}")
        
        # 检查commit_id是否匹配
        if actual_commit_id == commit_id:
            logger.info("  ✅ Commit ID匹配")
        else:
            logger.warning(f"  ⚠️  Commit ID不匹配，期望: {commit_id}，实际: {actual_commit_id}")
        
        return True
        
    except Exception as e:
        logger.error(f"检查commit信息异常: {e}")
        logger.exception("详细异常信息:")
        return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="直接通过commit_id获取Gerrit信息")
    parser.add_argument("commit_id", help="Gerrit Commit ID")
    
    args = parser.parse_args()
    
    logger.info(f"开始检查Commit ID: {args.commit_id}")
    success = check_commit_info(args.commit_id)
    
    if success:
        logger.info("检查完成")
    else:
        logger.error("检查失败")

if __name__ == "__main__":
    main() 
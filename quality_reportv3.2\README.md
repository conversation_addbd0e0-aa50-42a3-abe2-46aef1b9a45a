# 代码质量报告生成工具

这是一个用于生成代码质量报告的工具集，支持生成日报、周报和月报。报告包含详细的数据统计和可视化图表，帮助团队更好地追踪和改进代码质量。

## 功能特点

- 支持生成日报、周报和月报
- 自动过滤周末数据
- 自动过滤无效数据（所有指标为0的数据）
- 包含丰富的数据可视化图表
- 统计关键指标的变化趋势
- 支持自定义项目配置

## 目录结构

```
quality_reportv3.0/
├── config.ini              # 配置文件
├── quality_data.db         # SQLite数据库
├── run_daily_report.py     # 日报生成脚本
├── run_weekly_report.py    # 周报生成脚本
├── run_monthly_report.py   # 月报生成脚本
├── report_visualizer.py    # 报告可视化模块
└── reports/               # 报告输出目录
    ├── {项目ID}/          # 日报存储目录
    ├── {项目ID}-week/     # 周报存储目录
    └── {项目ID}-month/    # 月报存储目录
```

## 使用前准备

1. 确保已安装所需的Python包：
```bash
pip install pandas openpyxl pillow
```

2. 配置项目信息：
   - 在 `config.ini` 文件中配置项目ID：
```ini
[SonarQube]
ProjectID = your_project_id
```

## 使用方法

### 1. 获取数据


运行以下命令从 SonarQube 获取最近 7 天的数据：

```
python generate_weekly_data.py
```

运行以下命令从 SonarQube 获取固定月数的数据：

```
python generate_mouth_data.py
```

### 2. 生成代码质量日报

运行以下命令生成当天的质量日简报并发送企业微信通知：

```
# 生成当天的日报
python run_daily_report.py
```
报告将保存在 reports/{项目ID}/ 目录下
文件名格式：daily_report_{项目ID}_{日期}.png


### 3. 生成代码质量周报

```
# 生成本周的周报
python run_weekly_report.py
```
报告将保存在 reports/{项目ID}-week/ 目录下
文件名格式：weekly_report_{项目ID}_workday_{日期}.xlsx

### 4. 生成代码质量月报数据

运行以下命令生成质量月报数据（默认只包含工作日数据）：

```
python run_monthly_report.py 2025-05
```

生成的报告将保存在 `reports/{项目ID}-mouth/` 目录下。


### 5. 查看数据库数据

查看最近 7 天的数据：

```
python view_db_data.py
```

查看指定项目的数据：

```
python view_db_data.py 项目ID
```

列出所有可用项目：

```
python view_db_data.py --list
```

## 报告内容

### 日报内容
- 项目基本信息：项目名称、日期等
- 关键指标数据：展示当天的所有质量指标
- 同比分析：与前一个工作日的数据对比（如为周一则与上周五对比）
- 质量等级：基于SQALE评级系统的质量等级展示
- 图表展示：以图片格式展示关键指标和变化趋势

### 周报内容
- 每日数据表：展示每个工作日的所有质量指标
- 周统计表：展示本周各指标的起始值、结束值、变化量和变化率
- 可视化图表：展示各项指标的变化趋势
- 自动过滤：不包含周末数据和无效数据（所有指标为0的数据）

### 月报内容
- 每日数据表：展示当月每个工作日的所有质量指标
- 月统计表：展示本月各指标的起始值、结束值、变化量和变化率
- 可视化图表：展示各项指标的月度变化趋势
- 自动过滤：不包含周末数据和无效数据

## 关键指标说明

报告包含以下关键指标：
- Bugs数量：代码中的Bug数量
- 漏洞数量：安全漏洞数量
- 代码异味：需要改进的代码问题数量
- 阻断问题：严重的代码问题数量
- 主要问题：重要的代码问题数量
- 提示问题：建议改进的代码问题数量
- 代码重复率：重复代码的百分比
- 重复行数：重复的代码行数
- 重复块数：重复的代码块数量
- 重复文件数：包含重复代码的文件数量
- 代码行数：总代码行数
- 注释行数：总注释行数
- 注释率：代码注释覆盖率
- 圈复杂度：代码复杂度指标

## 注意事项

1. 确保 `quality_data.db` 数据库文件存在且包含有效数据
2. 报告生成时会自动创建所需的目录结构
3. 如果某天的所有指标都为0，该天的数据将被自动过滤
4. 周末数据会被自动过滤，只保留工作日数据
5. 生成报告时至少需要两天的有效数据才能计算变化趋势
6. 日报生成需要安装 pillow 包以支持图片生成

## 常见问题

1. 如果报告生成失败，请检查：
   - 数据库文件是否存在
   - 配置文件中的项目ID是否正确
   - 指定的日期范围内是否有有效数据
   - 对于日报，是否已安装 pillow 包

2. 如果报告中缺少某些数据：
   - 检查该时间段是否为周末（会被自动过滤）
   - 检查该天的数据是否全为0（会被自动过滤）
   - 检查数据库中是否存在对应日期的数据

3. 如果日报图片生成失败：
   - 检查是否已正确安装 pillow 包
   - 确保有足够的磁盘空间
   - 确保对输出目录有写入权限 
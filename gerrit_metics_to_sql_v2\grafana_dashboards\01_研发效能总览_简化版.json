{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"id": 1, "title": "提交数量统计", "type": "stat", "datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT COUNT(*) as total_commits FROM commit_metrics", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": []}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "开发者数量", "type": "stat", "datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT COUNT(DISTINCT author) as unique_authors FROM commit_metrics", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": []}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "每日提交趋势", "type": "timeseries", "datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT \n  commit_time::date as time,\n  COUNT(*) as \"提交数\"\nFROM commit_metrics \nWHERE commit_time >= CURRENT_DATE - INTERVAL '30 days'\nGROUP BY commit_time::date\nORDER BY commit_time::date", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "showPoints": "always", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisLabel": "", "axisPlacement": "auto", "barWidth": 0.97, "scaleDistribution": {"type": "linear"}, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "options": {"tooltip": {"mode": "single", "sort": "none"}, "legend": {"showLegend": true, "displayMode": "list", "placement": "bottom", "calcs": []}}, "pluginVersion": "12.0.2", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}}, {"id": 4, "title": "开发者活跃度趋势", "type": "timeseries", "datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT \n  commit_time::date as time,\n  COUNT(DISTINCT author) as \"活跃开发者\"\nFROM commit_metrics \nWHERE commit_time >= CURRENT_DATE - INTERVAL '30 days'\nGROUP BY commit_time::date\nORDER BY commit_time::date", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "showPoints": "always", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisLabel": "", "axisPlacement": "auto", "barWidth": 0.97, "scaleDistribution": {"type": "linear"}, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "options": {"tooltip": {"mode": "single", "sort": "none"}, "legend": {"showLegend": true, "displayMode": "list", "placement": "bottom", "calcs": []}}, "pluginVersion": "12.0.2", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}}], "refresh": "5m", "schemaVersion": 27, "style": "dark", "tags": ["研发效能", "代码质量"], "templating": {"list": []}, "time": {"from": "now-30d", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "研发效能总览-简化版", "uid": "dev-efficiency-simple", "version": 0}
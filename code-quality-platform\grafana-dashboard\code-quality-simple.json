{"annotations": {"list": []}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.0.0", "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  COUNT(*) as \"总提交数\",\n  COUNT(DISTINCT author) as \"活跃开发者\",\n  COUNT(DISTINCT gerrit_project) as \"活跃项目\",\n  COALESCE(SUM(total_issues), 0) as \"总问题数\",\n  ROUND(COALESCE(AVG(issue_density), 0)::numeric, 2) as \"平均问题密度\",\n  ROUND((COUNT(CASE WHEN quality_level = 'clean' THEN 1 END)::float / NULLIF(COUNT(*), 0) * 100)::numeric, 2) as \"清洁代码率%\"\nFROM commit_metrics \nWHERE commit_time >= NOW() - INTERVAL '30 days'", "refId": "A"}], "title": "关键指标概览 (最近30天)", "type": "stat"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "time_series", "rawQuery": true, "rawSql": "SELECT \n  commit_date as time,\n  COUNT(*) as \"提交数\",\n  COALESCE(SUM(total_issues), 0) as \"问题数\",\n  COALESCE(SUM(CASE WHEN sq_blocker > 0 OR sq_critical > 0 THEN 1 ELSE 0 END), 0) as \"严重问题数\"\nFROM commit_metrics \nWHERE commit_date >= CURRENT_DATE - INTERVAL '30 days'\nGROUP BY commit_date\nORDER BY commit_date", "refId": "A"}], "title": "每日提交趋势", "type": "timeseries"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "clean"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "critical"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "major"}, "properties": [{"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "minor"}, "properties": [{"id": "color", "value": {"fixedColor": "yellow", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6}, "id": 3, "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "pieType": "pie", "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  COALESCE(quality_level, 'unknown') as metric,\n  COUNT(*) as value\nFROM commit_metrics\nGROUP BY quality_level\nORDER BY \n  CASE COALESCE(quality_level, 'unknown')\n    WHEN 'clean' THEN 1 \n    WHEN 'minor' THEN 2 \n    WHEN 'major' THEN 3 \n    WHEN 'critical' THEN 4 \n    ELSE 5\n  END", "refId": "A"}], "title": "代码质量等级分布", "type": "piechart"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 14}, "id": 4, "options": {"showHeader": true}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  gerrit_project as \"项目名称\",\n  COUNT(*) as \"提交数\",\n  COUNT(DISTINCT author) as \"开发者数\",\n  COALESCE(SUM(total_issues), 0) as \"问题数\",\n  ROUND(COALESCE(AVG(issue_density), 0)::numeric, 2) as \"问题密度\",\n  ROUND((COUNT(CASE WHEN quality_level = 'clean' THEN 1 END)::float / NULLIF(COUNT(*), 0) * 100)::numeric, 2) as \"清洁代码率%\"\nFROM commit_metrics \nGROUP BY gerrit_project\nHAVING COUNT(*) >= 3\nORDER BY COUNT(*) DESC \nLIMIT 10", "refId": "A"}], "title": "项目质量排名 (Top 10)", "type": "table"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 14}, "id": 5, "options": {"showHeader": true}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  author as \"开发者\",\n  COUNT(*) as \"提交数\",\n  SUM(changed_lines) as \"代码行数\",\n  COALESCE(SUM(total_issues), 0) as \"问题数\",\n  ROUND(COALESCE(AVG(issue_density), 0)::numeric, 2) as \"问题密度\",\n  ROUND((COUNT(CASE WHEN quality_level = 'clean' THEN 1 END)::float / NULLIF(COUNT(*), 0) * 100)::numeric, 2) as \"清洁代码率%\"\nFROM commit_metrics \nGROUP BY author\nHAVING COUNT(*) >= 3\nORDER BY COUNT(*) DESC \nLIMIT 10", "refId": "A"}], "title": "开发者贡献排名 (Top 10)", "type": "table"}], "refresh": "5m", "schemaVersion": 36, "style": "dark", "tags": ["code-quality", "overview"], "templating": {"list": []}, "time": {"from": "now-30d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "代码质量效能分析 - 简化版", "uid": "code-quality-simple", "version": 1, "weekStart": ""}
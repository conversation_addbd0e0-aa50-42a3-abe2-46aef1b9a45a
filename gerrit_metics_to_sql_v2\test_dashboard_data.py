#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import psycopg2
from configparser import ConfigParser
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_dashboard_data():
    """测试看板数据"""
    
    # 读取配置
    config = ConfigParser()
    config.read('config.ini')
    
    try:
        # 连接数据库
        conn = psycopg2.connect(
            host=config.get('database', 'host'),
            port=config.get('database', 'port'),
            database=config.get('database', 'database'),
            user=config.get('database', 'user'),
            password=config.get('database', 'password')
        )
        
        cursor = conn.cursor()
        
        print("=== 数据库连接测试 ===")
        
        # 1. 检查表是否存在
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'commit_metrics'
            );
        """)
        table_exists = cursor.fetchone()[0]
        print(f"表 commit_metrics 存在: {table_exists}")
        
        if not table_exists:
            print("❌ 表不存在，请先创建表")
            return
        
        # 2. 检查总记录数
        cursor.execute("SELECT COUNT(*) FROM commit_metrics;")
        total_count = cursor.fetchone()[0]
        print(f"总记录数: {total_count}")
        
        if total_count == 0:
            print("❌ 表中没有数据")
            return
        
        # 3. 检查时间字段
        cursor.execute("""
            SELECT 
                MIN(commit_time) as min_time,
                MAX(commit_time) as max_time,
                MIN(commit_date) as min_date,
                MAX(commit_date) as max_date
            FROM commit_metrics;
        """)
        time_info = cursor.fetchone()
        print(f"时间范围:")
        print(f"  commit_time: {time_info[0]} 到 {time_info[1]}")
        print(f"  commit_date: {time_info[2]} 到 {time_info[3]}")
        
        # 4. 检查作者数据
        cursor.execute("SELECT DISTINCT author FROM commit_metrics LIMIT 10;")
        authors = [row[0] for row in cursor.fetchall()]
        print(f"作者列表 (前10个): {authors}")
        
        # 5. 测试开发者个人效能查询
        if authors:
            test_author = authors[0]
            print(f"\n=== 测试作者: {test_author} ===")
            
            # 测试提交数量趋势查询
            cursor.execute("""
                SELECT 
                    commit_time::date as time,
                    COUNT(*) as commit_count
                FROM commit_metrics 
                WHERE author = %s 
                  AND commit_time >= CURRENT_DATE - INTERVAL '30 days'
                GROUP BY commit_time::date
                ORDER BY commit_time::date
                LIMIT 5;
            """, (test_author,))
            
            trend_data = cursor.fetchall()
            print(f"提交数量趋势查询结果: {len(trend_data)} 条记录")
            for row in trend_data:
                print(f"  {row[0]}: {row[1]} 次提交")
            
            # 测试代码质量分布查询
            cursor.execute("""
                SELECT 
                    COALESCE(quality_level, 'unknown') as quality_level,
                    COUNT(*) as count
                FROM commit_metrics 
                WHERE author = %s 
                  AND commit_time >= CURRENT_DATE - INTERVAL '30 days'
                GROUP BY quality_level;
            """, (test_author,))
            
            quality_data = cursor.fetchall()
            print(f"代码质量分布查询结果: {len(quality_data)} 条记录")
            for row in quality_data:
                print(f"  {row[0]}: {row[1]} 次")
            
            # 测试平均变更行数查询
            cursor.execute("""
                SELECT AVG(COALESCE(changed_lines, 0)) as avg_lines
                FROM commit_metrics 
                WHERE author = %s 
                  AND commit_time >= CURRENT_DATE - INTERVAL '30 days';
            """, (test_author,))
            
            avg_lines = cursor.fetchone()[0]
            print(f"平均变更行数: {avg_lines}")
            
            # 测试总提交数查询
            cursor.execute("""
                SELECT COUNT(*) as total_commits
                FROM commit_metrics 
                WHERE author = %s 
                  AND commit_time >= CURRENT_DATE - INTERVAL '30 days';
            """, (test_author,))
            
            total_commits = cursor.fetchone()[0]
            print(f"总提交数: {total_commits}")
        
        cursor.close()
        conn.close()
        
        print("\n=== 测试完成 ===")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_dashboard_data() 
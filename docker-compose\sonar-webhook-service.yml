version: '3.7'
services:
  sonar-webhook-service:
    build: ./webhook-service
    container_name: sonar-webhook-service
    ports:
      - "8080:8080"
    environment:
      - VICTORIA_METRICS_URL=http://victoriametrics:8428
      - TZ=Asia/Shanghai
    volumes:
      - ./webhook-service/logs:/app/logs
    restart: unless-stopped
    depends_on:
      - victoriametrics
    networks:
      - monitoring

  victoriametrics:
    image: victoriametrics/victoria-metrics:v1.120.0
    container_name: victoriametrics
    ports:
      - "8428:8428"
    volumes:
      - ./victoria-data:/victoria-metrics-data
    restart: unless-stopped
    command:
      - "-retentionPeriod=12"
      - "-storageDataPath=/victoria-metrics-data"
      - "-influxSkipSingleField"
      - "-influxSkipMeasurement"
    environment:
      - TZ=Asia/Shanghai
    networks:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - TZ=Asia/Shanghai
    volumes:
      - grafana-data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
    restart: unless-stopped
    networks:
      - monitoring

volumes:
  grafana-data:

networks:
  monitoring:
    driver: bridge

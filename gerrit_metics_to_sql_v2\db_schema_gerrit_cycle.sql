-- 提交指标表 - 周期分析专用
CREATE TABLE IF NOT EXISTS commit_metrics_cycle (
    -- 提交标识
    commit_id VARCHAR(50) PRIMARY KEY, -- Gerrit提交唯一标识。例如：Iabc123def456
    change_id VARCHAR(50) NOT NULL, -- Gerrit变更ID。例如：Iabc123def456
    change_id_short VARCHAR(10) NOT NULL, -- 变更ID短码，便于快速检索。例如：Iabc123de
    patch_set INT NOT NULL, -- 补丁集编号，表示第几次提交。例如：3
    
    -- Gerrit 基础信息
    gerrit_project VARCHAR(200) NOT NULL, -- Gerrit项目名。例如：wearable-wr02
    branch VARCHAR(100) NOT NULL, -- 分支名。例如：master
    subject TEXT NOT NULL, -- 提交主题/标题。
    status VARCHAR(20) NOT NULL, -- Gerrit变更状态。例如：MERGED, NEW, ABANDONED
    commit_time TIMESTAMPTZ NOT NULL, -- 提交时间，含时区。
    created TIMESTAMPTZ NOT NULL, -- 变更创建时间。
    updated TIMESTAMPTZ NOT NULL, -- 变更最后更新时间。
    author VARCHAR(100) NOT NULL, -- 作者名称。
    owner VARCHAR(100) NOT NULL, -- 所有者名称。
    number INT NOT NULL, -- Gerrit变更号。
    url TEXT NOT NULL, -- Gerrit变更详情URL。
    reviewers TEXT[] NOT NULL, -- 评审人列表。
    changed_lines INT NOT NULL, -- 变更总行数（插入+删除）。
    insertions INT NOT NULL, -- 插入行数。
    deletions INT NOT NULL, -- 删除行数。
    patchset_count INT NOT NULL, -- 补丁集总数。
    repo_path VARCHAR(500) NOT NULL, -- 仓库路径。
    
    -- SonarQube 项目信息
    sonar_project VARCHAR(200), -- SonarQube项目名称。
    sonar_key VARCHAR(200), -- SonarQube项目Key。
    sonar_creation_date TIMESTAMPTZ, -- SonarQube项目创建时间。
    
    -- SonarQube 聚合指标 - 当前问题
    sq_blocker INT DEFAULT 0, -- 当前阻断级别问题数（Blocker）。示例：1
    sq_critical INT DEFAULT 0, -- 当前严重级别问题数（Critical）。示例：2
    sq_major INT DEFAULT 0, -- 当前主要级别问题数（Major）。示例：3
    sq_minor INT DEFAULT 0, -- 当前次要级别问题数（Minor）。示例：4
    sq_info INT DEFAULT 0, -- 当前信息级别问题数（Info）。示例：0
    
    -- SonarQube 聚合指标 - 已解决问题
    sq_resolved_blocker INT DEFAULT 0, -- 已解决阻断级别问题数。示例：1
    sq_resolved_critical INT DEFAULT 0, -- 已解决严重级别问题数。示例：2
    sq_resolved_major INT DEFAULT 0, -- 已解决主要级别问题数。示例：3
    sq_resolved_minor INT DEFAULT 0, -- 已解决次要级别问题数。示例：4
    sq_resolved_info INT DEFAULT 0, -- 已解决信息级别问题数。示例：0
    
    -- SonarQube 代码质量与规模指标
    ncloc INTEGER,                        -- 非注释代码行数（NCLOC），即有效代码行数。示例：12000
    statements INTEGER,                   -- 语句数。示例：8000
    functions INTEGER,                    -- 方法/函数数。示例：300
    files INTEGER,                        -- 文件数。示例：50
    comment_lines INTEGER,                -- 注释行数。示例：1500
    comment_lines_density FLOAT,          -- 注释率（百分比，浮点型）。示例：12.5
    complexity INTEGER,                   -- 代码圈复杂度总和。示例：150
    duplicated_lines_density FLOAT,       -- 重复密度（百分比，浮点型）。示例：3.2
    duplicated_lines INTEGER,             -- 重复行数，代码中被检测为重复的行数。示例：120
    duplicated_blocks INTEGER,            -- 重复块数，代码中被检测为重复的代码块数量。示例：5
    duplicated_files INTEGER,             -- 重复文件数，包含重复代码的文件数量。示例：2
    
    -- 新增：数据分析增强字段
    -- 时间维度字段（便于按时间分组统计）
    commit_date DATE, -- 提交日期（仅日期部分）
    commit_year INT, -- 提交年份
    commit_month INT, -- 提交月份
    commit_week INT, -- 提交周数
    commit_hour INT, -- 提交小时
    
    -- 统计计算字段
    total_issues INT, -- 总问题数
    total_resolved_issues INT, -- 总已解决问题数
    critical_issues INT, -- 严重问题数（阻断+严重）
    issue_density FLOAT, -- 问题密度（每千行代码的问题数）
    
    -- 分类字段
    change_size_category VARCHAR(20), -- 变更大小分类
    quality_level VARCHAR(20), -- 代码质量等级
    
    -- 原始数据备份
    gerrit_raw JSONB, -- Gerrit原始数据JSON。
    sonar_issues JSONB, -- SonarQube原始问题数据JSON。
    
    -- 创建和更新时间
    created_at TIMESTAMPTZ DEFAULT NOW(), -- 记录创建时间。
    updated_at TIMESTAMPTZ DEFAULT NOW()  -- 记录最后更新时间。
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_commit_metrics_cycle_commit_time ON commit_metrics_cycle(commit_time);
CREATE INDEX IF NOT EXISTS idx_commit_metrics_cycle_author ON commit_metrics_cycle(author);
CREATE INDEX IF NOT EXISTS idx_commit_metrics_cycle_owner ON commit_metrics_cycle(owner);
CREATE INDEX IF NOT EXISTS idx_commit_metrics_cycle_repo_path ON commit_metrics_cycle(repo_path);
CREATE INDEX IF NOT EXISTS idx_commit_metrics_cycle_reviewers ON commit_metrics_cycle USING GIN(reviewers);
CREATE INDEX IF NOT EXISTS idx_commit_metrics_cycle_gerrit_project ON commit_metrics_cycle(gerrit_project);
CREATE INDEX IF NOT EXISTS idx_commit_metrics_cycle_branch ON commit_metrics_cycle(branch);
CREATE INDEX IF NOT EXISTS idx_commit_metrics_cycle_status ON commit_metrics_cycle(status);
CREATE INDEX IF NOT EXISTS idx_commit_metrics_cycle_sonar_key ON commit_metrics_cycle(sonar_key);

-- 新增：为数据分析字段创建索引
CREATE INDEX IF NOT EXISTS idx_commit_metrics_cycle_commit_date ON commit_metrics_cycle(commit_date);
CREATE INDEX IF NOT EXISTS idx_commit_metrics_cycle_commit_year ON commit_metrics_cycle(commit_year);
CREATE INDEX IF NOT EXISTS idx_commit_metrics_cycle_commit_month ON commit_metrics_cycle(commit_month);
CREATE INDEX IF NOT EXISTS idx_commit_metrics_cycle_commit_week ON commit_metrics_cycle(commit_week);
CREATE INDEX IF NOT EXISTS idx_commit_metrics_cycle_total_issues ON commit_metrics_cycle(total_issues);
CREATE INDEX IF NOT EXISTS idx_commit_metrics_cycle_critical_issues ON commit_metrics_cycle(critical_issues);
CREATE INDEX IF NOT EXISTS idx_commit_metrics_cycle_change_size_category ON commit_metrics_cycle(change_size_category);
CREATE INDEX IF NOT EXISTS idx_commit_metrics_cycle_quality_level ON commit_metrics_cycle(quality_level);

-- 为 JSONB 字段创建 GIN 索引以支持高效查询
CREATE INDEX IF NOT EXISTS idx_commit_metrics_cycle_gerrit_raw ON commit_metrics_cycle USING GIN(gerrit_raw jsonb_path_ops);
CREATE INDEX IF NOT EXISTS idx_commit_metrics_cycle_sonar_issues ON commit_metrics_cycle USING GIN(sonar_issues jsonb_path_ops);

-- 添加自动更新 updated_at 的触发器（注意：触发器函数已在原表创建时定义，这里只需创建触发器）
CREATE TRIGGER update_commit_metrics_cycle_modtime
BEFORE UPDATE ON commit_metrics_cycle
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

-- 创建计算字段的触发器（注意：触发器函数已在原表创建时定义，这里只需创建触发器）
CREATE TRIGGER calculate_derived_fields_cycle_trigger
BEFORE INSERT OR UPDATE ON commit_metrics_cycle
FOR EACH ROW
EXECUTE FUNCTION calculate_derived_fields();
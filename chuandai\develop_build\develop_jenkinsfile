pipeline {
    agent { label 'WinBuild' }
    options {
        timestamps()
        disableConcurrentBuilds()
        timeout(time: 1, unit: 'HOURS')
        buildDiscarder(logRotator(numToKeepStr: '30'))
    }
    parameters {
        string(
            name: 'BuildTagVersion',
            trim: true,
            description: '''可选参数，如果需要推送tag到Gerrit仓库，需填写tag名。例如：
        wr02_release_v0.80
        wr02_release_v0.81'''
        )

    }
    environment {
        // 项目基础配置
        PROJECT_NAME = "wr02"

        // 构建信息 - 在环境变量定义时生成时间戳
        BUILD_TIMESTAMP = sh(script: 'date +%Y%m%d%H%M', returnStdout: true).trim()
        ARTIFACT_NAME = "wr02-release-temp"
        
        // 标签版本处理
        DEFAULT_TAG_VERSION = "release"
        FORMATTED_TAG_VERSION = sh(script: "echo '${BuildTagVersion ?: DEFAULT_TAG_VERSION}' | tr '_' '-'", returnStdout: true).trim()
        
        // 仓库配置
        REPO_URL = "ssh://jenkinsadmin@********:29418/wr02"
        REPO_MANIFEST = "wearable-sifli-wr02-release.xml"
        REPO_BRANCH = "wr02_release"
        
        // 制品库配置
        NEXUS_URL = "*********:8089"
        NEXUS_REPO = "wearable-build"
        NEXUS_GROUP_ID = "wearable-wr02-release"
        
        // 系统路径配置
        //PATH = "/home/<USER>/bin:/usr/local/bin:/usr/bin:/usr/bin:/sbin:/usr/local/bin:/home/<USER>/bin:/cygdrive/c/Program Files/Common Files/Oracle/Java/javapath:/cygdrive/c/App/OpenSSH-Win64:/cygdrive/c/Windows/system32:/cygdrive/c/Windows:/cygdrive/c/Windows/System32/Wbem:/cygdrive/c/Windows/System32/WindowsPowerShell/v1.0:/cygdrive/c/Windows:/cygdrive/c/App/curl/bin:/cygdrive/c/App/GnuWin32/bin:/cygdrive/c/App/7-Zip:/cygdrive/c/Program Files/dotnet:/cygdrive/c/Keil_v5/ARM/ARMCC/bin:/cygdrive/c/Users/<USER>/bin:/cygdrive/c/Users/<USER>/.vscode/extensions/cl.eide-3.20.0/res/tools/win32/unify_builder:/cygdrive/c/Users/<USER>/.vscode/extensions/cl.eide-3.20.1/res/tools/win32/unify_builder:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python37/Scripts:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python37:/cygdrive/c/Program Files (x86)/GNU Arm Embedded Toolchain/10 2021.10/bin:/cygdrive/c/Users/<USER>/AppData/Local/Microsoft/WindowsApps:/cygdrive/c/App/Microsoft VS Code/bin"
    }
    stages {
        stage('校验Tag参数格式') {
            when {
                expression {
                    return params.BuildTagVersion != null && params.BuildTagVersion.trim() != ""
                }
            }
            steps {
                script {
                    def tag = params.BuildTagVersion.trim()
                    if (!(tag ==~ /^wr02_release_v\d+\.\d+$/)) {
                        error "BuildTagVersion参数格式错误，必须为wr02_beta_v0.40这种格式，当前为: ${tag}"
                    }
                }
            }
        }
        stage('清理构建缓存') {
            steps {
                sh '''
                echo "清理工作空间: CIBuildScript,CIBuildArtifacts";
                rm -rf CIBuild*;
                mkdir CIBuildArtifacts;
                '''
            }
        }
        stage('下载代码') {
            options {
                timeout(time: 6, unit: 'MINUTES')
            }
            steps {
                sh """
                if [ ! -d ".repo" ]; then
                    echo "首次初始化repo";
                    repo init -u ${env.REPO_URL} -m ${env.REPO_MANIFEST} -b ${env.REPO_BRANCH};
                    repo sync -c --no-clone-bundle;
                else
                    echo "已存在repo，回退所有仓库到基线并同步"
                    repo start --all ${env.REPO_BRANCH};
                    repo forall -c "git gc --prune=now";
                    repo forall -c "git reset --hard HEAD^";
                    repo forall -c "git clean -dfx -e .clangd  .eide.usr.ctx.json  eide.json";
                    repo sync -c --no-clone-bundle;
                fi
                unix2dos tools/upgrade_tools/*.bat;
                cmd.exe /c 'icacls  tools/upgrade_tools/bin_update_src/qw_file_packerbg1.exe  /grant Everyone:F'
                """
                script {
                    def exitCode = sh(script: "if [ -d '.repo' ]; then echo 'success'; else echo 'fail'; fi", returnStdout: true).trim()
                    if (exitCode == 'fail') {
                        error "代码同步失败，请检查网络或仓库配置"
                    }
                }
            }
        }
        stage('生成编译配置文件') {
            steps {
                sh '''
                cp -r /home/<USER>/CIBuildScript ./;
                sh CIBuildScript/builder_boot/getbuilder_bootv32.sh version=PIPELINE workdir=Rwr02;
                sh CIBuildScript/builder_lcpu/getbuilder_lcpuv32.sh version=PIPELINE workdir=Rwr02;
                sh CIBuildScript/builder_app/getbuilder_appv32.sh version=PIPELINE workdir=Rwr02;
                '''
                script {
                    def configFiles = sh(script: "find CIBuildScript -name '*.params' | wc -l", returnStdout: true).trim()
                    if (configFiles == '0') {
                        error "编译配置文件生成失败，请检查CIBuildScript路径"
                    }
                }
            }
        }
        stage('编译BOOT') {
            options {
                timeout(time: 5, unit: 'MINUTES')
            }
            steps {
                sh '''
                echo "编译BOOT"
                unify_builder -p CIBuildScript/builder_boot/builder_boot.params --rebuild
                '''
                script {
                    def bootBuildSuccess = sh(script: "find . -name '*boot*.bin' | wc -l", returnStdout: true).trim()
                    if (bootBuildSuccess == '0') {
                        error "BOOT编译失败，请检查编译日志"
                    }
                }
            }
        }
        stage('编译LCPU') {
            options {
                timeout(time: 5, unit: 'MINUTES')
            }
            steps {
                sh '''
                echo "编译LCPU"
                unify_builder -p CIBuildScript/builder_lcpu/builder_lcpu.params --rebuild
                '''
                script {
                    def lcpuBuildSuccess = sh(script: "find . -name '*lcpu*.bin' | wc -l", returnStdout: true).trim()
                    if (lcpuBuildSuccess == '0') {
                        error "LCPU编译失败，请检查编译日志"
                    }
                }
            }
        }
        stage('编译APP') {
            options {
                timeout(time: 10, unit: 'MINUTES')
            }
            steps {
                sh '''
                echo "编译APP"
                unify_builder -p CIBuildScript/builder_app/builder_app.params --rebuild
                '''
                script {
                    def appBuildSuccess = sh(script: "find . -name '*app*.bin' | wc -l", returnStdout: true).trim()
                    if (appBuildSuccess == '0') {
                        error "APP编译失败，请检查编译日志"
                    }
                }
            }
        }
        stage('执行打包BAT') {
            steps {
                script {
                    bat """
                        cd "tools\\upgrade_tools"
                        call WR02_OTA_Upgrade.bat
                            
                    """
                }
            }
        }
        // stage('执行打包BAT') {
        //     steps {
        //         sh '''
        //         cd tools/upgrade_tools/;
        //         /cygdrive/c/Windows/System32/cmd.exe /c 'WR02_OTA_Upgrade.bat'
        //         '''
        //     }
        // }        
        stage('推送标签-owner') {
            when {
                expression {
                    return env.BuildTagVersion != null && env.BuildTagVersion.trim() != ""
                }
            }
            options {
                timeout(time: 5, unit: 'MINUTES')
            }
            steps {
                sh '''
                # 使用环境变量中的时间戳，确保与Nexus版本一致
                currTime=${BUILD_TIMESTAMP}
                TagName="${BuildTagVersion}_${currTime}"
                maniName="${TagName}.xml"
                VerMess=$(echo $BuildTagVersion | awk -F'_' '{print $3}')
                
                # 生成manifest文件
                echo "生成manifest文件: ${maniName}"
                repo manifest -r -o ${maniName} || { echo "错误: manifest文件生成失败"; exit 1; }
                
                # 获取仓库列表并处理标签
                echo "开始处理仓库标签"
                repo list -n > repolist.xml
                while read repo; do
                    [ -z "$repo" ] && continue
                    echo "处理仓库: $repo"
                    cd ${WORKSPACE}/$repo
                    [ ! -d ".git" ] && { echo "错误: $repo 不是有效的git仓库"; continue; }
                    
                    git tag -l | xargs git tag -d
                    git tag -a ${TagName} -m "Version ${VerMess}"
                    git push qwkj ${TagName} || echo "警告: 推送tag到 $repo 失败"
                done < repolist.xml
                
                # 处理WR02仓库
                echo "处理WR02仓库"
                cd ${WORKSPACE}
                rm -rf wr02
                git clone "${REPO_URL}" || { echo "错误: 克隆wr02仓库失败"; exit 1; }
                
                cd wr02
                mkdir -p `git rev-parse --git-dir`/hooks/
                curl -Lo `git rev-parse --git-dir`/hooks/commit-msg http://********:8081/tools/hooks/commit-msg
                chmod +x `git rev-parse --git-dir`/hooks/commit-msg
                
                # 提交manifest文件
                git checkout ${REPO_BRANCH}
                mkdir -p qwkj_manifests/version/
                mv ${WORKSPACE}/${maniName} qwkj_manifests/version/
                git add qwkj_manifests
                git commit -m "backup: The ${TagName} is automatically generated by Jenkins pipeline." -m "Signed-off-by: ${BUILD_USER} <${BUILD_USER}@igpsport.com>"
                git push origin ${REPO_BRANCH} || { echo "错误: 推送${REPO_BRANCH}分支失败"; exit 1; }
                
                # 创建并推送标签
                git tag -a ${TagName} -m "Version ${VerMess}"
                git push origin ${TagName} || { echo "错误: 推送tag到wr02仓库失败"; exit 1; }
                
                echo "标签推送完成"
                cd ${WORKSPACE}
                '''
            }
        }
        stage('拷贝制品') {
            steps {
                sh '''
                echo "拷贝制品"
                cp -r tools/upgrade_tools CIBuildArtifacts/
                rm -rf CIBuildArtifacts/upgrade_tools/Asset
                rm -rf CIBuildArtifacts/upgrade_tools/bin_update_src
                7z a -tzip CIBuildArtifacts.zip CIBuildArtifacts
                '''
            }
        }
        stage('制品备份归档至jenkins') {
            steps {
                archiveArtifacts artifacts: '*.zip', fingerprint: true, onlyIfSuccessful: true
            }
        }
        stage('制品推送至nexus') {
            steps {
                script {
                    def nexusArtifactId
                    
                    if (env.BuildTagVersion) {
                        nexusArtifactId = env.FORMATTED_TAG_VERSION
                    } else {
                        nexusArtifactId = "${env.ARTIFACT_NAME}"
                    }
                    nexusArtifactUploader artifacts: [[artifactId: "${nexusArtifactId}", 
                                                    classifier: "", 
                                                    file: "CIBuildArtifacts.zip", 
                                                    type: "zip"]], 
                                    credentialsId: "nexus", 
                                    groupId: "${NEXUS_GROUP_ID}", 
                                    nexusUrl: "${NEXUS_URL}", 
                                    nexusVersion: "nexus3", 
                                    protocol: "http", 
                                    repository: "${NEXUS_REPO}", 
                                    version: "${BUILD_TIMESTAMP}"
                }
            }
        }
    }
    post {
        success {
            echo "构建成功"
        }
        failure {
            echo "构建失败"
        }
        always {
            sh '''
            echo "清理临时文件"
            rm -rf .repo/repo_cache
            '''
        }
    }
} 
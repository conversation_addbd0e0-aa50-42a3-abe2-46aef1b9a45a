#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import logging
import psycopg2
from datetime import datetime
from flask import Flask, request, jsonify
from dateutil import parser
from psycopg2.extras import RealDictCursor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/app/logs/webhook.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# 数据库配置
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': os.getenv('DB_PORT', '5432'),
    'database': os.getenv('DB_NAME', 'sonar_grafana'),
    'user': os.getenv('DB_USER', 'grafana_user'),
    'password': os.getenv('DB_PASSWORD', 'grafana_pass')
}

def get_db_connection():
    """获取数据库连接"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def save_quality_gate_data(webhook_data):
    """保存质量门数据到数据库"""
    try:
        conn = get_db_connection()
        if not conn:
            return False
        
        cursor = conn.cursor()
        
        # 解析webhook数据
        project_key = webhook_data.get('project', {}).get('key', 'unknown')
        project_name = webhook_data.get('project', {}).get('name', 'unknown')
        task_id = webhook_data.get('taskId', 'unknown')
        status = webhook_data.get('status', 'unknown')
        analysed_at = webhook_data.get('analysedAt', datetime.now().isoformat())
        
        # 解析时间戳
        try:
            analysed_timestamp = parser.parse(analysed_at)
        except:
            analysed_timestamp = datetime.now()
        
        # 质量门状态
        quality_gate_status = 'unknown'
        if 'qualityGate' in webhook_data:
            quality_gate_status = webhook_data['qualityGate'].get('status', 'unknown')
        
        # 插入主记录
        insert_query = """
        INSERT INTO sonar_quality_gates 
        (project_key, project_name, task_id, status, quality_gate_status, analysed_at, webhook_data)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        RETURNING id
        """
        
        cursor.execute(insert_query, (
            project_key, project_name, task_id, status, 
            quality_gate_status, analysed_timestamp, json.dumps(webhook_data)
        ))
        
        quality_gate_id = cursor.fetchone()[0]
        
        # 插入条件详情
        if 'qualityGate' in webhook_data and 'conditions' in webhook_data['qualityGate']:
            for condition in webhook_data['qualityGate']['conditions']:
                metric_key = condition.get('metricKey', 'unknown')
                operator = condition.get('operator', 'unknown')
                cond_status = condition.get('status', 'unknown')
                actual_value = condition.get('actualValue', '0')
                threshold_value = condition.get('threshold', '0')
                
                try:
                    actual_value_num = float(actual_value) if actual_value else 0
                except:
                    actual_value_num = 0
                
                try:
                    threshold_value_num = float(threshold_value) if threshold_value else 0
                except:
                    threshold_value_num = 0
                
                condition_query = """
                INSERT INTO sonar_conditions 
                (quality_gate_id, metric_key, operator, status, actual_value, threshold_value)
                VALUES (%s, %s, %s, %s, %s, %s)
                """
                
                cursor.execute(condition_query, (
                    quality_gate_id, metric_key, operator, cond_status,
                    actual_value_num, threshold_value_num
                ))
                
                # 同时插入到指标历史表
                metrics_query = """
                INSERT INTO sonar_metrics_history 
                (project_key, metric_key, metric_value, measured_at)
                VALUES (%s, %s, %s, %s)
                """
                
                cursor.execute(metrics_query, (
                    project_key, metric_key, actual_value_num, analysed_timestamp
                ))
        
        conn.commit()
        cursor.close()
        conn.close()
        
        logger.info(f"成功保存项目 {project_key} 的质量门数据")
        return True
        
    except Exception as e:
        logger.error(f"保存质量门数据时出错: {e}")
        if conn:
            conn.rollback()
            conn.close()
        return False

@app.route('/webhook/sonarqube', methods=['POST'])
def sonarqube_webhook():
    """接收SonarQube webhook"""
    try:
        # 获取webhook数据
        webhook_data = request.get_json()
        
        if not webhook_data:
            logger.warning("收到空的webhook数据")
            return jsonify({'error': 'No data received'}), 400
        
        logger.info(f"收到SonarQube webhook: 项目={webhook_data.get('project', {}).get('key', 'unknown')}")
        
        # 保存到数据库
        if save_quality_gate_data(webhook_data):
            return jsonify({'status': 'success', 'message': 'Webhook processed successfully'}), 200
        else:
            return jsonify({'status': 'error', 'message': 'Failed to save data'}), 500
            
    except Exception as e:
        logger.error(f"处理webhook时出错: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    try:
        conn = get_db_connection()
        if conn:
            conn.close()
            return jsonify({'status': 'healthy', 'database': 'connected'}), 200
        else:
            return jsonify({'status': 'unhealthy', 'database': 'disconnected'}), 500
    except Exception as e:
        return jsonify({'status': 'unhealthy', 'error': str(e)}), 500

@app.route('/stats', methods=['GET'])
def get_stats():
    """获取统计信息"""
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'Database connection failed'}), 500
        
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        # 获取项目数量
        cursor.execute("SELECT COUNT(DISTINCT project_key) as project_count FROM sonar_quality_gates")
        project_count = cursor.fetchone()['project_count']
        
        # 获取最近的分析
        cursor.execute("""
        SELECT project_key, project_name, status, quality_gate_status, analysed_at 
        FROM sonar_quality_gates 
        ORDER BY analysed_at DESC 
        LIMIT 10
        """)
        recent_analyses = cursor.fetchall()
        
        cursor.close()
        conn.close()
        
        return jsonify({
            'project_count': project_count,
            'recent_analyses': [dict(row) for row in recent_analyses]
        }), 200
        
    except Exception as e:
        logger.error(f"获取统计信息时出错: {e}")
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    # 确保日志目录存在
    os.makedirs('/app/logs', exist_ok=True)
    
    logger.info("启动SonarQube Webhook服务...")
    app.run(host='0.0.0.0', port=8080, debug=False)

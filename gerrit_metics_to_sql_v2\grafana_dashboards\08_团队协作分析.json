{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  COUNT(DISTINCT author) as \"活跃开发者数\",\n  COUNT(DISTINCT gerrit_project) as \"活跃项目数\",\n  COUNT(*) as \"总协作次数\",\n  ROUND(AVG(array_length(reviewers, 1)), 1) as \"平均审查者数\",\n  COUNT(CASE WHEN array_length(reviewers, 1) >= 2 THEN 1 END) as \"多人审查数\",\n  ROUND(COUNT(CASE WHEN array_length(reviewers, 1) >= 2 THEN 1 END)::float / COUNT(*) * 100, 2) as \"多人审查率%\"\nFROM commit_metrics \nWHERE commit_time >= NOW() - INTERVAL '30 days'\n  AND reviewers IS NOT NULL", "refId": "A"}], "title": "团队协作关键指标 (最近30天)", "type": "stat"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "time_series", "rawQuery": true, "rawSql": "SELECT \n  commit_date as time,\n  COUNT(DISTINCT author) as \"每日活跃开发者数\",\n  COUNT(*) as \"每日协作次数\",\n  ROUND(AVG(array_length(reviewers, 1)), 1) as \"平均审查者数\"\nFROM commit_metrics \nWHERE commit_date >= CURRENT_DATE - INTERVAL '30 days'\n  AND reviewers IS NOT NULL\nGROUP BY commit_date\nORDER BY commit_date", "refId": "A"}], "title": "每日协作活动趋势", "type": "timeseries"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6}, "id": 3, "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "pieType": "pie", "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  CASE \n    WHEN array_length(reviewers, 1) IS NULL OR array_length(reviewers, 1) = 0 THEN '无审查者'\n    WHEN array_length(reviewers, 1) = 1 THEN '单人审查'\n    WHEN array_length(reviewers, 1) = 2 THEN '双人审查'\n    WHEN array_length(reviewers, 1) = 3 THEN '三人审查'\n    ELSE '多人审查(4+)'\n  END as metric,\n  COUNT(*) as value\nFROM commit_metrics\nWHERE commit_time >= NOW() - INTERVAL '30 days'\nGROUP BY \n  CASE \n    WHEN array_length(reviewers, 1) IS NULL OR array_length(reviewers, 1) = 0 THEN '无审查者'\n    WHEN array_length(reviewers, 1) = 1 THEN '单人审查'\n    WHEN array_length(reviewers, 1) = 2 THEN '双人审查'\n    WHEN array_length(reviewers, 1) = 3 THEN '三人审查'\n    ELSE '多人审查(4+)'\n  END\nORDER BY COUNT(*) DESC", "refId": "A"}], "title": "审查者数量分布", "type": "piechart"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "多人审查率%"}, "properties": [{"id": "unit", "value": "percent"}, {"id": "custom.displayMode", "value": "color-background"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 50}, {"color": "green", "value": 80}]}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 14}, "id": 4, "options": {"showHeader": true}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  author as \"开发者\",\n  COUNT(*) as \"提交数\",\n  COUNT(DISTINCT gerrit_project) as \"参与项目数\",\n  ROUND(AVG(array_length(reviewers, 1)), 1) as \"平均审查者数\",\n  COUNT(CASE WHEN array_length(reviewers, 1) >= 2 THEN 1 END) as \"多人审查数\",\n  ROUND(COUNT(CASE WHEN array_length(reviewers, 1) >= 2 THEN 1 END)::float / COUNT(*) * 100, 2) as \"多人审查率%\",\n  SUM(changed_lines) as \"总代码行数\"\nFROM commit_metrics \nWHERE commit_time >= NOW() - INTERVAL '30 days'\n  AND reviewers IS NOT NULL\nGROUP BY author\nHAVING COUNT(*) >= 3\nORDER BY \"提交数\" DESC\nLIMIT 15", "refId": "A"}], "title": "开发者协作统计", "type": "table"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 14}, "id": 5, "options": {"showHeader": true}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  gerrit_project as \"项目名称\",\n  COUNT(*) as \"协作次数\",\n  COUNT(DISTINCT author) as \"参与开发者数\",\n  ROUND(AVG(array_length(reviewers, 1)), 1) as \"平均审查者数\",\n  COUNT(CASE WHEN array_length(reviewers, 1) >= 2 THEN 1 END) as \"多人审查数\",\n  ROUND(COUNT(CASE WHEN array_length(reviewers, 1) >= 2 THEN 1 END)::float / COUNT(*) * 100, 2) as \"多人审查率%\",\n  ROUND(AVG(patchset_count), 1) as \"平均修订次数\"\nFROM commit_metrics \nWHERE commit_time >= NOW() - INTERVAL '30 days'\n  AND reviewers IS NOT NULL\nGROUP BY gerrit_project\nHAVING COUNT(*) >= 5\nORDER BY \"参与开发者数\" DESC, \"协作次数\" DESC\nLIMIT 15", "refId": "A"}], "title": "项目协作统计", "type": "table"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 22}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "time_series", "rawQuery": true, "rawSql": "SELECT \n  EXTRACT(HOUR FROM commit_time) as time,\n  COUNT(*) as \"协作次数\",\n  COUNT(DISTINCT author) as \"活跃开发者数\"\nFROM commit_metrics \nWHERE commit_time >= NOW() - INTERVAL '7 days'\nGROUP BY EXTRACT(HOUR FROM commit_time)\nORDER BY time", "refId": "A"}], "title": "每小时协作活动分布 (最近7天)", "type": "timeseries"}], "refresh": "5m", "schemaVersion": 36, "style": "dark", "tags": ["团队协作", "开发者"], "templating": {"list": []}, "time": {"from": "now-30d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "团队协作分析仪表板", "uid": "team-collaboration", "version": 1, "weekStart": ""}
{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  COUNT(*) as \"总提交数\",\n  ROUND(AVG(changed_lines), 0) as \"平均变更行数\",\n  ROUND(AVG(EXTRACT(EPOCH FROM (updated - created))/3600), 1) as \"平均处理时长(小时)\",\n  COUNT(CASE WHEN change_size_category = 'XL' THEN 1 END) as \"大型变更数\",\n  ROUND(AVG(ncloc), 0) as \"平均代码行数\",\n  COUNT(CASE WHEN EXTRACT(EPOCH FROM (updated - created))/3600 > 24 THEN 1 END) as \"超24小时处理数\"\nFROM commit_metrics \nWHERE commit_time >= NOW() - INTERVAL '30 days'", "refId": "A"}], "title": "开发性能关键指标 (最近30天)", "type": "stat"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "XS"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "XL"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6}, "id": 2, "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "pieType": "pie", "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  change_size_category as metric,\n  COUNT(*) as value\nFROM commit_metrics\nWHERE commit_time >= NOW() - INTERVAL '30 days'\nGROUP BY change_size_category\nORDER BY \n  CASE change_size_category \n    WHEN 'XS' THEN 1 \n    WHEN 'S' THEN 2 \n    WHEN 'M' THEN 3 \n    WHEN 'L' THEN 4 \n    WHEN 'XL' THEN 5 \n  END", "refId": "A"}], "title": "变更大小分布", "type": "piechart"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "time_series", "rawQuery": true, "rawSql": "SELECT \n  commit_date as time,\n  COUNT(*) as \"每日提交数\",\n  ROUND(AVG(changed_lines), 0) as \"平均变更行数\",\n  ROUND(AVG(EXTRACT(EPOCH FROM (updated - created))/3600), 1) as \"平均处理时长(小时)\"\nFROM commit_metrics \nWHERE commit_date >= CURRENT_DATE - INTERVAL '30 days'\nGROUP BY commit_date\nORDER BY commit_date", "refId": "A"}], "title": "每日性能指标趋势", "type": "timeseries"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "平均处理时长(小时)"}, "properties": [{"id": "custom.displayMode", "value": "color-background"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 24}, {"color": "red", "value": 72}]}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 14}, "id": 4, "options": {"showHeader": true}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  author as \"开发者\",\n  COUNT(*) as \"提交数\",\n  ROUND(AVG(changed_lines), 0) as \"平均变更行数\",\n  SUM(changed_lines) as \"总变更行数\",\n  ROUND(AVG(EXTRACT(EPOCH FROM (updated - created))/3600), 1) as \"平均处理时长(小时)\",\n  COUNT(CASE WHEN change_size_category IN ('L', 'XL') THEN 1 END) as \"大型变更数\",\n  ROUND(AVG(patchset_count), 1) as \"平均修订次数\"\nFROM commit_metrics \nWHERE commit_time >= NOW() - INTERVAL '30 days'\nGROUP BY author\nHAVING COUNT(*) >= 3\nORDER BY \"总变更行数\" DESC\nLIMIT 15", "refId": "A"}], "title": "开发者性能排行", "type": "table"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 14}, "id": 5, "options": {"showHeader": true}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  gerrit_project as \"项目名称\",\n  COUNT(*) as \"提交数\",\n  ROUND(AVG(changed_lines), 0) as \"平均变更行数\",\n  SUM(changed_lines) as \"总变更行数\",\n  ROUND(AVG(EXTRACT(EPOCH FROM (updated - created))/3600), 1) as \"平均处理时长(小时)\",\n  COUNT(CASE WHEN change_size_category IN ('L', 'XL') THEN 1 END) as \"大型变更数\",\n  COUNT(DISTINCT author) as \"参与开发者数\"\nFROM commit_metrics \nWHERE commit_time >= NOW() - INTERVAL '30 days'\nGROUP BY gerrit_project\nHAVING COUNT(*) >= 5\nORDER BY \"总变更行数\" DESC\nLIMIT 15", "refId": "A"}], "title": "项目性能统计", "type": "table"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "points", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 22}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "time_series", "rawQuery": true, "rawSql": "SELECT \n  commit_time as time,\n  changed_lines as \"变更行数\",\n  EXTRACT(EPOCH FROM (updated - created))/3600 as \"处理时长(小时)\",\n  patchset_count as \"修订次数\"\nFROM commit_metrics \nWHERE commit_time >= NOW() - INTERVAL '7 days'\n  AND (changed_lines > 500 OR EXTRACT(EPOCH FROM (updated - created))/3600 > 48 OR patchset_count > 5)\nORDER BY commit_time", "refId": "A"}], "title": "异常性能指标散点图 (最近7天)", "type": "timeseries"}], "refresh": "5m", "schemaVersion": 36, "style": "dark", "tags": ["性能监控", "开发效率"], "templating": {"list": []}, "time": {"from": "now-30d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "性能监控仪表板", "uid": "performance-monitor", "version": 1, "weekStart": ""}
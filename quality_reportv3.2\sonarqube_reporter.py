#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 导入必要的库
import os
import sys
import json
import time
import datetime
import requests
import sqlite3
from PIL import Image, ImageDraw, ImageFont
import configparser
from wechat_sender import WeChatWorkSender

class SonarQubeReporter:
    """
    SonarQube质量报告生成器
    用于从SonarQube获取代码质量数据，生成报告并发送通知
    """
    def __init__(self, sonar_url=None, project_id=None, token=None, config_path=None):
        """
        初始化报告生成器
        
        参数:
            sonar_url (str): SonarQube服务器URL，如果为None则从配置文件读取
            project_id (str): 项目ID，如果为None则从配置文件读取
            token (str): 访问令牌，如果为None则从配置文件读取
            config_path (str): 配置文件路径，如果为None则使用默认路径
        """
        # 获取基础路径
        if getattr(sys, 'frozen', False):
            # 如果是打包后的可执行文件
            self.base_path = os.path.dirname(sys.executable)
        else:
            # 如果是源码运行
            self.base_path = os.path.dirname(os.path.abspath(__file__))
            
        # 配置文件路径
        self.config_path = config_path or os.path.join(self.base_path, "config.ini")
        self.wechat_config_path = os.path.join(self.base_path, "wechat_config.ini")
        
        # 如果直接提供了参数，使用参数值
        self.sonar_url = sonar_url
        self.project_id = project_id
        self.token = token
        
        # 如果没有提供参数，从配置文件读取
        if not all([self.sonar_url, self.project_id, self.token]):
            # load_config由configparser提供，默认不传递路径的情况下，依赖self.base_path的值
            self._load_config()
        
        # 数据库路径 - 使用项目ID作为数据库名称的一部分
        self.db_path = os.path.join(self.base_path, "quality_data.db")
        
        # 报告输出目录
        self.reports_base_dir = os.path.join(self.base_path, "reports")
        
        # 字体目录
        self.fonts_dir = os.path.join(self.base_path, "fonts")
        
        # 初始化数据库
        self.init_database()
        
    def _load_config(self):
        """加载配置文件"""
        if not os.path.exists(self.config_path):
            self._create_default_config()
            
        config = configparser.ConfigParser()
        with open(self.config_path, 'r', encoding='utf-8') as f:
            config.read_file(f)
        
        # 从配置文件读取项目ID
        self.project_id = config['SonarQube']['ProjectID']
        
        # 从 SonarQube_Servers 部分读取项目特定的配置
        servers_section = 'SonarQube_Servers'
        if servers_section in config:
            url_key = f'{self.project_id}_url'
            token_key = f'{self.project_id}_token'
            
            if url_key in config[servers_section] and token_key in config[servers_section]:
                self.sonar_url = config[servers_section][url_key]
                self.token = config[servers_section][token_key]
            else:
                raise ValueError(f"未找到项目 {self.project_id} 的SonarQube配置")
        else:
            raise ValueError(f"配置文件中缺少 {servers_section} 部分")
        
        # 读取通知配置
        self.enable_wechat = config.getboolean('Notification', 'EnableWeChatNotification', fallback=True)
        
    def _create_default_config(self):
        """创建默认配置文件"""
        config = configparser.ConfigParser()
        config['SonarQube'] = {
            'ProjectID': 'your-project-id'
        }
        config['SonarQube_Servers'] = {
            'your-project-id_url': 'http://your-sonarqube-server:9000',
            'your-project-id_token': 'your-token'
        }
        config['Notification'] = {
            'EnableWeChatNotification': 'true'
        }
        
        os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
        with open(self.config_path, 'w') as configfile:
            config.write(configfile)
            
    def _ensure_directories(self):
        """确保所需目录存在"""
        # 创建报告基础目录
        os.makedirs(self.reports_base_dir, exist_ok=True)
        
        # 创建项目特定的报告目录
        self.project_report_dir = os.path.join(self.reports_base_dir, self.project_id)
        os.makedirs(self.project_report_dir, exist_ok=True)
        
        # 创建字体目录
        os.makedirs(self.fonts_dir, exist_ok=True)
        
    def _get_font_path(self):
        """获取可用的字体路径"""
        # 定义字体搜索路径
        font_search_paths = [
            # 本地字体目录
            os.path.join(self.fonts_dir, "wqy-microhei.ttc"),
            os.path.join(self.fonts_dir, "simhei.ttf"),
            
            # Linux系统字体路径
            "/usr/share/fonts/wqy-microhei/wqy-microhei.ttc",
            "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
            "/usr/share/fonts/chinese/TrueType/wqy-microhei.ttc",
            "/usr/share/fonts/wqy/wqy-microhei.ttc",
            "/usr/share/fonts/truetype/wqy-microhei/wqy-microhei.ttc",
            "/usr/share/fonts/chinese/TrueType/simhei.ttf",
            "/usr/share/fonts/truetype/arphic/uming.ttc",
            "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc",
            
            # Windows系统字体路径
            "C:\\Windows\\Fonts\\simhei.ttf",
            "C:\\Windows\\Fonts\\msyh.ttf"
        ]
        
        # 查找第一个可用的字体
        for font_path in font_search_paths:
            if os.path.exists(font_path):
                print(f"使用字体文件: {font_path}")
                return font_path
                
        # 如果没有找到可用字体，返回None
        print("警告：未找到可用的中文字体")
        return None
        
    def init_database(self):
        """
        初始化SQLite数据库以存储质量指标
        只在表不存在时新建表，升级结构时只用ALTER TABLE，不重建。
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        try:
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='metrics'")
            if not cursor.fetchone():
                # 表不存在时新建表，包含所有字段
                cursor.execute('''
                CREATE TABLE metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT,
                    project_id TEXT,
                    bugs INTEGER,
                    vulnerabilities INTEGER,
                    code_smells INTEGER,
                    blocker_issues INTEGER,
                    major_issues INTEGER,
                    info_issues INTEGER DEFAULT 0,
                    duplications_percentage REAL,
                    comment_percentage REAL,
                    code_lines INTEGER,
                    comment_lines INTEGER,
                    duplicated_lines INTEGER,
                    duplicated_blocks INTEGER,
                    duplicated_files INTEGER,
                    complexity INTEGER,
                    total_lines INTEGER
                )
                ''')
                print("metrics表已创建")
            conn.commit()
        except Exception as e:
            print(f"初始化数据库时出错: {str(e)}")
            conn.rollback()
        finally:
            conn.close()
        # 验证表结构
        self._verify_table_structure()

    def _verify_table_structure(self):
        """验证表结构并自动补充缺失字段"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        try:
            cursor.execute("PRAGMA table_info(metrics)")
            columns = [col[1] for col in cursor.fetchall()]
            # 需要的字段
            required_columns = [
                "id", "date", "project_id", "bugs", "vulnerabilities", "code_smells",
                "blocker_issues", "major_issues", "info_issues", "duplications_percentage", "comment_percentage",
                "code_lines", "comment_lines", "duplicated_lines", "duplicated_blocks", "duplicated_files",
                "complexity", "total_lines"
            ]
            for col in required_columns:
                if col not in columns:
                    if col == "info_issues":
                        cursor.execute("ALTER TABLE metrics ADD COLUMN info_issues INTEGER DEFAULT 0;")
                        print("已自动添加info_issues字段")
                    # 可按需补充其他字段
            conn.commit()
        except Exception as e:
            print(f"验证表结构时发生错误: {str(e)}")
        finally:
            conn.close()
        
    def get_metric_value(self, metric_key):
        """
        从SonarQube API获取指标值
        
        参数:
            metric_key (str): 指标键名
            
        返回:
            str: 指标值
        """
        url = f"{self.sonar_url}/api/measures/component"
        params = {
            "component": self.project_id,
            "metricKeys": metric_key
        }
        
        try:
            print(f"正在获取指标 {metric_key} (项目ID: {self.project_id})")
            # 使用认证发送GET请求
            response = requests.get(url, params=params, auth=(self.token, ''))
            print(f"API请求URL: {response.url}")
            
            if response.status_code == 200:
                data = response.json()
                # 检查返回的数据中是否包含measures
                if 'component' in data and 'measures' in data['component'] and len(data['component']['measures']) > 0:
                    value = data['component']['measures'][0]['value']
                    print(f"成功获取指标 {metric_key} = {value}")
                    return value
                print(f"警告：API返回数据中未找到指标 {metric_key}")
                print(f"API返回数据: {data}")
                return "0"
            else:
                print(f"错误：获取指标 {metric_key} 失败")
                print(f"HTTP状态码: {response.status_code}")
                print(f"错误响应: {response.text}")
                if response.status_code == 404:
                    print(f"提示：项目 {self.project_id} 可能不存在，请检查项目ID是否正确")
                elif response.status_code == 401:
                    print("提示：Token可能无效或没有权限访问该项目")
                return "0"
        except Exception as e:
            print(f"错误：获取指标 {metric_key} 时发生异常")
            print(f"异常信息: {str(e)}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return "0"
    
    def get_sqale_rating(self, metric_type):
        """
        获取指标的SQALE评级
        
        参数:
            metric_type (str): 指标类型
            
        返回:
            str: SQALE评级 (A-E) 或 "-"
        """
        # 评级映射
        rating_map = {
            "1.0": "A",  # 优秀
            "2.0": "B",  # 良好
            "3.0": "C",  # 一般
            "4.0": "D",  # 较差
            "5.0": "E"   # 糟糕
        }
        
        # 阻断问题、主要问题、提示问题没有SQALE评级
        if metric_type in ["blocker_issues", "major_issues", "info_issues"]:
            return "-"
        
        # 根据指标类型获取对应的评级键和指标
        if metric_type == "bugs":
            # 构建API请求参数
            url = f"{self.sonar_url}/api/measures/component"
            params = {
                "component": self.project_id,
                "metricKeys": "reliability_rating,bugs"
            }
            
            try:
                # 使用认证发送GET请求
                response = requests.get(url, params=params, auth=(self.token, ''))
                if response.status_code == 200:
                    data = response.json()
                    if 'component' in data and 'measures' in data['component']:
                        measures = data['component']['measures']
                        for measure in measures:
                            if measure['metric'] == 'reliability_rating':
                                return rating_map.get(measure['value'], "-")
                return "-"
            except Exception as e:
                print(f"获取{metric_type}评级时发生错误: {str(e)}")
                return "-"
                
        elif metric_type == "vulnerabilities":
            rating_key = "security_rating"     # 安全性评级
        elif metric_type == "code_smells":
            rating_key = "sqale_rating"        # 可维护性评级
        else:
            return "-"
            
        # 获取评级值并转换（对于其他指标）
        rating = self.get_metric_value(rating_key)
        return rating_map.get(rating, "-")
            
    def get_latest_metrics(self):
        """
        获取最新的指标数据。
        如果当天没有数据，则获取最近一次的数据。
        
        返回:
            tuple: (数据日期, 指标数据字典)
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 获取最新的数据记录
            cursor.execute("""
                SELECT 
                    date,
                    bugs,
                    vulnerabilities,
                    code_smells,
                    debt,
                    duplications_percentage,
                    duplicated_lines,
                    duplicated_blocks,
                    duplicated_files,
                    lines_of_code,
                    comment_lines,
                    comment_percentage,
                    complexity,
                    blocker_issues,
                    major_issues,
                    info_issues
                FROM metrics
                WHERE project_id = ?
                ORDER BY date DESC
                LIMIT 1
            """, (self.project_id,))
            
            row = cursor.fetchone()
            
            if row:
                metrics = {
                    "date": row[0],
                    "bugs": row[1],
                    "vulnerabilities": row[2],
                    "code_smells": row[3],
                    "debt": row[4],
                    "duplications_percentage": row[5],
                    "duplicated_lines": row[6],
                    "duplicated_blocks": row[7],
                    "duplicated_files": row[8],
                    "lines_of_code": row[9],
                    "comment_lines": row[10],
                    "comment_percentage": row[11],
                    "complexity": row[12],
                    "blocker_issues": row[13],
                    "major_issues": row[14],
                    "info_issues": row[15]
                }
                return row[0], metrics
            
            return None, None
            
        except sqlite3.Error as e:
            print(f"数据库错误: {str(e)}")
            return None, None
        finally:
            conn.close()

    def collect_metrics(self):
        """
        收集所有需要的SonarQube指标
        
        返回:
            dict: 包含所有指标的字典
        """
        print(f"\n开始收集项目 {self.project_id} 的质量指标...")
        
        today = datetime.date.today()
        
        # 从数据库获取今天的数据
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute("""
            SELECT 
                bugs, vulnerabilities, code_smells,
                blocker_issues, major_issues,
                duplications_percentage, comment_percentage,
                code_lines, comment_lines,
                duplicated_lines, duplicated_blocks, duplicated_files,
                complexity
            FROM metrics 
            WHERE date = ? AND project_id = ?
            ORDER BY id DESC 
            LIMIT 1
            """, (today.strftime('%Y-%m-%d'), self.project_id))
            
            row = cursor.fetchone()
            
            if row:
                print("从数据库获取到今天的数据")
                metrics = {
                    'bugs': row[0] or 0,
                    'vulnerabilities': row[1] or 0,
                    'code_smells': row[2] or 0,
                    'blocker_issues': row[3] or 0,
                    'major_issues': row[4] or 0,
                    'duplications_percentage': row[5] or 0,
                    'comment_percentage': row[6] or 0,
                    'code_lines': row[7] or 0,
                    'comment_lines': row[8] or 0,
                    'duplicated_lines': row[9] or 0,
                    'duplicated_blocks': row[10] or 0,
                    'duplicated_files': row[11] or 0,
                    'complexity': row[12] or 0
                }
                return metrics
                
        except Exception as e:
            print(f"从数据库获取数据时出错: {str(e)}")
        finally:
            conn.close()
            
        print("数据库中没有今天的数据，尝试从SonarQube API获取...")
        
        # 如果数据库中没有今天的数据，从SonarQube API获取
        metrics = {
            "bugs": int(self.get_metric_value("bugs")),
            "vulnerabilities": int(self.get_metric_value("vulnerabilities")),
            "code_smells": int(self.get_metric_value("code_smells")),
            "blocker_issues": int(self.get_metric_value("blocker_violations")),
            "major_issues": int(self.get_metric_value("major_violations")),
            "duplications_percentage": float(self.get_metric_value("duplicated_lines_density")),
            "comment_percentage": float(self.get_metric_value("comment_lines_density")),
            "code_lines": int(self.get_metric_value("ncloc")),
            "comment_lines": int(self.get_metric_value("comment_lines")),
            "duplicated_lines": int(self.get_metric_value("duplicated_lines")),
            "duplicated_blocks": int(self.get_metric_value("duplicated_blocks")),
            "duplicated_files": int(self.get_metric_value("duplicated_files")),
            "complexity": int(self.get_metric_value("complexity"))
        }
        
        # 检查是否所有指标都为0
        if all(value == 0 for value in metrics.values()):
            print("\n警告：所有指标都为0，这可能表示：")
            print("1. 项目ID不正确")
            print("2. 项目还未进行分析")
            print("3. Token没有足够的权限\n")
        else:
            print("\n成功获取到项目指标数据")
            
        return metrics
    
    def store_metrics(self, date, metrics):
        """
        将指标数据存入SQLite数据库
        
        参数:
            date (str): 日期
            metrics (dict): 指标数据字典
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 插入指标数据到数据库
        cursor.execute('''
        INSERT INTO metrics (
            project_id, date, bugs, vulnerabilities, code_smells, 
            debt, duplications_percentage, duplicated_lines, 
            duplicated_blocks, duplicated_files, lines_of_code,
            comment_lines, comment_percentage, complexity,
            blocker_issues, major_issues, info_issues
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            self.project_id, date, metrics["bugs"], metrics["vulnerabilities"],
            metrics["code_smells"], metrics["debt"], metrics["duplications_percentage"],
            metrics["duplicated_lines"], metrics["duplicated_blocks"], metrics["duplicated_files"],
            metrics["lines_of_code"], metrics["comment_lines"], metrics["comment_percentage"],
            metrics["complexity"], metrics["blocker_issues"], metrics["major_issues"],
            metrics["info_issues"]
        ))
        
        conn.commit()
        conn.close()
    
    def get_previous_metrics(self, days_ago=1):
        """
        获取前一天的指标数据
        
        参数:
            days_ago (int): 要获取多少天前的数据，默认为1（昨天）
            
        返回:
            dict: 包含前一天指标数据的字典，如果没有数据则返回None
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取指定日期的数据
            target_date = (datetime.datetime.now() - datetime.timedelta(days=days_ago)).strftime('%Y-%m-%d')
            print(f"正在查找 {target_date} 的数据")
            print(f"当前项目ID: {self.project_id}")
            
            # 首先检查数据库中有哪些数据
            cursor.execute("SELECT DISTINCT date, project_id FROM metrics ORDER BY date DESC")
            available_data = cursor.fetchall()
            print("数据库中可用的数据:")
            for date, proj_id in available_data:
                print(f"  日期: {date}, 项目: {proj_id}")
            
            cursor.execute("""
            SELECT 
                bugs, vulnerabilities, code_smells,
                blocker_issues, major_issues,
                duplications_percentage, comment_percentage,
                code_lines, comment_lines,
                duplicated_lines, duplicated_blocks, duplicated_files,
                complexity
            FROM metrics 
            WHERE date = ? AND project_id = ?
            ORDER BY id DESC 
            LIMIT 1
            """, (target_date, self.project_id))
            
            row = cursor.fetchone()
            
            if row:
                print(f"找到 {target_date} 的数据")
                return {
                    'bugs': row[0] or 0,
                    'vulnerabilities': row[1] or 0,
                    'code_smells': row[2] or 0,
                    'blocker_issues': row[3] or 0,
                    'major_issues': row[4] or 0,
                    'duplications_percentage': row[5] or 0,
                    'comment_percentage': row[6] or 0,
                    'code_lines': row[7] or 0,
                    'comment_lines': row[8] or 0,
                    'duplicated_lines': row[9] or 0,
                    'duplicated_blocks': row[10] or 0,
                    'duplicated_files': row[11] or 0,
                    'complexity': row[12] or 0
                }
            
            # 如果没有找到指定日期的数据，尝试找最近的一条历史数据
            print(f"未找到 {target_date} 的数据，尝试查找最近的历史数据")
            cursor.execute("""
            SELECT 
                bugs, vulnerabilities, code_smells,
                blocker_issues, major_issues,
                duplications_percentage, comment_percentage,
                code_lines, comment_lines,
                duplicated_lines, duplicated_blocks, duplicated_files,
                complexity, date
            FROM metrics 
            WHERE date < ? AND project_id = ?
            ORDER BY date DESC 
            LIMIT 1
            """, (target_date, self.project_id))
            
            row = cursor.fetchone()
            
            if row:
                print(f"找到历史数据，日期为: {row[13]}")
                return {
                    'bugs': row[0] or 0,
                    'vulnerabilities': row[1] or 0,
                    'code_smells': row[2] or 0,
                    'blocker_issues': row[3] or 0,
                    'major_issues': row[4] or 0,
                    'duplications_percentage': row[5] or 0,
                    'comment_percentage': row[6] or 0,
                    'code_lines': row[7] or 0,
                    'comment_lines': row[8] or 0,
                    'duplicated_lines': row[9] or 0,
                    'duplicated_blocks': row[10] or 0,
                    'duplicated_files': row[11] or 0,
                    'complexity': row[12] or 0
                }
            
            print(f"警告：未找到任何历史数据用于对比")
            return None
            
        except Exception as e:
            print(f"获取历史数据时发生错误: {str(e)}")
            import traceback
            print(traceback.format_exc())
            return None
        finally:
            conn.close()
    
    def calculate_change(self, current, previous, use_percentage=True):
        """
        计算相对于上次的变化
        
        参数:
            current (int/float): 当前值
            previous (int/float): 上次的值
            use_percentage (bool): 是否使用百分比显示（默认True）
            
        返回:
            str: 变化值（百分比或具体数值）
        """
        # 如果上次的值不存在，返回N/A
        if previous is None:
            return "N/A"
            
        # 计算变化
        if use_percentage:
            # 如果上次的值为0，返回N/A
            if previous == 0:
                return "N/A"
            # 计算变化百分比
            change = ((current - previous) / previous) * 100
            if change > 0:
                return f"+{change:.1f}%"  # 增加显示为+
            return f"{change:.1f}%"       # 减少直接显示-
        else:
            # 计算具体数值变化
            change = current - previous
            if change > 0:
                return f"较昨日+{change}"
            elif change < 0:
                return f"较昨日{change}"  # 负数自带减号
            else:
                return "无变化"
    
    def generate_daily_report_image(self):
        """生成每日报告图片"""
        print(f"开始为项目 {self.project_id} 生成报告...")  # 添加调试输出
        
        # 确保目录存在
        self._ensure_directories()
        
        # 获取字体
        font = get_chinese_font(15)
        title_font = get_chinese_font(16)
        header_font = get_chinese_font(18)
        
        # 获取当前指标
        current_metrics = self.collect_metrics()
        if not any(current_metrics.values()):  # 检查是否所有指标都为0
            raise Exception(f"无法获取项目 {self.project_id} 的任何指标数据，请检查项目ID是否正确以及是否有访问权限")
        
        # 判断今天是否为周一，决定环比对比数据的天数
        today = datetime.date.today()
        if today.weekday() == 0:  # 周一
            days_ago = 3  # 上周五
        else:
            days_ago = 1  # 昨天
        
        # 获取前一天或上周五的指标
        previous_metrics = self.get_previous_metrics(days_ago=days_ago)
        print(f"当前指标: {current_metrics}")  # 调试输出
        print(f"对比指标: {previous_metrics}")  # 调试输出
        
        # 计算变化率
        bugs_change = "N/A"
        blocker_issues_change = "N/A"
        
        # 风险预警信息列表
        risk_warnings = []
        
        if previous_metrics:
            # Bugs变化
            current_bugs = current_metrics.get("bugs", 0)
            previous_bugs = previous_metrics.get("bugs", 0)
            bugs_diff = current_bugs - previous_bugs
            print(f"Bugs对比 - 当前: {current_bugs}, 对比日: {previous_bugs}, 差值: {bugs_diff}")  # 调试输出
            
            if bugs_diff > 0:
                bugs_change = f"较上次+{bugs_diff}"
                risk_warnings.append(f"Bugs较上次增加{bugs_diff}个")
            elif bugs_diff < 0:
                bugs_change = f"较上次{bugs_diff}"
            else:
                bugs_change = "无变化"
            
            # 阻断问题变化
            current_blocker = current_metrics.get("blocker_issues", 0)
            previous_blocker = previous_metrics.get("blocker_issues", 0)
            blocker_diff = current_blocker - previous_blocker
            print(f"阻断问题对比 - 当前: {current_blocker}, 对比日: {previous_blocker}, 差值: {blocker_diff}")  # 调试输出
            
            if blocker_diff > 0:
                blocker_issues_change = f"较上次+{blocker_diff}"
                risk_warnings.append(f"阻断问题较上次增加{blocker_diff}个")
            elif blocker_diff < 0:
                blocker_issues_change = f"较上次{blocker_diff}"
            else:
                blocker_issues_change = "无变化"
        
        # 获取SQALE评级
        bugs_rating = self.get_sqale_rating("bugs")
        blocker_rating = self.get_sqale_rating("blocker_issues")
        
        # 创建图像
        width, height = 800, 290  # 增加高度以容纳新增的行
        img = Image.new('RGB', (width, height), color='white')
        d = ImageDraw.Draw(img)
        
        today_str = today.strftime("%Y-%m-%d")
        
        # 绘制标题
        title = f"质量日简报 ({today_str}) - {self.project_id}"
        # 计算标题文本宽度并居中显示
        title_width = title_font.getsize(title)[0]
        title_x = (width - title_width) / 2
        d.text((title_x, 10), title, fill='black', font=title_font)
        
        # 定义颜色
        header_bg_color = (221, 235, 247)  # Excel风格的淡蓝色
        border_color = (200, 200, 200)  # 浅灰色边框
        text_color = (60, 60, 60)  # 深灰色文字
        
        # 定义单元格尺寸
        cell_width = width / 5
        cell_height = 40  # 减小单元格高度
        
        # 绘制表格标题行
        headers = ["类别", "指标", "当前数量", "环比", "SQALE评级"]
        y_start = 40  # 从40像素开始绘制表格
        
        # 绘制表头行
        for i, header in enumerate(headers):
            x = i * cell_width
            # 绘制背景和边框
            d.rectangle([x, y_start, x + cell_width, y_start + cell_height], 
                       fill=header_bg_color, outline=border_color)
            
            # 计算文字居中位置
            text_width = font.getsize(header)[0]
            text_x = x + (cell_width - text_width) / 2
            text_y = y_start + (cell_height - font.size) / 2
            d.text((text_x, text_y), header, fill=text_color, font=header_font)
        
        # 绘制数据行
        def draw_row(y_pos, category, indicator, value, change, rating):
            # 绘制每个单元格
            cells = [category, indicator, str(value), change, rating]
            for i, cell_text in enumerate(cells):
                x = i * cell_width
                d.rectangle([x, y_pos, x + cell_width, y_pos + cell_height], 
                          fill='white', outline=border_color)
                
                # 计算文字居中位置
                text_width = font.getsize(cell_text)[0]
                text_x = x + (cell_width - text_width) / 2
                text_y = y_pos + (cell_height - font.size) / 2
                d.text((text_x, text_y), cell_text, fill=text_color, font=font)
                
                # 如果是最后一列且是评级，添加红色背景
                if i == 4 and cell_text == 'E':
                    d.rectangle([x + 5, y_pos + 5, x + cell_width - 5, y_pos + cell_height - 5],
                              fill=(255, 0, 0))
                    # 重新绘制白色文字
                    d.text((text_x, text_y), cell_text, fill='white', font=font)
        
        # 绘制第一行数据
        draw_row(y_start + cell_height, "可靠性", "Bugs", 
                current_metrics["bugs"], bugs_change, bugs_rating)
        
        # 绘制第二行数据
        draw_row(y_start + cell_height * 2, "问题", "阻断问题", 
                current_metrics["blocker_issues"], blocker_issues_change, blocker_rating)
        
        # 计算重复文件变化
        duplicated_files_change = "N/A"
        if previous_metrics:
            current_duplicated_files = current_metrics.get("duplicated_files", 0)
            previous_duplicated_files = previous_metrics.get("duplicated_files", 0)
            duplicated_files_diff = current_duplicated_files - previous_duplicated_files
            
            if duplicated_files_diff > 0:
                duplicated_files_change = f"较上次+{duplicated_files_diff}"
            elif duplicated_files_diff < 0:
                duplicated_files_change = f"较上次{duplicated_files_diff}"
            else:
                duplicated_files_change = "无变化"
        
        # 绘制第三行数据 - 重复文件信息
        draw_row(y_start + cell_height * 3, "大小", "重复文件", 
                current_metrics["duplicated_files"], duplicated_files_change, "-")
        
        # 在表格下方添加风险预警
        warning_y = y_start + cell_height * 4 + 20  # 表格底部下方20像素
        if risk_warnings:
            warning_text = "风险预警：" + "；".join(risk_warnings) + "！"
        else:
            warning_text = "风险预警：暂无！"
        d.text((20, warning_y), warning_text, fill='black', font=font)
        
        # 添加链接文本（使用黑色，左对齐）
        link_y = warning_y + 30  # 风险预警下方30像素
        link_text = f"链接直达：{self.sonar_url}/project/issues?id={self.project_id}&resolved=false"
        d.text((20, link_y), link_text, fill='black', font=font)  # 改为黑色
        
        # 生成报告文件名
        report_filename = f"daily_report_{today_str}.png"
        image_path = os.path.join(self.project_report_dir, report_filename)
        
        # 保存图像
        img.save(image_path)
        
        # 创建归档链接文件
        archive_info = {
            "date": today_str,
            "project_id": self.project_id,
            "metrics": current_metrics,
            "changes": {
                "bugs": bugs_change,
                "blocker_issues": blocker_issues_change,
                "duplicated_files": duplicated_files_change
            },
            "ratings": {
                "bugs": bugs_rating,
                "blocker": blocker_rating
            }
        }
        
        # 保存归档信息
        archive_json = os.path.join(self.project_report_dir, f"report_info_{today_str}.json")
        with open(archive_json, 'w', encoding='utf-8') as f:
            json.dump(archive_info, f, ensure_ascii=False, indent=2)
        
        return image_path
    
    def send_wechat_notification(self, image_path):
        """发送企业微信通知"""
        if not self.enable_wechat:
            print("企业微信通知已禁用")
            return True
            
        # 创建企业微信发送器
        wechat_sender = WeChatWorkSender(config_file=self.wechat_config_path)
        
        try:
            return wechat_sender.send_image(image_path)
        except Exception as e:
            print(f"发送通知时发生错误: {str(e)}")
            return False

def get_chinese_font(size):
    font_paths = [
        # Linux常见路径
        "/usr/share/fonts/wqy-microhei/wqy-microhei.ttc",
        "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
        "/usr/share/fonts/chinese/TrueType/simhei.ttf",
        # Windows常见路径
        "C:\\Windows\\Fonts\\simhei.ttf",
        "C:\\Windows\\Fonts\\msyh.ttf",
        # 项目自带fonts目录
        os.path.join(os.path.dirname(os.path.abspath(__file__)), "fonts", "simhei.ttf"),
        os.path.join(os.path.dirname(os.path.abspath(__file__)), "fonts", "wqy-microhei.ttc"),
    ]
    for font_path in font_paths:
        if os.path.exists(font_path):
            try:
                return ImageFont.truetype(font_path, size)
            except Exception:
                continue
    raise Exception("未找到可用的中文字体文件，请在Linux下安装 wqy-microhei，或在Windows下安装 simhei.ttf/msyh.ttf，并放到系统或项目fonts目录下。")

def main():
    """主函数"""
    try:
        # 支持通过命令行参数指定配置文件路径
        config_path = sys.argv[1] if len(sys.argv) > 1 else None
        
        # 创建报告生成器
        reporter = SonarQubeReporter(config_path)
        
        # 生成每日报告
        image_path = reporter.generate_daily_report_image()
        
        # 发送企业微信通知
        reporter.send_wechat_notification(image_path)
        
        print(f"日简报已生成: {image_path}")
        print("任务完成!")
        
    except Exception as e:
        print(f"执行过程中发生错误: {str(e)}")
        import traceback
        print(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main() 
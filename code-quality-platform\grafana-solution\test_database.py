#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import psycopg2
import sys

# 数据库配置
DB_CONFIG = {
    'host': '**********',
    'port': '5434',
    'database': 'mydatabase',
    'user': 'admin',
    'password': 'admin123'
}

def test_database_connection():
    """测试数据库连接"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        print("✅ 数据库连接成功")
        return conn
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def test_table_exists(conn, table_name):
    """测试表是否存在"""
    try:
        cursor = conn.cursor()
        cursor.execute(f"""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_name = '{table_name}'
        );
        """)
        exists = cursor.fetchone()[0]
        cursor.close()
        return exists
    except Exception as e:
        print(f"❌ 检查表 {table_name} 时出错: {e}")
        return False

def test_view_exists(conn, view_name):
    """测试视图是否存在"""
    try:
        cursor = conn.cursor()
        cursor.execute(f"""
        SELECT EXISTS (
            SELECT FROM information_schema.views 
            WHERE table_name = '{view_name}'
        );
        """)
        exists = cursor.fetchone()[0]
        cursor.close()
        return exists
    except Exception as e:
        print(f"❌ 检查视图 {view_name} 时出错: {e}")
        return False

def test_data_count(conn, table_name):
    """测试表中的数据数量"""
    try:
        cursor = conn.cursor()
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        cursor.close()
        return count
    except Exception as e:
        print(f"❌ 查询 {table_name} 数据数量时出错: {e}")
        return 0

def test_sample_data(conn, table_name, limit=3):
    """查看表中的示例数据"""
    try:
        cursor = conn.cursor()
        cursor.execute(f"SELECT * FROM {table_name} LIMIT {limit}")
        rows = cursor.fetchall()
        
        # 获取列名
        cursor.execute(f"""
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = '{table_name}' 
        ORDER BY ordinal_position
        """)
        columns = [row[0] for row in cursor.fetchall()]
        
        cursor.close()
        return columns, rows
    except Exception as e:
        print(f"❌ 查询 {table_name} 示例数据时出错: {e}")
        return [], []

def main():
    print("🔍 代码质量数据库测试工具")
    print("=" * 50)
    
    # 测试数据库连接
    conn = test_database_connection()
    if not conn:
        sys.exit(1)
    
    # 测试主表
    print("\n📊 检查主表...")
    if test_table_exists(conn, 'commit_metrics'):
        count = test_data_count(conn, 'commit_metrics')
        print(f"✅ commit_metrics 表存在，包含 {count:,} 条记录")
        
        if count > 0:
            print("\n📋 commit_metrics 示例数据:")
            columns, rows = test_sample_data(conn, 'commit_metrics', 2)
            if columns and rows:
                # 显示关键列
                key_columns = ['commit_id', 'author', 'gerrit_project', 'commit_time', 'total_issues', 'quality_level']
                available_columns = [col for col in key_columns if col in columns]
                
                print(f"关键字段: {', '.join(available_columns)}")
                for row in rows:
                    row_dict = dict(zip(columns, row))
                    key_values = [str(row_dict.get(col, 'N/A')) for col in available_columns]
                    print(f"  {' | '.join(key_values)}")
    else:
        print("❌ commit_metrics 表不存在")
    
    # 测试视图
    print("\n📈 检查分析视图...")
    views_to_check = [
        'daily_commit_stats',
        'author_contribution_stats', 
        'project_quality_stats',
        'monthly_trend_stats',
        'quality_level_distribution'
    ]
    
    for view_name in views_to_check:
        if test_view_exists(conn, view_name):
            count = test_data_count(conn, view_name)
            print(f"✅ {view_name} 视图存在，包含 {count} 条记录")
        else:
            print(f"❌ {view_name} 视图不存在")
    
    # 测试关键查询
    print("\n🔍 测试关键查询...")
    
    # 测试总览查询
    try:
        cursor = conn.cursor()
        cursor.execute("""
        SELECT 
          COUNT(*) as total_commits,
          COUNT(DISTINCT author) as total_authors,
          COUNT(DISTINCT gerrit_project) as total_projects,
          SUM(total_issues) as total_issues
        FROM commit_metrics 
        WHERE commit_time >= NOW() - INTERVAL '30 days'
        """)
        result = cursor.fetchone()
        print(f"✅ 总览查询成功: 提交数={result[0]}, 开发者数={result[1]}, 项目数={result[2]}, 问题数={result[3]}")
        cursor.close()
    except Exception as e:
        print(f"❌ 总览查询失败: {e}")
    
    # 测试质量分布查询
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT quality_level, COUNT(*) FROM commit_metrics GROUP BY quality_level")
        results = cursor.fetchall()
        print("✅ 质量分布查询成功:")
        for quality_level, count in results:
            print(f"  {quality_level}: {count} 条")
        cursor.close()
    except Exception as e:
        print(f"❌ 质量分布查询失败: {e}")
    
    conn.close()
    
    print("\n🎉 数据库测试完成!")
    print("\n📝 下一步操作:")
    print("1. 如果所有检查都通过，可以直接导入Grafana仪表板")
    print("2. 如果缺少视图，请运行 init-scripts/01-init-views.sql 创建视图")
    print("3. 如果数据为空，请确认 find_and_collect.py 脚本正常运行")

if __name__ == "__main__":
    main()

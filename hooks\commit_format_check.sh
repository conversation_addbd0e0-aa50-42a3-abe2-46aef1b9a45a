#!/bin/bash
# Commit格式校验钩子脚本
# 用于校验commit格式是否符合规范
# 校验内容：
# 1. 标题（必有）
# 2. 正文（可有可无）
# 3. 禅道关联单号（可有可无）
# 4. ChangeID（必有）
# 5. 作者邮箱（必有）

# 定义变量
projectName="$2"
commitID="$(echo "$@" | awk -F '--newrev ' '{print $2}' | awk '{print $1}')"
uploaderUsername="$(echo "$@" | awk -F '--uploader-username ' '{print $2}' | awk '{print $1}')"
gerritPath="/home/<USER>/gerrit_site/git"
projectPath="${gerritPath}/${projectName}"
iniCommit=${commitID:0:7}
iniPath="/home/<USER>/gerrit_site/hooks/inidir/commit-format-check"
iniTittle="${iniPath}/tittle_${iniCommit}"
iniContent="${iniPath}/content_${iniCommit}"

# 创建临时目录
if [ ! -d ${iniPath} ];then
    mkdir -p ${iniPath}
fi

# 获取完整的commit提交信息
commitTittle="$(git --git-dir="${projectPath}.git" log --pretty=format:"%s" -n 1 ${commitID})"
commitMsg="$(git --git-dir="${projectPath}.git" log --pretty=format:"%b" -n 1 ${commitID})"
commitFull="$(git --git-dir="${projectPath}.git" log --pretty=format:"%B" -n 1 ${commitID})"

# 调试打印信息
echo "$@" > /home/<USER>/gerrit_site/hooks/commit-format-check.ini
echo "${commitTittle}"  > ${iniTittle}
echo "${commitMsg}"  >  ${iniContent}
echo "${commitFull}" >  "${iniPath}/full_${iniCommit}"

# 白名单用户，跳过校验
whitelist_users=("gerritadmin" "jenkinsadmin" "sonarreview")

# 检查是否为白名单用户
is_whitelist_user() {
    local username="$1"
    for user in "${whitelist_users[@]}"; do
        if [[ "$username" == "$user" ]]; then
            return 0
        fi
    done
    return 1
}

# 如果提交者是白名单用户，跳过校验
if is_whitelist_user "$uploaderUsername"; then
    echo "跳过: 提交者 ${uploaderUsername} 在白名单中，跳过commit格式校验."
    exit 0
fi

# 函数：校验标题格式
check_title_format() {
    local title="$1"
    
    # 检查标题是否为空
    if [[ -z "$title" ]]; then
        echo "错误: Commit标题不能为空"
        return 1
    fi
    
    # 检查标题长度（中文40，纯英文80）
    # 添加调试信息查看标题实际内容
    echo "调试: 标题内容: '$title'" >&2
    echo "调试: 标题长度: ${#title}" >&2
    
    # 更严格的中文字符检测正则表达式
    # [\x4E00-\x9FFF] 匹配基本汉字
    # [\x3400-\x4DBF] 匹配扩展A
    # [\x20000-\x2A6DF] 匹配扩展B
    # 使用PCRE模式进行更精确匹配
    if [[ "$title" =~ $'[\x4E00-\x9FFF]' ]]; then
        echo "调试: 检测到中文字符，使用40字符限制" >&2
        max_len=40
    else
        max_len=80
    fi
    if [[ ${#title} -gt $max_len ]]; then
        echo "错误: Commit标题长度不能超过${max_len}个字符，当前为${#title}字符"
        return 1
    fi
    
    # 检查标题长度（建议不超过72字符）
    if [[ ${#title} -gt 72 ]]; then
        echo "警告: Commit标题过长（${#title}字符），建议不超过72字符"
    fi
    
    # 检查是否包含冒号（建议使用英文冒号）
    if [[ "$title" == *"："* ]]; then
        echo "警告: 建议使用英文冒号(:)而不是中文冒号(：)"
    fi
    
    # 检查冒号后是否有空格
    if [[ "$title" =~ :[^[:space:]] ]]; then
        echo "错误: 冒号后必须有空格"
        return 1
    fi
    
    echo "标题格式校验通过"
    return 0
}

# 函数：校验正文格式
check_body_format() {
    local body="$1"
    
    # 如果正文为空，直接通过
    if [[ -z "$body" ]]; then
        echo "正文为空，跳过正文格式校验"
        return 0
    fi
    
    # 新增：正文只有一行且为纯数字，报错
    local line_count=$(echo "$body" | wc -l)
    if [[ $line_count -eq 1 ]] && [[ "$body" =~ ^[0-9]+$ ]]; then
        echo "错误: 正文不能只包含纯数字，请检查下禅道单号【bug-view-ID】是否写错"
        return 1
    fi
    
    # # 检查多行改动是否有序号
    # local line_count=$(echo "$body" | wc -l)
    # if [[ $line_count -gt 1 ]]; then
    #     # 检查是否包含数字序号
    #     if ! echo "$body" | grep -q "^[0-9]\+\.\? "; then
    #         echo "警告: 多行改动建议使用数字序号（1、2、3...）进行区分"
    #     fi
    # fi
    
    echo "正文格式校验通过"
    return 0
}

# 函数：校验禅道关联单号
check_zentao_reference() {
    local full_commit="$1"
    
    # 检查是否包含bug-view-关键字
    if echo "$full_commit" | grep -q "bug-view-"; then
        # 检查bug-view-格式是否正确（bug-view-后跟5~6位数字）
        local bug_line=$(echo "$full_commit" | grep -Eo "bug-view-[0-9]{5,6}")
        if [[ -z "$bug_line" ]]; then
            echo "错误: 禅道关联单号格式错误，应为 'bug-view-12345' 或 'bug-view-123456'，数字为5~6位"
            return 1
        fi
        # 检查是否有多于一个bug-view-，或有不合规的bug-view-串
        local all_bug_lines=$(echo "$full_commit" | grep -o "bug-view-[^ ]*")
        for bug in $all_bug_lines; do
            if ! [[ "$bug" =~ ^bug-view-[0-9]{5,6}$ ]]; then
                echo "错误: 检测到不合规的禅道单号: $bug，应为 'bug-view-12345' 或 'bug-view-123456'，数字为5~6位"
                return 1
            fi
        done
        echo "禅道关联单号校验通过: $bug_line"
    else
        echo "未发现禅道关联单号（可选）"
    fi
    
    return 0
}

# 函数：校验ChangeID
check_change_id() {
    local full_commit="$1"
    
    # 检查是否包含Change-Id
    if ! echo "$full_commit" | grep -q "^Change-Id: "; then
        echo "错误: 缺少Change-Id，请使用 'git commit --amend -s' 生成"
        return 1
    fi
    
    # 检查Change-Id格式
    local change_id=$(echo "$full_commit" | grep "^Change-Id: " | sed 's/Change-Id: //')
    if [[ -z "$change_id" ]]; then
        echo "错误: Change-Id不能为空"
        return 1
    fi
    
    # 检查Change-Id是否为Gerrit标准格式：I+39位十六进制
    if ! [[ "$change_id" =~ ^I[a-f0-9]{40}$ ]]; then
        echo "错误: Change-Id格式不正确，应为大写I开头+40位小写十六进制字符（如：Ia39aac81dfacacd6c3fdec8922c5edf0b72e6413）"
        return 1
    fi
    
    echo "Change-Id校验通过: ${change_id:0:8}..."
    return 0
}

# 函数：校验作者邮箱
check_author_email() {
    local full_commit="$1"
    
    # 检查是否包含Signed-off-by
    if ! echo "$full_commit" | grep -q "^Signed-off-by: "; then
        echo "错误: 缺少Signed-off-by，请使用 'git commit --amend -s' 生成"
        return 1
    fi
    
    # 检查Signed-off-by格式
    local signed_off=$(echo "$full_commit" | grep "^Signed-off-by: " | sed 's/Signed-off-by: //')
    if [[ -z "$signed_off" ]]; then
        echo "错误: Signed-off-by不能为空"
        return 1
    fi
    
    # 检查邮箱格式
    local email=$(echo "$signed_off" | grep -o '<[^>]*@[^>]*>' | sed 's/[<>]//g')
    if [[ -z "$email" ]]; then
        echo "错误: Signed-off-by中缺少有效的邮箱地址"
        return 1
    fi
    
    # 简单的邮箱格式校验
    if ! [[ "$email" =~ ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$ ]]; then
        echo "错误: 邮箱格式不正确: $email"
        return 1
    fi
    
    echo "作者邮箱校验通过: $email"
    return 0
}

# 函数：校验冒号格式
check_colon_format() {
    local full_commit="$1"
    
    # 检查是否使用了中文冒号
    if echo "$full_commit" | grep -q "："; then
        echo "警告: 发现中文冒号(：)，建议统一使用英文冒号(:)"
    fi
    
    # 检查冒号后是否有空格
    local lines_with_colon=$(echo "$full_commit" | grep ":")
    while IFS= read -r line; do
        if [[ "$line" =~ :[^[:space:]] ]]; then
            echo "警告: 行 '$line' 中冒号后缺少空格"
        fi
    done <<< "$lines_with_colon"
    
    return 0
}

# 主校验函数
main_check() {
    local title="$1"
    local body="$2"
    local full_commit="$3"
    
    echo "开始校验commit格式..."
    echo "提交者: $uploaderUsername"
    echo "Commit ID: $commitID"
    echo "----------------------------------------"
    
    local has_error=false
    
    # 1. 校验标题
    echo "1. 校验标题..."
    if ! check_title_format "$title"; then
        has_error=true
    fi
    echo ""
    
    # 2. 校验正文
    echo "2. 校验正文..."
    if ! check_body_format "$body"; then
        has_error=true
    fi
    echo ""
    
    # 3. 校验禅道关联单号
    echo "3. 校验禅道关联单号..."
    if ! check_zentao_reference "$full_commit"; then
        has_error=true
    fi
    echo ""
    
    # 4. 校验ChangeID
    echo "4. 校验Change-Id..."
    if ! check_change_id "$full_commit"; then
        has_error=true
    fi
    echo ""
    
    # 5. 校验作者邮箱
    echo "5. 校验作者邮箱..."
    if ! check_author_email "$full_commit"; then
        has_error=true
    fi
    echo ""
    
    # 6. 校验冒号格式
    echo "6. 校验冒号格式..."
    check_colon_format "$full_commit"
    echo ""
    
    if [[ "$has_error" == "true" ]]; then
        echo "----------------------------------------"
        echo "Commit格式校验失败，请根据上述错误信息修正后重新提交"
        echo ""
        echo "建议的commit格式："
        echo "模块名: 简洁描述"
        echo ""
        echo "正文: (可选，多行改动建议使用序号)"
        echo "1. 第一项改动"
        echo "2. 第二项改动"
        echo ""
        echo "bug-view-禅道单号 (可选)"
        echo ""
        echo "Change-Id: I1234567890abcdef1234567890abcdef12345678"
        echo "Signed-off-by: 作者名 <邮箱地址>"
        exit 1
    else
        echo "----------------------------------------"
        echo "Commit格式校验通过"
        exit 0
    fi
}

# 执行主校验
main_check "$commitTittle" "$commitMsg" "$commitFull"
# SonarQube告警数据导出工具

## 项目简介
这是一个用于从SonarQube导出告警数据到Excel的Python脚本，并对Excel进行格式优化。

## 功能特点
- 从SonarQube获取指定项目的告警数据（阻断问题、严重问题、主要问题）
- 处理问题路径，移除键名部分，只保留有效路径
- 将问题明细导出到Excel的"问题明细"工作表
- 筛选状态为OPEN的问题，并按问题路径创建数据透视表
- Excel格式优化：
  - A列左对齐
  - 首行加粗，其他行取消加粗
  - C列第一行添加"责任人"标题并加粗
  - ABC三列添加网格线

## 依赖环境
- Python 3.6+
- 所需Python库：
  - requests
  - pandas
  - openpyxl
  - argparse
  - base64
  - datetime

## 安装说明
1. 确保已安装Python 3.6或更高版本
2. 安装所需依赖库：
   ```
   pip install requests pandas openpyxl
   ```
3. 下载脚本到本地

## 使用方法
```
python export_sonar_alerts.py <project_key> <token> [--url <sonarqube_url>] [--output <output_file>]
```

### 参数说明
- `<project_key>`: 必需，要导出的SonarQube项目键
- `<token>`: 必需，SonarQube访问令牌
- `--url`: 可选，SonarQube服务器URL，默认为`http://**********:9002`
- `--output`: 可选，输出Excel文件名，默认为`sonar_alerts_<timestamp>.xlsx`

## 使用示例
1. 使用默认URL并生成带时间戳的输出文件：
   ```
python export_sonar_alerts.py wearable-wr02-daily-sonaree <your_sonarqube_token>
   ```

2. 指定URL和输出文件名：
   ```
python export_sonar_alerts.py wearable-wr02-daily-sonaree <your_sonarqube_token> --url http://your-sonarqube-server:9000 --output my_alerts.xlsx
   ```

## 输出说明
1. 脚本执行过程中会显示获取数据的进度
2. 成功执行后，会在当前目录生成Excel文件，包含两个工作表：
   - "问题明细": 所有获取到的问题的详细信息
   - "OPEN问题路径统计": 状态为OPEN的问题按路径统计的数据透视表

## 注意事项
1. 请妥善保管您的SonarQube令牌，不要硬编码在脚本中
2. 脚本默认只获取阻断问题(BLOCKER)、严重问题(CRITICAL)和主要问题(MAJOR)
3. SonarQube API有请求限制，最多返回10000个结果
4. 如果项目问题数量较多，获取数据可能需要一定时间，请耐心等待

## 版本历史
- v1.0: 初始版本，实现基本功能
- v1.1: 优化Excel格式，添加网格线和责任人列
- v1.2: 移除硬编码token，改为命令行参数传入
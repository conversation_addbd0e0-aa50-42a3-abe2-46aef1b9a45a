{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT \n  DATE_TRUNC('month', commit_time) as time,\n  COUNT(*) as \"月度提交数\",\n  COUNT(DISTINCT author) as \"活跃开发者数\",\n  COALESCE(SUM(total_issues), 0) as \"月度问题数\"\nFROM commit_metrics \nWHERE commit_time >= CURRENT_DATE - INTERVAL '12 months'\nGROUP BY DATE_TRUNC('month', commit_time)\nORDER BY time", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "月度活动趋势 (最近12个月)", "type": "timeseries"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 70}, {"color": "green", "value": 90}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT \n  DATE_TRUNC('month', commit_time) as time,\n  ROUND((COUNT(CASE WHEN quality_level = 'clean' THEN 1 END)::float / NULLIF(COUNT(*), 0) * 100)::numeric, 2) as \"清洁代码率\"\nFROM commit_metrics \nWHERE commit_time >= CURRENT_DATE - INTERVAL '12 months'\nGROUP BY DATE_TRUNC('month', commit_time)\nORDER BY time", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "月度清洁代码率趋势", "type": "timeseries"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT \n  DATE_TRUNC('month', commit_time) as time,\n  ROUND(COALESCE(AVG(issue_density), 0)::numeric, 2) as \"平均问题密度\"\nFROM commit_metrics \nWHERE commit_time >= CURRENT_DATE - INTERVAL '12 months'\nGROUP BY DATE_TRUNC('month', commit_time)\nORDER BY time", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "月度问题密度趋势", "type": "timeseries"}], "refresh": "5m", "schemaVersion": 36, "style": "dark", "tags": ["code-quality", "trends"], "templating": {"list": []}, "time": {"from": "now-1y", "to": "now"}, "timepicker": {}, "timezone": "", "title": "代码质量效能分析 - 趋势分析", "uid": "code-quality-trends", "version": 1, "weekStart": ""}
#!/bin/bash

echo "=== SonarQube到Grafana简单集成方案启动脚本 ==="

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ 错误: Docker未运行，请先启动Docker"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p logs
mkdir -p grafana/provisioning/datasources
mkdir -p grafana/provisioning/dashboards

# 启动服务
echo "🚀 启动服务栈..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

echo ""
echo "🏥 执行健康检查..."

# 检查PostgreSQL
if docker exec sonar-grafana-postgres pg_isready -U grafana_user > /dev/null 2>&1; then
    echo "✅ PostgreSQL运行正常"
else
    echo "❌ PostgreSQL异常"
fi

# 检查Webhook服务
if curl -f http://localhost:8080/health > /dev/null 2>&1; then
    echo "✅ Webhook服务运行正常"
else
    echo "❌ Webhook服务异常"
fi

# 检查Grafana
if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
    echo "✅ Grafana运行正常"
else
    echo "❌ Grafana异常"
fi

echo ""
echo "📊 服务访问地址:"
echo "  Grafana: http://localhost:3000 (admin/admin123)"
echo "  Webhook: http://localhost:8080"
echo "  PostgreSQL: localhost:5435"

echo ""
echo "📋 下一步操作:"
echo "  1. 运行webhook配置: python setup_webhooks.py"
echo "  2. 发送测试数据验证功能"
echo "  3. 在SonarQube中触发分析"
echo "  4. 在Grafana中查看仪表板"

echo ""
echo "🔧 测试命令:"
echo "  健康检查: curl http://localhost:8080/health"
echo "  查看统计: curl http://localhost:8080/stats"
echo "  发送测试: curl -X POST http://localhost:8080/webhook/sonarqube -H 'Content-Type: application/json' -d '{\"test\": true}'"

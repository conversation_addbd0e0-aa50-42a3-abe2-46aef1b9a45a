#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SonarQube质量日简报生成器

这个脚本用于生成并发送SonarQube代码质量日简报。
支持多个项目的质量报告生成。
适用于Jenkins环境下的定时任务执行。
"""

import sys
import os
import argparse
import configparser
import logging
import datetime
from sonarqube_reporter import SonarQubeReporter

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('Quality_Report')

def parse_args():
    """
    解析命令行参数
    
    返回:
        argparse.Namespace: 解析后的参数
    """
    parser = argparse.ArgumentParser(description='生成并发送SonarQube质量报告')
    parser.add_argument('--config', type=str, default='config.ini', help='配置文件路径')
    parser.add_argument('--no-send', action='store_true', help="仅生成报告而不发送通知")
    parser.add_argument('--jenkins', action='store_true', help="指示脚本在Jenkins环境中运行")
    return parser.parse_args()

def main():
    try:
        logger.info("===== SonarQube质量日简报生成器 =====")
        logger.info(f"当前时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("运行环境: 本地")
        
        # 获取脚本所在目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        config_path = os.path.join(script_dir, "config.ini")
        logger.info(f"读取配置文件: {config_path}")
        
        # 初始化报告生成器
        reporter = SonarQubeReporter(config_path=config_path)
        logger.info(f"SonarQube URL: {reporter.sonar_url}")
        logger.info(f"项目ID: {reporter.project_id}")
        logger.info(f"Token: {reporter.token[:4]}...{reporter.token[-4:]}")
        
        logger.info("初始化质量报告生成器...")
        
        # 生成报告
        logger.info("生成质量日简报...")
        report_path = reporter.generate_daily_report_image()
        
        # 发送企业微信通知
        logger.info("发送企业微信通知...")
        reporter.send_wechat_notification(report_path)
        logger.info("通知发送成功!")
        
        logger.info("===== 任务完成 =====")
        
    except Exception as e:
        logger.error(f"生成报告时发生错误: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main() 
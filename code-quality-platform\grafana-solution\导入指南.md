# 🚀 Grafana仪表板导入指南

## 📋 您的环境信息
- **数据源名称**: `grafana-postgresql-datasource`
- **数据库地址**: `10.0.0.199:5434`
- **推荐仪表板**: `code-quality-dashboard-simple.json`

## 🎯 快速导入步骤

### 步骤1: 导入仪表板

1. **登录Grafana**
   - 访问您的Grafana地址
   - 使用管理员账号登录

2. **导入仪表板**
   - 点击左侧菜单 **"+"** → **"Import"**
   - 点击 **"Upload JSON file"**
   - 选择 `code-quality-dashboard-simple.json` 文件
   - 点击 **"Load"**

3. **确认数据源**
   - 系统会自动识别您的 `grafana-postgresql-datasource` 数据源
   - 如果需要，在下拉框中选择正确的数据源
   - 点击 **"Import"**

### 步骤2: 验证仪表板

导入成功后，您应该看到：
- ✅ **关键指标概览**: 显示总提交数、开发者数等统计
- ✅ **每日提交趋势**: 时间序列图表
- ✅ **质量等级分布**: 饼图显示clean/minor/major/critical分布
- ✅ **项目质量排名**: 表格显示Top 10项目
- ✅ **开发者贡献排名**: 表格显示Top 10开发者

## 🔍 数据验证

### 检查数据是否正常显示

如果看到空白或错误，请检查：

1. **数据库连接**
   ```sql
   -- 在您的数据库中运行以下查询验证数据
   SELECT COUNT(*) FROM commit_metrics;
   SELECT DISTINCT quality_level FROM commit_metrics;
   SELECT COUNT(DISTINCT author) FROM commit_metrics;
   ```

2. **时间范围**
   - 仪表板默认显示最近30天数据
   - 如果数据较旧，请调整右上角的时间范围

3. **字段检查**
   ```sql
   -- 检查关键字段是否存在
   SELECT 
     commit_id, author, gerrit_project, commit_time, 
     total_issues, quality_level, issue_density
   FROM commit_metrics 
   LIMIT 5;
   ```

## 🛠️ 故障排除

### 问题1: 面板显示 "No data"

**可能原因**:
- 时间范围内没有数据
- 字段名称不匹配
- 数据库连接问题

**解决方案**:
1. 调整时间范围到更大的范围（如最近1年）
2. 检查 `commit_metrics` 表是否有数据
3. 验证数据源连接是否正常

### 问题2: 查询错误

**可能原因**:
- 表结构与查询不匹配
- 字段类型问题

**解决方案**:
1. 检查表结构：
   ```sql
   \d commit_metrics
   ```
2. 确认关键字段存在：
   - `commit_time` (时间字段)
   - `author` (作者字段)
   - `gerrit_project` (项目字段)
   - `total_issues` (问题数字段)
   - `quality_level` (质量等级字段)

### 问题3: 饼图显示异常

**可能原因**:
- `quality_level` 字段值不规范

**解决方案**:
检查质量等级数据：
```sql
SELECT quality_level, COUNT(*) 
FROM commit_metrics 
GROUP BY quality_level;
```

## 🎨 自定义建议

### 调整显示内容

1. **修改时间范围**
   - 点击右上角时间选择器
   - 选择适合的时间范围（如最近7天、90天等）

2. **调整表格显示数量**
   - 编辑面板，修改SQL中的 `LIMIT 10` 为其他数值

3. **添加过滤条件**
   - 可以在SQL中添加 `WHERE` 条件过滤特定项目或开发者

### 创建更多面板

基于现有数据，您还可以创建：

1. **月度趋势面板**
   ```sql
   SELECT 
     DATE_TRUNC('month', commit_time) as time,
     COUNT(*) as commits,
     COUNT(DISTINCT author) as authors
   FROM commit_metrics 
   GROUP BY DATE_TRUNC('month', commit_time)
   ORDER BY time;
   ```

2. **问题严重程度分布**
   ```sql
   SELECT 
     'Blocker' as severity, SUM(sq_blocker) as count
   FROM commit_metrics
   UNION ALL
   SELECT 'Critical', SUM(sq_critical)
   FROM commit_metrics
   UNION ALL
   SELECT 'Major', SUM(sq_major)
   FROM commit_metrics;
   ```

## 📊 使用建议

### 日常监控
- 设置自动刷新（建议5-15分钟）
- 关注清洁代码率趋势
- 监控问题密度变化

### 团队分析
- 定期查看开发者贡献排名
- 识别需要改进的项目
- 制定代码质量提升计划

### 数据洞察
- 观察提交数与问题数的关系
- 分析不同项目的质量差异
- 跟踪质量改进效果

## 🎉 完成！

导入成功后，您就拥有了一个功能完整的代码质量效能分析仪表板！

如果遇到任何问题，请：
1. 检查数据库连接和数据完整性
2. 验证字段名称和类型
3. 调整时间范围和查询条件

---

**祝您使用愉快！通过数据驱动的方式提升代码质量！** 🚀

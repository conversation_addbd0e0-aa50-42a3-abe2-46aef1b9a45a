{"name": "WR02_BOOT", "type": "ARM", "dependenceList": [], "srcDirs": [], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": [{"name": "Application", "files": [{"path": "board/main.c"}, {"path": "../../../../../sifli/customer/peripherals/firmware_ota/firmware_iap.c"}, {"path": "../../src/boot_event_task.c"}, {"path": "../../src/periodic_task.c"}], "folders": []}, {"name": "BF0_HAL", "files": [{"path": "../../../../../sifli/drivers/hal/bf0_hal_adc.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_aes.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_audcodec.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_audprc.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_bleaon.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_busmon.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_cortex.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_crc.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_dma.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_dsi.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_efuse.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_epic.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_ext_dma.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_ezip.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_facc.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_fft.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_gpio.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_hcd.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_hlp.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_hpaon.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_i2c.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_i2s.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_lcdc.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_lcpu_config.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_lpaon.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_lpcomp.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_lptim.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_lrc_cal.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_mailbox.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_nn_acc.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_patch.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_pcd.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_pdm.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_pinmux.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_pmu.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_psram.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_ptc.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_qspi_ex.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_qspi.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_rcc.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_rng.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_rtc.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_sd_ex.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_sdadc.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_sdhci.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_sdmmc.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_secu.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_spi.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_tim_ex.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_tim.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_tsen.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_uart.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal_wdt.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_hal.c"}, {"path": "../../../../../sifli/drivers/hal/bf0_sys_cfg.c"}, {"path": "../../../../../sifli/drivers/hal/flash_table.c"}, {"path": "../../../../../sifli/drivers/hal/nand_table.c"}, {"path": "../../../../../sifli/drivers/hal/sifli_bbm.c"}], "folders": []}, {"name": "CMSIS_BF0", "files": [{"path": "../../../../../sifli/drivers/cmsis/sf32lb55x/bf0_pin_const.c"}, {"path": "../../../../../sifli/drivers/cmsis/sf32lb55x/ble_rf_fulcal.c"}, {"path": "../../../../../sifli/drivers/cmsis/sf32lb55x/lcpu_patch.c"}, {"path": "../../../../../sifli/drivers/cmsis/sf32lb55x/Templates/arm/startup_bf0_hcpu.S"}, {"path": "../../../../../sifli/drivers/cmsis/sf32lb55x/Templates/system_bf0_ap.c"}], "folders": []}, {"name": "CORTEX-M33", "files": [{"path": "../../../../../sifli/rtos/rtthread/libcpu/arm/common/backtrace.c"}, {"path": "../../../../../sifli/rtos/rtthread/libcpu/arm/common/div0.c"}, {"path": "../../../../../sifli/rtos/rtthread/libcpu/arm/common/showmem.c"}, {"path": "../../../../../sifli/rtos/rtthread/libcpu/arm/Cortex-M33/context_rvds.S"}, {"path": "../../../../../sifli/rtos/rtthread/libcpu/arm/Cortex-M33/cpuport.c"}], "folders": []}, {"name": "DeviceDrivers", "files": [{"path": "../../../../../sifli/rtos/rtthread/components/drivers/i2c/i2c_core.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/drivers/i2c/i2c_dev.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/drivers/misc/pin.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/drivers/sdio/block_dev.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/drivers/sdio/mmc.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/drivers/sdio/mmcsd_core.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/drivers/sdio/sd.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/drivers/sdio/sdio.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/drivers/serial/serial.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/drivers/src/completion.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/drivers/src/dataqueue.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/drivers/src/pipe.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/drivers/src/ringblk_buf.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/drivers/src/ringbuffer.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/drivers/src/waitqueue.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/drivers/src/workqueue.c"}, {"path": "../../../../Application/Drivers/driver_api/power_ctl.c"}, {"path": "../../../../Application/pm_manager/pm_notify_app.c"}], "folders": []}, {"name": "Drivers", "files": [{"path": "../../../../../sifli/customer/boards/common/flash.c"}, {"path": "../../../../../sifli/customer/boards/ec-lb557xxx/bsp_board.c"}, {"path": "../../../../../sifli/customer/boards/ec-lb557xxx/bsp_pinmux.c"}, {"path": "../../../../../sifli/customer/boards/ec-lb557xxx/drv_io.c"}, {"path": "../../../../../sifli/customer/peripherals/co5300/co5300.c"}, {"path": "../../../../../sifli/customer/peripherals/ft6146/ft6146.c"}, {"path": "../../../../../sifli/customer/peripherals/pmic_controller/pmic_controller.c"}, {"path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_common.c"}, {"path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_dbg.c"}, {"path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_ext_dma.c"}, {"path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_gpio.c"}, {"path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_i2c.c"}, {"path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_lcd_private.c"}, {"path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_lcd_test.c"}, {"path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_lcd.c"}, {"path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_psram.c"}, {"path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_sdhci.c"}, {"path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_spi_flash.c"}, {"path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_spi_nand.c"}, {"path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_sys_cfg.c"}, {"path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_touch.c"}, {"path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_usart.c"}, {"path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_usb_otg.c"}, {"path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_usbd.c"}, {"path": "board/bf0_ap_hal_msp.c"}, {"path": "board/board.c"}], "folders": [{"name": "CW6307", "files": [{"path": "../../../../../sifli/customer/peripherals/CW6307/cellwise_charger_cw6307.c"}], "folders": []}]}, {"name": "ERPC", "files": [], "folders": [{"name": "infra", "files": [{"path": "../../../../../sifli/external/erpc/infra/erpc_arbitrated_client_manager.cpp"}, {"path": "../../../../../sifli/external/erpc/infra/erpc_basic_codec.cpp"}, {"path": "../../../../../sifli/external/erpc/infra/erpc_client_manager.cpp"}, {"path": "../../../../../sifli/external/erpc/infra/erpc_crc16.cpp"}, {"path": "../../../../../sifli/external/erpc/infra/erpc_framed_transport.cpp"}, {"path": "../../../../../sifli/external/erpc/infra/erpc_message_buffer.cpp"}, {"path": "../../../../../sifli/external/erpc/infra/erpc_message_loggers.cpp"}, {"path": "../../../../../sifli/external/erpc/infra/erpc_pre_post_action.cpp"}, {"path": "../../../../../sifli/external/erpc/infra/erpc_server.cpp"}, {"path": "../../../../../sifli/external/erpc/infra/erpc_simple_server.cpp"}, {"path": "../../../../../sifli/external/erpc/infra/erpc_transport_arbitrator.cpp"}], "folders": []}, {"name": "port", "files": [{"path": "../../../../../sifli/external/erpc/port/erpc_port_rtthread.cpp"}, {"path": "../../../../../sifli/external/erpc/port/erpc_threading_pthreads.cpp"}], "folders": []}, {"name": "setup", "files": [{"path": "../../../../../sifli/external/erpc/setup/erpc_arbitrated_client_setup.cpp"}, {"path": "../../../../../sifli/external/erpc/setup/erpc_client_setup.cpp"}, {"path": "../../../../../sifli/external/erpc/setup/erpc_server_setup.cpp"}, {"path": "../../../../../sifli/external/erpc/setup/erpc_setup_mbf_dynamic.cpp"}, {"path": "../../../../../sifli/external/erpc/setup/erpc_setup_mbf_static.cpp"}, {"path": "../../../../../sifli/external/erpc/setup/erpc_setup_rpmsg_sifli.cpp"}], "folders": []}, {"name": "transport", "files": [{"path": "../../../../../sifli/external/erpc/transports/erpc_rpmsg_sifli_transport.cpp"}], "folders": []}]}, {"name": "Filesystem", "files": [{"path": "../../../../../sifli/rtos/rtthread/components/dfs/filesystems/devfs/devfs.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/dfs/filesystems/elmfat/dfs_elm.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/dfs/filesystems/elmfat/ff.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/dfs/filesystems/elmfat/ffunicode.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/dfs/src/dfs_file.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/dfs/src/dfs_fs.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/dfs/src/dfs_posix.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/dfs/src/dfs.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/dfs/src/poll.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/dfs/src/select.c"}], "folders": []}, {"name": "<PERSON>h", "files": [{"path": "../../../../../sifli/rtos/rtthread/components/finsh/cmd.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/finsh/finsh_compiler.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/finsh/finsh_error.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/finsh/finsh_heap.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/finsh/finsh_init.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/finsh/finsh_node.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/finsh/finsh_ops.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/finsh/finsh_parser.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/finsh/finsh_token.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/finsh/finsh_var.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/finsh/finsh_vm.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/finsh/msh_cmd.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/finsh/msh_file.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/finsh/msh.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/finsh/shell.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/finsh/symbol.c"}], "folders": []}, {"name": "<PERSON><PERSON>", "files": [{"path": "../../../../../sifli/rtos/rtthread/src/clock.c"}, {"path": "../../../../../sifli/rtos/rtthread/src/components.c"}, {"path": "../../../../../sifli/rtos/rtthread/src/cpu.c"}, {"path": "../../../../../sifli/rtos/rtthread/src/device.c"}, {"path": "../../../../../sifli/rtos/rtthread/src/idle.c"}, {"path": "../../../../../sifli/rtos/rtthread/src/ipc.c"}, {"path": "../../../../../sifli/rtos/rtthread/src/irq.c"}, {"path": "../../../../../sifli/rtos/rtthread/src/kservice.c"}, {"path": "../../../../../sifli/rtos/rtthread/src/mem.c"}, {"path": "../../../../../sifli/rtos/rtthread/src/memheap.c"}, {"path": "../../../../../sifli/rtos/rtthread/src/mempool.c"}, {"path": "../../../../../sifli/rtos/rtthread/src/object.c"}, {"path": "../../../../../sifli/rtos/rtthread/src/scheduler.c"}, {"path": "../../../../../sifli/rtos/rtthread/src/signal.c"}, {"path": "../../../../../sifli/rtos/rtthread/src/thread.c"}, {"path": "../../../../../sifli/rtos/rtthread/src/timer.c"}], "folders": []}, {"name": "libc", "files": [{"path": "../../../../../sifli/rtos/rtthread/components/libc/compilers/armlibc/libc.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/libc/compilers/armlibc/mem_std.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/libc/compilers/armlibc/stdio.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/libc/compilers/armlibc/stubs.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/libc/compilers/armlibc/time.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/libc/compilers/common/gmtime_r.c"}], "folders": []}, {"name": "m_system", "files": [{"path": "../../../../../sifli/middleware/system/bf0_common.c"}], "folders": []}, {"name": "middleware", "files": [{"path": "../../../../../sifli/middleware/ipc_queue/common/circular_buf.c"}, {"path": "../../../../../sifli/middleware/ipc_queue/common/ipc_hw.c"}, {"path": "../../../../../sifli/middleware/ipc_queue/common/ipc_queue.c"}, {"path": "../../../../../sifli/middleware/ipc_queue/porting/sf32lb55x/hcpu/ipc_hw_port.c"}], "folders": []}, {"name": "os_adapter", "files": [{"path": "../../../../../sifli/rtos/os_adaptor/src/os_adaptor_rtthread.c"}], "folders": []}, {"name": "POSIX", "files": [{"path": "../../../../../sifli/rtos/rtthread/components/libc/pthreads/mqueue.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/libc/pthreads/pthread_attr.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/libc/pthreads/pthread_barrier.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/libc/pthreads/pthread_cond.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/libc/pthreads/pthread_mutex.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/libc/pthreads/pthread_rwlock.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/libc/pthreads/pthread_spin.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/libc/pthreads/pthread_tls.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/libc/pthreads/pthread.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/libc/pthreads/sched.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/libc/pthreads/semaphore.c"}], "folders": []}, {"name": "pthreads", "files": [{"path": "../../../../../sifli/rtos/rtthread/components/libc/time/clock_time.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/libc/time/posix_sleep.c"}], "folders": []}, {"name": "rt_usbd", "files": [{"path": "../../../../../sifli/rtos/rtthread/components/drivers/usb/usbdevice/class/mstorage.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/drivers/usb/usbdevice/core/core.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/drivers/usb/usbdevice/core/usbdevice.c"}], "folders": []}, {"name": "sifli_lib", "files": [{"path": "../../../../../sifli/middleware/sifli_lib/lib/dummy.c"}, {"path": "../../../../../sifli/middleware/sifli_lib/lib/sifli_lib_rvds.lib"}], "folders": []}, {"name": "Utilities", "files": [{"path": "../../../../../sifli/rtos/rtthread/components/utilities/llt_mem/llt_mem.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/utilities/ulog/backend/console_be.c"}, {"path": "../../../../../sifli/rtos/rtthread/components/utilities/ulog/ulog.c"}, {"path": "../../../../Application/msg_services/little_notify_list.c"}], "folders": []}]}, "outDir": "build", "deviceName": null, "packDir": null, "miscInfo": {"uid": "399ed895008f55cb4a02f11ea28e4066"}, "targets": {"rt-thread": {"excludeList": [], "toolchain": "AC6", "compileConfig": {"cpuType": "Cortex-M33", "floatingPointHardware": "single", "scatterFilePath": "build/link.sct", "useCustomScatterFile": true, "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x20000"}, "isChecked": true, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x20200000", "size": "0x20000"}, "isChecked": false, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x200000"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x200000", "size": "0x200000"}, "isChecked": false, "isStartup": false}]}, "options": "null"}, "uploader": "JLink", "uploadConfig": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "<PERSON><PERSON><PERSON>", "cpuName": "SF32LB55X_SD"}, "proType": 1, "speed": 3000, "otherCmds": ""}, "uploadConfigMap": {}, "custom_dep": {"name": "default", "incList": [".", "../../../../../sifli/rtos/rtthread/include", "board", "../../../../../sifli/customer/boards/common", "../../../../../sifli/customer/boards/include", "../../../../../sifli/customer/boards/include/config/sf32lb55x", "../../../../../sifli/customer/boards/ec-lb557xxx", "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers", "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/config/sf32lb55x", "../../../../../sifli/drivers/cmsis/sf32lb55x", "../../../../../sifli/drivers/cmsis/Include", "../../../../../sifli/external/CMSIS/Include", "../../../../../sifli/drivers/Include", "../../../../../sifli/middleware/include", "../../../../../sifli/middleware/ipc_queue/common", "../../../../../sifli/middleware/ipc_queue/porting/os", "../../../../../sifli/middleware/ipc_queue/porting/os/rtthread", "../../../../../sifli/middleware/ipc_queue/porting/sf32lb55x", "../../../../../sifli/middleware/ipc_queue/porting/sf32lb55x/hcpu", "../../../../../sifli/middleware/sifli_lib/lib", "../../../../../sifli/rtos/os_adaptor/src", "../../../../../sifli/rtos/os_adaptor/inc", "../../../../../sifli/rtos/rtthread/libcpu/arm/Cortex-M33", "../../../../../sifli/rtos/rtthread/libcpu/arm/common", "../../../../../sifli/rtos/rtthread/components/dfs/include", "../../../../../sifli/rtos/rtthread/components/dfs/filesystems/devfs", "../../../../../sifli/rtos/rtthread/components/dfs/filesystems/elmfat", "../../../../../sifli/rtos/rtthread/components/dfs/filesystems/dhara/dhara", "../../../../../sifli/rtos/rtthread/components/drivers/include", "../../../../../sifli/rtos/rtthread/components/finsh", "../../../../../sifli/rtos/rtthread/components/libc/compilers/armlibc", "../../../../../sifli/rtos/rtthread/components/libc/compilers/common", "../../../../../sifli/rtos/rtthread/components/utilities/llt_mem", "../../../../../sifli/rtos/rtthread/components/utilities/ulog", "../../../../../sifli/rtos/rtthread/components/utilities/ulog/backend", "RTE/_rt-thread", "../../../../../sifli/customer/peripherals/pmic_controller", "../../../../../sifli/rtos/rtthread/components/libc/pthreads", "../../../../../sifli/rtos/rtthread/components/libc/time", "../../../../../sifli/external/erpc/infra", "../../../../../sifli/external/erpc/config", "../../../../../sifli/external/erpc/port", "../../../../../sifli/external/erpc/setup", "../../../../../sifli/external/erpc/transports", "build", "../../../../Application/Drivers/gps", "../../../../../sifli/customer/peripherals/airoha_gps", "../../../../../sifli/customer/peripherals/airoha_gps/downloadfw/inc", "../../../../../sifli/customer/peripherals/CW6307", "../../../../Application/Drivers/driver_api", "../../../../Application/Lib_New/utility", "../../../../Application/App", "../../../../../sifli/external/CMSIS/RTOS2/Include", "../../../../Application/Mem_Manager", "../../../../Application/App/common", "../../../../../sifli/external/nRF5_SDK_16.0.0/stm32_hw_adapter", "../../../../../sifli/external/nRF5_SDK_16.0.0/components/softdevice/common", "../../../../../sifli/rtos/rtthread/components/drivers/include/drivers", "../../src", "../../../../../sifli/customer/peripherals/firmware_ota"], "libList": ["../../../../../sifli/external/erpc/config"], "defineList": ["SIFLI_VERSION=33619975", "__FILE__=__FILE_NAME__", "SF32LB55X", "USE_HAL_DRIVER", "ARM_MATH_LOOPUNROLL", "SOC_BF0_HCPU", "USE_FULL_ASSERT", "SIFLI_BUILD=\"000000\"", "RT_USING_ARM_LIBC", "LB55X_CHIP_ID=3", "ERPC_THREADS=ERPC_THREADS_PTHREADS", "RTOS_RTTHREAD", "ERPC_PRE_POST_ACTION=1", "IGS_BOOT", "ENV_OTA"]}, "builderOptions": {"AC6": {"version": 3, "beforeBuildTasks": [{"name": "copysign.bat", "command": "cd .\\. && copysign.bat", "disable": true, "abortAfterFailed": true, "stopBuildAfterFailed": true}], "afterBuildTasks": [{"name": "postbuild.bat !L", "command": "cd .\\. && postbuild.bat .\\${OutDirBase}\\${ProjectName}.axf", "disable": false, "abortAfterFailed": true}, {"name": "fromelf --text -c -o \"$<EMAIL>\" \"#L\"", "command": "cd .\\. && fromelf --text -c -o \"${OutDir}\\${ProjectName}.lst\" \"${ExecutableName}.axf\"", "disable": true, "abortAfterFailed": true}, {"name": "copy_axf", "disable": true, "abortAfterFailed": true, "command": "copy _build\\WR02_BOOT.axf build\\keil\\Obj\\bootloader.axf"}], "global": {"use-microLIB": true, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-image-size", "language-c": "c11", "language-cpp": "c++11", "one-elf-section-per-function": true, "short-enums#wchar": true, "warnings": "ac5-like-warnings", "C_FLAGS": "-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c -fno-rtti -funsigned-char -fshort-enums -fshort-wchar -Wno-builtin-macro-redefined", "CXX_FLAGS": "-Wno-builtin-macro-redefined"}, "asm-compiler": {"$use": "asm", "misc-controls": "--cpreproc --cpreproc_opts=--target=arm-arm-none-eabi --cpreproc_opts=-mcpu=cortex-m33 --cpreproc_opts=-mfpu=fpv5-sp-d16 --cpreproc_opts=-mfloat-abi=hard"}, "linker": {"output-format": "elf", "misc-controls": "--symdefs @L.sym --cpu=Cortex-M33 --strict --scatter build/link.sct --summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols --info sizes --info totals --info unused --info veneers --any_contingency --list build/bootloader.map --symdefs=build/bootloader.symdefs --library_type=microlib --predefine=-DLB55X_CHIP_ID=2 --predefine=\"-DSIFLI_VERSION=33619975\" --predefine=\"-DSF32LB55X\" --predefine=\"-DUSE_HAL_DRIVER\" --predefine=\"-DLB55X_CHIP_ID=2\" --predefine=\"-DARM_MATH_LOOPUNROLL\" --predefine=\"-DSOC_BF0_HCPU\" --predefine=\"-DUSE_FULL_ASSERT\" --predefine=\"-DSIFLI_BUILD=\"000000\"\" --predefine=\"-DRT_USING_ARM_LIBC\"", "ro-base": "0x00000000", "rw-base": "0x20000000"}}}}}, "version": "3.5"}
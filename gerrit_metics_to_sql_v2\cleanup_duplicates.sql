-- =====================================================
-- 清理重复数据并添加唯一约束脚本
-- =====================================================

-- 1. 分析当前重复数据情况
SELECT 
    gerrit_project,
    branch,
    change_id_short,
    COUNT(*) as duplicate_count,
    MIN(patch_set) as min_patch_set,
    MAX(patch_set) as max_patch_set,
    MIN(created_at) as first_created,
    MAX(updated_at) as last_updated
FROM commit_metrics
GROUP BY gerrit_project, branch, change_id_short
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC, last_updated DESC;

-- 2. 备份原始数据（可选）
CREATE TABLE commit_metrics_backup AS 
SELECT * FROM commit_metrics;

-- 3. 标记要删除的重复记录
WITH ranked_commits AS (
    SELECT 
        commit_id,
        gerrit_project,
        branch,
        change_id_short,
        patch_set,
        updated_at,
        ROW_NUMBER() OVER (
            PARTITION BY gerrit_project, branch, change_id_short 
            ORDER BY 
                patch_set DESC,           -- 优先保留最新的patch set
                updated_at DESC,          -- 其次按更新时间
                created_at DESC           -- 最后按创建时间
        ) as rn
    FROM commit_metrics
),
duplicates_to_delete AS (
    SELECT 
        commit_id,
        gerrit_project || '~' || branch || '~' || change_id_short as unique_key,
        patch_set,
        updated_at
    FROM ranked_commits 
    WHERE rn > 1
)
SELECT 
    COUNT(*) as records_to_delete,
    COUNT(DISTINCT unique_key) as affected_changes
FROM duplicates_to_delete;

-- 4. 删除重复记录（保留最新的patch set）
WITH ranked_commits AS (
    SELECT 
        commit_id,
        ROW_NUMBER() OVER (
            PARTITION BY gerrit_project, branch, change_id_short 
            ORDER BY 
                patch_set DESC,           -- 优先保留最新的patch set
                updated_at DESC,          -- 其次按更新时间
                created_at DESC           -- 最后按创建时间
        ) as rn
    FROM commit_metrics
)
DELETE FROM commit_metrics 
WHERE commit_id IN (
    SELECT commit_id 
    FROM ranked_commits 
    WHERE rn > 1
);

-- 5. 验证清理结果
SELECT 
    'After cleanup' as status,
    COUNT(*) as total_records,
    COUNT(DISTINCT gerrit_project || '~' || branch || '~' || change_id_short) as unique_changes
FROM commit_metrics;

-- 6. 添加唯一约束
ALTER TABLE commit_metrics 
ADD CONSTRAINT uk_commit_metrics_unique 
UNIQUE (gerrit_project, branch, change_id_short);

-- 7. 创建相关索引以提高性能
CREATE INDEX IF NOT EXISTS idx_commit_metrics_lookup 
ON commit_metrics (gerrit_project, branch, change_id_short, patch_set);

CREATE INDEX IF NOT EXISTS idx_commit_metrics_updated 
ON commit_metrics (updated_at DESC);

CREATE INDEX IF NOT EXISTS idx_commit_metrics_project_branch 
ON commit_metrics (gerrit_project, branch);

-- 8. 创建视图以便查看最新状态
CREATE OR REPLACE VIEW v_latest_commits AS
SELECT 
    cm.*,
    CASE 
        WHEN cm.patch_set = max_ps.max_patch_set THEN 'LATEST'
        ELSE 'OUTDATED'
    END as patch_status
FROM commit_metrics cm
JOIN (
    SELECT 
        gerrit_project,
        branch,
        change_id_short,
        MAX(patch_set) as max_patch_set
    FROM commit_metrics
    GROUP BY gerrit_project, branch, change_id_short
) max_ps ON cm.gerrit_project = max_ps.gerrit_project 
         AND cm.branch = max_ps.branch 
         AND cm.change_id_short = max_ps.change_id_short;

-- 9. 创建监控查询
-- 查看最近的重复数据情况
CREATE OR REPLACE VIEW v_duplicate_monitor AS
SELECT 
    gerrit_project,
    branch,
    change_id_short,
    COUNT(*) as record_count,
    ARRAY_AGG(patch_set ORDER BY patch_set) as patch_sets,
    MAX(updated_at) as last_updated
FROM commit_metrics
GROUP BY gerrit_project, branch, change_id_short
HAVING COUNT(*) > 1;

-- 10. 验证脚本
SELECT 
    'Verification' as check_type,
    CASE 
        WHEN EXISTS (SELECT 1 FROM v_duplicate_monitor) 
        THEN 'FAILED - Still have duplicates'
        ELSE 'PASSED - No duplicates found'
    END as result;

-- 显示清理统计
SELECT 
    'Final Statistics' as info,
    COUNT(*) as total_records,
    COUNT(DISTINCT gerrit_project) as unique_projects,
    COUNT(DISTINCT gerrit_project || '~' || branch) as unique_project_branches,
    COUNT(DISTINCT change_id_short) as unique_changes,
    MIN(commit_time) as earliest_commit,
    MAX(commit_time) as latest_commit
FROM commit_metrics;

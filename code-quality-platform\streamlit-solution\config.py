#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from sqlalchemy import create_engine
import streamlit as st

# 数据库配置
DB_CONFIG = {
    'host': os.getenv('DB_HOST', '**********'),
    'port': os.getenv('DB_PORT', '5434'),
    'database': os.getenv('DB_NAME', 'mydatabase'),
    'user': os.getenv('DB_USER', 'admin'),
    'password': os.getenv('DB_PASSWORD', 'admin123')
}

# 创建数据库连接
@st.cache_resource
def get_database_engine():
    """获取数据库连接引擎"""
    connection_string = f"postgresql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}"
    return create_engine(connection_string)

# 页面配置
PAGE_CONFIG = {
    "page_title": "代码质量效能分析平台",
    "page_icon": "📊",
    "layout": "wide",
    "initial_sidebar_state": "expanded"
}

# 图表颜色配置
COLORS = {
    'primary': '#1f77b4',
    'success': '#2ca02c',
    'warning': '#ff7f0e',
    'danger': '#d62728',
    'info': '#17a2b8',
    'quality_levels': {
        'clean': '#2ca02c',
        'minor': '#ff7f0e', 
        'major': '#d62728',
        'critical': '#8b0000'
    },
    'severity_levels': {
        'Blocker': '#8b0000',
        'Critical': '#d62728',
        'Major': '#ff7f0e',
        'Minor': '#1f77b4',
        'Info': '#2ca02c'
    }
}

# 时间范围选项
TIME_RANGES = {
    '最近7天': 7,
    '最近30天': 30,
    '最近90天': 90,
    '最近180天': 180,
    '最近1年': 365
}

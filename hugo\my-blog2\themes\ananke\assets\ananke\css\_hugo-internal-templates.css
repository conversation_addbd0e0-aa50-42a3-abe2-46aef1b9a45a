/* pagination.html: https://github.com/spf13/hugo/blob/master/tpl/tplimpl/template_embedded.go#L117 */
.pagination {
  margin: 3rem 0;
}

.pagination li {
  display: inline-block;
  margin-right: .375rem;
  font-size: .875rem;
  margin-bottom: 2.5em;
}

[dir="rtl"] .pagination li {
  margin-left: .375rem;
  margin-right: 0;
}

.pagination li a {
  padding: .5rem .625rem;
  background-color: white;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 3px;
  text-decoration: none;
}
.pagination li.disabled {
  display: none;
}
.pagination li.active a,
.pagination li.active a:link,
.pagination li.active a:active,
.pagination li.active a:visited {
  background-color: #ddd;
}

#TableOfContents ul li {
  margin-bottom: 1em;
}

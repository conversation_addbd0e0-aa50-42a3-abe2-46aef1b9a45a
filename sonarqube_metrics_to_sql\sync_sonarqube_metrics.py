import sys
import configparser
import pg8000.native
import requests
import json
from datetime import datetime
import re
from datetime import timed<PERSON><PERSON>

def parse_sonar_datetime(dt_str):
    # 处理Z结尾
    if dt_str.endswith('Z'):
        dt_str = dt_str.replace('Z', '+00:00')
    # 处理+0800/-0800等无冒号时区
    match = re.search(r'([+-]\d{2})(\d{2})$', dt_str)
    if match:
        dt_str = dt_str[:-5] + match.group(1) + ':' + match.group(2)
    try:
        return datetime.fromisoformat(dt_str)
    except Exception as e:
        print(f"lastAnalysisDate格式错误: {dt_str}")
        raise e

# 参数校验
if len(sys.argv) != 4:
    print("用法: python sync_sonarqube_metrics.py [sonar平台标识] [sonarqube项目ID] [表名]")
    sys.exit(1)

platform = sys.argv[1]
project_key = sys.argv[2]

# 确定表名（必填参数）
table_name = sys.argv[3]

# 读取配置
import os
config = configparser.ConfigParser()

# 使用绝对路径读取配置文件
config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config.ini')
config.read(config_path, encoding='utf-8')

# 检查配置文件是否包含指定平台
if platform not in config:    
    print(f"配置文件中未找到平台: {platform}")
    print(f"可用平台: {', '.join(config.sections()[1:])}")  # 排除Database部分
    sys.exit(1)

db_conf = config['Database']
sonar_conf = config[platform]

# 检查token是否存在且不为默认值
if 'token' not in sonar_conf or sonar_conf['token'] in ['', 'your_token_here', '2c3d4e5f6a7b8c9d0e1f2a3b4c5d6e7f8g9h0i1j']:
    print(f"平台 {platform} 的token配置无效，请检查config.ini文件")
    print(f"当前token值: {sonar_conf.get('token', '未设置')}")
    sys.exit(1)

# 提取base_url（提前定义）
base_url = sonar_conf['url'].rstrip('/')

# 数据库连接
print(f"正在连接数据库: {db_conf['host']}:{db_conf['port']}/{db_conf['name']}")
try:
    conn = pg8000.native.Connection(
        user=db_conf['user'],
        password=db_conf['password'],
        host=db_conf['host'],
        port=int(db_conf['port']),
        database=db_conf['name'],
        timeout=10
    )
    print(f"数据库连接成功: {db_conf['host']}:{db_conf['port']}/{db_conf['name']}")
except Exception as e:
    print(f"数据库连接失败: {str(e)}")
    print(f"连接参数: host={db_conf['host']}, port={db_conf['port']}, database={db_conf['name']}, user={db_conf['user']}")
    sys.exit(1)

# 获取SonarQube项目信息

# 使用基本认证
auth = (sonar_conf['token'], '')

# 获取项目名称
project_url = f"{base_url}/api/projects/search?projects={project_key}"
print(f"正在请求SonarQube项目信息: {project_url}")
resp = requests.get(project_url, auth=auth, timeout=30)

if resp.status_code != 200:
    print(f"请求项目信息失败，状态码: {resp.status_code}")
    print(f"响应内容: {resp.text}")
    print(f"请求URL: {project_url}")
    sys.exit(1)
else:
    print(f"SonarQube连接成功，请求项目信息返回状态码: {resp.status_code}")

try:
    project_data = resp.json()
except json.JSONDecodeError as e:
    print(f"解析项目信息响应JSON失败: {e}")
    sys.exit(1)

if not project_data.get('components'):
    print(f"未找到项目: {project_key}")
    sys.exit(1)
project_name = project_data['components'][0]['name']

# 获取指标
metrics = [
    'bugs', 'vulnerabilities', 'code_smells', 'coverage', 'duplicated_lines_density',
    'ncloc', 'blocker_violations', 'critical_violations', 'major_violations',
    'minor_violations', 'info_violations', 'comment_lines_density', 'complexity',
    'files', 'functions', 'statements', 'duplicated_lines', 'duplicated_blocks', 'duplicated_files'
]
metrics_str = ','.join(metrics)
metric_url = f"{base_url}/api/measures/component?component={project_key}&metricKeys={metrics_str}"
print(f"正在请求SonarQube指标信息: {metric_url}")
metric_resp = requests.get(metric_url, auth=auth, timeout=30)

if metric_resp.status_code != 200:
    print(f"请求指标信息失败，状态码: {metric_resp.status_code}")
    print(f"响应内容: {metric_resp.text}")
    print(f"请求URL: {metric_url}")
    sys.exit(1)
else:
    print(f"指标数据响应状态码: {metric_resp.status_code}")
    print(f"指标数据响应内容: {metric_resp.text[:500]}...")  # 打印前500个字符，避免输出过长

try:
    metric_data = metric_resp.json()
except json.JSONDecodeError as e:
    print(f"解析指标信息响应JSON失败: {e}")
    sys.exit(1)

# 获取 analysis_date，优先用 lastAnalysisDate
last_analysis_date = project_data['components'][0].get('lastAnalysisDate')
if last_analysis_date:
    # 转为 datetime 对象
    analysis_date = parse_sonar_datetime(last_analysis_date)
else:
    analysis_date = datetime.now()

# 以本地日期为日维度（只保留当天最后一条）
analysis_date_local = analysis_date.astimezone().date()

# 查询当天是否已有数据（以本地日为准）
select_sql = f"""
SELECT id FROM {table_name} WHERE analysis_date >= :start_dt AND analysis_date < :end_dt ORDER BY analysis_date DESC LIMIT 1;
"""
start_dt = datetime.combine(analysis_date_local, datetime.min.time()).astimezone()
end_dt = start_dt + timedelta(days=1)
existing = conn.run(select_sql, start_dt=start_dt, end_dt=end_dt)

# 组装数据
metric_map = {m['metric']: m['value'] for m in metric_data.get('component', {}).get('measures', [])}

# 组装数据
row = {
    'analysis_date': analysis_date,
    'project_name': project_name,
    'bugs': int(metric_map.get('bugs', 0)),
    'vulnerabilities': int(metric_map.get('vulnerabilities', 0)),
    'code_smells': int(metric_map.get('code_smells', 0)),
    'coverage': float(metric_map.get('coverage', 0)),
    'duplicated_lines_density': float(metric_map.get('duplicated_lines_density', 0)),
    'ncloc': int(metric_map.get('ncloc', 0)),
    'sq_blocker': int(metric_map.get('blocker_violations', 0)),
    'sq_critical': int(metric_map.get('critical_violations', 0)),
    'sq_major': int(metric_map.get('major_violations', 0)),
    'sq_minor': int(metric_map.get('minor_violations', 0)),
    'sq_info': int(metric_map.get('info_violations', 0)),
    'comment_lines_density': float(metric_map.get('comment_lines_density', 0)),
    'complexity': int(metric_map.get('complexity', 0)),
    'files': int(metric_map.get('files', 0)),
    'functions': int(metric_map.get('functions', 0)),
    'statements': int(metric_map.get('statements', 0)),
    'duplicated_lines': int(metric_map.get('duplicated_lines', 0)),
    'duplicated_blocks': int(metric_map.get('duplicated_blocks', 0)),
    'duplicated_files': int(metric_map.get('duplicated_files', 0)),
    'raw_json': json.dumps(metric_data),
    'status': 'active',
    'description': None,
    'project_key': project_key,
    'branch': project_data['components'][0].get('branch', 'main')  # 尝试从项目数据获取分支，默认为main
}

upsert_sql = f'''
INSERT INTO {table_name} (
    analysis_date, project_name, sonar_key, branch, bugs, vulnerabilities, code_smells, coverage,
    duplicated_lines_density, ncloc, sq_blocker, sq_critical, sq_major, sq_minor, sq_info,
    comment_lines_density, complexity, files, functions, statements,
    duplicated_lines, duplicated_blocks, duplicated_files,
    raw_json, status, description
) VALUES (
    :analysis_date, :project_name, :project_key, :branch, :bugs, :vulnerabilities, :code_smells, :coverage,
    :duplicated_lines_density, :ncloc, :sq_blocker, :sq_critical, :sq_major, :sq_minor, :sq_info,
    :comment_lines_density, :complexity, :files, :functions, :statements,
    :duplicated_lines, :duplicated_blocks, :duplicated_files,
    :raw_json, :status, :description
)
RETURNING id;
'''

if existing:
    # 有数据则UPDATE（只更新最新一条）
    update_sql = f"""
    UPDATE {table_name} SET
        analysis_date = :analysis_date,
        project_name = :project_name,
        bugs = :bugs,
        vulnerabilities = :vulnerabilities,
        code_smells = :code_smells,
        coverage = :coverage,
        duplicated_lines_density = :duplicated_lines_density,
        ncloc = :ncloc,
        sq_blocker = :sq_blocker,
        sq_critical = :sq_critical,
        sq_major = :sq_major,
        sq_minor = :sq_minor,
        sq_info = :sq_info,
        comment_lines_density = :comment_lines_density,
        complexity = :complexity,
        files = :files,
        functions = :functions,
        statements = :statements,
        duplicated_lines = :duplicated_lines,
        duplicated_blocks = :duplicated_blocks,
        duplicated_files = :duplicated_files,
        raw_json = :raw_json,
        status = :status,
        description = :description,
        updated_at = NOW()
    WHERE id = :id;
    """
    row['id'] = existing[0][0]
    conn.run(update_sql, **row)
else:
    # 无数据则INSERT
    conn.run(upsert_sql, **row)

print(f"同步完成: {project_key} -> {table_name} ({analysis_date.strftime('%Y-%m-%d %H:%M:%S%z')})")
conn.close()
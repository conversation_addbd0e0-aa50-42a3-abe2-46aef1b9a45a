('c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6',
 'tcl',
 ['demos', '*.lib', 'tclConfig.sh'],
 'DATA',
 [('tcl\\auto.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('tcl\\clock.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('tcl\\history.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('tcl\\init.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('tcl\\package.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('tcl\\parray.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('tcl\\safe.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('tcl\\tclIndex',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('tcl\\tm.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('tcl\\word.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('tcl\\tzdata\\CET',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('tcl\\tzdata\\CST6CDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('tcl\\tzdata\\Cuba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('tcl\\tzdata\\EET',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('tcl\\tzdata\\Egypt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('tcl\\tzdata\\Eire',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('tcl\\tzdata\\EST',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('tcl\\tzdata\\EST5EDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('tcl\\tzdata\\GB',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('tcl\\tzdata\\GB-Eire',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('tcl\\tzdata\\GMT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('tcl\\tzdata\\GMT+0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('tcl\\tzdata\\GMT-0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('tcl\\tzdata\\GMT0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('tcl\\tzdata\\Greenwich',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('tcl\\tzdata\\Hongkong',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('tcl\\tzdata\\HST',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('tcl\\tzdata\\Iceland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('tcl\\tzdata\\Iran',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('tcl\\tzdata\\Israel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('tcl\\tzdata\\Jamaica',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('tcl\\tzdata\\Japan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('tcl\\tzdata\\Kwajalein',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('tcl\\tzdata\\Libya',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('tcl\\tzdata\\MET',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('tcl\\tzdata\\MST',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('tcl\\tzdata\\MST7MDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('tcl\\tzdata\\Navajo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('tcl\\tzdata\\NZ',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('tcl\\tzdata\\NZ-CHAT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('tcl\\tzdata\\Poland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('tcl\\tzdata\\Portugal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('tcl\\tzdata\\PRC',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('tcl\\tzdata\\PST8PDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('tcl\\tzdata\\ROC',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('tcl\\tzdata\\ROK',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('tcl\\tzdata\\Singapore',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('tcl\\tzdata\\Turkey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('tcl\\tzdata\\UCT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('tcl\\tzdata\\Universal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('tcl\\tzdata\\UTC',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('tcl\\tzdata\\W-SU',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('tcl\\tzdata\\WET',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('tcl\\tzdata\\Zulu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('tcl\\tzdata\\US\\Alaska',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('tcl\\tzdata\\US\\Aleutian',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('tcl\\tzdata\\US\\Arizona',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('tcl\\tzdata\\US\\Central',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('tcl\\tzdata\\US\\East-Indiana',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('tcl\\tzdata\\US\\Eastern',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('tcl\\tzdata\\US\\Hawaii',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('tcl\\tzdata\\US\\Indiana-Starke',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('tcl\\tzdata\\US\\Michigan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('tcl\\tzdata\\US\\Mountain',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific-New',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('tcl\\tzdata\\US\\Samoa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4ADT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6CDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5EDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\HST10',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7MDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8PDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9YDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Apia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Auckland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Bougainville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chatham',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chuuk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Easter',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Efate',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Enderbury',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fakaofo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fiji',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Funafuti',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Galapagos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Gambier',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guadalcanal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guam',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Honolulu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Johnston',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kiritimati',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kosrae',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kwajalein',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Majuro',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Marquesas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Midway',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Nauru',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Niue',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Norfolk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Noumea',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pago_Pago',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Palau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pitcairn',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pohnpei',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Ponape',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Port_Moresby',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Rarotonga',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Saipan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Samoa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tahiti',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tarawa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tongatapu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Truk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wake',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wallis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Yap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaNorte',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaSur',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\General',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Antananarivo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Chagos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Christmas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Cocos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Comoro',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Kerguelen',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mahe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Maldives',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mauritius',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mayotte',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Reunion',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Amsterdam',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Andorra',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Astrakhan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Athens',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belfast',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belgrade',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Berlin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bratislava',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Brussels',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bucharest',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Budapest',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Busingen',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Chisinau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Copenhagen',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Dublin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Gibraltar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Guernsey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Helsinki',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Isle_of_Man',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Istanbul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Jersey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kaliningrad',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kiev',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kirov',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Lisbon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ljubljana',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('tcl\\tzdata\\Europe\\London',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Luxembourg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Madrid',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Malta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Mariehamn',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Minsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Monaco',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Moscow',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Nicosia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Oslo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Paris',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Podgorica',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Prague',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Riga',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Rome',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Samara',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('tcl\\tzdata\\Europe\\San_Marino',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sarajevo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Saratov',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Simferopol',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Skopje',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sofia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Stockholm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tallinn',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tirane',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tiraspol',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ulyanovsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Uzhgorod',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vaduz',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vatican',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vienna',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vilnius',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Volgograd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Warsaw',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zagreb',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zaporozhye',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zurich',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+1',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+10',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+11',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+12',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+2',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+3',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+4',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+5',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+6',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+7',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+8',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+9',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-1',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-10',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-11',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-12',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-13',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-14',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-2',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-3',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-4',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-5',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-6',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-7',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-8',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-9',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Greenwich',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UCT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Universal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UTC',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Zulu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('tcl\\tzdata\\Chile\\Continental',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('tcl\\tzdata\\Chile\\EasterIsland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Atlantic',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Central',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('tcl\\tzdata\\Canada\\East-Saskatchewan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Eastern',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Mountain',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Newfoundland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Pacific',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Saskatchewan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Yukon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\Acre',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\DeNoronha',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\East',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\West',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('tcl\\tzdata\\Australia\\ACT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Adelaide',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Brisbane',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Broken_Hill',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Canberra',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Currie',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Darwin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Eucla',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Hobart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('tcl\\tzdata\\Australia\\LHI',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lindeman',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lord_Howe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Melbourne',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('tcl\\tzdata\\Australia\\North',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('tcl\\tzdata\\Australia\\NSW',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Perth',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Queensland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('tcl\\tzdata\\Australia\\South',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Sydney',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Tasmania',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Victoria',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('tcl\\tzdata\\Australia\\West',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Yancowinna',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Azores',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Bermuda',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Canary',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Cape_Verde',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faeroe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faroe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Jan_Mayen',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Madeira',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Reykjavik',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\South_Georgia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Stanley',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\St_Helena',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aden',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Almaty',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Amman',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Anadyr',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtobe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashgabat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashkhabad',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Atyrau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baghdad',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bahrain',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baku',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bangkok',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Barnaul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Beirut',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bishkek',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Brunei',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Calcutta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chita',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Choibalsan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chongqing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chungking',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Colombo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dacca',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Damascus',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dhaka',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dili',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dubai',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dushanbe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Famagusta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Gaza',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Harbin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hebron',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hong_Kong',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hovd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ho_Chi_Minh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Irkutsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Istanbul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jakarta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jayapura',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jerusalem',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kabul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kamchatka',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Karachi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kashgar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kathmandu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Katmandu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Khandyga',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kolkata',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Krasnoyarsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuala_Lumpur',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuching',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuwait',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macao',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Magadan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Makassar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Manila',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Muscat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Nicosia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novokuznetsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novosibirsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Omsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Oral',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Phnom_Penh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pontianak',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pyongyang',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qatar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qyzylorda',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Rangoon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Riyadh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Saigon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Sakhalin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Samarkand',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Seoul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Shanghai',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Singapore',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Srednekolymsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Taipei',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tashkent',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tbilisi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tehran',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tel_Aviv',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimbu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimphu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tokyo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tomsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ujung_Pandang',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulaanbaatar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulan_Bator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Urumqi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ust-Nera',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vientiane',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vladivostok',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yakutsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yangon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yekaterinburg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yerevan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('tcl\\tzdata\\Arctic\\Longyearbyen',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Casey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Davis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\DumontDUrville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Macquarie',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Mawson',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\McMurdo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Palmer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Rothera',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\South_Pole',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Syowa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Troll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Vostok',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('tcl\\tzdata\\America\\Adak',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('tcl\\tzdata\\America\\Anchorage',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('tcl\\tzdata\\America\\Anguilla',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('tcl\\tzdata\\America\\Antigua',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('tcl\\tzdata\\America\\Araguaina',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('tcl\\tzdata\\America\\Aruba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('tcl\\tzdata\\America\\Asuncion',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('tcl\\tzdata\\America\\Atikokan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('tcl\\tzdata\\America\\Atka',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia_Banderas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('tcl\\tzdata\\America\\Barbados',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('tcl\\tzdata\\America\\Belem',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('tcl\\tzdata\\America\\Belize',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('tcl\\tzdata\\America\\Blanc-Sablon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('tcl\\tzdata\\America\\Boa_Vista',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('tcl\\tzdata\\America\\Bogota',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('tcl\\tzdata\\America\\Boise',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('tcl\\tzdata\\America\\Buenos_Aires',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\America\\Cambridge_Bay',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('tcl\\tzdata\\America\\Campo_Grande',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('tcl\\tzdata\\America\\Cancun',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('tcl\\tzdata\\America\\Caracas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('tcl\\tzdata\\America\\Catamarca',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayenne',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayman',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('tcl\\tzdata\\America\\Chicago',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('tcl\\tzdata\\America\\Chihuahua',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('tcl\\tzdata\\America\\Coral_Harbour',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('tcl\\tzdata\\America\\Cordoba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('tcl\\tzdata\\America\\Costa_Rica',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('tcl\\tzdata\\America\\Creston',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('tcl\\tzdata\\America\\Cuiaba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('tcl\\tzdata\\America\\Curacao',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('tcl\\tzdata\\America\\Danmarkshavn',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson_Creek',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('tcl\\tzdata\\America\\Denver',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('tcl\\tzdata\\America\\Detroit',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('tcl\\tzdata\\America\\Dominica',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('tcl\\tzdata\\America\\Edmonton',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('tcl\\tzdata\\America\\Eirunepe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('tcl\\tzdata\\America\\El_Salvador',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('tcl\\tzdata\\America\\Ensenada',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('tcl\\tzdata\\America\\Fortaleza',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Nelson',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Wayne',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('tcl\\tzdata\\America\\Glace_Bay',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('tcl\\tzdata\\America\\Godthab',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('tcl\\tzdata\\America\\Goose_Bay',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('tcl\\tzdata\\America\\Grand_Turk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('tcl\\tzdata\\America\\Grenada',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('tcl\\tzdata\\America\\Guadeloupe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('tcl\\tzdata\\America\\Guatemala',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('tcl\\tzdata\\America\\Guayaquil',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('tcl\\tzdata\\America\\Guyana',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('tcl\\tzdata\\America\\Halifax',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('tcl\\tzdata\\America\\Havana',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('tcl\\tzdata\\America\\Hermosillo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('tcl\\tzdata\\America\\Indianapolis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('tcl\\tzdata\\America\\Inuvik',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('tcl\\tzdata\\America\\Iqaluit',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('tcl\\tzdata\\America\\Jamaica',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('tcl\\tzdata\\America\\Jujuy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\America\\Juneau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('tcl\\tzdata\\America\\Knox_IN',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('tcl\\tzdata\\America\\Kralendijk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('tcl\\tzdata\\America\\La_Paz',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('tcl\\tzdata\\America\\Lima',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('tcl\\tzdata\\America\\Los_Angeles',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('tcl\\tzdata\\America\\Louisville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('tcl\\tzdata\\America\\Lower_Princes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('tcl\\tzdata\\America\\Maceio',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('tcl\\tzdata\\America\\Managua',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('tcl\\tzdata\\America\\Manaus',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('tcl\\tzdata\\America\\Marigot',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('tcl\\tzdata\\America\\Martinique',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('tcl\\tzdata\\America\\Matamoros',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('tcl\\tzdata\\America\\Mazatlan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('tcl\\tzdata\\America\\Mendoza',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('tcl\\tzdata\\America\\Menominee',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('tcl\\tzdata\\America\\Merida',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('tcl\\tzdata\\America\\Metlakatla',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('tcl\\tzdata\\America\\Mexico_City',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('tcl\\tzdata\\America\\Miquelon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('tcl\\tzdata\\America\\Moncton',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('tcl\\tzdata\\America\\Monterrey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('tcl\\tzdata\\America\\Montevideo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('tcl\\tzdata\\America\\Montreal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('tcl\\tzdata\\America\\Montserrat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('tcl\\tzdata\\America\\Nassau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('tcl\\tzdata\\America\\New_York',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('tcl\\tzdata\\America\\Nipigon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('tcl\\tzdata\\America\\Nome',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('tcl\\tzdata\\America\\Noronha',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('tcl\\tzdata\\America\\Ojinaga',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('tcl\\tzdata\\America\\Panama',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('tcl\\tzdata\\America\\Pangnirtung',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('tcl\\tzdata\\America\\Paramaribo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('tcl\\tzdata\\America\\Phoenix',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('tcl\\tzdata\\America\\Port-au-Prince',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Acre',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Velho',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('tcl\\tzdata\\America\\Port_of_Spain',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('tcl\\tzdata\\America\\Puerto_Rico',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('tcl\\tzdata\\America\\Punta_Arenas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('tcl\\tzdata\\America\\Rainy_River',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('tcl\\tzdata\\America\\Rankin_Inlet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('tcl\\tzdata\\America\\Recife',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('tcl\\tzdata\\America\\Regina',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('tcl\\tzdata\\America\\Resolute',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('tcl\\tzdata\\America\\Rio_Branco',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('tcl\\tzdata\\America\\Rosario',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('tcl\\tzdata\\America\\Santarem',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('tcl\\tzdata\\America\\Santa_Isabel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('tcl\\tzdata\\America\\Santiago',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('tcl\\tzdata\\America\\Santo_Domingo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('tcl\\tzdata\\America\\Sao_Paulo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('tcl\\tzdata\\America\\Scoresbysund',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('tcl\\tzdata\\America\\Shiprock',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('tcl\\tzdata\\America\\Sitka',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Barthelemy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Johns',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Kitts',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Lucia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Thomas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Vincent',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('tcl\\tzdata\\America\\Swift_Current',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('tcl\\tzdata\\America\\Tegucigalpa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('tcl\\tzdata\\America\\Thule',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('tcl\\tzdata\\America\\Thunder_Bay',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('tcl\\tzdata\\America\\Tijuana',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('tcl\\tzdata\\America\\Toronto',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('tcl\\tzdata\\America\\Tortola',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('tcl\\tzdata\\America\\Vancouver',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('tcl\\tzdata\\America\\Virgin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('tcl\\tzdata\\America\\Whitehorse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('tcl\\tzdata\\America\\Winnipeg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('tcl\\tzdata\\America\\Yakutat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('tcl\\tzdata\\America\\Yellowknife',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Beulah',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Center',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\New_Salem',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Louisville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Monticello',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Indianapolis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Knox',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Marengo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Petersburg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Tell_City',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vevay',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vincennes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Winamac',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Buenos_Aires',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Catamarca',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\ComodRivadavia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Cordoba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Jujuy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\La_Rioja',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Mendoza',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Salta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Juan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Luis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Tucuman',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Ushuaia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Abidjan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Accra',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Addis_Ababa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Algiers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmara',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmera',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bamako',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bangui',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Banjul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bissau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Blantyre',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Brazzaville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bujumbura',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Cairo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Casablanca',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ceuta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Conakry',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dakar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dar_es_Salaam',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Djibouti',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Douala',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('tcl\\tzdata\\Africa\\El_Aaiun',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Freetown',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Gaborone',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Harare',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Johannesburg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Juba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kampala',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Khartoum',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kigali',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kinshasa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lagos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Libreville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lome',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Luanda',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lubumbashi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lusaka',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Malabo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maputo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maseru',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mbabane',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mogadishu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Monrovia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nairobi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ndjamena',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Niamey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nouakchott',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ouagadougou',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Porto-Novo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Sao_Tome',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Timbuktu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tripoli',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tunis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Windhoek',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('tcl\\opt0.4\\optparse.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('tcl\\opt0.4\\pkgIndex.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\msgs\\af.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('tcl\\msgs\\af_za.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('tcl\\msgs\\ar.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('tcl\\msgs\\ar_in.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('tcl\\msgs\\ar_jo.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('tcl\\msgs\\ar_lb.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('tcl\\msgs\\ar_sy.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('tcl\\msgs\\be.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('tcl\\msgs\\bg.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('tcl\\msgs\\bn.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('tcl\\msgs\\bn_in.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('tcl\\msgs\\ca.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('tcl\\msgs\\cs.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('tcl\\msgs\\da.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('tcl\\msgs\\de.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('tcl\\msgs\\de_at.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('tcl\\msgs\\de_be.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('tcl\\msgs\\el.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('tcl\\msgs\\en_au.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('tcl\\msgs\\en_be.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('tcl\\msgs\\en_bw.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('tcl\\msgs\\en_ca.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('tcl\\msgs\\en_gb.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tcl\\msgs\\en_hk.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('tcl\\msgs\\en_ie.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('tcl\\msgs\\en_in.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('tcl\\msgs\\en_nz.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('tcl\\msgs\\en_ph.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('tcl\\msgs\\en_sg.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('tcl\\msgs\\en_za.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('tcl\\msgs\\en_zw.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('tcl\\msgs\\eo.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('tcl\\msgs\\es.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('tcl\\msgs\\es_ar.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('tcl\\msgs\\es_bo.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('tcl\\msgs\\es_cl.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('tcl\\msgs\\es_co.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('tcl\\msgs\\es_cr.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('tcl\\msgs\\es_do.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('tcl\\msgs\\es_ec.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('tcl\\msgs\\es_gt.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('tcl\\msgs\\es_hn.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('tcl\\msgs\\es_mx.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('tcl\\msgs\\es_ni.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('tcl\\msgs\\es_pa.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('tcl\\msgs\\es_pe.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('tcl\\msgs\\es_pr.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('tcl\\msgs\\es_py.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('tcl\\msgs\\es_sv.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('tcl\\msgs\\es_uy.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('tcl\\msgs\\es_ve.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('tcl\\msgs\\et.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('tcl\\msgs\\eu.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('tcl\\msgs\\eu_es.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('tcl\\msgs\\fa.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('tcl\\msgs\\fa_in.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('tcl\\msgs\\fa_ir.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('tcl\\msgs\\fi.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('tcl\\msgs\\fo.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('tcl\\msgs\\fo_fo.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('tcl\\msgs\\fr.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('tcl\\msgs\\fr_be.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('tcl\\msgs\\fr_ca.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('tcl\\msgs\\fr_ch.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('tcl\\msgs\\ga.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('tcl\\msgs\\ga_ie.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('tcl\\msgs\\gl.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('tcl\\msgs\\gl_es.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('tcl\\msgs\\gv.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('tcl\\msgs\\gv_gb.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('tcl\\msgs\\he.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('tcl\\msgs\\hi.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('tcl\\msgs\\hi_in.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('tcl\\msgs\\hr.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('tcl\\msgs\\hu.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('tcl\\msgs\\id.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('tcl\\msgs\\id_id.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('tcl\\msgs\\is.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('tcl\\msgs\\it.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('tcl\\msgs\\it_ch.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('tcl\\msgs\\ja.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('tcl\\msgs\\kl.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('tcl\\msgs\\kl_gl.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('tcl\\msgs\\ko.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('tcl\\msgs\\kok.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('tcl\\msgs\\kok_in.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('tcl\\msgs\\ko_kr.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('tcl\\msgs\\kw.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('tcl\\msgs\\kw_gb.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('tcl\\msgs\\lt.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('tcl\\msgs\\lv.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('tcl\\msgs\\mk.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('tcl\\msgs\\mr.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('tcl\\msgs\\mr_in.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('tcl\\msgs\\ms.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('tcl\\msgs\\ms_my.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('tcl\\msgs\\mt.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('tcl\\msgs\\nb.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('tcl\\msgs\\nl.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('tcl\\msgs\\nl_be.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('tcl\\msgs\\nn.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('tcl\\msgs\\pl.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('tcl\\msgs\\pt.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('tcl\\msgs\\pt_br.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('tcl\\msgs\\ro.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('tcl\\msgs\\ru.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('tcl\\msgs\\ru_ua.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('tcl\\msgs\\sh.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('tcl\\msgs\\sk.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('tcl\\msgs\\sl.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('tcl\\msgs\\sq.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('tcl\\msgs\\sr.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('tcl\\msgs\\sv.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('tcl\\msgs\\sw.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('tcl\\msgs\\ta.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('tcl\\msgs\\ta_in.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('tcl\\msgs\\te.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('tcl\\msgs\\te_in.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('tcl\\msgs\\th.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('tcl\\msgs\\tr.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('tcl\\msgs\\uk.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('tcl\\msgs\\vi.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('tcl\\msgs\\zh.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('tcl\\msgs\\zh_cn.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('tcl\\msgs\\zh_hk.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('tcl\\msgs\\zh_sg.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('tcl\\msgs\\zh_tw.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('tcl\\http1.0\\http.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('tcl\\http1.0\\pkgIndex.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\encoding\\ascii.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('tcl\\encoding\\big5.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('tcl\\encoding\\cp1250.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('tcl\\encoding\\cp1251.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('tcl\\encoding\\cp1252.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('tcl\\encoding\\cp1253.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('tcl\\encoding\\cp1254.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('tcl\\encoding\\cp1255.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('tcl\\encoding\\cp1256.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('tcl\\encoding\\cp1257.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('tcl\\encoding\\cp1258.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('tcl\\encoding\\cp437.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('tcl\\encoding\\cp737.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('tcl\\encoding\\cp775.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('tcl\\encoding\\cp850.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('tcl\\encoding\\cp852.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('tcl\\encoding\\cp855.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('tcl\\encoding\\cp857.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('tcl\\encoding\\cp860.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('tcl\\encoding\\cp861.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('tcl\\encoding\\cp862.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('tcl\\encoding\\cp863.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('tcl\\encoding\\cp864.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('tcl\\encoding\\cp865.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('tcl\\encoding\\cp866.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('tcl\\encoding\\cp869.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('tcl\\encoding\\cp874.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('tcl\\encoding\\cp932.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('tcl\\encoding\\cp936.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('tcl\\encoding\\cp949.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('tcl\\encoding\\cp950.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('tcl\\encoding\\dingbats.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('tcl\\encoding\\ebcdic.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('tcl\\encoding\\euc-cn.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('tcl\\encoding\\euc-jp.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('tcl\\encoding\\euc-kr.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('tcl\\encoding\\gb12345.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('tcl\\encoding\\gb1988.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('tcl\\encoding\\gb2312-raw.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('tcl\\encoding\\gb2312.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('tcl\\encoding\\iso2022-jp.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('tcl\\encoding\\iso2022-kr.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('tcl\\encoding\\iso2022.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-1.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-10.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-13.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-14.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-15.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-16.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-2.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-3.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-4.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-5.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-6.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-7.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-8.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-9.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('tcl\\encoding\\jis0201.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('tcl\\encoding\\jis0208.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('tcl\\encoding\\jis0212.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('tcl\\encoding\\koi8-r.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('tcl\\encoding\\koi8-u.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('tcl\\encoding\\ksc5601.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('tcl\\encoding\\macCentEuro.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('tcl\\encoding\\macCroatian.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('tcl\\encoding\\macCyrillic.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('tcl\\encoding\\macDingbats.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('tcl\\encoding\\macGreek.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('tcl\\encoding\\macIceland.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('tcl\\encoding\\macJapan.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('tcl\\encoding\\macRoman.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('tcl\\encoding\\macRomania.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('tcl\\encoding\\macThai.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('tcl\\encoding\\macTurkish.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('tcl\\encoding\\macUkraine.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('tcl\\encoding\\shiftjis.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('tcl\\encoding\\symbol.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('tcl\\encoding\\tis-620.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA')])

          "path": "board/main.c",
          "path": "../../../../../sifli/customer/peripherals/firmware_ota/firmware_iap.c",
          "path": "../../src/boot_event_task.c",
          "path": "../../src/periodic_task.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_adc.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_aes.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_audcodec.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_audprc.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_bleaon.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_busmon.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_cortex.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_crc.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_dma.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_dsi.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_efuse.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_epic.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_ext_dma.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_ezip.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_facc.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_fft.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_gpio.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_hcd.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_hlp.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_hpaon.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_i2c.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_i2s.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_lcdc.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_lcpu_config.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_lpaon.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_lpcomp.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_lptim.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_lrc_cal.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_mailbox.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_nn_acc.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_patch.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_pcd.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_pdm.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_pinmux.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_pmu.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_psram.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_ptc.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_qspi_ex.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_qspi.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_rcc.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_rng.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_rtc.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_sd_ex.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_sdadc.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_sdhci.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_sdmmc.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_secu.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_spi.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_tim_ex.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_tim.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_tsen.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_uart.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal_wdt.c",
          "path": "../../../../../sifli/drivers/hal/bf0_hal.c",
          "path": "../../../../../sifli/drivers/hal/bf0_sys_cfg.c",
          "path": "../../../../../sifli/drivers/hal/flash_table.c",
          "path": "../../../../../sifli/drivers/hal/nand_table.c",
          "path": "../../../../../sifli/drivers/hal/sifli_bbm.c",
          "path": "../../../../../sifli/drivers/cmsis/sf32lb55x/bf0_pin_const.c",
          "path": "../../../../../sifli/drivers/cmsis/sf32lb55x/ble_rf_fulcal.c",
          "path": "../../../../../sifli/drivers/cmsis/sf32lb55x/lcpu_patch.c",
          "path": "../../../../../sifli/drivers/cmsis/sf32lb55x/Templates/arm/startup_bf0_hcpu.S",
          "path": "../../../../../sifli/drivers/cmsis/sf32lb55x/Templates/system_bf0_ap.c",
          "path": "../../../../../sifli/rtos/rtthread/libcpu/arm/common/backtrace.c",
          "path": "../../../../../sifli/rtos/rtthread/libcpu/arm/common/div0.c",
          "path": "../../../../../sifli/rtos/rtthread/libcpu/arm/common/showmem.c",
          "path": "../../../../../sifli/rtos/rtthread/libcpu/arm/Cortex-M33/context_rvds.S",
          "path": "../../../../../sifli/rtos/rtthread/libcpu/arm/Cortex-M33/cpuport.c",
          "path": "../../../../../sifli/rtos/rtthread/components/drivers/i2c/i2c_core.c",
          "path": "../../../../../sifli/rtos/rtthread/components/drivers/i2c/i2c_dev.c",
          "path": "../../../../../sifli/rtos/rtthread/components/drivers/misc/pin.c",
          "path": "../../../../../sifli/rtos/rtthread/components/drivers/sdio/block_dev.c",
          "path": "../../../../../sifli/rtos/rtthread/components/drivers/sdio/mmc.c",
          "path": "../../../../../sifli/rtos/rtthread/components/drivers/sdio/mmcsd_core.c",
          "path": "../../../../../sifli/rtos/rtthread/components/drivers/sdio/sd.c",
          "path": "../../../../../sifli/rtos/rtthread/components/drivers/sdio/sdio.c",
          "path": "../../../../../sifli/rtos/rtthread/components/drivers/serial/serial.c",
          "path": "../../../../../sifli/rtos/rtthread/components/drivers/src/completion.c",
          "path": "../../../../../sifli/rtos/rtthread/components/drivers/src/dataqueue.c",
          "path": "../../../../../sifli/rtos/rtthread/components/drivers/src/pipe.c",
          "path": "../../../../../sifli/rtos/rtthread/components/drivers/src/ringblk_buf.c",
          "path": "../../../../../sifli/rtos/rtthread/components/drivers/src/ringbuffer.c",
          "path": "../../../../../sifli/rtos/rtthread/components/drivers/src/waitqueue.c",
          "path": "../../../../../sifli/rtos/rtthread/components/drivers/src/workqueue.c",
          "path": "../../../../Application/Drivers/driver_api/power_ctl.c",
          "path": "../../../../Application/pm_manager/pm_notify_app.c",
          "path": "../../../../../sifli/customer/boards/common/flash.c",
          "path": "../../../../../sifli/customer/boards/ec-lb557xxx/bsp_board.c",
          "path": "../../../../../sifli/customer/boards/ec-lb557xxx/bsp_pinmux.c",
          "path": "../../../../../sifli/customer/boards/ec-lb557xxx/drv_io.c",
          "path": "../../../../../sifli/customer/peripherals/co5300/co5300.c",
          "path": "../../../../../sifli/customer/peripherals/ft6146/ft6146.c",
          "path": "../../../../../sifli/customer/peripherals/pmic_controller/pmic_controller.c",
          "path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_common.c",
          "path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_dbg.c",
          "path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_ext_dma.c",
          "path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_gpio.c",
          "path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_i2c.c",
          "path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_lcd_private.c",
          "path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_lcd_test.c",
          "path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_lcd.c",
          "path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_psram.c",
          "path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_sdhci.c",
          "path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_spi_flash.c",
          "path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_spi_nand.c",
          "path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_sys_cfg.c",
          "path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_touch.c",
          "path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_usart.c",
          "path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_usb_otg.c",
          "path": "../../../../../sifli/rtos/rtthread/bsp/sifli/drivers/drv_usbd.c",
          "path": "board/bf0_ap_hal_msp.c",
          "path": "board/board.c",
              "path": "../../../../../sifli/customer/peripherals/CW6307/cellwise_charger_cw6307.c",
              "path": "../../../../../sifli/external/erpc/infra/erpc_arbitrated_client_manager.cpp",
              "path": "../../../../../sifli/external/erpc/infra/erpc_basic_codec.cpp",
              "path": "../../../../../sifli/external/erpc/infra/erpc_client_manager.cpp",
              "path": "../../../../../sifli/external/erpc/infra/erpc_crc16.cpp",
              "path": "../../../../../sifli/external/erpc/infra/erpc_framed_transport.cpp",
              "path": "../../../../../sifli/external/erpc/infra/erpc_message_buffer.cpp",
              "path": "../../../../../sifli/external/erpc/infra/erpc_message_loggers.cpp",
              "path": "../../../../../sifli/external/erpc/infra/erpc_pre_post_action.cpp",
              "path": "../../../../../sifli/external/erpc/infra/erpc_server.cpp",
              "path": "../../../../../sifli/external/erpc/infra/erpc_simple_server.cpp",
              "path": "../../../../../sifli/external/erpc/infra/erpc_transport_arbitrator.cpp",
              "path": "../../../../../sifli/external/erpc/port/erpc_port_rtthread.cpp",
              "path": "../../../../../sifli/external/erpc/port/erpc_threading_pthreads.cpp",
              "path": "../../../../../sifli/external/erpc/setup/erpc_arbitrated_client_setup.cpp",
              "path": "../../../../../sifli/external/erpc/setup/erpc_client_setup.cpp",
              "path": "../../../../../sifli/external/erpc/setup/erpc_server_setup.cpp",
              "path": "../../../../../sifli/external/erpc/setup/erpc_setup_mbf_dynamic.cpp",
              "path": "../../../../../sifli/external/erpc/setup/erpc_setup_mbf_static.cpp",
              "path": "../../../../../sifli/external/erpc/setup/erpc_setup_rpmsg_sifli.cpp",
              "path": "../../../../../sifli/external/erpc/transports/erpc_rpmsg_sifli_transport.cpp",
          "path": "../../../../../sifli/rtos/rtthread/components/dfs/filesystems/devfs/devfs.c",
          "path": "../../../../../sifli/rtos/rtthread/components/dfs/filesystems/elmfat/dfs_elm.c",
          "path": "../../../../../sifli/rtos/rtthread/components/dfs/filesystems/elmfat/ff.c",
          "path": "../../../../../sifli/rtos/rtthread/components/dfs/filesystems/elmfat/ffunicode.c",
          "path": "../../../../../sifli/rtos/rtthread/components/dfs/src/dfs_file.c",
          "path": "../../../../../sifli/rtos/rtthread/components/dfs/src/dfs_fs.c",
          "path": "../../../../../sifli/rtos/rtthread/components/dfs/src/dfs_posix.c",
          "path": "../../../../../sifli/rtos/rtthread/components/dfs/src/dfs.c",
          "path": "../../../../../sifli/rtos/rtthread/components/dfs/src/poll.c",
          "path": "../../../../../sifli/rtos/rtthread/components/dfs/src/select.c",
          "path": "../../../../../sifli/rtos/rtthread/components/finsh/cmd.c",
          "path": "../../../../../sifli/rtos/rtthread/components/finsh/finsh_compiler.c",
          "path": "../../../../../sifli/rtos/rtthread/components/finsh/finsh_error.c",
          "path": "../../../../../sifli/rtos/rtthread/components/finsh/finsh_heap.c",
          "path": "../../../../../sifli/rtos/rtthread/components/finsh/finsh_init.c",
          "path": "../../../../../sifli/rtos/rtthread/components/finsh/finsh_node.c",
          "path": "../../../../../sifli/rtos/rtthread/components/finsh/finsh_ops.c",
          "path": "../../../../../sifli/rtos/rtthread/components/finsh/finsh_parser.c",
          "path": "../../../../../sifli/rtos/rtthread/components/finsh/finsh_token.c",
          "path": "../../../../../sifli/rtos/rtthread/components/finsh/finsh_var.c",
          "path": "../../../../../sifli/rtos/rtthread/components/finsh/finsh_vm.c",
          "path": "../../../../../sifli/rtos/rtthread/components/finsh/msh_cmd.c",
          "path": "../../../../../sifli/rtos/rtthread/components/finsh/msh_file.c",
          "path": "../../../../../sifli/rtos/rtthread/components/finsh/msh.c",
          "path": "../../../../../sifli/rtos/rtthread/components/finsh/shell.c",
          "path": "../../../../../sifli/rtos/rtthread/components/finsh/symbol.c",
          "path": "../../../../../sifli/rtos/rtthread/src/clock.c",
          "path": "../../../../../sifli/rtos/rtthread/src/components.c",
          "path": "../../../../../sifli/rtos/rtthread/src/cpu.c",
          "path": "../../../../../sifli/rtos/rtthread/src/device.c",
          "path": "../../../../../sifli/rtos/rtthread/src/idle.c",
          "path": "../../../../../sifli/rtos/rtthread/src/ipc.c",
          "path": "../../../../../sifli/rtos/rtthread/src/irq.c",
          "path": "../../../../../sifli/rtos/rtthread/src/kservice.c",
          "path": "../../../../../sifli/rtos/rtthread/src/mem.c",
          "path": "../../../../../sifli/rtos/rtthread/src/memheap.c",
          "path": "../../../../../sifli/rtos/rtthread/src/mempool.c",
          "path": "../../../../../sifli/rtos/rtthread/src/object.c",
          "path": "../../../../../sifli/rtos/rtthread/src/scheduler.c",
          "path": "../../../../../sifli/rtos/rtthread/src/signal.c",
          "path": "../../../../../sifli/rtos/rtthread/src/thread.c",
          "path": "../../../../../sifli/rtos/rtthread/src/timer.c",
          "path": "../../../../../sifli/rtos/rtthread/components/libc/compilers/armlibc/libc.c",
          "path": "../../../../../sifli/rtos/rtthread/components/libc/compilers/armlibc/mem_std.c",
          "path": "../../../../../sifli/rtos/rtthread/components/libc/compilers/armlibc/stdio.c",
          "path": "../../../../../sifli/rtos/rtthread/components/libc/compilers/armlibc/stubs.c",
          "path": "../../../../../sifli/rtos/rtthread/components/libc/compilers/armlibc/time.c",
          "path": "../../../../../sifli/rtos/rtthread/components/libc/compilers/common/gmtime_r.c",
          "path": "../../../../../sifli/middleware/system/bf0_common.c",
          "path": "../../../../../sifli/middleware/ipc_queue/common/circular_buf.c",
          "path": "../../../../../sifli/middleware/ipc_queue/common/ipc_hw.c",
          "path": "../../../../../sifli/middleware/ipc_queue/common/ipc_queue.c",
          "path": "../../../../../sifli/middleware/ipc_queue/porting/sf32lb55x/hcpu/ipc_hw_port.c",
          "path": "../../../../../sifli/rtos/os_adaptor/src/os_adaptor_rtthread.c",
          "path": "../../../../../sifli/rtos/rtthread/components/libc/pthreads/mqueue.c",
          "path": "../../../../../sifli/rtos/rtthread/components/libc/pthreads/pthread_attr.c",
          "path": "../../../../../sifli/rtos/rtthread/components/libc/pthreads/pthread_barrier.c",
          "path": "../../../../../sifli/rtos/rtthread/components/libc/pthreads/pthread_cond.c",
          "path": "../../../../../sifli/rtos/rtthread/components/libc/pthreads/pthread_mutex.c",
          "path": "../../../../../sifli/rtos/rtthread/components/libc/pthreads/pthread_rwlock.c",
          "path": "../../../../../sifli/rtos/rtthread/components/libc/pthreads/pthread_spin.c",
          "path": "../../../../../sifli/rtos/rtthread/components/libc/pthreads/pthread_tls.c",
          "path": "../../../../../sifli/rtos/rtthread/components/libc/pthreads/pthread.c",
          "path": "../../../../../sifli/rtos/rtthread/components/libc/pthreads/sched.c",
          "path": "../../../../../sifli/rtos/rtthread/components/libc/pthreads/semaphore.c",
          "path": "../../../../../sifli/rtos/rtthread/components/libc/time/clock_time.c",
          "path": "../../../../../sifli/rtos/rtthread/components/libc/time/posix_sleep.c",
          "path": "../../../../../sifli/rtos/rtthread/components/drivers/usb/usbdevice/class/mstorage.c",
          "path": "../../../../../sifli/rtos/rtthread/components/drivers/usb/usbdevice/core/core.c",
          "path": "../../../../../sifli/rtos/rtthread/components/drivers/usb/usbdevice/core/usbdevice.c",
          "path": "../../../../../sifli/middleware/sifli_lib/lib/dummy.c",
          "path": "../../../../../sifli/middleware/sifli_lib/lib/sifli_lib_rvds.lib",
          "path": "../../../../../sifli/rtos/rtthread/components/utilities/llt_mem/llt_mem.c",
          "path": "../../../../../sifli/rtos/rtthread/components/utilities/ulog/backend/console_be.c",
          "path": "../../../../../sifli/rtos/rtthread/components/utilities/ulog/ulog.c",
          "path": "../../../../Application/msg_services/little_notify_list.c"

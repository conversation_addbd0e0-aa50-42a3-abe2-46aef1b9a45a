version: '3'
services:
  postgres:
    image: postgres:latest
    container_name: postgres
    restart: always
    privileged: true
    networks:
      - sonar
    volumes:
      - /data/sonar/postgres/postgresql:/var/lib/postgresql
      - /data/sonar/postgres/data:/var/lib/postgresql/data
      - /etc/localtime:/etc/localtime:ro
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: sonar
      POSTGRES_PASSWORD: sonar
      POSTGRES_DB: sonar
      TZ: Asia/Shanghai

  sonar:
    image: sonarqube:8.9.8-community
    container_name: sonar
    restart: always
    privileged: true
    networks:
      - sonar
    volumes:
      - /data/sonar/sonarqube/logs:/opt/sonarqube/logs
      - /data/sonar/sonarqube/conf:/opt/sonarqube/conf
      - /data/sonar/sonarqube/data:/opt/sonarqube/data
      - /data/sonar/sonarqube/extensions:/opt/sonarqube/extensions
    ports:
      - "9090:9000"
    links:
      - "postgres:postgres"
    environment:
      ALLOW_EMPTY_PASSWORD: "yes"
      SONARQUBE_JDBC_USERNAME: sonar
      SONARQUBE_JDBC_PASSWORD: sonar
      SONARQUBE_JDBC_URL: "*************************************"

networks:
  sonar:
    driver: bridge

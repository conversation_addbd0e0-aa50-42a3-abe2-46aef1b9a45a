# 🔄 UPSERT 数据插入逻辑集成指南

## 🎯 功能说明

新的 `save_commit_metrics` 函数实现了基于 `(gerrit_project, branch, change_id_short)` 三元组的 UPSERT 操作：

- ✅ **如果数据库中存在相同的三元组**：直接覆盖更新所有字段
- ✅ **如果数据库中不存在**：插入新记录
- ✅ **无版本判断**：简化逻辑，直接覆盖
- ✅ **完整日志记录**：便于调试和监控

## 🚀 集成步骤

### 步骤1: 备份现有脚本

```bash
# 备份原始文件
cp find_and_collect.py find_and_collect.py.backup.$(date +%Y%m%d_%H%M%S)
```

### 步骤2: 替换保存函数

#### 方法A: 直接替换函数（推荐）

1. 打开 `find_and_collect.py` 文件
2. 找到现有的 `save_commit_metrics` 函数
3. 将整个函数替换为 `save_commit_metrics_final.py` 中的函数

#### 方法B: 导入新函数

在 `find_and_collect.py` 文件顶部添加：

```python
# 导入新的保存函数
from save_commit_metrics_final import save_commit_metrics as save_commit_metrics_upsert

# 在需要保存数据的地方，将：
# save_commit_metrics(conn, gerrit_data, sonar_data, issues_json, measures_json)
# 替换为：
# save_commit_metrics_upsert(conn, gerrit_data, sonar_data, issues_json, measures_json)
```

### 步骤3: 验证数据库约束

确保数据库中已经添加了唯一约束：

```sql
-- 检查约束是否存在
SELECT constraint_name, constraint_type 
FROM information_schema.table_constraints 
WHERE table_name = 'commit_metrics' 
  AND constraint_type = 'UNIQUE';

-- 如果没有约束，添加约束
ALTER TABLE commit_metrics 
ADD CONSTRAINT uk_commit_metrics_unique 
UNIQUE (gerrit_project, branch, change_id_short);
```

## 🧪 测试验证

### 测试1: 新记录插入

```bash
# 运行一次数据采集
python3 find_and_collect.py

# 检查记录是否正确插入
psql -h 10.0.0.199 -p 5434 -U admin -d mydatabase -c "
SELECT gerrit_project, branch, change_id_short, patch_set, updated_at 
FROM commit_metrics 
ORDER BY updated_at DESC 
LIMIT 5;
"
```

### 测试2: 重复数据覆盖

```bash
# 再次运行相同的数据采集（模拟Jenkins重复触发）
python3 find_and_collect.py

# 检查是否没有产生重复记录
psql -h 10.0.0.199 -p 5434 -U admin -d mydatabase -c "
SELECT 
    gerrit_project, 
    branch, 
    change_id_short, 
    COUNT(*) as record_count,
    MAX(updated_at) as last_updated
FROM commit_metrics 
GROUP BY gerrit_project, branch, change_id_short
HAVING COUNT(*) > 1;
"
```

### 测试3: 数据更新验证

```bash
# 检查数据是否正确更新
psql -h 10.0.0.199 -p 5434 -U admin -d mydatabase -c "
SELECT 
    gerrit_project, 
    change_id_short, 
    patch_set,
    created_at,
    updated_at,
    CASE 
        WHEN created_at = updated_at THEN 'NEW'
        ELSE 'UPDATED'
    END as record_status
FROM commit_metrics 
WHERE updated_at >= CURRENT_DATE
ORDER BY updated_at DESC
LIMIT 10;
"
```

## 📊 监控查询

### 1. 数据质量监控

```sql
-- 每日数据质量报告
SELECT 
    CURRENT_DATE as report_date,
    COUNT(*) as total_records,
    COUNT(DISTINCT gerrit_project || '~' || branch || '~' || change_id_short) as unique_changes,
    COUNT(*) - COUNT(DISTINCT gerrit_project || '~' || branch || '~' || change_id_short) as duplicate_count,
    ROUND(
        (COUNT(DISTINCT gerrit_project || '~' || branch || '~' || change_id_short)::float / COUNT(*) * 100)::numeric, 
        2
    ) as data_quality_score
FROM commit_metrics;
```

### 2. 最近更新记录

```sql
-- 查看最近更新的记录
SELECT 
    gerrit_project,
    branch,
    change_id_short,
    patch_set,
    author,
    subject,
    updated_at
FROM commit_metrics 
WHERE updated_at >= NOW() - INTERVAL '1 hour'
ORDER BY updated_at DESC;
```

### 3. 重复数据检查

```sql
-- 检查是否还有重复数据
SELECT 
    gerrit_project,
    branch,
    change_id_short,
    COUNT(*) as count,
    ARRAY_AGG(patch_set ORDER BY patch_set) as patch_sets
FROM commit_metrics
GROUP BY gerrit_project, branch, change_id_short
HAVING COUNT(*) > 1;
```

## 🔧 配置调整

### 日志级别调整

在 `find_and_collect.py` 中调整日志级别：

```python
import logging

# 设置详细日志
logging.basicConfig(
    level=logging.INFO,  # 或 logging.DEBUG 获取更详细信息
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
```

### URL配置

修改 `save_commit_metrics_final.py` 中的Gerrit URL：

```python
# 第58行左右，修改为您的实际Gerrit服务器地址
url = f"https://your-actual-gerrit-server/c/{number}"
```

## 🚨 注意事项

### 1. 数据一致性

- ✅ 确保数据库约束已正确添加
- ✅ 在生产环境部署前先在测试环境验证
- ✅ 监控日志确保没有错误

### 2. 性能考虑

- ✅ UPSERT操作比先查询再插入/更新更高效
- ✅ 唯一约束会自动创建索引，提高查询性能
- ✅ 建议定期分析表统计信息

### 3. 回滚方案

如果出现问题，可以快速回滚：

```bash
# 恢复原始脚本
cp find_and_collect.py.backup.YYYYMMDD_HHMMSS find_and_collect.py

# 如果需要，移除唯一约束
# ALTER TABLE commit_metrics DROP CONSTRAINT uk_commit_metrics_unique;
```

## 📈 预期效果

部署后您应该看到：

1. **数据去重**：每个Change只有一条记录
2. **性能提升**：减少重复数据，查询更快
3. **日志清晰**：明确显示是插入还是更新操作
4. **数据一致性**：数据库约束确保不会产生新的重复

## 🎉 完成验证

当以下条件都满足时，说明集成成功：

- ✅ 重复数据检查查询返回0条记录
- ✅ 日志显示正确的INSERT/UPDATE操作
- ✅ Jenkins多次触发不会产生重复数据
- ✅ 现有的Grafana仪表板正常显示数据

---

**🚀 按照以上步骤操作后，您的重复数据问题将彻底解决！**

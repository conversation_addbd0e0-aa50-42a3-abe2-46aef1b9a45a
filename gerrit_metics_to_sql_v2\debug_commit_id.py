#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试脚本：验证commit_id生成和数据库插入逻辑
"""

import os
import sys
import json
import logging
import configparser
import subprocess
from datetime import datetime
import pg8000.native

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 读取配置文件
config = configparser.ConfigParser()
config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config.ini')
config.read(config_path)

# 数据库配置
DB_HOST = config.get('Database', 'host')
DB_PORT = config.get('Database', 'port')
DB_NAME = config.get('Database', 'name')
DB_USER = config.get('Database', 'user')
DB_PASS = config.get('Database', 'password')

# Gerrit配置
GERRIT_HOST = config.get('Gerrit', 'host')
GERRIT_PORT = config.get('Gerrit', 'port')
GERRIT_USER = config.get('Gerrit', 'user')

def get_db_connection():
    """获取数据库连接"""
    try:
        conn = pg8000.native.Connection(
            user=DB_USER,
            password=DB_PASS,
            host=DB_HOST,
            port=int(DB_PORT),
            database=DB_NAME
        )
        return conn
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def get_gerrit_change_details(change_id):
    """获取Gerrit变更详情"""
    try:
        cmd = f'ssh -p {GERRIT_PORT} {GERRIT_USER}@{GERRIT_HOST} gerrit query --current-patch-set --all-approvals --files --comments --format=JSON {change_id}'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error(f"获取Gerrit变更详情失败: {result.stderr}")
            return None
        
        lines = result.stdout.strip().split('\n')
        if not lines:
            logger.error("Gerrit查询返回空结果")
            return None
        
        change_details = None
        for line in lines:
            obj = json.loads(line)
            if obj.get("type") == "stats":
                continue
            change_details = obj
        return change_details
    except Exception as e:
        logger.error(f"获取Gerrit变更详情异常: {e}")
        return None

def debug_commit_metrics(change_id):
    """调试commit_metrics数据插入"""
    try:
        # 获取Gerrit变更详情
        gerrit_data = get_gerrit_change_details(change_id)
        if not gerrit_data:
            logger.error(f"无法获取Gerrit变更详情: {change_id}")
            return False
        
        # 提取关键信息
        commit_id = gerrit_data.get("currentPatchSet", {}).get("revision", "")
        branch = gerrit_data.get("branch", "")
        gerrit_project = gerrit_data.get("project", "")
        change_id_short = change_id[:8] if len(change_id) >= 8 else change_id
        
        logger.info(f"=== 调试信息 ===")
        logger.info(f"Change ID: {change_id}")
        logger.info(f"Change ID Short: {change_id_short}")
        logger.info(f"Commit ID: {commit_id}")
        logger.info(f"Branch: {branch}")
        logger.info(f"Gerrit Project: {gerrit_project}")
        
        # 连接数据库
        conn = get_db_connection()
        if not conn:
            logger.error("无法连接到数据库")
            return False
        
        try:
            # 检查是否已存在相同commit_id的记录
            existing_check = conn.run(
                "SELECT commit_id, branch, gerrit_project FROM commit_metrics WHERE commit_id = :commit_id",
                commit_id=commit_id
            )
            
            if len(existing_check) > 0:
                logger.warning(f"已存在相同commit_id的记录:")
                for row in existing_check:
                    logger.warning(f"  - Commit ID: {row[0]}, Branch: {row[1]}, Project: {row[2]}")
            else:
                logger.info("数据库中不存在相同commit_id的记录")
            
            # 检查是否已存在相同(gerrit_project, branch, change_id_short)的记录
            triple_check = conn.run(
                "SELECT commit_id, branch, gerrit_project FROM commit_metrics WHERE gerrit_project = :gerrit_project AND branch = :branch AND change_id_short = :change_id_short",
                gerrit_project=gerrit_project,
                branch=branch,
                change_id_short=change_id_short
            )
            
            if len(triple_check) > 0:
                logger.warning(f"已存在相同(project, branch, change_id_short)的记录:")
                for row in triple_check:
                    logger.warning(f"  - Commit ID: {row[0]}, Branch: {row[1]}, Project: {row[2]}")
            else:
                logger.info("数据库中不存在相同(project, branch, change_id_short)的记录")
            
            # 尝试插入一条测试记录
            logger.info("尝试插入测试记录...")
            
            test_data = {
                'commit_id': commit_id,
                'change_id': change_id,
                'change_id_short': change_id_short,
                'patch_set': 1,
                'gerrit_project': gerrit_project,
                'branch': branch,
                'subject': 'Test Subject',
                'status': 'NEW',
                'commit_time': datetime.now(),
                'created': datetime.now(),
                'updated': datetime.now(),
                'author': 'Test Author',
                'owner': 'Test Owner',
                'number': 0,
                'url': 'http://test.com',
                'reviewers': ['reviewer1'],
                'changed_lines': 0,
                'insertions': 0,
                'deletions': 0,
                'patchset_count': 1,
                'repo_path': gerrit_project,
                'sonar_project': '',
                'sonar_key': '',
                'sonar_creation_date': None,
                'sq_blocker': 0,
                'sq_critical': 0,
                'sq_major': 0,
                'sq_minor': 0,
                'sq_info': 0,
                'sq_resolved_blocker': 0,
                'sq_resolved_critical': 0,
                'sq_resolved_major': 0,
                'sq_resolved_minor': 0,
                'sq_resolved_info': 0,
                'gerrit_raw': json.dumps(gerrit_data),
                'sonar_issues': json.dumps({}),
                'ncloc': None,
                'statements': None,
                'functions': None,
                'files': None,
                'comment_lines': None,
                'comment_lines_density': None,
                'complexity': None,
                'duplicated_lines_density': None,
                'duplicated_lines': None,
                'duplicated_blocks': None,
                'duplicated_files': None,
                'commit_date': datetime.now().date(),
                'commit_year': datetime.now().year,
                'commit_month': datetime.now().month,
                'commit_week': datetime.now().isocalendar()[1],
                'commit_hour': datetime.now().hour,
                'total_issues': 0,
                'total_resolved_issues': 0,
                'critical_issues': 0,
                'issue_density': 0.0,
                'change_size_category': 'small',
                'quality_level': 'clean'
            }
            
            # 执行插入
            try:
                conn.run("""
                    INSERT INTO commit_metrics (
                        commit_id, change_id, change_id_short, patch_set,
                        gerrit_project, branch, subject, status,
                        commit_time, created, updated, author, owner, number, url, reviewers,
                        changed_lines, insertions, deletions, patchset_count, repo_path,
                        sonar_project, sonar_key, sonar_creation_date,
                        sq_blocker, sq_critical, sq_major, sq_minor, sq_info,
                        sq_resolved_blocker, sq_resolved_critical, sq_resolved_major, 
                        sq_resolved_minor, sq_resolved_info,
                        gerrit_raw, sonar_issues,
                        ncloc, statements, functions, files, comment_lines, comment_lines_density, 
                        complexity, duplicated_lines_density, duplicated_lines, duplicated_blocks, duplicated_files,
                        commit_date, commit_year, commit_month, commit_week, commit_hour,
                        total_issues, total_resolved_issues, critical_issues, issue_density, 
                        change_size_category, quality_level
                    ) VALUES (
                        :commit_id, :change_id, :change_id_short, :patch_set,
                        :gerrit_project, :branch, :subject, :status,
                        :commit_time, :created, :updated, :author, :owner, :number, :url, :reviewers,
                        :changed_lines, :insertions, :deletions, :patchset_count, :repo_path,
                        :sonar_project, :sonar_key, :sonar_creation_date,
                        :sq_blocker, :sq_critical, :sq_major, :sq_minor, :sq_info,
                        :sq_resolved_blocker, :sq_resolved_critical, :sq_resolved_major, 
                        :sq_resolved_minor, :sq_resolved_info,
                        :gerrit_raw, :sonar_issues,
                        :ncloc, :statements, :functions, :files, :comment_lines, :comment_lines_density, 
                        :complexity, :duplicated_lines_density, :duplicated_lines, :duplicated_blocks, :duplicated_files,
                        :commit_date, :commit_year, :commit_month, :commit_week, :commit_hour,
                        :total_issues, :total_resolved_issues, :critical_issues, :issue_density, 
                        :change_size_category, :quality_level
                    )
                """, **test_data)
                
                logger.info("✅ 测试记录插入成功！")
                
                # 验证插入
                verify_result = conn.run(
                    "SELECT commit_id, branch, gerrit_project FROM commit_metrics WHERE commit_id = :commit_id",
                    commit_id=commit_id
                )
                
                if len(verify_result) > 0:
                    logger.info("✅ 验证成功：记录确实存在于数据库中")
                    for row in verify_result:
                        logger.info(f"  - Commit ID: {row[0]}, Branch: {row[1]}, Project: {row[2]}")
                else:
                    logger.error("❌ 验证失败：记录未找到")
                
            except Exception as e:
                logger.error(f"❌ 插入测试记录失败: {e}")
                logger.exception("详细异常信息:")
                return False
            
        except Exception as e:
            logger.error(f"数据库操作异常: {e}")
            logger.exception("详细异常信息:")
            return False
            
    except Exception as e:
        logger.error(f"调试过程异常: {e}")
        logger.exception("详细异常信息:")
        return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="调试commit_metrics数据插入")
    parser.add_argument("change_id", help="Gerrit Change ID")
    
    args = parser.parse_args()
    
    logger.info(f"开始调试Change ID: {args.change_id}")
    success = debug_commit_metrics(args.change_id)
    
    if success:
        logger.info("调试完成")
    else:
        logger.error("调试失败")

if __name__ == "__main__":
    main() 
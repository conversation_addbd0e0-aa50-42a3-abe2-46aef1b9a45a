{
{
  "annotations": {
    "list": [
      {
        "builtIn": 1,
        "datasource": {
          "type": "grafana",
          "uid": "-- <PERSON>ana --"
        },
        "enable": true,
        "hide": true,
        "iconColor": "rgba(0, 211, 255, 1)",
        "name": "Annotations & Alerts",
        "type": "dashboard"
      }
    ]
  },
  "editable": true,
  "fiscalYearStartMonth": 0,
  "graphTooltip": 0,
  "id": null,
  "links": [],
  "panels": [
    {
      "datasource": {
        "type": "postgres",
        "uid": "grafana-postgresql-datasource-sonar"
      },
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "unit": "short"
        },
        "overrides": [
          {
            "matcher": {
              "id": "byName",
              "options": "WR02 项目数"
            },
            "properties": [
              {
                "id": "color",
                "value": {
                  "fixedColor": "blue",
                  "mode": "fixed"
                }
              }
            ]
          },
          {
            "matcher": {
              "id": "byName",
              "options": "BG03 项目数"
            },
            "properties": [
              {
                "id": "color",
                "value": {
                  "fixedColor": "green",
                  "mode": "fixed"
                }
              }
            ]
          }
        ]
      },
      "gridPos": {
        "h": 6,
        "w": 24,
        "x": 0,
        "y": 0
      },
      "id": 1,
      "options": {
        "colorMode": "value",
        "graphMode": "area",
        "justifyMode": "auto",
        "orientation": "auto",
        "reduceOptions": {
          "calcs": ["lastNotNull"],
          "fields": "",
          "values": false
        },
        "textMode": "auto"
      },
      "targets": [
        {
          "datasource": {
            "type": "postgres",
            "uid": "grafana-postgresql-datasource-sonar"
          },
          "format": "table",
          "rawQuery": true,
          "rawSql": "SELECT 
  (SELECT COUNT(DISTINCT project_name) FROM sonaree_wr02 WHERE analysis_date >= NOW() - INTERVAL '30 days') as \"WR02 项目数\",
  (SELECT COUNT(DISTINCT project_name) FROM sonaree_bg03 WHERE analysis_date >= NOW() - INTERVAL '30 days') as \"BG03 项目数\",
  (SELECT SUM(bugs) FROM sonaree_wr02 WHERE analysis_date >= NOW() - INTERVAL '30 days') as \"WR02 Bug总数\",
  (SELECT SUM(bugs) FROM sonaree_bg03 WHERE analysis_date >= NOW() - INTERVAL '30 days') as \"BG03 Bug总数\",
  (SELECT ROUND(AVG(coverage), 2) FROM sonaree_wr02 WHERE analysis_date >= NOW() - INTERVAL '30 days') as \"WR02 平均覆盖率%\",
  (SELECT ROUND(AVG(coverage), 2) FROM sonaree_bg03 WHERE analysis_date >= NOW() - INTERVAL '30 days') as \"BG03 平均覆盖率%\",
  (SELECT ROUND(AVG(duplicated_lines_density), 2) FROM sonaree_wr02 WHERE analysis_date >= NOW() - INTERVAL '30 days') as \"WR02 平均重复率%\",
  (SELECT ROUND(AVG(duplicated_lines_density), 2) FROM sonaree_bg03 WHERE analysis_date >= NOW() - INTERVAL '30 days') as \"BG03 平均重复率%\"",
          "refId": "A"
        }
      ],
      "title": "SonarQube 指标对比 (最近30天)",
      "type": "stat"
    },
    {
      "datasource": {
        "type": "postgres",
        "uid": "grafana-postgresql-datasource-sonar"
      },
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "barAlignment": 0,
            "drawStyle": "bar",
            "fillOpacity": 80,
            "gradientMode": "none",
            "hideFrom": {
              "legend": false,
              "tooltip": false,
              "vis": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 2,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "auto",
            "spanNulls": false,
            "stacking": {
              "group": "A",
              "mode": "none"
            },
            "thresholdsStyle": {
              "mode": "off"
            }
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "short"
        },
        "overrides": [
          {
            "matcher": {
              "id": "byName",
              "options": "WR02 Bug数"
            },
            "properties": [
              {
                "id": "color",
                "value": {
                  "fixedColor": "blue",
                  "mode": "fixed"
                }
              }
            ]
          },
          {
            "matcher": {
              "id": "byName",
              "options": "BG03 Bug数"
            },
            "properties": [
              {
                "id": "color",
                "value": {
                  "fixedColor": "green",
                  "mode": "fixed"
                }
              }
            ]
          }
        ]
      },
      "gridPos": {
        "h": 8,
        "w": 12,
        "x": 0,
        "y": 6
      },
      "id": 2,
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "mode": "single",
          "sort": "none"
        }
      },
      "targets": [
        {
          "datasource": {
            "type": "postgres",
            "uid": "grafana-postgresql-datasource-sonar"
          },
          "format": "time_series",
          "rawQuery": true,
          "rawSql": "SELECT 
  analysis_date as time,
  SUM(bugs) as \"WR02 Bug数\" 
FROM sonaree_wr02 
WHERE analysis_date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY analysis_date
ORDER BY analysis_date

UNION ALL

SELECT 
  analysis_date as time,
  SUM(bugs) as \"BG03 Bug数\" 
FROM sonaree_bg03 
WHERE analysis_date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY analysis_date
ORDER BY analysis_date",
          "refId": "A"
        }
      ],
      "title": "每日Bug数量对比",
      "type": "timeseries"
    },
    {
      "datasource": {
        "type": "postgres",
        "uid": "grafana-postgresql-datasource-sonar"
      },
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 10,
            "gradientMode": "none",
            "hideFrom": {
              "legend": false,
              "tooltip": false,
              "vis": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 2,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "auto",
            "spanNulls": false,
            "stacking": {
              "group": "A",
              "mode": "none"
            },
            "thresholdsStyle": {
              "mode": "off"
            }
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "percent"
        },
        "overrides": [
          {
            "matcher": {
              "id": "byName",
              "options": "WR02 覆盖率"
            },
            "properties": [
              {
                "id": "color",
                "value": {
                  "fixedColor": "blue",
                  "mode": "fixed"
                }
              }
            ]
          },
          {
            "matcher": {
              "id": "byName",
              "options": "BG03 覆盖率"
            },
            "properties": [
              {
                "id": "color",
                "value": {
                  "fixedColor": "green",
                  "mode": "fixed"
                }
              }
            ]
          }
        ]
      },
      "gridPos": {
        "h": 8,
        "w": 12,
        "x": 12,
        "y": 6
      },
      "id": 3,
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "mode": "single",
          "sort": "none"
        }
      },
      "targets": [
        {
          "datasource": {
            "type": "postgres",
            "uid": "grafana-postgresql-datasource-sonar"
          },
          "format": "time_series",
          "rawQuery": true,
          "rawSql": "SELECT 
  analysis_date as time,
  ROUND(AVG(coverage), 2) as \"WR02 覆盖率\" 
FROM sonaree_wr02 
WHERE analysis_date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY analysis_date
ORDER BY analysis_date

UNION ALL

SELECT 
  analysis_date as time,
  ROUND(AVG(coverage), 2) as \"BG03 覆盖率\" 
FROM sonaree_bg03 
WHERE analysis_date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY analysis_date
ORDER BY analysis_date",
          "refId": "A"
        }
      ],
      "title": "覆盖率趋势对比",
      "type": "timeseries"
    },
    {
      "datasource": {
        "type": "postgres",
        "uid": "grafana-postgresql-datasource-sonar"
      },
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "thresholds"
          },
          "custom": {
            "align": "auto",
            "displayMode": "auto",
            "inspect": false
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          }
        },
        "overrides": [
          {
            "matcher": {
              "id": "byName",
              "options": "WR02 平均问题密度"
            },
            "properties": [
              {
                "id": "custom.displayMode",
                "value": "color-background"
              },
              {
                "id": "thresholds",
                "value": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "yellow",
                      "value": 5
                    },
                    {
                      "color": "red",
                      "value": 10
                    }
                  ]
                }
              }
            ]
          },
          {
            "matcher": {
              "id": "byName",
              "options": "BG03 平均问题密度"
            },
            "properties": [
              {
                "id": "custom.displayMode",
                "value": "color-background"
              },
              {
                "id": "thresholds",
                "value": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "yellow",
                      "value": 5
                    },
                    {
                      "color": "red",
                      "value": 10
                    }
                  ]
                }
              }
            ]
          }
        ]
      },
      "gridPos": {
        "h": 8,
        "w": 24,
        "x": 0,
        "y": 14
      },
      "id": 4,
      "options": {
        "showHeader": true
      },
      "targets": [
        {
          "datasource": {
            "type": "postgres",
            "uid": "grafana-postgresql-datasource-sonar"
          },
          "format": "table",
          "rawQuery": true,
          "rawSql": "SELECT 
  project_name as \"项目名称\",
  (SELECT ROUND(AVG(issue_density), 2) FROM sonaree_wr02 WHERE project_name = p.project_name AND analysis_date >= NOW() - INTERVAL '30 days') as \"WR02 平均问题密度\",
  (SELECT ROUND(AVG(issue_density), 2) FROM sonaree_bg03 WHERE project_name = p.project_name AND analysis_date >= NOW() - INTERVAL '30 days') as \"BG03 平均问题密度\",
  (SELECT ROUND(AVG(coverage), 2) FROM sonaree_wr02 WHERE project_name = p.project_name AND analysis_date >= NOW() - INTERVAL '30 days') as \"WR02 平均覆盖率%\",
  (SELECT ROUND(AVG(coverage), 2) FROM sonaree_bg03 WHERE project_name = p.project_name AND analysis_date >= NOW() - INTERVAL '30 days') as \"BG03 平均覆盖率%\",
  (SELECT quality_level FROM sonaree_wr02 WHERE project_name = p.project_name AND analysis_date >= NOW() - INTERVAL '30 days' ORDER BY analysis_date DESC LIMIT 1) as \"WR02 质量等级\",
  (SELECT quality_level FROM sonaree_bg03 WHERE project_name = p.project_name AND analysis_date >= NOW() - INTERVAL '30 days' ORDER BY analysis_date DESC LIMIT 1) as \"BG03 质量等级\" 
FROM (
  SELECT DISTINCT project_name FROM sonaree_wr02 WHERE analysis_date >= NOW() - INTERVAL '30 days'
  UNION
  SELECT DISTINCT project_name FROM sonaree_bg03 WHERE analysis_date >= NOW() - INTERVAL '30 days'
) p
ORDER BY p.project_name
LIMIT 20",
          "refId": "A"
        }
      ],
      "title": "项目质量对比详情",
      "type": "table"
    }
  ],
  "refresh": "5m",
  "schemaVersion": 36,
  "style": "dark",
  "tags": ["SonarQube", "对比分析", "质量监控"],
  "templating": {
    "list": []
  },
  "time": {
    "from": "now-30d",
    "to": "now"
  },
  "timepicker": {},
  "timezone": "",
  "title": "SonarQube 质量对比仪表板",
  "uid": "sonarqube-comparison-monitor",
  "version": 1,
  "weekStart": ""
}
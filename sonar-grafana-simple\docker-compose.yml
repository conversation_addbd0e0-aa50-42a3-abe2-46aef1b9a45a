version: '3.7'
services:
  # 使用现有的PostgreSQL数据库配置
  postgres:
    image: postgres:15
    container_name: sonar-grafana-postgres
    environment:
      POSTGRES_DB: sonar_grafana
      POSTGRES_USER: grafana_user
      POSTGRES_PASSWORD: grafana_pass
      TZ: Asia/Shanghai
    ports:
      - "5435:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    networks:
      - sonar-grafana

  # Webhook接收服务
  webhook-service:
    build: ./webhook-service
    container_name: sonar-webhook-receiver
    ports:
      - "8080:8080"
    environment:
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=sonar_grafana
      - DB_USER=grafana_user
      - DB_PASSWORD=grafana_pass
      - TZ=Asia/Shanghai
    depends_on:
      - postgres
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - sonar-grafana

  # Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: sonar-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - TZ=Asia/Shanghai
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
    restart: unless-stopped
    networks:
      - sonar-grafana

volumes:
  postgres_data:
  grafana_data:

networks:
  sonar-grafana:
    driver: bridge

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
从SonarQube API获取项目质量数据
"""

import os
import requests
import logging
import configparser
from datetime import datetime, timedelta
import sqlite3
import base64

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('SonarQube_Data_Fetcher')

class SonarQubeDataFetcher:
    def __init__(self):
        self.config = self._load_config()
        self.session = requests.Session()
        
    def _load_config(self):
        """加载配置文件"""
        script_dir = os.path.dirname(os.path.abspath(__file__))
        config_path = os.path.join(script_dir, "config.ini")
        
        config = configparser.ConfigParser()
        with open(config_path, 'r', encoding='utf-8') as f:
            config.read_file(f)
        return config

    def _encode_token(self, token):
        """将token进行Base64编码"""
        # 检查token是否已经包含sqa_前缀
        if token.startswith('sqa_'):
            # 对于sqa_前缀的token，直接使用
            token_with_colon = f"{token}:"
        else:
            # 对于其他格式的token，保持原有处理方式
            token_with_colon = f"{token}:"
        
        encoded = base64.b64encode(token_with_colon.encode('utf-8')).decode('utf-8')
        logger.debug(f"Token编码: 原始token={token}, 编码后={encoded}")
        return encoded

    def _get_project_config(self):
        """获取项目配置信息"""
        try:
            project_id = self.config['SonarQube']['ProjectID']
            
            # 从SonarQube_Servers部分获取项目特定的配置
            servers_section = 'SonarQube_Servers'
            if servers_section in self.config:
                url_key = f'{project_id}_url'
                token_key = f'{project_id}_token'
                
                if url_key in self.config[servers_section] and token_key in self.config[servers_section]:
                    url = self.config[servers_section][url_key]
                    token = self.config[servers_section][token_key]
                    logger.info(f"找到项目 {project_id} 的配置: URL={url}")
                else:
                    logger.error(f"项目 {project_id} 的配置不完整，缺少URL或Token")
                    return {}
            else:
                logger.error(f"配置文件中缺少 {servers_section} 部分")
                return {}
            
            return {
                project_id: {
                    'url': url,
                    'token': self._encode_token(token),
                    'display_name': project_id
                }
            }
        except Exception as e:
            logger.error(f"获取项目配置时出错: {str(e)}")
            return {}

    def get_metrics_for_date(self, project_id, token, date):
        """获取指定日期的项目指标"""
        headers = {
            'Authorization': f'Basic {token}',
            'Accept': 'application/json'
        }
        
        # 获取项目特定的URL
        servers_section = 'SonarQube_Servers'
        url_key = f'{project_id}_url'
        if servers_section in self.config and url_key in self.config[servers_section]:
            base_url = self.config[servers_section][url_key]
        else:
            logger.error(f"未找到项目 {project_id} 的URL配置")
            return None
        
        logger.info(f"使用URL: {base_url}")
        logger.info(f"项目ID: {project_id}")
        
        # 更新指标列表，只保留支持的指标
        metrics = [
            'bugs',
            'vulnerabilities',
            'code_smells',
            'blocker_violations',
            'critical_violations',
            'major_violations',
            'info_violations',
            'duplicated_lines_density',
            'comment_lines_density',
            'ncloc',
            'comment_lines',
            'duplicated_lines',
            'duplicated_blocks',
            'duplicated_files',
            'complexity',
            'lines'
        ]
        
        try:
            # 使用 search_history API 获取历史数据
            api_url = f"{base_url}/api/measures/search_history"
            params = {
                'component': project_id,
                'metrics': ','.join(metrics),
                'from': date.strftime('%Y-%m-%d'),
                'to': date.strftime('%Y-%m-%d')
            }
            
            logger.info(f"请求URL: {api_url}")
            logger.info(f"请求参数: {params}")
            logger.info(f"完整URL: {api_url}?component={project_id}&metrics={','.join(metrics)}&from={date.strftime('%Y-%m-%d')}&to={date.strftime('%Y-%m-%d')}")
            
            response = requests.get(
                api_url,
                headers=headers,
                params=params,
                verify=False,
                timeout=10
            )
            
            # 添加更详细的错误日志
            if response.status_code != 200:
                logger.error(f"API请求失败: 状态码 {response.status_code}")
                logger.error(f"响应内容: {response.text}")
                logger.error(f"请求URL: {response.url}")
                logger.error(f"请求头: {headers}")
                response.raise_for_status()
                
            data = response.json()
            logger.debug(f"API返回的原始数据: {data}")  # 添加调试日志
            
            # 解析返回的数据
            metrics_data = {}
            if 'measures' in data:
                for measure in data['measures']:
                    metric_key = measure['metric']
                    # 获取历史数据中的值
                    if 'history' in measure and measure['history']:
                        value = float(measure['history'][0]['value']) if 'value' in measure['history'][0] else 0
                    else:
                        value = 0
                    logger.debug(f"处理指标 {metric_key}: {value}")  # 添加调试日志
                    
                    # 将API返回的指标名称映射到数据库中的字段名
                    metric_mapping = {
                        'bugs': 'bugs',
                        'vulnerabilities': 'vulnerabilities',
                        'code_smells': 'code_smells',
                        'blocker_violations': 'blocker_issues',
                        'major_violations': 'major_issues',
                        'info_violations': 'info_issues',
                        'duplicated_lines_density': 'duplications_percentage',
                        'comment_lines_density': 'comment_percentage',
                        'ncloc': 'code_lines',
                        'comment_lines': 'comment_lines',
                        'duplicated_lines': 'duplicated_lines',
                        'duplicated_blocks': 'duplicated_blocks',
                        'duplicated_files': 'duplicated_files',
                        'complexity': 'complexity',
                        'lines': 'total_lines'
                    }
                    
                    if metric_key in metric_mapping:
                        metrics_data[metric_mapping[metric_key]] = value
            
            # 如果没有获取到总行数，计算总行数
            if 'total_lines' not in metrics_data:
                # 使用代码行数和注释行数的和作为总行数
                code_lines = metrics_data.get('code_lines', 0)
                comment_lines = metrics_data.get('comment_lines', 0)
                metrics_data['total_lines'] = code_lines + comment_lines
                logger.info(f"计算得到的总行数: {metrics_data['total_lines']} (代码行: {code_lines}, 注释行: {comment_lines})")
            
            # 确保所有必需的字段都有值，如果没有则设为0
            required_fields = [
                'bugs', 'vulnerabilities', 'code_smells',
                'blocker_issues', 'major_issues', 'info_issues',
                'duplications_percentage', 'comment_percentage',
                'total_lines', 'code_lines', 'comment_lines',
                'duplicated_lines', 'duplicated_blocks', 'duplicated_files',
                'complexity'
            ]
            
            for field in required_fields:
                if field not in metrics_data:
                    logger.warning(f"字段 {field} 未从API获取到数据，设置为默认值0")
                    metrics_data[field] = 0
            
            # 打印调试信息
            logger.info(f"获取到的指标数据: {metrics_data}")
            
            return metrics_data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"获取项目 {project_id} 的指标时发生错误: {str(e)}")
            return None

    def update_database_with_sonar_data(self, days=7):
        """更新数据库中的数据，使用SonarQube API获取的数据
        
        Args:
            days: 要获取最近多少天的数据，默认7天。
                 注意：这个参数现在只影响新数据的获取，不会删除更早的历史数据。
        """
        projects = self._get_project_config()
        if not projects:
            logger.error("未能获取有效的项目配置")
            return
            
        today = datetime.now().date()
        
        # 连接数据库
        script_dir = os.path.dirname(os.path.abspath(__file__))
        db_path = os.path.join(script_dir, "quality_data.db")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建metrics表（如果不存在）
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS metrics (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT,              -- 日期
            project_id TEXT,        -- 项目ID
            bugs INTEGER,           -- Bug数量
            vulnerabilities INTEGER, -- 漏洞数量
            code_smells INTEGER,    -- 代码异味数量
            blocker_issues INTEGER, -- 阻断问题数
            major_issues INTEGER,   -- 主要问题数
            info_issues INTEGER DEFAULT 0,    -- 提示问题数
            duplications_percentage REAL, -- 重复率
            comment_percentage REAL, -- 注释率
            code_lines INTEGER,     -- 代码行数
            comment_lines INTEGER,  -- 注释行数
            duplicated_lines INTEGER, -- 重复行数
            duplicated_blocks INTEGER, -- 重复块数
            duplicated_files INTEGER, -- 重复文件数
            complexity INTEGER,     -- 圈复杂度
            total_lines INTEGER     -- 总行数
        )
        ''')
        
        days_ago = today - timedelta(days=days)
        success_count = 0
        update_count = 0
        
        for project_id, project_info in projects.items():
            logger.info(f"正在获取项目 {project_info['display_name']} 的数据...")
            
            for i in range(days):
                date = today - timedelta(days=i)
                date_str = date.strftime('%Y-%m-%d')
                
                # 检查是否已存在该日期的数据
                cursor.execute("""
                SELECT COUNT(*) FROM metrics 
                WHERE date = ? AND project_id = ?
                """, (date_str, project_id))
                
                exists = cursor.fetchone()[0] > 0
                
                # 获取当天的数据
                metrics = self.get_metrics_for_date(project_id, project_info['token'], date)
                
                if metrics:
                    try:
                        if exists:
                            # 更新已存在的数据
                            cursor.execute("""
                            UPDATE metrics SET 
                                bugs = ?,
                                vulnerabilities = ?,
                                code_smells = ?,
                                blocker_issues = ?,
                                major_issues = ?,
                                info_issues = ?,
                                duplications_percentage = ?,
                                comment_percentage = ?,
                                code_lines = ?,
                                comment_lines = ?,
                                duplicated_lines = ?,
                                duplicated_blocks = ?,
                                duplicated_files = ?,
                                complexity = ?,
                                total_lines = ?
                            WHERE date = ? AND project_id = ?
                            """, (
                                metrics.get('bugs', 0),
                                metrics.get('vulnerabilities', 0),
                                metrics.get('code_smells', 0),
                                metrics.get('blocker_issues', 0),
                                metrics.get('major_issues', 0),
                                metrics.get('info_issues', 0),
                                metrics.get('duplications_percentage', 0),
                                metrics.get('comment_percentage', 0),
                                metrics.get('code_lines', 0),
                                metrics.get('comment_lines', 0),
                                metrics.get('duplicated_lines', 0),
                                metrics.get('duplicated_blocks', 0),
                                metrics.get('duplicated_files', 0),
                                metrics.get('complexity', 0),
                                metrics.get('total_lines', 0),
                                date_str,
                                project_id
                            ))
                            update_count += 1
                            logger.info(f"已更新 {date_str} 的数据")
                        else:
                            # 插入新数据
                            cursor.execute("""
                            INSERT INTO metrics (
                                date, project_id, bugs, vulnerabilities, code_smells,
                                blocker_issues, major_issues, info_issues,
                                duplications_percentage, comment_percentage,
                                code_lines, comment_lines,
                                duplicated_lines, duplicated_blocks, duplicated_files,
                                complexity, total_lines
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            """, (
                                date_str,
                                project_id,
                                metrics.get('bugs', 0),
                                metrics.get('vulnerabilities', 0),
                                metrics.get('code_smells', 0),
                                metrics.get('blocker_issues', 0),
                                metrics.get('major_issues', 0),
                                metrics.get('info_issues', 0),
                                metrics.get('duplications_percentage', 0),
                                metrics.get('comment_percentage', 0),
                                metrics.get('code_lines', 0),
                                metrics.get('comment_lines', 0),
                                metrics.get('duplicated_lines', 0),
                                metrics.get('duplicated_blocks', 0),
                                metrics.get('duplicated_files', 0),
                                metrics.get('complexity', 0),
                                metrics.get('total_lines', 0)
                            ))
                            success_count += 1
                            logger.info(f"已插入 {date_str} 的数据")
                    except sqlite3.Error as e:
                        logger.error(f"处理 {date_str} 的数据时发生错误: {str(e)}")
                        logger.error(f"尝试处理的数据: {metrics}")
        
        # 提交更改
        conn.commit()
        conn.close()
        logger.info(f"数据更新完成！新增 {success_count} 条记录，更新 {update_count} 条记录")

if __name__ == "__main__":
    try:
        fetcher = SonarQubeDataFetcher()
        fetcher.update_database_with_sonar_data()
    except Exception as e:
        logger.error(f"更新数据时发生错误: {str(e)}")
        import traceback
        logger.error(traceback.format_exc()) 
{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  COUNT(DISTINCT gerrit_project) as \"活跃项目数\",\n  COUNT(*) as \"总提交数\",\n  ROUND(AVG(CASE WHEN quality_level = 'clean' THEN 100 ELSE 0 END), 2) as \"平均清洁代码率%\",\n  COUNT(CASE WHEN sq_blocker > 0 OR sq_critical > 0 THEN 1 END) as \"高风险提交数\",\n  ROUND(AVG(issue_density), 2) as \"平均问题密度\",\n  COUNT(CASE WHEN duplicated_lines_density > 5 THEN 1 END) as \"高重复度提交数\"\nFROM commit_metrics \nWHERE commit_time >= NOW() - INTERVAL '30 days'", "refId": "A"}], "title": "项目质量关键指标 (最近30天)", "type": "stat"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "clean"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "critical"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6}, "id": 2, "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "pieType": "pie", "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  quality_level as metric,\n  COUNT(*) as value\nFROM commit_metrics\nWHERE commit_time >= NOW() - INTERVAL '30 days'\nGROUP BY quality_level\nORDER BY \n  CASE quality_level \n    WHEN 'clean' THEN 1 \n    WHEN 'minor' THEN 2 \n    WHEN 'major' THEN 3 \n    WHEN 'critical' THEN 4 \n  END", "refId": "A"}], "title": "代码质量等级分布", "type": "piechart"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "time_series", "rawQuery": true, "rawSql": "SELECT \n  commit_date as time,\n  ROUND(AVG(issue_density), 2) as \"平均问题密度\",\n  ROUND(AVG(duplicated_lines_density), 2) as \"平均重复度\",\n  ROUND(AVG(comment_lines_density), 2) as \"平均注释密度\"\nFROM commit_metrics \nWHERE commit_date >= CURRENT_DATE - INTERVAL '30 days'\nGROUP BY commit_date\nORDER BY commit_date", "refId": "A"}], "title": "每日质量指标趋势", "type": "timeseries"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "清洁代码率%"}, "properties": [{"id": "unit", "value": "percent"}, {"id": "custom.displayMode", "value": "color-background"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 70}, {"color": "green", "value": 90}]}}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 14}, "id": 4, "options": {"showHeader": true}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  gerrit_project as \"项目名称\",\n  COUNT(*) as \"提交数\",\n  COUNT(DISTINCT author) as \"开发者数\",\n  ROUND((COUNT(CASE WHEN quality_level = 'clean' THEN 1 END)::float / COUNT(*) * 100), 2) as \"清洁代码率%\",\n  ROUND(AVG(issue_density), 2) as \"平均问题密度\",\n  ROUND(AVG(duplicated_lines_density), 2) as \"平均重复度%\",\n  COUNT(CASE WHEN sq_blocker > 0 OR sq_critical > 0 THEN 1 END) as \"高风险提交数\",\n  ROUND(AVG(complexity), 0) as \"平均复杂度\"\nFROM commit_metrics \nWHERE commit_time >= NOW() - INTERVAL '30 days'\nGROUP BY gerrit_project\nHAVING COUNT(*) >= 3\nORDER BY \"清洁代码率%\" DESC, \"提交数\" DESC\nLIMIT 20", "refId": "A"}], "title": "项目质量排行榜 (最近30天)", "type": "table"}], "refresh": "5m", "schemaVersion": 36, "style": "dark", "tags": ["项目质量", "监控"], "templating": {"list": []}, "time": {"from": "now-30d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "项目质量监控仪表板", "uid": "project-quality-monitor", "version": 1, "weekStart": ""}
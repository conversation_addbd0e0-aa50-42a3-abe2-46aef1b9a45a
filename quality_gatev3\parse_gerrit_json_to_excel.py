import sys
import json
import os
import argparse
from datetime import datetime
import pandas as pd

def parse_args():
    parser = argparse.ArgumentParser(description='Parse Gerrit changes JSON and export to Excel.')
    parser.add_argument('json_file', help='Path to gerrit_changes_xxx.json')
    parser.add_argument('excel_file', nargs='?', help='Output Excel file path (optional, default: auto)')
    return parser.parse_args()

def timestamp_to_str(ts):
    try:
        return datetime.fromtimestamp(ts).strftime('%Y-%m-%d %H:%M:%S')
    except Exception:
        return ts

def main():
    args = parse_args()
    json_path = args.json_file
    excel_path = args.excel_file
    if not os.path.isfile(json_path):
        print(f'File not found: {json_path}')
        sys.exit(1)
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    if not isinstance(data, list):
        print('JSON root should be a list of changes!')
        sys.exit(1)
    # 自动识别所有字段
    all_keys = set()
    for item in data:
        all_keys.update(item.keys())
    all_keys = sorted(all_keys)
    # 处理时间戳字段
    for item in data:
        for k in ['created', 'updated']:
            if k in item:
                item[k] = timestamp_to_str(item[k])
    # 转为DataFrame
    df = pd.DataFrame(data, columns=all_keys)
    # 自动生成excel文件名
    if not excel_path:
        base = os.path.splitext(os.path.basename(json_path))[0]
        excel_path = base + '.xlsx'
    df.to_excel(excel_path, index=False)
    print(f'导出成功: {excel_path}')

if __name__ == '__main__':
    main() 
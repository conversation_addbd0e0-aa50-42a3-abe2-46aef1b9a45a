{
    "name": "WR02_BOOT",
    "target": "rt-thread",
    "toolchain": "AC6",
    "toolchainLocation": "C:\\Keil_v5\\ARM\\ARMCLANG",
    "toolchainCfgFile": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.20.1\\res\\data\\models\\win32/arm.v6.model.json",
    "buildMode": "fast|multhread",
    "showRepathOnLog": true,
    "threadNum": 16,
    "rootDir": "c:\\Users\\<USER>\\Pwearwr02\\app\\project\\WR02_Boot\\project\\ec-lb555",
    "dumpPath": "build\\rt-thread",
    "outDir": "build\\rt-thread",
    "incDirs": replace_incList,
    "libDirs": replace_libList,
    "defines": replace_defineList,
    "sourceList": [
        replace_sourceList
    ],
    "sourceParams": {},
    "sourceParamsMtime": 1734396498936.3384,
    "options": {
        "version": 3,
        "beforeBuildTasks": [
            {
                "name": "copysign.bat",
                "command": "cd .\\. && copysign.bat",
                "disable": true,
                "abortAfterFailed": true,
                "stopBuildAfterFailed": true
            }
        ],
        "afterBuildTasks": [
            {
                "name": "postbuild.bat !L",
                "command": "cd .\\. && postbuild.bat .\\${OutDirBase}\\${ProjectName}.axf",
                "disable": false,
                "abortAfterFailed": true
            },
            {
                "name": "fromelf --text -c -o \"$<EMAIL>\" \"#L\"",
                "command": "cd .\\. && fromelf --text -c -o \"${OutDir}\\${ProjectName}.lst\" \"${ExecutableName}.axf\"",
                "disable": true,
                "abortAfterFailed": true
            },
            {
                "name": "copy_axf",
                "disable": true,
                "abortAfterFailed": true,
                "command": "copy _build\\WR02_BOOT.axf build\\keil\\Obj\\bootloader.axf"
            }
        ],
        "global": {
            "use-microLIB": true,
            "output-debug-info": "enable",
            "microcontroller-cpu": "cortex-m33-sp",
            "microcontroller-fpu": "cortex-m33-sp",
            "microcontroller-float": "cortex-m33-sp",
            "target": "cortex-m33-sp"
        },
        "c/cpp-compiler": {
            "optimization": "level-image-size",
            "language-c": "c11",
            "language-cpp": "c++11",
            "one-elf-section-per-function": true,
            "short-enums#wchar": true,
            "warnings": "ac5-like-warnings",
            "C_FLAGS": "-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c -fno-rtti -funsigned-char -fshort-enums -fshort-wchar -Wno-builtin-macro-redefined",
            "CXX_FLAGS": "-Wno-builtin-macro-redefined"
        },
        "asm-compiler": {
            "$use": "asm",
            "misc-controls": "--cpreproc --cpreproc_opts=--target=arm-arm-none-eabi --cpreproc_opts=-mcpu=cortex-m33 --cpreproc_opts=-mfpu=fpv5-sp-d16 --cpreproc_opts=-mfloat-abi=hard"
        },
        "linker": {
            "output-format": "elf",
            "misc-controls": "--symdefs @L.sym --cpu=Cortex-M33 --strict --scatter build/link.sct --summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols --info sizes --info totals --info unused --info veneers --any_contingency --list build/bootloader.map --symdefs=build/bootloader.symdefs --library_type=microlib --predefine=-DLB55X_CHIP_ID=2 --predefine=\"-DSIFLI_VERSION=33619975\" --predefine=\"-DSF32LB55X\" --predefine=\"-DUSE_HAL_DRIVER\" --predefine=\"-DLB55X_CHIP_ID=2\" --predefine=\"-DARM_MATH_LOOPUNROLL\" --predefine=\"-DSOC_BF0_HCPU\" --predefine=\"-DUSE_FULL_ASSERT\" --predefine=\"-DSIFLI_BUILD=\"000000\"\" --predefine=\"-DRT_USING_ARM_LIBC\"",
            "ro-base": "0x00000000",
            "rw-base": "0x20000000",
            "link-scatter": [
                "\"c:/Users/<USER>/Pwearwr02/app/project/WR02_Boot/project/ec-lb555/build/link.sct\""
            ]
        }
    },
    "env": {
        "workspaceFolder": "Users\\Administrator\\Pwearwr02\\app\\project\\WR02_Boot\\project\\ec-lb555",
        "workspaceFolderBasename": "ec-lb555",
        "OutDir": "c:\\Users\\<USER>\\Pwearwr02\\app\\project\\WR02_Boot\\project\\ec-lb555\\build\\rt-thread",
        "OutDirRoot": "build",
        "OutDirBase": "build\\rt-thread",
        "ProjectName": "WR02_BOOT",
        "ConfigName": "rt-thread",
        "ProjectRoot": "c:\\Users\\<USER>\\Pwearwr02\\app\\project\\WR02_Boot\\project\\ec-lb555",
        "ExecutableName": "c:\\Users\\<USER>\\Pwearwr02\\app\\project\\WR02_Boot\\project\\ec-lb555\\build\\rt-thread\\WR02_BOOT",
        "ChipPackDir": "",
        "ChipName": "",
        "SYS_Platform": "win32",
        "SYS_DirSep": "\\",
        "SYS_DirSeparator": "\\",
        "SYS_PathSep": ";",
        "SYS_PathSeparator": ";",
        "SYS_EOL": "\r\n",
        "EIDE_BUILDER_DIR": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.20.1\\res\\tools\\win32\\unify_builder",
        "EIDE_BINARIES_VER": "12.0.1",
        "EIDE_MSYS": "C:\\Users\\<USER>\\.eide\\bin\\builder\\msys\\bin",
        "EIDE_TOOL_GCC_ARM": "C:\\Program Files (x86)\\GNU Arm Embedded Toolchain\\10 2021.10\\bin",
        "EIDE_TOOL_JLINK": "C:\\Program Files (x86)\\SEGGER\\JLink",
        "EIDE_TOOL_OPENOCD": ".",
        "ToolchainRoot": "C:\\Keil_v5\\ARM\\ARMCLANG"
    },
    "sysPaths": [],
    "sha": {
        "c/cpp-defines": "3917ba7edc36090050c93bb3f2084ac1",
        "beforeBuildTasks": "05c00b270c9cd6a8f24a583e62b47b70",
        "afterBuildTasks": "78e39dbb118230c50078b62f18bd81b4",
        "global": "3ff48b1dc77de63bee514437f2e3c87c",
        "c/cpp-compiler": "ae393c5c05ccbd6c9e77c9f5fde212a1",
        "asm-compiler": "e91d8e39c37e44ef8be259c068672fd5",
        "linker": "82360a08dcee441a2a77a48d6148ec81"
    }
}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-


"""
生成代码质量月报
包含Excel数据表格和可视化图表
使用方式：
python run_monthly_report.py
python run_monthly_report.py 2024-03
"""

import os
import sqlite3
from datetime import datetime, timedelta
import pandas as pd
import logging
import configparser
from report_visualizer import generate_excel_report

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('Monthly_Report')

def load_project_config():
    """
    加载项目配置
    从config.ini的[SonarQube]部分读取ProjectID
    返回一个字典，其中ProjectID作为键和显示名称
    """
    script_dir = os.path.dirname(os.path.abspath(__file__))
    config_path = os.path.join(script_dir, "config.ini")
    
    config = configparser.ConfigParser()
    # 使用 utf-8 编码读取配置文件
    with open(config_path, 'r', encoding='utf-8') as f:
        config.read_file(f)
    
    if 'SonarQube' not in config:
        raise ValueError("配置文件中缺少 [SonarQube] 部分")
    
    if 'ProjectID' not in config['SonarQube']:
        raise ValueError("配置文件中缺少 ProjectID 配置项")
    
    project_id = config['SonarQube']['ProjectID'].strip()
    # 使用项目ID作为显示名称
    return {project_id: project_id}

def get_monthly_data(project_id, target_month=None):
    """
    获取指定月份的数据
    如果未指定月份，则获取当前月份的数据
    只返回有效数据（非空且非零）的日期
    """
    script_dir = os.path.dirname(os.path.abspath(__file__))
    db_path = os.path.join(script_dir, "quality_data.db")
    
    # 连接数据库
    conn = sqlite3.connect(db_path)
    
    # 确定目标月份
    if target_month:
        try:
            target_date = datetime.strptime(target_month, '%Y-%m')
        except ValueError:
            raise ValueError("月份格式错误，请使用YYYY-MM格式")
    else:
        target_date = datetime.now()
    
    # 计算月份的开始和结束日期
    start_date = target_date.replace(day=1)
    if target_date.month == 12:
        end_date = target_date.replace(year=target_date.year + 1, month=1, day=1) - timedelta(days=1)
    else:
        end_date = target_date.replace(month=target_date.month + 1, day=1) - timedelta(days=1)
    
    # 构建查询
    query = """
    SELECT date, bugs, vulnerabilities, code_smells,
           blocker_issues, major_issues, info_issues,
           duplications_percentage, duplicated_lines,
           duplicated_blocks, duplicated_files,
           code_lines, comment_lines,
           comment_percentage, complexity
    FROM metrics
    WHERE date >= ? AND date <= ?
    AND project_id = ?
    ORDER BY date ASC
    """
    
    # 使用pandas读取数据
    df = pd.read_sql_query(
        query, conn,
        params=(start_date.strftime('%Y-%m-%d'),
                end_date.strftime('%Y-%m-%d'),
                project_id)
    )
    
    if df.empty:
        conn.close()
        return pd.DataFrame()
    
    # 重命名列
    df.columns = [
        '日期', 'Bugs数量', '漏洞数量', '代码异味',
        '阻断问题', '主要问题', '提示问题', '代码重复率', '重复行数',
        '重复块数', '重复文件数', '代码行数', '注释行数',
        '注释率', '圈复杂度'
    ]
    
    # 转换日期字符串为日期对象
    df['日期'] = pd.to_datetime(df['日期'])
    
    # 筛选工作日（周一到周五）
    df['工作日'] = df['日期'].dt.dayofweek < 5  # 0-4 对应周一到周五
    df = df[df['工作日'] == True].drop('工作日', axis=1)
    
    # 检查数据有效性
    # 计算每行数据的有效指标数量（非空且非零）
    numeric_columns = ['Bugs数量', '漏洞数量', '代码异味', '阻断问题', '主要问题', 
                      '提示问题', '代码重复率', '重复行数', '重复块数', '重复文件数', 
                      '代码行数', '注释行数', '注释率', '圈复杂度']
    
    # 将空值替换为0
    df[numeric_columns] = df[numeric_columns].fillna(0)
    
    # 计算每行有效指标的数量（非零指标的数量）
    df['有效指标数'] = (df[numeric_columns] != 0).sum(axis=1)
    
    # 只保留有效指标数大于0的行（至少有一个非零指标）
    df = df[df['有效指标数'] > 0].drop('有效指标数', axis=1)
    
    # 日期重新转回字符串格式
    df['日期'] = df['日期'].dt.strftime('%Y-%m-%d')
    
    conn.close()
    return df

def generate_monthly_report(project_id, target_month=None):
    """生成月报Excel文件和可视化图表（只包含有效数据的工作日）"""
    if not project_id:
        logger.error("未指定项目ID")
        return None
        
    # 获取数据
    df = get_monthly_data(project_id, target_month)
    
    if df.empty:
        logger.error(f"没有找到项目 {project_id} 在指定月份的有效数据！")
        return None
    
    # 如果数据少于2行，无法计算变化
    if len(df) < 2:
        logger.error(f"项目 {project_id} 的有效数据不足，至少需要两天的数据！")
        return None
        
    # 计算一些统计信息
    stats = {
        '指标': [
            'Bugs数量',
            '漏洞数量',
            '代码异味',
            '阻断问题',
            '主要问题',
            '提示问题',
            '代码重复率',
            '重复行数',
            '重复块数',
            '重复文件数',
            '注释率'
        ]
    }
    
    # 获取第一行和最后一行数据
    first_row = df.iloc[0]
    last_row = df.iloc[-1]
    
    # 为每个指标获取月初值和月末值
    stats['月初值'] = [
        first_row['Bugs数量'],
        first_row['漏洞数量'],
        first_row['代码异味'],
        first_row['阻断问题'],
        first_row['主要问题'],
        first_row['提示问题'],
        first_row['代码重复率'],
        first_row['重复行数'],
        first_row['重复块数'],
        first_row['重复文件数'],
        first_row['注释率']
    ]
    
    stats['月末值'] = [
        last_row['Bugs数量'],
        last_row['漏洞数量'],
        last_row['代码异味'],
        last_row['阻断问题'],
        last_row['主要问题'],
        last_row['提示问题'],
        last_row['代码重复率'],
        last_row['重复行数'],
        last_row['重复块数'],
        last_row['重复文件数'],
        last_row['注释率']
    ]
    
    # 计算变化量和变化率
    stats['变化量'] = []
    stats['变化率'] = []
    
    for start, end in zip(stats['月初值'], stats['月末值']):
        # 处理变化量
        if start is None or end is None:
            stats['变化量'].append(0)
            stats['变化率'].append('N/A')
        else:
            try:
                change = end - start
                stats['变化量'].append(change)
                # 处理变化率
                if start == 0 and end == 0:
                    stats['变化率'].append('0.0%')
                elif start != 0:
                    stats['变化率'].append(f"{(change / start * 100):.1f}%")
                else:
                    stats['变化率'].append('N/A')
            except (TypeError, ValueError):
                stats['变化量'].append(0)
                stats['变化率'].append('N/A')
    
    # 创建统计DataFrame
    stats_df = pd.DataFrame(stats)
    
    # 生成报告路径
    script_dir = os.path.dirname(os.path.abspath(__file__))
    date_str = datetime.now().strftime("%Y%m%d")
    month_str = target_month.replace('-', '') if target_month else datetime.now().strftime("%Y%m")
    
    # 使用新的目录命名格式：项目-month
    report_dir = os.path.join(script_dir, "reports", f"{project_id}-month")
    os.makedirs(report_dir, exist_ok=True)
    
    # 修改Excel报告文件名，包含projectid和月份标记
    excel_path = os.path.join(
        report_dir,
        f"monthly_report_{project_id}_{month_str}_{date_str}.xlsx"
    )
    
    # 创建Excel写入器
    with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
        # 添加工作日说明
        days_covered = len(df)
        
        # 写入每日数据
        df.to_excel(writer, sheet_name='每日数据', index=False)
        
        # 写入统计数据
        stats_df.to_excel(writer, sheet_name='月统计', index=False)
        
        # 生成可视化图表和分析文案
        generate_excel_report(df, writer, project_id, report_type='monthly')
        
        # 设置列宽
        for sheet_name in writer.sheets:
            worksheet = writer.sheets[sheet_name]
            for idx, col in enumerate(worksheet.columns, 1):
                worksheet.column_dimensions[chr(64 + idx)].width = 15
        
        # 添加说明：这是有效工作日数据
        daily_sheet = writer.sheets['每日数据']
        daily_sheet.cell(row=1, column=len(df.columns) + 2).value = f"注：此报告仅包含{month_str}月份的有效工作日数据（不含周末和无效数据）"

    logger.info(f"月报已生成: {excel_path}")
    
    return {
        'excel_report': excel_path
    }

def generate_all_projects_reports(target_month=None):
    """生成项目月报"""
    try:
        projects = load_project_config()
        project_id = list(projects.keys())[0]  # 获取唯一的项目ID
        return generate_monthly_report(project_id, target_month)
    except Exception as e:
        logger.error(f"生成月报时发生错误: {str(e)}")
        return None

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        target_month = sys.argv[1]
        generate_all_projects_reports(target_month)
    else:
        generate_all_projects_reports() 
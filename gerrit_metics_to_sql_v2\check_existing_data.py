#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查数据库中是否已存在相同change_id的记录
"""

import os
import sys
import logging
import configparser
import pg8000.native

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 读取配置文件
config = configparser.ConfigParser()
config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config.ini')
config.read(config_path)

# 数据库配置
DB_HOST = config.get('Database', 'host')
DB_PORT = config.get('Database', 'port')
DB_NAME = config.get('Database', 'name')
DB_USER = config.get('Database', 'user')
DB_PASS = config.get('Database', 'password')

def get_db_connection():
    """获取数据库连接"""
    try:
        conn = pg8000.native.Connection(
            user=DB_USER,
            password=DB_PASS,
            host=DB_HOST,
            port=int(DB_PORT),
            database=DB_NAME
        )
        return conn
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def check_existing_data(commit_id):
    """检查数据库中是否已存在相同commit_id的记录"""
    try:
        conn = get_db_connection()
        if not conn:
            logger.error("无法连接到数据库")
            return False
        
        logger.info(f"=== 检查数据库中的记录 ===")
        logger.info(f"Commit ID: {commit_id}")
        
        # 检查是否有相同commit_id的记录
        same_commit_id = conn.run(
            "SELECT commit_id, change_id, branch, gerrit_project, created_at FROM commit_metrics WHERE commit_id = :commit_id",
            commit_id=commit_id
        )
        
        if len(same_commit_id) > 0:
            logger.warning(f"找到 {len(same_commit_id)} 条相同commit_id的记录:")
            for row in same_commit_id:
                logger.warning(f"  - Commit ID: {row[0]}, Change ID: {row[1]}, Branch: {row[2]}, Project: {row[3]}, Created: {row[4]}")
        else:
            logger.info("没有找到相同commit_id的记录")
        
        # 检查所有wr02_release分支的记录
        wr02_release_records = conn.run(
            "SELECT commit_id, change_id, gerrit_project, created_at FROM commit_metrics WHERE branch = 'wr02_release' ORDER BY created_at DESC LIMIT 10"
        )
        
        if len(wr02_release_records) > 0:
            logger.info(f"找到 {len(wr02_release_records)} 条wr02_release分支的记录:")
            for row in wr02_release_records:
                logger.info(f"  - Commit ID: {row[0]}, Change ID: {row[1]}, Project: {row[2]}, Created: {row[3]}")
        else:
            logger.info("没有找到wr02_release分支的记录")
        
        # 检查所有develop分支的记录
        develop_records = conn.run(
            "SELECT commit_id, change_id, gerrit_project, created_at FROM commit_metrics WHERE branch = 'develop' ORDER BY created_at DESC LIMIT 10"
        )
        
        if len(develop_records) > 0:
            logger.info(f"找到 {len(develop_records)} 条develop分支的记录:")
            for row in develop_records:
                logger.info(f"  - Commit ID: {row[0]}, Change ID: {row[1]}, Project: {row[2]}, Created: {row[3]}")
        else:
            logger.info("没有找到develop分支的记录")
        
        # 检查是否有相同change_id的记录（用于理解原始脚本的逻辑）
        # 这里需要先通过commit_id找到对应的change_id
        change_id_records = conn.run(
            "SELECT DISTINCT change_id FROM commit_metrics WHERE commit_id = :commit_id",
            commit_id=commit_id
        )
        
        if len(change_id_records) > 0:
            change_id = change_id_records[0][0]
            logger.info(f"通过commit_id找到的change_id: {change_id}")
            
            # 检查是否有相同change_id的记录
            same_change_id = conn.run(
                "SELECT commit_id, change_id, branch, gerrit_project, created_at FROM commit_metrics WHERE change_id = :change_id",
                change_id=change_id
            )
            
            if len(same_change_id) > 0:
                logger.warning(f"找到 {len(same_change_id)} 条相同change_id的记录:")
                for row in same_change_id:
                    logger.warning(f"  - Commit ID: {row[0]}, Change ID: {row[1]}, Branch: {row[2]}, Project: {row[3]}, Created: {row[4]}")
            else:
                logger.info("没有找到相同change_id的记录")
        
        return True
        
    except Exception as e:
        logger.error(f"检查数据异常: {e}")
        logger.exception("详细异常信息:")
        return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="检查数据库中是否已存在相同commit_id的记录")
    parser.add_argument("commit_id", help="Gerrit Commit ID")
    
    args = parser.parse_args()
    
    logger.info(f"开始检查Commit ID: {args.commit_id}")
    success = check_existing_data(args.commit_id)
    
    if success:
        logger.info("检查完成")
    else:
        logger.error("检查失败")

if __name__ == "__main__":
    main() 
# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

Generated by [`auto-changelog`](https://github.com/CookPete/auto-changelog).

## [v2.6.1](https://github.com/theNewDynamic/gohugo-theme-ananke/compare/v2.6.0...v2.6.1) - 2020-06-25

### Commits

- Updated minimum theme to .55 [`df4c78a`](https://github.com/theNewDynamic/gohugo-theme-ananke/commit/df4c78adb2ed004c3780f7a76254e9756dd024b5)

## [v2.6.0](https://github.com/theNewDynamic/gohugo-theme-ananke/compare/2.6.0...v2.6.0) - 2020-06-23

### Merged

- Update spanish translations [`#304`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/304)
- Add automatic cover image support [`#303`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/303)

## [2.6.0](https://github.com/theNewDynamic/gohugo-theme-ananke/compare/v2.5.5...2.6.0) - 2020-06-17

### Merged

- Add translation for taxonomy page [`#299`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/299)
- Site logo [`#284`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/284)
- Add head partial [`#285`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/285)
-  Long urls or links extend beyond content and overlap sidebar [`#259`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/259)
- Use relative URL for favicon [`#251`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/251)
- Fix relURL for custom_css [`#252`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/252)
- Fixed a typo in form-contact.html [`#266`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/266)
- adding Bulgarian translation [`#267`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/267)
- Use | relLangURL for the base url in the site-navigation [`#277`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/277)
- RSS svg icon [`#282`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/282)
- Updated Windows instructions in README.md [`#276`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/276)
- Replace another 2 .URL occurrences with .Permalink [`#275`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/275)
- Add alternative method for running prod to the readme [`#273`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/273)
- Swap the page title and site title in page &lt;title&gt; elements [`#272`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/272)
- Add the post_content_classes param for changing post content font [`#260`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/260)
- Add sharing links for the posts [`#255`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/255)
- Safari Reader View lacks content [`#254`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/254)
- Add Keybase social icon [`#248`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/248)
- Add StackOverflow social [`#243`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/243)
- Fix to take care of multiple author list, or for setting the [`#221`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/221)
- Fix Slack icon size [`#237`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/237)
- Correct the original translation [`#241`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/241)

## [v2.5.6](https://github.com/theNewDynamic/gohugo-theme-ananke/compare/v2.6.1...v2.5.6) - 2019-12-30

### Merged

- Use Hugo's built in Site Config for copyright according to PR #199 [`#240`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/240)
- Add italian translation [`#239`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/239)

## [v2.5.5](https://github.com/theNewDynamic/gohugo-theme-ananke/compare/2.5.1...v2.5.5) - 2019-11-15

### Merged

- Remove stray grave accent [`#231`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/231)
- Add Slack to social options [`#236`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/236)
- Fix URL for menus [`#230`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/230)
- Fix word count heading typo in README.md [`#222`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/222)
- Add auto-changelog [`#228`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/228)
- Fix stackbit issues [`#226`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/226)
- Add Stackbit Configuration [`#223`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/223)
- Replace {{ .URL }} with {{ .Permalink }} [`#216`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/216)
- Adds an author to blog posts. [`#209`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/209)
- Fixes #212. [`#213`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/213)
- Add ukrainian translation [`#214`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/214)
- Add swedish translation [`#208`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/208)
- Deprecation messages fixes. [`#196`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/196)
- Fix README instructions [`#204`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/204)
- Use git submodules [`#183`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/183)
- Remove Google News meta tags [`#197`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/197)

### Fixed

- Fix URL for menus (#230) [`#229`](https://github.com/theNewDynamic/gohugo-theme-ananke/issues/229)
- Add auto-changelog (#228) [`#227`](https://github.com/theNewDynamic/gohugo-theme-ananke/issues/227) [`#227`](https://github.com/theNewDynamic/gohugo-theme-ananke/issues/227)
- Fix stackbit issues (#226) [`#224`](https://github.com/theNewDynamic/gohugo-theme-ananke/issues/224)
- Add Stackbit Configuration (#223) [`#200`](https://github.com/theNewDynamic/gohugo-theme-ananke/issues/200)
- Fixes #212. (#213) [`#212`](https://github.com/theNewDynamic/gohugo-theme-ananke/issues/212)
- Deprecation messages fixes. (#196) [`#180`](https://github.com/theNewDynamic/gohugo-theme-ananke/issues/180)

## 2.5.1 - 2019-08-12

### Merged

- remove deprecated meta tags for old Windows Mobile and BlackBerry [`#191`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/191)
- localization for form-contact shortcode [`#185`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/185)
- Fix min_version [`#189`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/189)
- Add portuguese translation [`#179`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/179)
- Add commento [`#178`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/178)
- feat: add RU translation [`#177`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/177)
- Spanish Translation [`#175`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/175)
- Dutch translations. [`#171`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/171)
- Correcting issue with cached i18n menu [`#174`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/174)
- Create zh.toml [`#170`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/170)
- Fix TOC header [`#168`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/168)
- Optimisation "partialCached" [`#165`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/165)
- Add a link to "mastodon" [`#159`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/159)
- Create fr.toml [`#157`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/157)
- add i18n translation support [`#156`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/156)
- Support hiding the featured image header text. [`#155`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/155)
- enable localization/modification of "Recent" string [`#154`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/154)
- add basic support for post translations [`#144`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/144)
- Keep article padding throughout widths [`#152`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/152)
- Improve semantic structure of pages [`#151`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/151)
- Improve social link accessibility [`#147`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/147)
- Add explicit path to image example [`#146`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/146)
- Open social media links in new tab and add Medium icon [`#143`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/143)
- Make cover dimming class customisable. [`#140`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/140)
- Removed hardcoded theme sample hero image. This will allow the user to "blank" out the hero default set in the config. The if statement for blank was unreachable. [`#133`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/133)
- Use relative url function for custom CSS files [`#132`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/132)
- Add Gitlab to social icons [`#131`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/131)
- Add div to wrap social icons [`#128`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/128)
- Fix asset paths when baseURL has sub-folder [`#103`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/103)
- Add inheritance for social links. [`#107`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/107)
- Issue 98 [`#101`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/101)
- Replace Asset References with a data file instead of paths [`#96`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/96)
- Pre-2.0 Enhancements [`#94`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/94)
- Don't duplicate site title in home page TITLE tag [`#78`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/78)
- Fix pagination [`#76`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/76)
- #68|Parmeterize number of recent posts in index.html [`#69`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/69)
- Fix typo in single.html [`#67`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/67)
- Fixed line breaks in code (resolves budparr/gohugo-theme-ananke#56). [`#57`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/57)
- Favicons [`#54`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/54)
- indent fix [`#45`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/45)
- Social icon updates [`#51`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/51)
- Add GitHub social icon [`#48`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/48)
- Make Hero image work out-of-the box [`#40`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/40)
- Removed excess o in Facebook [`#34`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/34)
- Fixes #31 [`#32`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/32)
- Bp/fix now function Fixes #29 [`#30`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/30)
- fix clunky construction on home page to get section name [`#25`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/25)
- fix clunky construction on home page to get section name [`#24`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/24)
- fix clunky construction on home page to get section name [`#17`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/17)
- tweak hero default behavior [`#16`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/16)
- improve terms template [`#15`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/15)
- improve image handling for edge cases Fixes #11 [`#14`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/14)
- Improve featured image handling Ref #11 + minor homepage impvs [`#12`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/12)
- Dev changes [`#10`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/10)
- pull in dev changes [`#9`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/9)
- keeping things in order [`#8`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/8)
- Improve home page posts  [`#7`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/7)
- make form email comment make more sense. Ref #5 [`#6`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/6)
- use a cleaner way to include language code [`#3`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/3)
- update from DEV [`#2`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/2)
- add taxonomy templates [`#1`](https://github.com/theNewDynamic/gohugo-theme-ananke/pull/1)

### Fixed

- Add blockquote styling [`#169`](https://github.com/theNewDynamic/gohugo-theme-ananke/issues/169)
- Keep article padding throughout widths (#152) [`#130`](https://github.com/theNewDynamic/gohugo-theme-ananke/issues/130)
- Update readme for formspree change [`#150`](https://github.com/theNewDynamic/gohugo-theme-ananke/issues/150)
- Improve semantic structure of pages (#151) [`#149`](https://github.com/theNewDynamic/gohugo-theme-ananke/issues/149)
- Add global background color class to footer [`#135`](https://github.com/theNewDynamic/gohugo-theme-ananke/issues/135)
- Add div to wrap social icons (#128) [`#127`](https://github.com/theNewDynamic/gohugo-theme-ananke/issues/127)
- Fix article padding on mobile [`#115`](https://github.com/theNewDynamic/gohugo-theme-ananke/issues/115)
- Make asset paths absolute [`#97`](https://github.com/theNewDynamic/gohugo-theme-ananke/issues/97)
- Fix linkedin icon to match the other social icons [`#70`](https://github.com/theNewDynamic/gohugo-theme-ananke/issues/70)
- Be smarter about linking to posts on home page. [`#50`](https://github.com/theNewDynamic/gohugo-theme-ananke/issues/50)
- Add body_classes parameter to body [`#43`](https://github.com/theNewDynamic/gohugo-theme-ananke/issues/43)
- Fixes #31 (#32) [`#31`](https://github.com/theNewDynamic/gohugo-theme-ananke/issues/31)
- Bp/fix now function Fixes #29 (#30) [`#29`](https://github.com/theNewDynamic/gohugo-theme-ananke/issues/29)
- Merge pull request #14 from budparr/dev [`#11`](https://github.com/theNewDynamic/gohugo-theme-ananke/issues/11)
- improve image handling for edge cases Fixes #11 [`#11`](https://github.com/theNewDynamic/gohugo-theme-ananke/issues/11)

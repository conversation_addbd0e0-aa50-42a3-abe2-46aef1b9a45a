#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
生成过去一个月的质量数据
从SonarQube API获取数据
"""

import os
import logging
from sonarqube_data_fetcher import SonarQubeDataFetcher
import sqlite3

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('Generate_Weekly_Data')

def ensure_metrics_table(db_path):
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    try:
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='metrics'")
        if not cursor.fetchone():
            cursor.execute('''
            CREATE TABLE metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date TEXT,
                project_id TEXT,
                bugs INTEGER,
                vulnerabilities INTEGER,
                code_smells INTEGER,
                blocker_issues INTEGER,
                major_issues INTEGER,
                info_issues INTEGER DEFAULT 0,
                duplications_percentage REAL,
                comment_percentage REAL,
                code_lines INTEGER,
                comment_lines INTEGER,
                duplicated_lines INTEGER,
                duplicated_blocks INTEGER,
                duplicated_files INTEGER,
                complexity INTEGER,
                total_lines INTEGER
            )
            ''')
            print("metrics表已创建")
        # 字段升级逻辑可参考sonarqube_reporter.py
        conn.commit()
    finally:
        conn.close()

def generate_weekly_data():
    """从SonarQube获取并生成过去一个月的数据"""
    try:
        fetcher = SonarQubeDataFetcher()
        fetcher.update_database_with_sonar_data(days=30)
        logger.info("数据生成完成！")
    except Exception as e:
        logger.error(f"生成数据时发生错误: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    # 在主逻辑前调用
    script_dir = os.path.dirname(os.path.abspath(__file__))
    db_path = os.path.join(script_dir, "quality_data.db")
    ensure_metrics_table(db_path)
    generate_weekly_data() 
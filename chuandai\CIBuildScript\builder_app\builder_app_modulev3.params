{
    "name": "WR02_App",
    "target": "WR02_APP",
    "toolchain": "AC6",
    "toolchainLocation": "C:\\Keil_v5\\ARM\\ARMCLANG",
    "toolchainCfgFile": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.20.1\\res\\data\\models\\win32/arm.v6.model.json",
    "buildMode": "fast|multhread",
    "showRepathOnLog": true,
    "threadNum": 32,
    "rootDir": "c:\\cygwin64\\workspace\\wearable-wr02-build\\app\\project\\WR02_App",
    "dumpPath": "build\\WR02_APP",
    "outDir": "build\\WR02_APP",
    "incDirs": replace_incList,
    "libDirs": replace_libList,
    "defines": replace_defineList,
    "sourceList": [
        replace_sourceList
    ],
    "sourceParams": {},
    "sourceParamsMtime": 1734396498814.3323,
    "options": {
        "version": 3,
        "beforeBuildTasks": [
            {
                "name": "copy.bat",
                "command": "cd .\\. && copy.bat",
                "disable": true,
                "abortAfterFailed": true,
                "stopBuildAfterFailed": true
            },
            {
                "name": "lcpu_build",
                "disable": false,
                "abortAfterFailed": false,
                "stopBuildAfterFailed": true,
                "command": "echo \"project name: ${ProjectName}\""
            }
        ],
        "afterBuildTasks": [
            {
                "name": "postbuild.bat !L",
                "command": "cd .\\. && postbuild.bat .\\${OutDirBase}\\${ProjectName}.axf",
                "disable": false,
                "abortAfterFailed": true
            },
            {
                "name": "loadm0.bat",
                "command": "cd .\\. && loadm0.bat",
                "disable": true,
                "abortAfterFailed": true
            },
            {
                "name": "make_elf",
                "disable": false,
                "abortAfterFailed": false,
                "command": "fromelf --elf --output=.\\${OutDirBase}\\${ProjectName}.elf .\\${OutDirBase}\\${ProjectName}.axf"
            }
        ],
        "global": {
            "output-debug-info": "enable",
            "microcontroller-cpu": "cortex-m33-sp",
            "microcontroller-fpu": "cortex-m33-sp",
            "microcontroller-float": "cortex-m33-sp",
            "target": "cortex-m33-sp"
        },
        "c/cpp-compiler": replace_cxxcompiler,
        "asm-compiler": {
            "$use": "asm",
            "misc-controls": "--cpreproc --cpreproc_opts=--target=arm-arm-none-eabi --cpreproc_opts=-mcpu=cortex-m33 --cpreproc_opts=-mfpu=fpv5-sp-d16 --cpreproc_opts=-mfloat-abi=hard"
        },
        "linker": {
            "output-format": "elf",
            "misc-controls": "--cpu=Cortex-M33 --strict --scatter ./linker_scripts/link_flash.sct --summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols --info sizes --info totals --info unused --info veneers --any_contingency --list build/bf0_ap.map --predefine=-DLB55X_CHIP_ID=3 --predefine=\"-DSOC_BF0_HCPU\" --predefine=\"-DSF32LB55X\" --predefine=\"-DUSE_HAL_DRIVER\" --predefine=\"-DLB55X_CHIP_ID=3\" --predefine=\"-DARM_MATH_LOOPUNROLL\" --predefine=\"-DSIFLI_VERSION=33554439\" --predefine=\"-DSIFLI_BUILD=\"000000\"\" --predefine=\"-DLV_CONF_KCONFIG_EXTERNAL_INCLUDE=<lv_conf_sifli.h>\" --predefine=\"-DRT_USING_ARM_LIBC\"",
            "ro-base": "0x00000000",
            "rw-base": "0x20000000",
            "link-scatter": [
                "\"c:/cygwin64/workspace/wearable-wr02-build/app/project/WR02_App/linker_scripts/link_flash.sct\""
            ]
        }
    },
    "env": {
        "workspaceFolder": "c:\\cygwin64\\workspace\\wearable-wr02-build\\app\\project\\WR02_App",
        "workspaceFolderBasename": "WR02_App",
        "OutDir": "c:\\cygwin64\\workspace\\wearable-wr02-build\\app\\project\\WR02_App\\build\\WR02_APP",
        "OutDirRoot": "build",
        "OutDirBase": "build\\WR02_APP",
        "ProjectName": "WR02_App",
        "ConfigName": "WR02_APP",
        "ProjectRoot": "c:\\cygwin64\\workspace\\wearable-wr02-build\\app\\project\\WR02_App",
        "ExecutableName": "c:\\cygwin64\\workspace\\wearable-wr02-build\\app\\project\\WR02_App\\build\\WR02_APP\\WR02_App",
        "ChipPackDir": "",
        "ChipName": "",
        "SYS_Platform": "win32",
        "SYS_DirSep": "\\",
        "SYS_DirSeparator": "\\",
        "SYS_PathSep": ";",
        "SYS_PathSeparator": ";",
        "SYS_EOL": "\r\n",
        "EIDE_BUILDER_DIR": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.20.1\\res\\tools\\win32\\unify_builder",
        "EIDE_BINARIES_VER": "12.0.1",
        "EIDE_MSYS": "C:\\Users\\<USER>\\.eide\\bin\\builder\\msys\\bin",
        "EIDE_TOOL_GCC_ARM": "C:\\Program Files (x86)\\GNU Arm Embedded Toolchain\\10 2021.10\\bin",
        "EIDE_TOOL_JLINK": "C:\\Program Files (x86)\\SEGGER\\JLink",
        "EIDE_TOOL_OPENOCD": ".",
        "ToolchainRoot": "C:\\Keil_v5\\ARM\\ARMCLANG"
    },
    "sysPaths": [],
    "sha": {
        "c/cpp-defines": "f2dcd6b2d6b7508e692fe840b5b79f31",
        "beforeBuildTasks": "5df8bf767c06de401b3fabbb7a1d7d80",
        "afterBuildTasks": "ae2f75938d5204ad709f1517b3912da1",
        "global": "63eadea4a733071a884dbbd0fd4cbda3",
        "c/cpp-compiler": "2bf45e4ad672f770100ab487578a1d91",
        "asm-compiler": "e91d8e39c37e44ef8be259c068672fd5",
        "linker": "33f881dfd8c5551bb0c5330657a792f0"
    }
}
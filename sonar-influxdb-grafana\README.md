# SonarQube InfluxDB Grafana 集成方案

## 🎯 方案概述

这是一个基于InfluxDB时序数据库的SonarQube到Grafana集成方案，专为时序数据优化，提供高性能的数据存储和查询能力。

### 架构流程
```
SonarQube → Webhook → InfluxDB → Grafana
```

### 🌟 方案优势

- **高性能**: InfluxDB专为时序数据设计，查询速度快
- **简单查询**: 使用Flux查询语言，语法简洁
- **自动压缩**: 时序数据自动压缩，节省存储空间
- **丰富可视化**: Grafana原生支持InfluxDB，图表类型丰富
- **易于扩展**: 支持集群部署，可处理大量数据

## 🚀 快速开始

### 1. 启动服务

```bash
cd sonar-influxdb-grafana
chmod +x start.sh
./start.sh
```

### 2. 验证服务状态

```bash
# 检查所有服务
docker-compose ps

# 健康检查
curl http://localhost:8080/health

# 查看统计信息
curl http://localhost:8080/stats
```

### 3. 配置SonarQube Webhooks

```bash
# 安装Python依赖
pip install requests

# 运行webhook配置脚本
python setup_webhooks.py
```

### 4. 发送测试数据

```bash
# 发送测试数据
python test_webhook.py

# 发送历史数据（用于趋势图测试）
python test_webhook.py --historical
```

### 5. 访问服务

- **Grafana**: http://localhost:3000 (admin/admin123)
- **InfluxDB**: http://localhost:8086 (admin/admin123456)
- **Webhook服务**: http://localhost:8080

## 📊 数据模型

### InfluxDB Measurements

#### 1. sonar_quality_gate
质量门主要状态信息
```
Tags: project_key, project_name, task_id, analysis_status, quality_gate_status
Fields: status_value (1=OK, 0=ERROR, -1=UNKNOWN), analysis_count
```

#### 2. sonar_condition
质量门条件详情
```
Tags: project_key, project_name, metric_key, operator, condition_status
Fields: actual_value, threshold_value, condition_status_value
```

#### 3. sonar_metric
指标历史数据
```
Tags: project_key, project_name, metric_key
Fields: value
```

#### 4. sonar_analysis
分析事件记录
```
Tags: project_key, project_name, status
Fields: duration, count
```

## 📈 Grafana查询示例

### 质量门通过率
```flux
from(bucket: "sonar_metrics")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r["_measurement"] == "sonar_quality_gate")
  |> filter(fn: (r) => r["_field"] == "status_value")
  |> group(columns: ["project_key"])
  |> mean()
```

### 代码覆盖率趋势
```flux
from(bucket: "sonar_metrics")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r["_measurement"] == "sonar_metric")
  |> filter(fn: (r) => r["metric_key"] == "coverage")
  |> filter(fn: (r) => r["_field"] == "value")
  |> group(columns: ["project_key"])
```

### 分析频率统计
```flux
from(bucket: "sonar_metrics")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r["_measurement"] == "sonar_analysis")
  |> filter(fn: (r) => r["_field"] == "count")
  |> aggregateWindow(every: 1h, fn: sum, createEmpty: false)
```

### Bug数量趋势
```flux
from(bucket: "sonar_metrics")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r["_measurement"] == "sonar_metric")
  |> filter(fn: (r) => r["metric_key"] == "bugs")
  |> filter(fn: (r) => r["_field"] == "value")
  |> group(columns: ["project_key"])
```

## 🔧 配置说明

### InfluxDB配置

默认配置：
- **组织**: sonarqube
- **存储桶**: sonar_metrics
- **Token**: sonar-admin-token-12345678901234567890
- **保留期**: 默认无限制

### Webhook服务配置

环境变量：
```bash
INFLUXDB_URL=http://influxdb:8086
INFLUXDB_TOKEN=sonar-admin-token-12345678901234567890
INFLUXDB_ORG=sonarqube
INFLUXDB_BUCKET=sonar_metrics
```

### SonarQube Webhook配置

在SonarQube中配置webhook：
- **URL**: `http://your-server-ip:8080/webhook/sonarqube`
- **事件**: 所有事件或仅质量门事件

## 🧪 测试验证

### 手动发送测试数据

```bash
curl -X POST http://localhost:8080/webhook/sonarqube \
  -H "Content-Type: application/json" \
  -d '{
    "taskId": "test-123",
    "status": "SUCCESS",
    "analysedAt": "2025-01-04T10:00:00+0000",
    "project": {
      "key": "test-project",
      "name": "Test Project"
    },
    "qualityGate": {
      "status": "OK",
      "conditions": [
        {
          "metricKey": "coverage",
          "operator": "LT",
          "status": "OK",
          "actualValue": "85.5",
          "threshold": "80.0"
        }
      ]
    }
  }'
```

### 查询InfluxDB数据

```bash
# 进入InfluxDB容器
docker exec -it sonar-influxdb influx

# 使用Flux查询
> from(bucket: "sonar_metrics") |> range(start: -1h) |> limit(n: 10)
```

## 🛠️ 故障排除

### 常见问题

1. **InfluxDB启动失败**
   ```bash
   # 检查日志
   docker logs sonar-influxdb
   
   # 重新初始化
   docker-compose down -v
   docker-compose up -d
   ```

2. **Webhook接收失败**
   ```bash
   # 检查服务状态
   curl http://localhost:8080/health
   
   # 查看日志
   docker logs sonar-webhook-influx
   ```

3. **Grafana连接InfluxDB失败**
   - 检查数据源配置中的Token是否正确
   - 确认InfluxDB服务正常运行
   - 验证组织和存储桶名称

### 日志查看

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker logs sonar-influxdb
docker logs sonar-webhook-influx
docker logs sonar-grafana-influx
```

## 📊 性能优化

### InfluxDB优化

1. **数据保留策略**
   ```bash
   # 设置30天保留期
   influx bucket update --name sonar_metrics --retention 720h
   ```

2. **压缩配置**
   - InfluxDB自动压缩旧数据
   - 可配置压缩级别和频率

3. **查询优化**
   - 使用适当的时间范围
   - 添加必要的过滤条件
   - 使用聚合函数减少数据量

### Grafana优化

1. **查询缓存**
   - 设置合适的刷新间隔
   - 使用查询缓存减少数据库负载

2. **面板优化**
   - 限制数据点数量
   - 使用适当的聚合窗口

## 🔒 安全建议

1. **修改默认密码**
   ```bash
   # 修改InfluxDB密码
   # 修改Grafana密码
   ```

2. **网络安全**
   - 使用防火墙限制访问
   - 配置HTTPS传输
   - 使用强密码和Token

3. **数据备份**
   ```bash
   # 备份InfluxDB数据
   docker exec sonar-influxdb influx backup /backup
   ```

## 🚀 扩展功能

### 添加告警

在Grafana中配置告警规则：
1. 质量门失败告警
2. 代码覆盖率下降告警
3. Bug数量增加告警

### 集成其他工具

- **Prometheus**: 导出指标到Prometheus
- **Slack/Teams**: 配置通知集成
- **Jenkins**: 集成CI/CD流水线

### 自定义仪表板

创建专门的仪表板：
- 项目概览仪表板
- 团队绩效仪表板
- 趋势分析仪表板

## 📞 支持

如有问题，请检查：
1. 服务日志
2. 网络连接
3. 配置文件
4. 防火墙设置

---

🎉 **恭喜！您已成功部署SonarQube InfluxDB Grafana集成方案！**

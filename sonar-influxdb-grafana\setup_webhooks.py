#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def setup_sonarqube_webhook(sonar_url, token, webhook_url, project_key=None):
    """为SonarQube设置webhook"""
    
    session = requests.Session()
    session.auth = (token, '')
    
    # 如果指定了项目，为单个项目创建webhook
    if project_key:
        webhook_name = f"InfluxDB Webhook - {project_key}"
        data = {
            'project': project_key,
            'name': webhook_name,
            'url': webhook_url
        }
    else:
        # 创建全局webhook
        webhook_name = "InfluxDB Global Webhook"
        data = {
            'name': webhook_name,
            'url': webhook_url
        }
    
    try:
        url = f"{sonar_url}/api/webhooks/create"
        response = session.post(url, data=data)
        
        if response.status_code == 200:
            webhook_data = response.json()
            print(f"✅ 成功创建webhook: {webhook_name}")
            print(f"   URL: {webhook_data['webhook']['url']}")
            return True
        else:
            print(f"❌ 创建webhook失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 创建webhook时出错: {e}")
        return False

def list_existing_webhooks(sonar_url, token):
    """列出现有的webhooks"""
    session = requests.Session()
    session.auth = (token, '')
    
    try:
        url = f"{sonar_url}/api/webhooks/list"
        response = session.get(url)
        
        if response.status_code == 200:
            webhooks_data = response.json()
            webhooks = webhooks_data.get('webhooks', [])
            
            if webhooks:
                print(f"📋 现有webhooks ({len(webhooks)}个):")
                for webhook in webhooks:
                    print(f"   - {webhook['name']}: {webhook['url']}")
            else:
                print("📋 暂无webhooks")
            return webhooks
        else:
            print(f"❌ 获取webhooks失败: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ 获取webhooks时出错: {e}")
        return []

def main():
    print("🚀 SonarQube InfluxDB Webhook 配置工具")
    print("=" * 50)
    
    # 配置您的SonarQube服务器
    sonar_configs = [
        {
            'name': 'SonarQube Community (端口9090)',
            'url': 'http://********:9000',
            'token': '0da72b170a333b2aa5d1a8e48cc8ba14ce3cb1fd'
        },
        {
            'name': 'SonarQube Enterprise (端口9001)', 
            'url': 'http://*********:9001',
            'token': 'squ_a60fc304111367163786cc06084df9b8d96e5ff1'
        }
    ]
    
    # Webhook接收地址（请根据实际情况修改IP地址）
    webhook_url = "http://*********:8080/webhook/sonarqube"
    
    print(f"📡 Webhook URL: {webhook_url}\n")
    
    for i, config in enumerate(sonar_configs, 1):
        print(f"{i}. 配置 {config['name']}")
        print(f"   URL: {config['url']}")
        
        # 列出现有webhooks
        list_existing_webhooks(config['url'], config['token'])
        
        # 创建新的webhook
        print("   创建新webhook...")
        success = setup_sonarqube_webhook(
            config['url'], 
            config['token'], 
            webhook_url
        )
        
        if success:
            print(f"   ✅ {config['name']} webhook配置完成")
        else:
            print(f"   ❌ {config['name']} webhook配置失败")
        
        print("-" * 50)
    
    print("\n🎉 Webhook配置完成!")
    print("\n📝 下一步操作:")
    print("1. 在SonarQube中运行项目分析")
    print("2. 检查webhook服务日志: docker logs sonar-webhook-influx")
    print("3. 访问Grafana查看数据: http://localhost:3000")
    print("4. 检查InfluxDB数据: http://localhost:8086")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
用于替换现有 find_and_collect.py 中 save_commit_metrics 函数的改进版本
"""

def save_commit_metrics_improved(conn, gerrit_data, sonar_data, issues_json, measures_json):
    """
    改进版的数据保存函数 - 解决重复数据问题
    
    主要改进：
    1. 基于 (gerrit_project, branch, change_id_short) 判断唯一性
    2. 使用 UPSERT 操作，只有更新的patch set才会更新记录
    3. 添加详细的日志记录
    """
    import json
    import logging
    from datetime import datetime
    
    logger = logging.getLogger(__name__)
    
    try:
        # 提取基本信息
        change_id = gerrit_data.get('id', '')
        change_id_short = change_id.split('~')[-1] if '~' in change_id else change_id
        gerrit_project = gerrit_data.get('project', '')
        branch = gerrit_data.get('branch', '')
        
        # 获取patch set信息
        revisions = gerrit_data.get('revisions', {})
        if revisions:
            # 获取最新的revision
            latest_revision = list(revisions.keys())[-1]
            revision_data = revisions[latest_revision]
            patch_set = revision_data.get('_number', 1)
            commit_id = latest_revision
        else:
            patch_set = 1
            commit_id = change_id_short
        
        # 构建唯一标识用于日志
        unique_key = f"{gerrit_project}~{branch}~{change_id_short}"
        
        logger.info(f"处理变更: {unique_key}, Patch Set: {patch_set}")
        
        # 检查是否已存在记录
        check_result = conn.run("""
            SELECT commit_id, patch_set, updated_at 
            FROM commit_metrics 
            WHERE gerrit_project = :gerrit_project 
              AND branch = :branch 
              AND change_id_short = :change_id_short
        """, 
        gerrit_project=gerrit_project,
        branch=branch,
        change_id_short=change_id_short
        )
        
        record_exists = len(check_result) > 0
        should_update = True
        
        if record_exists:
            existing_record = check_result[0]
            existing_patch_set = existing_record[1]
            
            logger.info(f"找到已存在记录: Patch Set {existing_patch_set} -> {patch_set}")
            
            # 只有当patch set更新时才更新记录
            if patch_set < existing_patch_set:
                logger.info(f"跳过旧版本: {unique_key} (当前: {patch_set}, 已存在: {existing_patch_set})")
                return True
            elif patch_set == existing_patch_set:
                logger.info(f"相同版本，检查更新时间: {unique_key}")
                # 可以选择是否更新相同patch set的记录
                # should_update = True  # 总是更新
        
        # 提取其他数据（保持原有逻辑）
        subject = gerrit_data.get('subject', '')
        status = gerrit_data.get('status', '')
        
        # 时间处理
        commit_time_str = gerrit_data.get('submitted') or gerrit_data.get('updated') or gerrit_data.get('created')
        if commit_time_str:
            commit_time = datetime.fromisoformat(commit_time_str.replace('Z', '+00:00'))
        else:
            commit_time = datetime.now()
        
        created_str = gerrit_data.get('created', '')
        if created_str:
            created = datetime.fromisoformat(created_str.replace('Z', '+00:00'))
        else:
            created = commit_time
            
        updated_str = gerrit_data.get('updated', '')
        if updated_str:
            updated = datetime.fromisoformat(updated_str.replace('Z', '+00:00'))
        else:
            updated = commit_time
        
        # 作者和所有者信息
        author = gerrit_data.get('owner', {}).get('name', 'Unknown')
        owner = gerrit_data.get('owner', {}).get('name', 'Unknown')
        number = gerrit_data.get('_number', 0)
        url = f"https://your-gerrit-server/c/{number}"  # 根据实际情况调整
        
        # 审查者信息
        reviewers = []
        if 'reviewers' in gerrit_data:
            for reviewer_type, reviewer_list in gerrit_data['reviewers'].items():
                for reviewer in reviewer_list:
                    reviewers.append(reviewer.get('name', 'Unknown'))
        
        # 代码变更统计
        changed_lines = 0
        insertions = 0
        deletions = 0
        
        if revisions and latest_revision in revisions:
            files = revisions[latest_revision].get('files', {})
            for file_path, file_data in files.items():
                if file_path != '/COMMIT_MSG':
                    insertions += file_data.get('lines_inserted', 0)
                    deletions += file_data.get('lines_deleted', 0)
            changed_lines = insertions + deletions
        
        patchset_count = len(revisions) if revisions else 1
        repo_path = gerrit_project
        
        # SonarQube数据处理（保持原有逻辑）
        sonar_project = sonar_data.get('name', '') if sonar_data else ''
        sonar_key = sonar_data.get('key', '') if sonar_data else ''
        sonar_creation_date = None
        if sonar_data and 'creationDate' in sonar_data:
            try:
                sonar_creation_date = datetime.fromisoformat(sonar_data['creationDate'].replace('Z', '+00:00'))
            except:
                pass
        
        # 问题统计（保持原有逻辑）
        sq_blocker = sq_critical = sq_major = sq_minor = sq_info = 0
        sq_resolved_blocker = sq_resolved_critical = sq_resolved_major = sq_resolved_minor = sq_resolved_info = 0
        
        if issues_json:
            for issue in issues_json.get('issues', []):
                severity = issue.get('severity', '').upper()
                status = issue.get('status', '').upper()
                
                if status == 'RESOLVED':
                    if severity == 'BLOCKER':
                        sq_resolved_blocker += 1
                    elif severity == 'CRITICAL':
                        sq_resolved_critical += 1
                    elif severity == 'MAJOR':
                        sq_resolved_major += 1
                    elif severity == 'MINOR':
                        sq_resolved_minor += 1
                    elif severity == 'INFO':
                        sq_resolved_info += 1
                else:
                    if severity == 'BLOCKER':
                        sq_blocker += 1
                    elif severity == 'CRITICAL':
                        sq_critical += 1
                    elif severity == 'MAJOR':
                        sq_major += 1
                    elif severity == 'MINOR':
                        sq_minor += 1
                    elif severity == 'INFO':
                        sq_info += 1
        
        # 代码度量（保持原有逻辑）
        ncloc = statements = functions = files_count = comment_lines = 0
        comment_lines_density = complexity = duplicated_lines_density = 0.0
        duplicated_lines = duplicated_blocks = duplicated_files = 0
        
        if measures_json:
            for measure in measures_json.get('component', {}).get('measures', []):
                metric = measure.get('metric', '')
                value = measure.get('value', '0')
                
                try:
                    if metric == 'ncloc':
                        ncloc = int(value)
                    elif metric == 'statements':
                        statements = int(value)
                    elif metric == 'functions':
                        functions = int(value)
                    elif metric == 'files':
                        files_count = int(value)
                    elif metric == 'comment_lines':
                        comment_lines = int(value)
                    elif metric == 'comment_lines_density':
                        comment_lines_density = float(value)
                    elif metric == 'complexity':
                        complexity = int(value)
                    elif metric == 'duplicated_lines_density':
                        duplicated_lines_density = float(value)
                    elif metric == 'duplicated_lines':
                        duplicated_lines = int(value)
                    elif metric == 'duplicated_blocks':
                        duplicated_blocks = int(value)
                    elif metric == 'duplicated_files':
                        duplicated_files = int(value)
                except ValueError:
                    pass
        
        # 计算衍生字段
        commit_date = commit_time.date()
        commit_year = commit_time.year
        commit_month = commit_time.month
        commit_week = commit_time.isocalendar()[1]
        commit_hour = commit_time.hour
        
        total_issues = sq_blocker + sq_critical + sq_major + sq_minor + sq_info
        total_resolved_issues = sq_resolved_blocker + sq_resolved_critical + sq_resolved_major + sq_resolved_minor + sq_resolved_info
        critical_issues = sq_blocker + sq_critical
        
        # 计算问题密度
        issue_density = (total_issues / max(ncloc, 1)) * 1000 if ncloc > 0 else 0
        
        # 变更大小分类
        if changed_lines <= 10:
            change_size_category = 'XS'
        elif changed_lines <= 50:
            change_size_category = 'S'
        elif changed_lines <= 200:
            change_size_category = 'M'
        elif changed_lines <= 500:
            change_size_category = 'L'
        else:
            change_size_category = 'XL'
        
        # 质量等级
        if critical_issues > 0:
            quality_level = 'critical'
        elif sq_major > 0:
            quality_level = 'major'
        elif sq_minor > 0:
            quality_level = 'minor'
        else:
            quality_level = 'clean'
        
        # 准备参数
        parameters = {
            'commit_id': commit_id,
            'change_id': change_id,
            'change_id_short': change_id_short,
            'patch_set': patch_set,
            'gerrit_project': gerrit_project,
            'branch': branch,
            'subject': subject,
            'status': gerrit_data.get('status', ''),
            'commit_time': commit_time,
            'created': created,
            'updated': updated,
            'author': author,
            'owner': owner,
            'number': number,
            'url': url,
            'reviewers': reviewers,
            'changed_lines': changed_lines,
            'insertions': insertions,
            'deletions': deletions,
            'patchset_count': patchset_count,
            'repo_path': repo_path,
            'sonar_project': sonar_project,
            'sonar_key': sonar_key,
            'sonar_creation_date': sonar_creation_date,
            'sq_blocker': sq_blocker,
            'sq_critical': sq_critical,
            'sq_major': sq_major,
            'sq_minor': sq_minor,
            'sq_info': sq_info,
            'sq_resolved_blocker': sq_resolved_blocker,
            'sq_resolved_critical': sq_resolved_critical,
            'sq_resolved_major': sq_resolved_major,
            'sq_resolved_minor': sq_resolved_minor,
            'sq_resolved_info': sq_resolved_info,
            'gerrit_raw': json.dumps(gerrit_data),
            'sonar_issues': json.dumps(issues_json) if issues_json else None,
            'ncloc': ncloc,
            'statements': statements,
            'functions': functions,
            'files': files_count,
            'comment_lines': comment_lines,
            'comment_lines_density': comment_lines_density,
            'complexity': complexity,
            'duplicated_lines_density': duplicated_lines_density,
            'duplicated_lines': duplicated_lines,
            'duplicated_blocks': duplicated_blocks,
            'duplicated_files': duplicated_files,
            'commit_date': commit_date,
            'commit_year': commit_year,
            'commit_month': commit_month,
            'commit_week': commit_week,
            'commit_hour': commit_hour,
            'total_issues': total_issues,
            'total_resolved_issues': total_resolved_issues,
            'critical_issues': critical_issues,
            'issue_density': issue_density,
            'change_size_category': change_size_category,
            'quality_level': quality_level
        }
        
        # 使用 UPSERT 操作
        upsert_sql = """
            INSERT INTO commit_metrics (
                commit_id, change_id, change_id_short, patch_set,
                gerrit_project, branch, subject, status,
                commit_time, created, updated, author, owner, number, url, reviewers,
                changed_lines, insertions, deletions, patchset_count, repo_path,
                sonar_project, sonar_key, sonar_creation_date,
                sq_blocker, sq_critical, sq_major, sq_minor, sq_info,
                sq_resolved_blocker, sq_resolved_critical, sq_resolved_major, 
                sq_resolved_minor, sq_resolved_info,
                gerrit_raw, sonar_issues,
                ncloc, statements, functions, files, comment_lines, comment_lines_density, 
                complexity, duplicated_lines_density, duplicated_lines, duplicated_blocks, duplicated_files,
                commit_date, commit_year, commit_month, commit_week, commit_hour,
                total_issues, total_resolved_issues, critical_issues, issue_density, 
                change_size_category, quality_level,
                created_at, updated_at
            ) VALUES (
                :commit_id, :change_id, :change_id_short, :patch_set,
                :gerrit_project, :branch, :subject, :status,
                :commit_time, :created, :updated, :author, :owner, :number, :url, :reviewers,
                :changed_lines, :insertions, :deletions, :patchset_count, :repo_path,
                :sonar_project, :sonar_key, :sonar_creation_date,
                :sq_blocker, :sq_critical, :sq_major, :sq_minor, :sq_info,
                :sq_resolved_blocker, :sq_resolved_critical, :sq_resolved_major, 
                :sq_resolved_minor, :sq_resolved_info,
                :gerrit_raw, :sonar_issues,
                :ncloc, :statements, :functions, :files, :comment_lines, :comment_lines_density, 
                :complexity, :duplicated_lines_density, :duplicated_lines, :duplicated_blocks, :duplicated_files,
                :commit_date, :commit_year, :commit_month, :commit_week, :commit_hour,
                :total_issues, :total_resolved_issues, :critical_issues, :issue_density, 
                :change_size_category, :quality_level,
                NOW(), NOW()
            )
            ON CONFLICT (gerrit_project, branch, change_id_short) 
            DO UPDATE SET
                commit_id = EXCLUDED.commit_id,
                patch_set = EXCLUDED.patch_set,
                subject = EXCLUDED.subject,
                status = EXCLUDED.status,
                commit_time = EXCLUDED.commit_time,
                updated = EXCLUDED.updated,
                reviewers = EXCLUDED.reviewers,
                changed_lines = EXCLUDED.changed_lines,
                insertions = EXCLUDED.insertions,
                deletions = EXCLUDED.deletions,
                patchset_count = EXCLUDED.patchset_count,
                sonar_project = EXCLUDED.sonar_project,
                sonar_key = EXCLUDED.sonar_key,
                sq_blocker = EXCLUDED.sq_blocker,
                sq_critical = EXCLUDED.sq_critical,
                sq_major = EXCLUDED.sq_major,
                sq_minor = EXCLUDED.sq_minor,
                sq_info = EXCLUDED.sq_info,
                sq_resolved_blocker = EXCLUDED.sq_resolved_blocker,
                sq_resolved_critical = EXCLUDED.sq_resolved_critical,
                sq_resolved_major = EXCLUDED.sq_resolved_major,
                sq_resolved_minor = EXCLUDED.sq_resolved_minor,
                sq_resolved_info = EXCLUDED.sq_resolved_info,
                gerrit_raw = EXCLUDED.gerrit_raw,
                sonar_issues = EXCLUDED.sonar_issues,
                ncloc = EXCLUDED.ncloc,
                statements = EXCLUDED.statements,
                functions = EXCLUDED.functions,
                files = EXCLUDED.files,
                comment_lines = EXCLUDED.comment_lines,
                comment_lines_density = EXCLUDED.comment_lines_density,
                complexity = EXCLUDED.complexity,
                duplicated_lines_density = EXCLUDED.duplicated_lines_density,
                duplicated_lines = EXCLUDED.duplicated_lines,
                duplicated_blocks = EXCLUDED.duplicated_blocks,
                duplicated_files = EXCLUDED.duplicated_files,
                total_issues = EXCLUDED.total_issues,
                total_resolved_issues = EXCLUDED.total_resolved_issues,
                critical_issues = EXCLUDED.critical_issues,
                issue_density = EXCLUDED.issue_density,
                change_size_category = EXCLUDED.change_size_category,
                quality_level = EXCLUDED.quality_level,
                updated_at = NOW()
            WHERE commit_metrics.patch_set <= EXCLUDED.patch_set
        """
        
        # 执行UPSERT操作
        conn.run(upsert_sql, **parameters)
        
        operation = "UPDATED" if record_exists else "INSERTED"
        logger.info(f"成功{operation}记录: {unique_key} (Patch Set: {patch_set})")
        return True
        
    except Exception as e:
        logger.error(f"保存数据时出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

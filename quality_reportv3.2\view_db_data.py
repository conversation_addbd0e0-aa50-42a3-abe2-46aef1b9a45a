#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
查看数据库中的数据
"""

import sqlite3
import os
import configparser
import sys
from datetime import datetime, timedelta

def ensure_metrics_table(db_path):
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    try:
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='metrics'")
        if not cursor.fetchone():
            cursor.execute('''
            CREATE TABLE metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date TEXT,
                project_id TEXT,
                bugs INTEGER,
                vulnerabilities INTEGER,
                code_smells INTEGER,
                blocker_issues INTEGER,
                major_issues INTEGER,
                info_issues INTEGER DEFAULT 0,
                duplications_percentage REAL,
                comment_percentage REAL,
                code_lines INTEGER,
                comment_lines INTEGER,
                duplicated_lines INTEGER,
                duplicated_blocks INTEGER,
                duplicated_files INTEGER,
                complexity INTEGER,
                total_lines INTEGER
            )
            ''')
            print("metrics表已创建")
        conn.commit()
    finally:
        conn.close()

def load_config():
    """加载配置文件"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    config_path = os.path.join(script_dir, "config.ini")
    
    config = configparser.ConfigParser()
    with open(config_path, 'r', encoding='utf-8') as f:
        config.read_file(f)
    return config

def get_project_id_from_config():
    """从配置文件获取项目ID"""
    config = load_config()
    try:
        return config['SonarQube']['ProjectID']
    except Exception as e:
        print(f"从配置文件获取ProjectID时出错: {str(e)}")
        return None

def view_recent_data(days=20, project_id=None):
    """查看最近几天的数据
    Args:
        days: 要查看最近多少天的数据
        project_id: 要查看的项目ID，如果为None则使用配置文件中的项目ID
    """
    script_dir = os.path.dirname(os.path.abspath(__file__))
    db_path = os.path.join(script_dir, "quality_data.db")
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    # 如果没有传入项目ID，则从配置文件获取
    if project_id is None:
        project_id = get_project_id_from_config()
        if not project_id:
            print("未能从配置文件中获取项目ID，请检查config.ini中的[SonarQube]部分是否包含ProjectID")
            return
    
    print(f"正在查询项目 {project_id} 的数据...")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 获取今天的日期
    today = datetime.now().date()
    days_ago = today - timedelta(days=days)
    
    # 查询最近的数据
    cursor.execute("""
    SELECT 
        date, 
        project_id, 
        bugs, 
        vulnerabilities, 
        code_smells,
        blocker_issues, 
        major_issues, 
        info_issues,    -- 添加提示类问题
        duplications_percentage, 
        comment_percentage,
        code_lines,           -- 代码行数
        duplicated_lines,     -- 重复行数
        duplicated_blocks,    -- 重复块数
        duplicated_files,     -- 重复文件数
        comment_lines,        -- 注释行数
        complexity           -- 圈复杂度
    FROM metrics
    WHERE date >= ? AND project_id = ?
    ORDER BY date DESC
    """, (days_ago.strftime('%Y-%m-%d'), project_id))
    
    rows = cursor.fetchall()
    
    if not rows:
        print(f"没有找到项目 {project_id} 的数据")
        return
        
    # 打印表头
    headers = [
        "日期", "项目ID", "Bugs", "漏洞", "异味", 
        "阻断问题", "主要问题", "提示问题", "重复率%", "注释率%",
        "代码行数", "重复行数", "重复块数", "重复文件数",
        "注释行数", "圈复杂度"
    ]
    
    # 计算每列的最大宽度
    col_widths = []
    for i in range(len(headers)):
        max_width = len(headers[i])
        for row in rows:
            cell_str = str(row[i] if row[i] is not None else 0)  # 将None替换为0
            max_width = max(max_width, len(cell_str))
        col_widths.append(max_width + 2)  # 添加一些填充空间
    
    # 打印表头
    header_line = " | ".join(f"{h:^{w}}" for h, w in zip(headers, col_widths))
    print("\n" + header_line)
    print("-" * len(header_line))
    
    # 打印数据
    for row in rows:
        formatted_row = []
        for i, cell in enumerate(row):
            if cell is None:
                formatted_row.append("0".center(col_widths[i]))
            else:
                # 对数值进行格式化，使其更易读
                if isinstance(cell, (int, float)) and i > 1:  # 跳过日期和项目ID
                    if i in [7, 8]:  # 重复率和注释率
                        formatted_value = f"{cell:.1f}"
                    else:
                        formatted_value = f"{int(cell):,}"
                else:
                    formatted_value = str(cell)
                formatted_row.append(formatted_value.center(col_widths[i]))
        print(" | ".join(formatted_row))
    
    # 关闭连接
    conn.close()

def list_available_projects():
    """列出数据库中所有可用的项目"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    db_path = os.path.join(script_dir, "quality_data.db")
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    cursor.execute("""
    SELECT DISTINCT project_id 
    FROM metrics 
    ORDER BY project_id
    """)
    
    projects = cursor.fetchall()
    
    if not projects:
        print("数据库中没有任何项目数据")
    else:
        print("\n可用的项目列表：")
        for project in projects:
            print(f"- {project[0]}")
    
    conn.close()

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1].lower() in ['--list', '-l']:
            list_available_projects()
        else:
            view_recent_data(project_id=sys.argv[1])
    else:
        view_recent_data()

# 在主逻辑前调用
script_dir = os.path.dirname(os.path.abspath(__file__))
db_path = os.path.join(script_dir, "quality_data.db")
ensure_metrics_table(db_path) 
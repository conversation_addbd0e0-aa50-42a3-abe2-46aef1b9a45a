#!/usr/bin/env python3
"""
Grafana 看板自动导入脚本
用于批量导入研发效能分析看板
"""

import requests
import json
import os
import sys
from pathlib import Path

class GrafanaDashboardImporter:
    def __init__(self, grafana_url, api_key, datasource_uid):
        """
        初始化导入器
        
        Args:
            grafana_url: Grafana 服务器地址 (如: http://localhost:3000)
            api_key: Grafana API Key
            datasource_uid: PostgreSQL 数据源 UID
        """
        self.grafana_url = grafana_url.rstrip('/')
        self.api_key = api_key
        self.datasource_uid = datasource_uid
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
    
    def import_dashboard(self, json_file_path):
        """
        导入单个看板
        
        Args:
            json_file_path: JSON 文件路径
            
        Returns:
            bool: 导入是否成功
        """
        try:
            # 读取 JSON 文件
            with open(json_file_path, 'r', encoding='utf-8') as f:
                dashboard_data = json.load(f)
            
            # 更新数据源 UID
            self._update_datasource_uid(dashboard_data)
            
            # 准备导入数据
            import_data = {
                'dashboard': dashboard_data['dashboard'],
                'overwrite': True,
                'inputs': [
                    {
                        'name': 'DS_POSTGRES',
                        'type': 'datasource',
                        'pluginId': 'postgres',
                        'value': self.datasource_uid
                    }
                ]
            }
            
            # 发送导入请求
            response = requests.post(
                f'{self.grafana_url}/api/dashboards/import',
                headers=self.headers,
                json=import_data
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 成功导入看板: {dashboard_data['dashboard']['title']}")
                print(f"   URL: {self.grafana_url}{result['url']}")
                return True
            else:
                print(f"❌ 导入失败: {dashboard_data['dashboard']['title']}")
                print(f"   错误: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 导入异常: {json_file_path}")
            print(f"   错误: {str(e)}")
            return False
    
    def _update_datasource_uid(self, dashboard_data):
        """
        更新看板中的数据源 UID
        """
        dashboard = dashboard_data['dashboard']
        
        # 更新模板变量中的数据源
        if 'templating' in dashboard and 'list' in dashboard['templating']:
            for var in dashboard['templating']['list']:
                if 'datasource' in var:
                    var['datasource']['uid'] = self.datasource_uid
        
        # 更新面板中的数据源
        if 'panels' in dashboard:
            for panel in dashboard['panels']:
                if 'targets' in panel:
                    for target in panel['targets']:
                        if 'datasource' in target:
                            target['datasource']['uid'] = self.datasource_uid
    
    def import_all_dashboards(self, dashboard_dir):
        """
        批量导入所有看板
        
        Args:
            dashboard_dir: 看板 JSON 文件目录
        """
        dashboard_dir = Path(dashboard_dir)
        json_files = list(dashboard_dir.glob('*.json'))
        
        if not json_files:
            print(f"❌ 在目录 {dashboard_dir} 中未找到 JSON 文件")
            return
        
        print(f"📁 找到 {len(json_files)} 个看板文件")
        print("=" * 50)
        
        success_count = 0
        for json_file in json_files:
            if json_file.name == 'README.md':
                continue
                
            print(f"📊 正在导入: {json_file.name}")
            if self.import_dashboard(json_file):
                success_count += 1
            print()
        
        print("=" * 50)
        print(f"🎉 导入完成! 成功: {success_count}/{len(json_files)}")
    
    def test_connection(self):
        """
        测试 Grafana 连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            response = requests.get(
                f'{self.grafana_url}/api/health',
                headers=self.headers
            )
            
            if response.status_code == 200:
                print("✅ Grafana 连接成功")
                return True
            else:
                print(f"❌ Grafana 连接失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Grafana 连接异常: {str(e)}")
            return False

def main():
    """主函数"""
    print("🚀 Grafana 研发效能看板导入工具")
    print("=" * 50)
    
    # 配置参数
    grafana_url = input("请输入 Grafana 地址 (默认: http://localhost:3000): ").strip()
    if not grafana_url:
        grafana_url = "http://localhost:3000"
    
    api_key = input("请输入 Grafana API Key: ").strip()
    if not api_key:
        print("❌ API Key 不能为空")
        sys.exit(1)
    
    datasource_uid = input("请输入 PostgreSQL 数据源 UID: ").strip()
    if not datasource_uid:
        print("❌ 数据源 UID 不能为空")
        sys.exit(1)
    
    # 创建导入器
    importer = GrafanaDashboardImporter(grafana_url, api_key, datasource_uid)
    
    # 测试连接
    print("\n🔍 测试连接...")
    if not importer.test_connection():
        sys.exit(1)
    
    # 获取当前目录
    current_dir = Path(__file__).parent
    dashboard_dir = current_dir
    
    # 开始导入
    print(f"\n📁 从目录导入: {dashboard_dir}")
    importer.import_all_dashboards(dashboard_dir)

if __name__ == "__main__":
    main() 
{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "postgres", "uid": "${DS_POSTGRESQL-CODEQUALITY}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.0.0", "targets": [{"datasource": {"type": "postgres", "uid": "${DS_POSTGRESQL-CODEQUALITY}"}, "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT \n  COUNT(*) as \"总提交数\",\n  COUNT(DISTINCT author) as \"活跃开发者\",\n  COUNT(DISTINCT gerrit_project) as \"活跃项目\",\n  SUM(total_issues) as \"总问题数\",\n  ROUND(AVG(issue_density)::numeric, 2) as \"平均问题密度\",\n  ROUND((COUNT(CASE WHEN quality_level = 'clean' THEN 1 END)::float / COUNT(*) * 100)::numeric, 2) as \"清洁代码率%\"\nFROM commit_metrics \nWHERE commit_time >= NOW() - INTERVAL '30 days'", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "关键指标概览 (最近30天)", "type": "stat"}, {"datasource": {"type": "postgres", "uid": "${DS_POSTGRESQL-CODEQUALITY}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "9.0.0", "targets": [{"datasource": {"type": "postgres", "uid": "${DS_POSTGRESQL-CODEQUALITY}"}, "format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT \n  commit_date as time,\n  total_commits as \"提交数\",\n  total_issues as \"问题数\",\n  total_critical_issues as \"严重问题数\"\nFROM daily_commit_stats \nWHERE commit_date >= CURRENT_DATE - INTERVAL '30 days'\nORDER BY commit_date", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "每日提交趋势", "type": "timeseries"}, {"datasource": {"type": "postgres", "uid": "${DS_POSTGRESQL-CODEQUALITY}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "clean"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "critical"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "major"}, "properties": [{"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "minor"}, "properties": [{"id": "color", "value": {"fixedColor": "yellow", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6}, "id": 3, "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "pieType": "pie", "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "${DS_POSTGRESQL-CODEQUALITY}"}, "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT \n  quality_level as metric,\n  commit_count as value\nFROM quality_level_distribution\nORDER BY \n  CASE quality_level \n    WHEN 'clean' THEN 1 \n    WHEN 'minor' THEN 2 \n    WHEN 'major' THEN 3 \n    WHEN 'critical' THEN 4 \n  END", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "代码质量等级分布", "type": "piechart"}, {"datasource": {"type": "postgres", "uid": "${DS_POSTGRESQL-CODEQUALITY}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "clean_rate_percent"}, "properties": [{"id": "unit", "value": "percent"}, {"id": "custom.displayMode", "value": "color-background"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 70}, {"color": "green", "value": 90}]}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 14}, "id": 4, "options": {"showHeader": true}, "pluginVersion": "9.0.0", "targets": [{"datasource": {"type": "postgres", "uid": "${DS_POSTGRESQL-CODEQUALITY}"}, "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT \n  gerrit_project as \"项目名称\",\n  total_commits as \"提交数\",\n  total_issues as \"问题数\",\n  clean_rate_percent as \"清洁代码率%\",\n  avg_issue_density as \"问题密度\"\nFROM project_quality_stats \nWHERE total_commits >= 5 \nORDER BY clean_rate_percent DESC \nLIMIT 10", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "项目质量排名 (Top 10)", "type": "table"}, {"datasource": {"type": "postgres", "uid": "${DS_POSTGRESQL-CODEQUALITY}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "clean_rate_percent"}, "properties": [{"id": "unit", "value": "percent"}, {"id": "custom.displayMode", "value": "color-background"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 70}, {"color": "green", "value": 90}]}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 14}, "id": 5, "options": {"showHeader": true}, "pluginVersion": "9.0.0", "targets": [{"datasource": {"type": "postgres", "uid": "${DS_POSTGRESQL-CODEQUALITY}"}, "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT \n  author as \"开发者\",\n  total_commits as \"提交数\",\n  total_changed_lines as \"代码行数\",\n  clean_rate_percent as \"清洁代码率%\",\n  avg_issue_density as \"问题密度\"\nFROM author_contribution_stats \nWHERE total_commits >= 5 \nORDER BY total_commits DESC \nLIMIT 10", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "开发者贡献排名 (Top 10)", "type": "table"}], "refresh": "5m", "schemaVersion": 36, "style": "dark", "tags": ["code-quality", "overview"], "templating": {"list": []}, "time": {"from": "now-30d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "代码质量效能分析 - 总览", "uid": "code-quality-overview", "version": 1, "weekStart": ""}
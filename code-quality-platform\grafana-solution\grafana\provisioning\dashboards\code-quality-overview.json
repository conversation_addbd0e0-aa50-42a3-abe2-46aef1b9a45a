{"dashboard": {"id": null, "title": "代码质量效能分析 - 总览", "tags": ["code-quality", "overview"], "timezone": "browser", "refresh": "5m", "time": {"from": "now-30d", "to": "now"}, "panels": [{"id": 1, "title": "关键指标概览", "type": "stat", "targets": [{"rawSql": "SELECT COUNT(*) as total_commits, COUNT(DISTINCT author) as total_authors, COUNT(DISTINCT gerrit_project) as total_projects, SUM(total_issues) as total_issues FROM commit_metrics WHERE commit_time >= NOW() - INTERVAL '30 days'", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 0}}, {"id": 2, "title": "每日提交趋势", "type": "timeseries", "targets": [{"rawSql": "SELECT commit_date as time, total_commits, total_issues, total_critical_issues FROM daily_commit_stats WHERE commit_date >= NOW() - INTERVAL '30 days' ORDER BY commit_date", "format": "time_series", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6}}, {"id": 3, "title": "质量等级分布", "type": "piechart", "targets": [{"rawSql": "SELECT quality_level, commit_count FROM quality_level_distribution", "format": "table", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6}}, {"id": 4, "title": "项目质量排名", "type": "table", "targets": [{"rawSql": "SELECT gerrit_project, total_commits, total_issues, clean_rate_percent, avg_issue_density FROM project_quality_stats WHERE total_commits >= 5 ORDER BY clean_rate_percent DESC LIMIT 10", "format": "table", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 14}}, {"id": 5, "title": "开发者贡献排名", "type": "table", "targets": [{"rawSql": "SELECT author, total_commits, total_changed_lines, clean_rate_percent, avg_issue_density FROM author_contribution_stats WHERE total_commits >= 5 ORDER BY total_commits DESC LIMIT 10", "format": "table", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 14}}]}}
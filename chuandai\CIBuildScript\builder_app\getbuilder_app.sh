#!/bin/bash

#获取eide.json文件
echo ${bashWorkSpace}
codeDir="$(pwd)"
scriptDir=${codeDir}/CIBuildScript

appEideDir="${codeDir}/app/project/WR02_App/.eide"
\cp ${appEideDir}/eide.json ${scriptDir}/builder_app/
cd ${scriptDir}/builder_app/

aimFileName="builder_app.params"
cp builder_app_module.params ${aimFileName}

# 调整模板配置文件
JOB_NAME=$1
if [ -z "$JOB_NAME" ]; then
    echo "生成门禁工程配置文件"
else
    echo "生成CI工程配置文件"
    sed -i "s/wearable-wr02-build/${JOB_NAME}/g" ${aimFileName}
fi

# 生成 incDirs linDirs defines临时文件
cat eide.json | jq .targets | jq .[] | jq .custom_dep > eideincDirsTemp.json

# 获取virtualFolder下所有文件（包括各级子文件夹中的文件）的path字段值
jq '.virtualFolder | recurse(.folders[]) |.files[].path' eide.json > all_paths.xml

# 获取排除虚拟文件/模块清单
jq -r '.targets.WR02_APP.excludeList[]' eide.json | sed 's/<virtual_root>\///' > exclude_list.xml


# 定义排除文件名和排除模块名
> execute_file.xml
> execute_module.xml


# 生成jq选择器语法的函数，核心函数很重要
function getaimjson (){
    #输入路径，追加输出路径至文件中
    input_path=$1
    IFS='/' read -ra path_parts <<< "$input_path"
    jq_query=".virtualFolder"
    for part in "${path_parts[@]}"; do
    jq_query+=" |.folders[] | select(.name == \"$part\") "
    done
    # 执行jq选择器命获取path
    jq "$jq_query" eide.json | grep \"path\" | awk '{print $NF}'
}

# 判断排除文件是否为空
if [ $(wc -l < exclude_list.xml) -eq 0 ]; then
    # 如果为空，则all_paths中的内容就是sourcelist
    echo "The exclude_list.xml is empty."
    cp all_paths.xml sourcelist.xml
    sed -i '$!s/$/,/' sourcelist.xml
else
    # 如果不为空，逐行处理excludeList，按规则提取并收集要排除的路径
    while read -r exclude_item; do
        if [[ "${exclude_item}" == *"."* ]]; then
            # 当如输入为文件，则获取路径和文件名
            exclude_item_dir_name=$(dirname "${exclude_item}")
            exclude_item_base_name=$(basename "${exclude_item}")
            # 将路径传给getjson函数解析获取该虚拟路径下的文件清单
            getaimjson "${exclude_item_dir_name}" >  tmp_file.list
            # 根据文件名在上面获取的文件清单中获取路径，因为同名的文件可能存在多个路径，这种方式最可靠
            sub_paths=$(cat tmp_file.list | grep "${exclude_item_base_name}")
            if [ -n "${sub_paths}" ];
            then
                echo "${sub_paths}" >> execute_file.xml
            else
                echo "无此文件$exclude_item_base_name"
            fi
        else
            # 如果没有文件后缀，则按目录处理，将路径传给getjson函数解析
            getaimjson "${exclude_item}" >> execute_module.xml
        fi
    done <<< "$(cat exclude_list.xml)"

    # 将文件名排除清单和目录名排除清单合并成execute_combined.xml，然后排序，从all_paths.xml中排除掉。
    cat execute_file.xml execute_module.xml > execute_combined.xml
    
    # 排序
    sort all_paths.xml  > fileall_sorted.xml
    sort execute_combined.xml > filecom_sorted.xml
    
    # 排除
    comm -23 fileall_sorted.xml filecom_sorted.xml > sourcelist.xml
    
    # 行尾加逗号，且最后一行不加
    sed -i '$!s/$/,/' sourcelist.xml
fi

# 通过读取eideincDirsTemp.json文件,将里面的incList,libList,defineList内容,写入builder_v2.params文件中的incDirs,libDirs,defines文件
jq -r '.incList' eideincDirsTemp.json > incList.json
jq -r '.libList' eideincDirsTemp.json > libList.json
jq -r '.defineList' eideincDirsTemp.json > defineList.json

# 将incList.json文件内容开头空6，文件内容不变
cp incList.json incListTemp.json
perl -pi -e 's/^/      /' incListTemp.json
# 去掉incListTemp.json文件中'[',']'行前面的空格
perl -pi -e 's/^\s+(\[|\])/$1/' incListTemp.json

# libList.json文件内容开头空6格，文件内容不变
cp libList.json libListTemp.json
perl -pi -e 's/^/      /' libListTemp.json
# libListTemp.json文件中'[',']'行前面的空格
perl -pi -e 's/^\s+(\[|\])/$1/' libListTemp.json

# defineList.json文件内容开头空6格
cp defineList.json defineListTemp.json
perl -pi -e 's/^/      /' defineListTemp.json
# defineListTemp.json文件中'[',']'行前面的空格
perl -pi -e 's/^\s+(\[|\])/$1/' defineListTemp.json

# 读取incList.json文件,将里面的内容替换入builder_v2.params文件中的 "replace_incList",并且前面空5格子
perl -pi -e "s|replace_incList|$(cat incListTemp.json)|g" ${aimFileName}
perl -pi -e "s|replace_libList|$(cat libListTemp.json)|g" ${aimFileName}
perl -pi -e "s|replace_defineList|$(cat defineListTemp.json)|g" ${aimFileName}
perl -pi -e "s|replace_sourceList|$(cat sourcelist.xml)|g" ${aimFileName}

# 特殊处理，"SIFLI_BUILD="000000"" 替换为"SIFLI_BUILD=\"000000\"",
sed -i 's/"SIFLI_BUILD="000000""/"SIFLI_BUILD=\\"000000\\""/g' ${aimFileName}

# 清理工作目录
#rm -rf incListTemp.json
#rm -rf libListTemp.json
#rm -rf defineListTemp.json
#rm -rf eideFolderPathTemp.xml
#rm -rf eideFolderPath.xml
#rm -rf filePathTemp.xml
#rm -rf incList.json
#rm -rf libList.json
#rm -rf defineList.json
#rm -rf eideincDirsTemp.json

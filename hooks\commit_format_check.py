#!/usr/bin/env python3
# Commit格式校验钩子脚本 - Python版本
# 用于校验commit格式是否符合规范
# 校验内容：
# 1. 标题（必有）
# 2. 正文（可有可无）
# 3. 禅道关联单号（可有可无）
# 4. ChangeID（必有）
# 5. 作者邮箱（必有）

import os
import re
import sys
import subprocess
from pathlib import Path

# 定义白名单用户，跳过校验
WHITELIST_USERS = ["gerritadmin", "jenkinsadmin", "sonarreview"]


def extract_param_value(args, param_name):
    """从参数列表中提取指定参数的值"""
    for i, arg in enumerate(args):
        if arg == param_name:
            if i + 1 < len(args):
                return args[i + 1]
    return ''


def parse_arguments():
    """解析命令行参数"""
    # 直接使用sys.argv模拟bash脚本的参数处理
    if len(sys.argv) >= 3:
        project_name = sys.argv[2]
    else:
        project_name = ''

    return {
        'projectName': project_name,
        'params': sys.argv[1:]
    }


def is_whitelist_user(username):
    """检查是否为白名单用户"""
    return username in WHITELIST_USERS


def check_title_format(title):
    """校验标题格式"""
    # 检查标题是否为空
    if not title:
        print("错误: Commit标题不能为空")
        return False

    # 计算标题的显示宽度（中文字符算2个单位，英文和其他字符算1个单位）
    print(f"调试: 标题内容: '{title}'")
    display_width = 0
    for char in title:
        if re.match(r'[\u4E00-\u9FFF]', char):  # 中文字符
            display_width += 2
        else:  # 非中文字符
            display_width += 1
    print(f"调试: 标题显示宽度: {display_width}")

    # 统一显示宽度上限为60个单位
    max_display_width = 60
    if display_width > max_display_width:
        print(f"错误: Commit标题显示宽度不能超过{max_display_width}个单位，当前为{display_width}个单位")
        return False

    # 检查标题长度（建议不超过72字符）
    if len(title) > 80:
        print(f"警告: Commit标题过长（{len(title)}字符），建议不超过72字符")

    # 检查是否包含中文冒号
    if '：' in title:
        print("警告: 建议使用英文冒号(:)而不是中文冒号(：)")

    # 检查冒号后是否有空格
    if re.search(r':[^\s]', title):
        print("错误: 冒号后必须有空格")
        return False

    print("标题格式校验通过")
    return True


def check_body_format(body):
    """校验正文格式"""
    # 如果正文为空，直接通过
    if not body.strip():
        print("正文为空，跳过正文格式校验")
        return True

    # 检查正文是否只有一行且为纯数字
    lines = body.strip().split('\n')
    if len(lines) == 1 and re.match(r'^\d+$', lines[0]):
        print("错误: 正文不能只包含纯数字，请检查下禅道单号【bug-view-ID】是否写错")
        return False

    print("正文格式校验通过")
    return True


def check_zentao_reference(full_commit):
    """校验禅道关联单号"""
    # 检查是否包含bug-view-关键字
    if 'bug-view-' in full_commit:
        # 检查bug-view-格式是否正确（bug-view-后跟5~6位数字）
        bug_lines = re.findall(r'bug-view-\d{5,6}', full_commit)
        if not bug_lines:
            print("错误: 禅道关联单号格式错误，应为 'bug-view-12345' 或 'bug-view-123456'，数字为5~6位")
            return False

        # 检查所有bug-view-串是否合规
        for bug in re.findall(r'bug-view-[^\s]*', full_commit):
            if not re.match(r'^bug-view-\d{5,6}$', bug):
                print(f"错误: 检测到不合规的禅道单号: {bug}，应为 'bug-view-12345' 或 'bug-view-123456'，数字为5~6位")
                return False

        print(f"禅道关联单号校验通过: {bug_lines[0]}")
    else:
        print("未发现禅道关联单号（可选）")

    return True


def check_change_id(full_commit):
    """校验ChangeID"""
    # 检查是否包含Change-Id
    change_id_match = re.search(r'^Change-Id: (.+)$', full_commit, re.MULTILINE)
    if not change_id_match:
        print("错误: 缺少Change-Id，请使用 'git commit --amend -s' 生成")
        return False

    change_id = change_id_match.group(1)
    if not change_id:
        print("错误: Change-Id不能为空")
        return False

    # 检查Change-Id是否为Gerrit标准格式：I+40位十六进制
    if not re.match(r'^I[a-f0-9]{40}$', change_id):
        print("错误: Change-Id格式不正确，应为大写I开头+40位小写十六进制字符（如：Ia39aac81dfacacd6c3fdec8922c5edf0b72e6413）")
        return False

    print(f"Change-Id校验通过: {change_id[:8]}...")
    return True


def check_author_email(full_commit):
    """校验作者邮箱"""
    # 检查是否包含Signed-off-by
    signed_off_match = re.search(r'^Signed-off-by: (.+)$', full_commit, re.MULTILINE)
    if not signed_off_match:
        print("错误: 缺少Signed-off-by，请使用 'git commit --amend -s' 生成")
        return False

    signed_off = signed_off_match.group(1)
    if not signed_off:
        print("错误: Signed-off-by不能为空")
        return False

    # 检查邮箱格式
    email_match = re.search(r'<([^>]*@[^>]*)>', signed_off)
    if not email_match:
        print("错误: Signed-off-by中缺少有效的邮箱地址")
        return False

    email = email_match.group(1)
    # 简单的邮箱格式校验
    if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
        print(f"错误: 邮箱格式不正确: {email}")
        return False

    print(f"作者邮箱校验通过: {email}")
    return True


def check_colon_format(full_commit):
    """校验冒号格式"""
    # 检查是否使用了中文冒号
    if '：' in full_commit:
        print("警告: 发现中文冒号(：)，建议统一使用英文冒号(:)")

    # 检查冒号后是否有空格
    lines_with_colon = [line for line in full_commit.split('\n') if ':' in line]
    for line in lines_with_colon:
        if re.search(r':[^\s]', line):
            print(f"警告: 行 '{line}' 中冒号后缺少空格")

    return True


def main_check(title, body, full_commit, uploader_username, commit_id):
    """主校验函数"""
    print("开始校验commit格式...")
    print(f"提交者: {uploader_username}")
    print(f"Commit ID: {commit_id}")
    print("----------------------------------------")

    has_error = False

    # 1. 校验标题
    print("1. 校验标题...")
    if not check_title_format(title):
        has_error = True
    print()

    # 2. 校验正文
    print("2. 校验正文...")
    if not check_body_format(body):
        has_error = True
    print()

    # 3. 校验禅道关联单号
    print("3. 校验禅道关联单号...")
    if not check_zentao_reference(full_commit):
        has_error = True
    print()

    # 4. 校验ChangeID
    print("4. 校验Change-Id...")
    if not check_change_id(full_commit):
        has_error = True
    print()

    # 5. 校验作者邮箱
    print("5. 校验作者邮箱...")
    if not check_author_email(full_commit):
        has_error = True
    print()

    # 6. 校验冒号格式
    print("6. 校验冒号格式...")
    check_colon_format(full_commit)
    print()

    if has_error:
        print("----------------------------------------")
        print("Commit格式校验失败，请根据上述错误信息修正后重新提交")
        print()
        print("建议的commit格式：")
        print("模块名: 简洁描述")
        print()
        print("正文: (可选，多行改动建议使用序号)")
        print("1. 第一项改动")
        print("2. 第二项改动")
        print()
        print("bug-view-禅道单号 (可选)")
        print()
        print("Change-Id: I1234567890abcdef1234567890abcdef12345678")
        print("Signed-off-by: 作者名 <邮箱地址>")
        sys.exit(1)
    else:
        print("----------------------------------------")
        print("Commit格式校验通过")
        sys.exit(0)


def run_command(cmd, cwd=None):
    """执行命令并返回输出"""
    try:
        result = subprocess.run(
            cmd, 
            cwd=cwd, 
            check=True, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE, 
            universal_newlines=True
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"执行命令失败: {cmd}")
        print(f"错误输出: {e.stderr}")
        sys.exit(1)


def main():
    """主函数"""
    args = parse_arguments()
    project_name = args['projectName']
    params = args['params']

    # 提取commitID和uploaderUsername
    commit_id = extract_param_value(params, '--newrev')
    uploader_username = extract_param_value(params, '--uploader-username')

    # 如果通过参数无法获取，尝试从环境变量获取
    if not commit_id:
        commit_id = os.environ.get('COMMIT_ID', '')
    if not uploader_username:
        uploader_username = os.environ.get('UPLOADER_USERNAME', '')

    # 定义路径，与shell脚本保持一致
    gerrit_path = os.environ.get('GERRIT_PATH', "/home/<USER>/gerrit_site/git")
    project_path = os.path.join(gerrit_path, project_name)
    ini_commit = commit_id[:7] if commit_id else ''
    ini_path = os.environ.get('INI_PATH', "/home/<USER>/gerrit_site/hooks/inidir/commit-format-check")
    ini_title = os.path.join(ini_path, f"tittle_{ini_commit}")
    ini_content = os.path.join(ini_path, f"content_{ini_commit}")
    ini_full = os.path.join(ini_path, f"full_{ini_commit}")
    debug_ini_path = os.environ.get('DEBUG_INI_PATH', "/home/<USER>/gerrit_site/hooks/commit-format-check.ini")

    # 创建临时目录
    Path(ini_path).mkdir(parents=True, exist_ok=True)

    # 获取完整的commit提交信息
    if commit_id and project_name:
        git_dir = f"{project_path}.git"
        commit_title = run_command(['git', '--git-dir', git_dir, 'log', '--pretty=format:%s', '-n', '1', commit_id])
        commit_msg = run_command(['git', '--git-dir', git_dir, 'log', '--pretty=format:%b', '-n', '1', commit_id])
        commit_full = run_command(['git', '--git-dir', git_dir, 'log', '--pretty=format:%B', '-n', '1', commit_id])

        # 写入调试信息，与shell脚本保持一致
        with open(debug_ini_path, 'w') as f:
            f.write(' '.join(sys.argv) + '\n')

        with open(ini_title, 'w') as f:
            f.write(commit_title + '\n')

        with open(ini_content, 'w') as f:
            f.write(commit_msg + '\n')

        with open(ini_full, 'w') as f:
            f.write(commit_full + '\n')
    else:
        # 测试模式下的默认值
        commit_title = "测试: 这是一个测试标题"
        commit_msg = "这是测试正文\n包含多行\nbug-view-12345"
        commit_full = f"{commit_title}\n\n{commit_msg}\n\nChange-Id: Ia39aac81dfacacd6c3fdec8922c5edf0b72e6413\nSigned-off-by: Test User <<EMAIL>>"
        print("警告: 未提供完整参数，使用测试数据")

    # 检查是否为白名单用户
    if is_whitelist_user(uploader_username):
        print(f"跳过: 提交者 {uploader_username} 在白名单中，跳过commit格式校验.")
        sys.exit(0)

    # 执行主校验
    main_check(commit_title, commit_msg, commit_full, uploader_username, commit_id)


if __name__ == "__main__":
    main()
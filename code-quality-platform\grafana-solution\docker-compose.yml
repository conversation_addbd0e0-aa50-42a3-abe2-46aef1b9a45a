version: '3.8'
services:
  # 使用现有PostgreSQL数据库的代理服务
  postgres-proxy:
    image: postgres:15
    container_name: code-quality-postgres
    environment:
      POSTGRES_DB: mydatabase
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: admin123
      TZ: Asia/Shanghai
    ports:
      - "5436:5432"
    # 注意：这里我们创建一个新的PostgreSQL实例用于演示
    # 实际使用时，您应该直接连接到现有的数据库
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    restart: unless-stopped
    networks:
      - quality-platform

  # Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: code-quality-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_INSTALL_PLUGINS=grafana-piechart-panel,grafana-worldmap-panel,grafana-clock-panel
      - TZ=Asia/Shanghai
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
    restart: unless-stopped
    networks:
      - quality-platform
    depends_on:
      - postgres-proxy

  # 数据API服务（可选，用于自定义数据处理）
  api-service:
    build: ./api-service
    container_name: code-quality-api
    ports:
      - "8081:8080"
    environment:
      - DB_HOST=postgres-proxy
      - DB_PORT=5432
      - DB_NAME=mydatabase
      - DB_USER=admin
      - DB_PASSWORD=admin123
      - TZ=Asia/Shanghai
    depends_on:
      - postgres-proxy
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - quality-platform

volumes:
  postgres_data:
  grafana_data:

networks:
  quality-platform:
    driver: bridge

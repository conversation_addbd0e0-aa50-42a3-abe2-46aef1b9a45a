version: '3'
services:
  postgres2:
    image: postgres:latest
    container_name: postgres2
    restart: always
    privileged: true
    networks:
      - sonar
    volumes:
      - /data/sonar2/postgres/postgresql:/var/lib/postgresql
      - /data/sonar2/postgres/data:/var/lib/postgresql/data
      - /etc/localtime:/etc/localtime:ro
    ports:
      - "5433:5432"
    environment:
      POSTGRES_USER: sonar2
      POSTGRES_PASSWORD: sonar2
      POSTGRES_DB: sonar2
      TZ: Asia/Shanghai

  sonar2:
    image: sonarqube:9.9.8-community
    container_name: sonar2
    restart: always
    privileged: true
    networks:
      - sonar
    volumes:
      - /data/sonar2/sonarqube/logs:/opt/sonarqube/logs
      - /data/sonar2/sonarqube/conf:/opt/sonarqube/conf
      - /data/sonar2/sonarqube/data:/opt/sonarqube/data
      - /data/sonar2/sonarqube/extensions:/opt/sonarqube/extensions
    ports:
      - "9091:9000"
    links:
      - "postgres2:postgres2"
    environment:
      ALLOW_EMPTY_PASSWORD: "yes"
      SONARQUBE_JDBC_USERNAME: sonar2
      SONARQUBE_JDBC_PASSWORD: sonar2
      SONARQUBE_JDBC_URL: "***************************************"

networks:
  sonar:
    driver: bridge
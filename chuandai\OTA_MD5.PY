import os
import hashlib
import re
import shutil
import sys
import io

sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

def calculate_md5(file_path):
    """计算文件的MD5值"""
    md5_hash = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            md5_hash.update(chunk)
    return md5_hash.hexdigest()

def update_ota_cfg(cfg_path, bin_dir_path):
    """更新ota.cfg文件中的MD5校验码"""
    output_path = cfg_path + ".new"
    
    # 获取bin目录下所有文件的列表
    bin_files = {}
    if os.path.exists(bin_dir_path) and os.path.isdir(bin_dir_path):
        for file_name in os.listdir(bin_dir_path):
            file_path = os.path.join(bin_dir_path, file_name)
            if os.path.isfile(file_path) and file_name != "ota.cfg" and file_name != "ota.cfg.new" and file_name != "ota.cfg.bak":
                bin_files[file_name] = file_path
    
    print(f"在 {bin_dir_path} 目录中找到 {len(bin_files)} 个二进制文件")
    for file_name, file_path in bin_files.items():
        print(f"  - {file_name}")
    
    # 读取配置文件
    try:
        with open(cfg_path, "r", encoding="utf-8") as f:
            lines = f.readlines()
    except UnicodeDecodeError:
        # 尝试不同的编码
        with open(cfg_path, "r", encoding="gbk") as f:
            lines = f.readlines()
    
    new_lines = []
    updated_count = 0
    
    for line in lines:
        # 跳过注释行和空行
        if line.strip().startswith("#") or not line.strip():
            new_lines.append(line)
            continue
        
        # 解析配置行
        parts = line.strip().split(",")
        if len(parts) < 6:
            new_lines.append(line)
            continue
        
        file_path = parts[0]
        hash_type = parts[1]
        
        # 从文件路径中提取文件名
        file_name = os.path.basename(file_path)
        
        # 检查文件是否在bin目录中
        if file_name in bin_files:
            # 计算MD5
            md5_value = calculate_md5(bin_files[file_name])
            # 更新MD5值
            parts[2] = md5_value
            updated_count += 1
            print(f"已更新文件 {file_name} 的MD5值: {md5_value}")
        
        # 重新组合配置行
        new_lines.append(",".join(parts) + "\n")
    
    # 写入新的配置文件
    with open(output_path, "w", encoding="utf-8") as f:
        f.writelines(new_lines)
    
    print(f"已完成! 共更新了 {updated_count} 个文件的MD5值")
    print(f"新配置文件已保存到: {output_path}")
    
    # 备份原文件并替换
    backup_path = cfg_path + ".bak"
    print(f"备份原配置文件到: {backup_path}")
    shutil.copy2(cfg_path, backup_path)
    shutil.move(output_path, cfg_path)
    print(f"已用新配置文件替换原配置文件")

    #删除bak文件
    os.remove(backup_path)

def main():
    """主函数"""
    print("OTA配置文件MD5更新工具")
    print("=" * 50)
    
    # 直接使用正确的路径 - cfg和二进制文件都在Bin目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    bin_dir = os.path.join(script_dir, "bin_update_src", "Bin")
    cfg_path = os.path.join(bin_dir, "ota.cfg")
    
    # 检查路径是否存在
    if not os.path.exists(cfg_path):
        print(f"错误: 配置文件不存在: {cfg_path}")
        cfg_path = input("请输入ota.cfg文件的完整路径: ")
    
    if not os.path.exists(bin_dir):
        print(f"错误: Bin目录不存在: {bin_dir}")
        bin_dir = input("请输入Bin目录的完整路径: ")
    
    print(f"使用配置文件: {cfg_path}")
    print(f"使用二进制文件目录: {bin_dir}")
    
    # 更新配置文件
    update_ota_cfg(cfg_path, bin_dir)
    
    # 打印当前工作目录
    print(f"当前工作目录: {os.getcwd()}")

if __name__ == "__main__":
    #跳转到py文件所在目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    main()
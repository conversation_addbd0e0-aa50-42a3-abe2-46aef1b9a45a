# SonarQube到Grafana简单集成方案

## 方案概述

这是一个简单可行的方案，通过PostgreSQL数据库将SonarQube的质量门数据传输到Grafana进行可视化。

### 架构流程
```
SonarQube → Webhook → PostgreSQL → Grafana
```

## 快速开始

### 1. 启动服务

```bash
cd sonar-grafana-simple
docker-compose up -d
```

### 2. 验证服务状态

```bash
# 检查所有服务
docker-compose ps

# 检查webhook服务健康状态
curl http://localhost:8080/health

# 查看统计信息
curl http://localhost:8080/stats
```

### 3. 配置SonarQube Webhooks

```bash
# 安装Python依赖
pip install requests

# 运行webhook配置脚本
python setup_webhooks.py
```

### 4. 访问Grafana

- URL: http://localhost:3000
- 用户名: admin
- 密码: admin123

## 测试验证

### 发送测试数据

```bash
curl -X POST http://localhost:8080/webhook/sonarqube \
  -H "Content-Type: application/json" \
  -d '{
    "taskId": "test-task-123",
    "status": "SUCCESS",
    "analysedAt": "2025-01-04T10:00:00+0000",
    "project": {
      "key": "test-project",
      "name": "Test Project"
    },
    "qualityGate": {
      "status": "OK",
      "conditions": [
        {
          "metricKey": "coverage",
          "operator": "LT",
          "status": "OK",
          "actualValue": "85.5",
          "threshold": "80.0"
        },
        {
          "metricKey": "bugs",
          "operator": "GT",
          "status": "OK", 
          "actualValue": "2",
          "threshold": "5"
        }
      ]
    }
  }'
```

### 查看数据库数据

```bash
# 进入PostgreSQL容器
docker exec -it sonar-grafana-postgres psql -U grafana_user -d sonar_grafana

# 查看质量门数据
SELECT * FROM sonar_quality_gates ORDER BY analysed_at DESC LIMIT 5;

# 查看条件数据
SELECT * FROM sonar_conditions ORDER BY created_at DESC LIMIT 10;

# 查看指标历史
SELECT * FROM sonar_metrics_history ORDER BY measured_at DESC LIMIT 10;
```

## 数据库表结构

### sonar_quality_gates (主表)
- `id`: 主键
- `project_key`: 项目键
- `project_name`: 项目名称
- `task_id`: 任务ID
- `status`: 分析状态
- `quality_gate_status`: 质量门状态
- `analysed_at`: 分析时间
- `webhook_data`: 原始webhook数据(JSON)

### sonar_conditions (条件表)
- `id`: 主键
- `quality_gate_id`: 关联质量门ID
- `metric_key`: 指标键
- `operator`: 操作符
- `status`: 条件状态
- `actual_value`: 实际值
- `threshold_value`: 阈值

### sonar_metrics_history (指标历史表)
- `id`: 主键
- `project_key`: 项目键
- `metric_key`: 指标键
- `metric_value`: 指标值
- `measured_at`: 测量时间

## Grafana查询示例

### 质量门通过率
```sql
SELECT 
  project_name,
  COUNT(*) as total_analyses,
  SUM(CASE WHEN quality_gate_status = 'OK' THEN 1 ELSE 0 END) as passed,
  ROUND(SUM(CASE WHEN quality_gate_status = 'OK' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as pass_rate
FROM sonar_quality_gates 
WHERE analysed_at >= NOW() - INTERVAL '30 days'
GROUP BY project_name
ORDER BY pass_rate DESC;
```

### 代码覆盖率趋势
```sql
SELECT 
  measured_at as time,
  project_key,
  metric_value as coverage
FROM sonar_metrics_history 
WHERE metric_key = 'coverage' 
  AND measured_at >= NOW() - INTERVAL '7 days'
ORDER BY time;
```

### Bug数量趋势
```sql
SELECT 
  measured_at as time,
  project_key,
  metric_value as bugs
FROM sonar_metrics_history 
WHERE metric_key = 'bugs' 
  AND measured_at >= NOW() - INTERVAL '7 days'
ORDER BY time;
```

## 故障排除

### 常见问题

1. **Webhook接收失败**
   ```bash
   # 检查服务日志
   docker logs sonar-webhook-receiver
   
   # 检查网络连通性
   curl http://localhost:8080/health
   ```

2. **数据库连接问题**
   ```bash
   # 检查PostgreSQL状态
   docker logs sonar-grafana-postgres
   
   # 测试数据库连接
   docker exec -it sonar-grafana-postgres psql -U grafana_user -d sonar_grafana -c "SELECT 1;"
   ```

3. **Grafana显示问题**
   ```bash
   # 检查Grafana日志
   docker logs sonar-grafana
   
   # 验证数据源连接
   # 在Grafana中: Configuration → Data Sources → Test
   ```

## 扩展配置

### 使用现有PostgreSQL数据库

如果您想使用现有的PostgreSQL数据库，请修改 `docker-compose.yml` 中的数据库配置：

```yaml
environment:
  - DB_HOST=your-existing-postgres-host
  - DB_PORT=5432
  - DB_NAME=your-database-name
  - DB_USER=your-username
  - DB_PASSWORD=your-password
```

### 添加更多指标

修改 `webhook_server.py` 中的 `save_quality_gate_data` 函数，添加更多SonarQube指标的处理逻辑。

### 自定义仪表板

在Grafana中创建新的仪表板，使用PostgreSQL数据源查询数据。

## 安全建议

1. 修改默认密码
2. 使用HTTPS传输
3. 配置防火墙规则
4. 定期备份数据库

## 性能优化

1. 添加数据库索引
2. 配置连接池
3. 设置数据保留策略
4. 监控资源使用情况

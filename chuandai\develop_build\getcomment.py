#!/usr/bin/env python3
import sys
import subprocess
import json
import os
import glob
from datetime import datetime, timedelta
import pandas as pd
import argparse

GERRIT_HOST = "********"
GERRIT_PORT = 29418
GERRIT_USER = "lijiajing"


def get_commits_between_tags(project, base_tag, build_tag):
    """
    获取两个tag之间的所有commit hash（不包含base_tag本身，包含build_tag指向的commit）。
    """
    print("base_tag:", base_tag)
    print("build_tag:", build_tag)
    
    print(f"[DEBUG] 检查目录 {project} 是否存在...")
    if not os.path.isdir(project):
        print(f"[ERROR] 目录 {project} 不存在！")
        sys.exit(1)
    git_dir = os.path.join(project, '.git')
    print(f"[DEBUG] 检查 {project} 是否为git仓库...")
    #if not os.path.isdir(git_dir):
    #    print(f"[ERROR] 目录 {project} 不是一个git仓库（缺少.git目录）！")
    #    sys.exit(1)
    # 检查tag是否存在
    for tag in [base_tag, build_tag]:
        tag_check_cmd = ["git", "-C", project, "rev-parse", "--verify", tag]
        print(f"[DEBUG] 检查tag是否存在: {' '.join(tag_check_cmd)}")
        tag_result = subprocess.run(tag_check_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, encoding='utf-8')
        if tag_result.returncode != 0:
            print(f"[ERROR] tag {tag} 不存在: {tag_result.stderr.strip()}")
            sys.exit(1)
    cmd = [
        "git", "-C", project, "log", f"{base_tag}..{build_tag}", "--pretty=format:%H"
    ]
    print(f"[DEBUG] 执行命令: {' '.join(cmd)}")
    try:
        result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True, universal_newlines=True)
        print(f"[DEBUG] git log 输出: {result.stdout.strip()}")
        commits = result.stdout.strip().split("\n") if result.stdout.strip() else []
        if not commits:
            print(f"[INFO] {base_tag}..{build_tag} 之间没有commit。")
        return commits
    except subprocess.CalledProcessError as e:
        print(f"[ERROR] 获取commit列表失败: {e.stderr}")
        sys.exit(1)


def get_gerrit_review_info(commit_hash):
    """
    通过ssh查询gerrit获取commit的详细评审信息，包含approvals、comments、files等。
    """
    query = f"commit:{commit_hash}"
    cmd = [
        'ssh',
        '-p', str(GERRIT_PORT),
        f'{GERRIT_USER}@{GERRIT_HOST}',
        'gerrit', 'query',
        '--format=JSON',
        '--all-approvals',
        '--no-limit',
        query
    ]
    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
            encoding='utf-8',
            errors='replace'
        )
        stdout, stderr = process.communicate()
        if process.returncode != 0:
            print(f"[ERROR] 查询Gerrit评审信息失败: {stderr.strip()}")
            return None
        # 解析JSON输出，过滤掉统计信息行
        reviews = []
        for line in stdout.strip().split('\n'):
            if line:
                try:
                    review = json.loads(line)
                    if 'id' in review:  # 过滤掉统计信息行
                        reviews.append(review)
                except json.JSONDecodeError:
                    continue
        return reviews[0] if reviews else None
    except Exception as e:
        print(f"[ERROR] 解析Gerrit返回信息失败: {e}")
        return None


def print_review_info(commit_hash, review_info):
    print(f"\nCommit: {commit_hash}")
    print(review_info)
    # if not review_info:
    #     print("  [未找到Gerrit评审信息]")
    #     return
    # print(f"  Subject: {review_info.get('subject', '')}")
    # print(f"  Change-Id: {review_info.get('id', '')}")
    # print(f"  Owner: {review_info.get('owner', {}).get('name', '')}")
    # print(f"  Status: {review_info.get('status', '')}")
    # print(f"  Approvals:")
    # if 'approvals' in review_info:
    #     for approval in review_info['approvals']:
    #         print(f"    Reviewer: {approval.get('by', {}).get('name', '')}")
    #         print(f"      Type: {approval.get('type', '')}")
    #         print(f"      Value: {approval.get('value', '')}")
    # else:
    #     print("    [无评审打分信息]")


def to_beijing_time(ts):
    try:
        # 只处理数字型时间戳
        if isinstance(ts, (int, float)) or (isinstance(ts, str) and ts.isdigit()):
            dt = datetime.utcfromtimestamp(int(ts)) + timedelta(hours=8)
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        return ts
    except Exception:
        return ts


def flatten_review_infos(data):
    flattened = []
    for item in data:
        commit = item.get('commit')
        review = item.get('review_info') or {}
        flat = {
            'commit': commit,
            'subject': review.get('subject', ''),
            'change_id': review.get('id', ''),
            'owner': review.get('owner', {}).get('name', ''),
            'status': review.get('status', ''),
            'branch': review.get('branch', ''),
            'project': review.get('project', ''),
            'createdOn': to_beijing_time(review.get('createdOn', '')),
            'lastUpdated': to_beijing_time(review.get('lastUpdated', '')),
            'url': review.get('url', ''),
        }
        # 只统计三种类型，只取最后一个patchSet的approvals
        approval_types = {'Code-Review': [], 'Verified': [], 'SUBM': []}
        patchsets = review.get('patchSets', [])
        last_patch = patchsets[-1] if patchsets else {}
        for a in last_patch.get('approvals', []):
            t = a.get('type', '')
            reviewer = a.get('by', {}).get('name', '')
            value = a.get('value', '')
            if t in approval_types:
                approval_types[t].append(f"{reviewer}:{value}")
        for t in approval_types:
            flat[t] = '; '.join(approval_types[t])
        # 兼容原有approvals列（只取最后一个patchSet）
        flat['approvals'] = '\n'.join([
            f"{a.get('by', {}).get('name', '')}:{a.get('type', '')}={a.get('value', '')}"
            for a in last_patch.get('approvals', [])
        ])
        # Comments 数量
        comments = review.get('comments', [])
        flat['comments_count'] = len(comments) if isinstance(comments, list) else 0
        # Files 数量
        files = review.get('files', [])
        flat['files_count'] = len(files) if isinstance(files, list) else 0
        flattened.append(flat)
    return flattened, ['Code-Review', 'Verified', 'SUBM']


def parse_repo_list_file(filepath):
    repo_paths = []
    with open(filepath, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            # 支持格式：project/path : /path/to/repo 或 /path/to/repo
            if ':' in line:
                parts = line.split(':')
                repo_path = parts[-1].strip()
            else:
                repo_path = line
            if repo_path and os.path.isdir(repo_path):
                repo_paths.append(repo_path)
    return repo_paths


def main():
    parser = argparse.ArgumentParser(description='Gerrit review info fetch and report')
    parser.add_argument('repo_list_file', nargs='?', help='仓库列表文件')
    parser.add_argument('base_tag', nargs='?', help='基线tag')
    parser.add_argument('build_tag', nargs='?', help='构建tag')
    parser.add_argument('--skip-fetch', action='store_true', help='跳过commit和gerrit信息抓取，直接用output目录下最新的all_*.json')
    args = parser.parse_args()

    if args.skip_fetch:
        # 必须有base_tag和build_tag参数
        if not (args.base_tag and args.build_tag):
            print(f"用法: python {sys.argv[0]} <repo_list_file> <base_tag> <build_tag> --skip-fetch")
            sys.exit(1)
        base_tag = args.base_tag
        build_tag = args.build_tag
        # 直接从output目录下最新的all_*.json加载
        import glob
        import re
        output_dir = 'output'
        json_files = glob.glob(os.path.join(output_dir, 'all_*.json'))
        if not json_files:
            print('[ERROR] output目录下未找到all_*.json文件！')
            sys.exit(1)
        # 按时间戳排序，取最新
        def extract_ts(f):
            m = re.search(r'all_.*_(\d{8}_\d{6})\.json', f)
            return m.group(1) if m else ''
        json_files.sort(key=lambda f: extract_ts(f), reverse=True)
        latest_json = json_files[0]
        print(f'[INFO] 跳过抓取，直接加载: {latest_json}')
        with open(latest_json, 'r', encoding='utf-8') as f:
            all_review_infos = json.load(f)
        # timestamp 依然用json文件名里的时间戳
        m = re.search(r'all_.*_.*_(\d{8}_\d{6})\.json', os.path.basename(latest_json))
        timestamp = m.group(1) if m else 'now'
        # 保证short_base_tag/short_build_tag在此分支也定义
        def short_tag(tag):
            return tag.rsplit('_', 1)[0] if '_' in tag else tag
        short_base_tag = short_tag(base_tag)
        short_build_tag = short_tag(build_tag)
    else:
        if not (args.repo_list_file and args.base_tag and args.build_tag):
            print(f"用法: python {sys.argv[0]} <repo_list_file> <base_tag> <build_tag> [--skip-fetch]")
            sys.exit(1)
        repo_list_file, base_tag, build_tag = args.repo_list_file, args.base_tag, args.build_tag
        repo_paths = parse_repo_list_file(repo_list_file)
        if not repo_paths:
            print(f"[ERROR] 未在 {repo_list_file} 中找到有效仓库路径！")
            sys.exit(1)
        print(f"共检测到{len(repo_paths)}个仓库，将依次处理...")
        all_review_infos = []  # 全部仓库的review信息
        for project in repo_paths:
            print(f"\n==== 处理仓库: {project} ====")
            commits = get_commits_between_tags(project, base_tag, build_tag)
            if not commits:
                print("[INFO] 未找到两个tag之间的commit。")
                continue
            print(f"共找到{len(commits)}个commit。开始查询Gerrit评审信息...")
            for commit in commits:
                review_info = get_gerrit_review_info(commit)
                print_review_info(commit, review_info)
                all_review_infos.append({'commit': commit, 'review_info': review_info})
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_dir = 'output'
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        # 统一输出到一个json文件
        # 文件名格式：all_<base_tag>_<build_tag>_<timestamp>.json，tag只保留最后一个下划线前面的部分
        def short_tag(tag):
            return tag.rsplit('_', 1)[0] if '_' in tag else tag
        short_base_tag = short_tag(base_tag)
        short_build_tag = short_tag(build_tag)
        output_file = os.path.join(output_dir, f"all_{short_base_tag}_{short_build_tag}_{timestamp}.json")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(all_review_infos, f, ensure_ascii=False, indent=2)
        print(f"所有review_info已保存到: {output_file}")
    # 统一导出为excel
    try:
        flat_data, approval_type_list = flatten_review_infos(all_review_infos)
        from openpyxl import Workbook, load_workbook
        from openpyxl.utils import get_column_letter
        excel_file = os.path.join('output', f"all_{short_base_tag}_{short_build_tag}_{timestamp}.xlsx")
        print("base_tag:", short_base_tag)
        print("timestamp:", timestamp)
        print("build_tag:", short_build_tag)
        # 指定列顺序
        col_order = [
            'project', 'branch', 'owner', 'change_id', 'commit', 'status', 'subject', 'url',
            'createdOn', 'lastUpdated', 'Code-Review', 'Verified', 'SUBM',
            'CodeReviewPassRate', 'approvals', 'comments_count', 'files_count'
        ]
        col_order = [col for col in col_order if any(col in row for row in flat_data)]
        other_cols = [col for col in flat_data[0] if col not in col_order]
        all_cols = col_order + other_cols
        wb = Workbook()
        ws = wb.active
        # Sheet1重命名为build_tag去掉最后一个下划线及其后面部分
        if '_' in short_build_tag:
            ws.title = short_build_tag.rsplit('_', 1)[0]
        else:
            ws.title = short_build_tag
        ws.append(all_cols)
        for row in flat_data:
            ws.append([row.get(col, '') for col in all_cols])
        # 隐藏G列（subject）
        if 'subject' in all_cols:
            ws.column_dimensions[get_column_letter(all_cols.index('subject')+1)].hidden = True
        # ====== 统计分析，写入Sheet2 ======
        df = pd.DataFrame(flat_data)
        report_rows = []
        for project, group in df.groupby('project'):
            total_commits = len(group)
            # 统计Code-Review分值
            cr_values = []
            for approvals in group['approvals']:
                for line in str(approvals).split('\n'):
                    if 'Code-Review' in line:
                        try:
                            value = int(line.split('=')[-1])
                            cr_values.append(value)
                        except:
                            continue
            cr_count = {v: cr_values.count(v) for v in [-2, -1, 0, 1, 2]}
            # 比例按合入笔数算
            cr2_ratio = cr_count[2] / total_commits if total_commits else 0
            cr1_ratio = cr_count[1] / total_commits if total_commits else 0
            cr_1_ratio = cr_count[-1] / total_commits if total_commits else 0
            # 通过率 = (CodeReview+2数+CodeReview+1数-CodeReview-1数)/(CodeReview+2数+CodeReview+1数-CodeReview-1数)
            cr_pass_numer = cr_count[2] + cr_count[1] - cr_count[-1]
            cr_pass_denom = cr_count[2] + cr_count[1] + cr_count[-1]
            cr_pass_rate = cr_pass_numer / cr_pass_denom if cr_pass_denom else 0
            report_rows.append({
                '仓库': project,
                '合入笔数': total_commits,
                'CodeReview+2数': cr_count[2],
                'CodeReview+1数': cr_count[1],
                'CodeReview-1数': cr_count[-1],
                'CodeReview-2数': cr_count[-2],
                'CodeReview+2比例': f"{cr2_ratio:.2%}",
                'CodeReview+1比例': f"{cr1_ratio:.2%}",
                'CodeReview-1比例': f"{cr_1_ratio:.2%}",
                'CodeReview通过率': f"{cr_pass_rate:.2%}",
            })
        # 写入Sheet2
        ws2 = wb.create_sheet('代码检视报告')
        if report_rows:
            ws2.append(list(report_rows[0].keys()))
            for row in report_rows:
                ws2.append(list(row.values()))
            # 设置宽度固定宽度（如18），加网格线、居中
            from openpyxl.styles import Alignment, Border, Side
            alignment = Alignment(horizontal='center', vertical='center')
            thin = Side(border_style="thin", color="000000")
            border = Border(left=thin, right=thin, top=thin, bottom=thin)
            for col in ws2.columns:
                col_letter = col[0].column_letter
                ws2.column_dimensions[col_letter].width = 18  # 固定宽度
                for cell in col:
                    cell.alignment = alignment
                    cell.border = border
            # 追加结论
            total_patch = sum(r['合入笔数'] for r in report_rows)
            total_cr2 = sum(r['CodeReview+2数'] for r in report_rows)
            total_cr_1 = sum(r['CodeReview-1数'] for r in report_rows)
            # 结论内容
            conclusion = f"本次版本累计合入patch：{total_patch}，CodeReview+2数累计：{total_cr2}，CodeReview-1数累计：{total_cr_1}；CodeReview评审通过率均通过95%以上，结论通过"
            ws2.append([])
            ws2.append([])
            ws2.append([conclusion])
            # 设置结论为斜体红色
            from openpyxl.styles import Font
            last_row = ws2.max_row
            cell = ws2.cell(row=last_row, column=1)
            cell.font = Font(italic=True, color='FF0000')
        wb.save(excel_file)
        print(f"已导出为Excel: {excel_file}")
        print(f"包含以下评审类型列: {', '.join(approval_type_list)}")
    except Exception as e:
        print(f"[WARN] 导出Excel失败: {e}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SonarQube项目查找和度量数据收集工具
集成了项目查找和度量数据收集功能
"""

import os
import sys
import json
import logging
import configparser
import requests
import subprocess
from datetime import datetime
import pg8000.native

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 读取配置文件
config = configparser.ConfigParser()
config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config.ini')
config.read(config_path)

# 数据库配置
DB_HOST = config.get('Database', 'host')
DB_PORT = config.get('Database', 'port')
DB_NAME = config.get('Database', 'name')
DB_USER = config.get('Database', 'user')
DB_PASS = config.get('Database', 'password')

# Gerrit配置
GERRIT_HOST = config.get('Gerrit', 'host')
GERRIT_PORT = config.get('Gerrit', 'port')
GERRIT_USER = config.get('Gerrit', 'user')

# SonarQube配置
# SONAR_URL = config.get('SonarQube', 'url')
# SONAR_TOKEN = config.get('SonarQube', 'token')
def get_sonarqube_config(section="SonarCO"):
    return {
        'url': config.get(section, 'url'),
        'token': config.get(section, 'token')
    }

SONAR_SECTION = "SonarCO"
SONAR_URL = get_sonarqube_config(SONAR_SECTION)['url']
SONAR_TOKEN = get_sonarqube_config(SONAR_SECTION)['token']

def search_sonar_projects(change_id_short):
    """
    搜索与特定Change ID相关的SonarQube项目
    
    Args:
        change_id_short: Change ID的短版本
        
    Returns:
        匹配的项目列表
    """
    try:
        if not SONAR_TOKEN:
            logger.error("SonarQube令牌未配置")
            return []
            
        # 构建API请求
        url = f"{SONAR_URL}/api/components/search"
        params = {
            "qualifiers": "TRK",  # 只搜索项目
            "ps": 100  # 每页结果数
        }
        auth = (SONAR_TOKEN, '')
        
        all_projects = []
        page = 1
        
        # 分页获取所有项目
        while True:
            params["p"] = page
            response = requests.get(url, params=params, auth=auth, timeout=30)
            
            if response.status_code != 200:
                logger.error(f"SonarQube API请求失败: {response.status_code}, {response.text}")
                return []
                
            data = response.json()
            components = data.get("components", [])
            all_projects.extend(components)
            
            # 检查是否还有更多页
            if len(all_projects) >= data.get("paging", {}).get("total", 0):
                break
                
            page += 1
        
        # 筛选匹配的项目
        matching_projects = []
        for project in all_projects:
            key = project.get("key", "")
            name = project.get("name", "")
            
            # 检查项目键或名称是否包含Change ID
            if change_id_short in key or change_id_short in name:
                matching_projects.append(project)
        
        return matching_projects
    except Exception as e:
        logger.error(f"搜索SonarQube项目异常: {e}")
        return []

def find_sonar_project_key(change_id_short):
    """
    查找与特定Change ID相关的SonarQube项目键
    
    Args:
        change_id_short: Change ID的短版本
        
    Returns:
        项目键，如果未找到则返回None
    """
    # 搜索项目
    projects = search_sonar_projects(change_id_short)
    
    if projects:
        # 找到匹配项目，返回第一个
        return projects[0].get("key")
    
    # 尝试常见的项目前缀
    common_prefixes = ["wearable-wr02-sonar-", "cycle-bg2-sonar-"]
    logger.info("尝试使用常见前缀查找项目...")
    
    for prefix in common_prefixes:
        project_key = f"{prefix}{change_id_short}"
        logger.info(f"尝试项目键: {project_key}")
        
        url = f"{SONAR_URL}/api/components/show"
        params = {"component": project_key}
        auth = (SONAR_TOKEN, '')
        
        try:
            response = requests.get(url, params=params, auth=auth, timeout=10)
            if response.status_code == 200:
                project = response.json().get("component", {})
                logger.info(f"找到匹配项目: {project.get('key')}")
                return project.get("key")
        except Exception:
            pass
    
    logger.warning(f"未找到与Change ID {change_id_short}相关的SonarQube项目")
    return None

def get_db_connection():
    """获取数据库连接"""
    try:
        conn = pg8000.native.Connection(
            user=DB_USER,
            password=DB_PASS,
            host=DB_HOST,
            port=int(DB_PORT),
            database=DB_NAME
        )
        return conn
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def get_gerrit_change_by_commit(commit_id):
    """直接通过commit_id获取Gerrit变更详情"""
    try:
        cmd = f'ssh -p {GERRIT_PORT} {GERRIT_USER}@{GERRIT_HOST} gerrit query --current-patch-set --all-approvals --files --comments --format=JSON commit:{commit_id}'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode != 0:
            logger.error(f"获取Gerrit变更详情失败: {result.stderr}")
            return None
        lines = result.stdout.strip().split('\n')
        if not lines:
            logger.error("Gerrit查询返回空结果")
            return None
        change_details = None
        for line in lines:
            try:
                obj = json.loads(line)
                if obj.get("type") == "stats":
                    continue
                change_details = obj
            except:
                continue
        return change_details
    except Exception as e:
        logger.error(f"获取Gerrit变更详情异常: {e}")
        return None

def get_sonar_project_info(project_key):
    """获取SonarQube项目信息"""
    try:
        if not SONAR_TOKEN:
            logger.error("SonarQube令牌未配置")
            return None
            
        # 构建API请求
        url = f"{SONAR_URL}/api/components/show"
        params = {
            "component": project_key
        }
        auth = (SONAR_TOKEN, '')
        
        response = requests.get(url, params=params, auth=auth, timeout=30)
        
        if response.status_code != 200:
            logger.error(f"SonarQube API请求失败: {response.status_code}, {response.text}")
            return None
            
        data = response.json()
        component = data.get("component", {})
        
        # 提取项目信息
        project_info = {
            "name": component.get("name", ""),
            "key": component.get("key", ""),
            "creation_date": component.get("analysisDate", ""),
            "last_analysis_date": component.get("lastAnalysisDate", "")
        }
        
        return project_info
    except Exception as e:
        logger.error(f"获取SonarQube项目信息异常: {e}")
        return None

def get_sonar_issues(project_key):
    """获取SonarQube项目的所有问题"""
    try:
        if not SONAR_TOKEN:
            logger.error("SonarQube令牌未配置")
            return None
            
        # 构建API请求 - 获取当前问题
        url = f"{SONAR_URL}/api/issues/search"
        params = {
            "componentKeys": project_key,
            "ps": 500,  # 每页结果数
            "resolved": "false"  # 只获取未解决的问题
        }
        auth = (SONAR_TOKEN, '')
        
        current_issues = []
        page = 1
        
        # 分页获取所有当前问题
        while True:
            params["p"] = page
            response = requests.get(url, params=params, auth=auth, timeout=30)
            
            if response.status_code != 200:
                logger.error(f"SonarQube API请求失败: {response.status_code}, {response.text}")
                return None
                
            data = response.json()
            issues = data.get("issues", [])
            current_issues.extend(issues)
            
            # 检查是否还有更多页
            total = data.get("total", 0)
            if len(current_issues) >= total:
                break
                
            page += 1
        
        # 获取已解决的问题
        params["resolved"] = "true"
        resolved_issues = []
        page = 1
        
        # 分页获取所有已解决问题
        while True:
            params["p"] = page
            response = requests.get(url, params=params, auth=auth, timeout=30)
            
            if response.status_code != 200:
                logger.error(f"SonarQube API请求失败: {response.status_code}, {response.text}")
                return None
                
            data = response.json()
            issues = data.get("issues", [])
            resolved_issues.extend(issues)
            
            # 检查是否还有更多页
            total = data.get("total", 0)
            if len(resolved_issues) >= total:
                break
                
            page += 1
        
        # 合并问题列表
        return {
            "current": current_issues,
            "resolved": resolved_issues
        }
    except Exception as e:
        logger.error(f"获取SonarQube问题异常: {e}")
        return None

def extract_reviewers(gerrit_data):
    """从Gerrit数据中提取评审人列表"""
    reviewers = set()
    
    # 从approvals中提取
    if "patchSets" in gerrit_data:
        for patchset in gerrit_data["patchSets"]:
            if "approvals" in patchset:
                for approval in patchset["approvals"]:
                    if "by" in approval and "name" in approval["by"]:
                        reviewers.add(approval["by"]["name"])
    
    # 从comments中提取
    if "comments" in gerrit_data:
        for comment in gerrit_data["comments"]:
            if "reviewer" in comment and "name" in comment["reviewer"]:
                reviewers.add(comment["reviewer"]["name"])
    
    # 移除作者自己
    if "owner" in gerrit_data and "name" in gerrit_data["owner"]:
        author = gerrit_data["owner"]["name"]
        if author in reviewers:
            reviewers.remove(author)
    
    return list(reviewers)

def count_sonar_issues_by_severity(sonar_issues):
    """按严重程度统计SonarQube问题"""
    if not sonar_issues:
        logger.warning("没有SonarQube问题数据可统计")
        return {
            "current": {},
            "resolved": {}
        }
    
    # 初始化计数器
    current_counts = {
        "BLOCKER": 0,
        "CRITICAL": 0,
        "MAJOR": 0,
        "MINOR": 0,
        "INFO": 0
    }
    
    resolved_counts = {
        "BLOCKER": 0,
        "CRITICAL": 0,
        "MAJOR": 0,
        "MINOR": 0,
        "INFO": 0
    }
    
    # 统计当前问题
    for issue in sonar_issues.get("current", []):
        severity = issue.get("severity", "")
        if severity in current_counts:
            current_counts[severity] += 1
    
    # 统计已解决问题
    for issue in sonar_issues.get("resolved", []):
        severity = issue.get("severity", "")
        if severity in resolved_counts:
            resolved_counts[severity] += 1
    
    # 记录统计结果
    logger.info(f"SonarQube问题统计 - 当前问题: {current_counts}")
    logger.info(f"SonarQube问题统计 - 已解决问题: {resolved_counts}")
    
    return {
        "current": current_counts,
        "resolved": resolved_counts
    }

def get_sonar_measures(project_key):
    """获取SonarQube项目的度量指标（扩展版，包含更多代码质量指标）"""
    try:
        if not SONAR_TOKEN:
            logger.error("SonarQube令牌未配置")
            return None
        
        # 需要采集的所有指标
        metric_keys = [
            "bugs", "vulnerabilities", "code_smells", "blocker_violations", "critical_violations", "major_violations", "minor_violations", "info_violations",
            "ncloc", "statements", "functions", "files", "comment_lines", "comment_lines_density", "complexity",
            "duplicated_lines_density", "duplicated_lines", "duplicated_blocks", "duplicated_files"
        ]
        # 构建API请求
        url = f"{SONAR_URL}/api/measures/component"
        params = {
            "component": project_key,
            "metricKeys": ",".join(metric_keys)
        }
        auth = (SONAR_TOKEN, '')
        
        response = requests.get(url, params=params, auth=auth, timeout=30)
        
        if response.status_code != 200:
            logger.error(f"SonarQube API请求失败: {response.status_code}, {response.text}")
            return None
            
        data = response.json()
        measures = data.get("component", {}).get("measures", [])
        
        # 转换为字典格式
        measures_dict = {}
        for measure in measures:
            metric = measure.get("metric")
            value = measure.get("value")
            # 注释率、重复密度等为百分比，需转为float，其余多为int
            if metric in ["comment_lines_density", "duplicated_lines_density"]:
                try:
                    measures_dict[metric] = float(value)
                except Exception:
                    measures_dict[metric] = None
            else:
                try:
                    measures_dict[metric] = int(value)
                except Exception:
                    measures_dict[metric] = value
        
        logger.info(f"获取到的度量指标: {measures_dict}")
        
        return measures_dict
    except Exception as e:
        logger.error(f"获取SonarQube度量指标异常: {str(e)}")
        return None

def store_commit_metrics(commit_id, sonar_project_key=None):
    """存储提交度量数据到宽表（以commit_id为唯一主键）"""
    try:
        # 获取Gerrit变更详情
        gerrit_data = get_gerrit_change_by_commit(commit_id)
        if not gerrit_data:
            logger.error(f"无法获取Gerrit变更详情: {commit_id}")
            return False
        
        # 提取基本信息
        change_id = gerrit_data.get("id", "")
        branch = gerrit_data.get("branch", "")
        gerrit_project = gerrit_data.get("project", "")
        subject = gerrit_data.get("subject", "")
        status = gerrit_data.get("status", "")
        patch_set = int(gerrit_data.get("currentPatchSet", {}).get("number", 0))
        created_timestamp = gerrit_data.get("createdOn", 0)
        created_time = datetime.fromtimestamp(created_timestamp)
        updated_timestamp = gerrit_data.get("lastUpdated", 0)
        updated_time = datetime.fromtimestamp(updated_timestamp)
        owner = gerrit_data.get("owner", {}).get("name", "Unknown")
        author = owner
        number = gerrit_data.get("number", 0)
        url = gerrit_data.get("url", "")
        patchset_count = len(gerrit_data.get("patchSets", []))
        reviewers = extract_reviewers(gerrit_data)
        insertions = 0
        deletions = 0
        if "currentPatchSet" in gerrit_data and "files" in gerrit_data["currentPatchSet"]:
            files = gerrit_data["currentPatchSet"]["files"]
            for file_info in files:
                insertions += file_info.get("insertions", 0)
                deletions += abs(file_info.get("deletions", 0))
        changed_lines = insertions + deletions
        repo_path = gerrit_project
        change_id_short = change_id[:8] if len(change_id) >= 8 else change_id
        
        # SonarQube项目键
        if not sonar_project_key:
            sonar_project_key = find_sonar_project_key(change_id_short)
            if not sonar_project_key:
                logger.warning(f"未找到与Change ID {change_id_short}相关的SonarQube项目，将使用默认值")
                sonar_project_key = f"wearable-wr02-sonar-{change_id_short}"
        logger.info(f"使用SonarQube项目键: {sonar_project_key}")
        
        # 获取SonarQube项目信息
        sonar_project_info = get_sonar_project_info(sonar_project_key)
        sonar_project_name = sonar_project_info.get("name", "") if sonar_project_info else ""
        sonar_key = sonar_project_info.get("key", "") if sonar_project_info else ""
        sonar_creation_date = None
        if sonar_project_info:
            creation_date_str = sonar_project_info.get("creation_date", "")
            if creation_date_str:
                try:
                    if '+' in creation_date_str and len(creation_date_str.split('+')[1]) == 4:
                        timezone_part = creation_date_str.split('+')[1]
                        hours = timezone_part[:2]
                        minutes = timezone_part[2:]
                        creation_date_str = creation_date_str.split('+')[0] + f"+{hours}:{minutes}"
                    sonar_creation_date = datetime.fromisoformat(creation_date_str.replace('Z', '+00:00'))
                except ValueError:
                    logger.warning(f"无法解析SonarQube创建日期: {creation_date_str}")
        
        # 获取SonarQube问题和度量
        sonar_issues = get_sonar_issues(sonar_project_key)
        sonar_measures = get_sonar_measures(sonar_project_key)
        issue_counts = count_sonar_issues_by_severity(sonar_issues)
        
        # 统计问题数量
        current_issues = issue_counts.get("current", {})
        resolved_issues = issue_counts.get("resolved", {})
        if sonar_measures:
            sq_blocker = sonar_measures.get("blocker_violations", current_issues.get("BLOCKER", 0))
            sq_critical = sonar_measures.get("critical_violations", current_issues.get("CRITICAL", 0))
            sq_major = sonar_measures.get("major_violations", current_issues.get("MAJOR", 0))
            sq_minor = sonar_measures.get("minor_violations", current_issues.get("MINOR", 0))
            sq_info = sonar_measures.get("info_violations", current_issues.get("INFO", 0))
        else:
            sq_blocker = current_issues.get("BLOCKER", 0)
            sq_critical = current_issues.get("CRITICAL", 0)
            sq_major = current_issues.get("MAJOR", 0)
            sq_minor = current_issues.get("MINOR", 0)
            sq_info = current_issues.get("INFO", 0)
        
        # 代码质量指标
        ncloc = sonar_measures.get("ncloc") if sonar_measures else None
        statements = sonar_measures.get("statements") if sonar_measures else None
        functions = sonar_measures.get("functions") if sonar_measures else None
        files = sonar_measures.get("files") if sonar_measures else None
        comment_lines = sonar_measures.get("comment_lines") if sonar_measures else None
        comment_lines_density = sonar_measures.get("comment_lines_density") if sonar_measures else None
        complexity = sonar_measures.get("complexity") if sonar_measures else None
        duplicated_lines_density = sonar_measures.get("duplicated_lines_density") if sonar_measures else None
        duplicated_lines = sonar_measures.get("duplicated_lines") if sonar_measures else None
        duplicated_blocks = sonar_measures.get("duplicated_blocks") if sonar_measures else None
        duplicated_files = sonar_measures.get("duplicated_files") if sonar_measures else None
        
        # 计算派生字段
        total_issues = sq_blocker + sq_critical + sq_major + sq_minor + sq_info
        total_resolved_issues = resolved_issues.get("BLOCKER", 0) + resolved_issues.get("CRITICAL", 0) + resolved_issues.get("MAJOR", 0) + resolved_issues.get("MINOR", 0) + resolved_issues.get("INFO", 0)
        critical_issues = sq_blocker + sq_critical
        issue_density = 0
        if ncloc and ncloc > 0:
            issue_density = total_issues / ncloc * 1000
        if changed_lines <= 10:
            change_size_category = 'small'
        elif changed_lines <= 50:
            change_size_category = 'medium'
        elif changed_lines <= 200:
            change_size_category = 'large'
        else:
            change_size_category = 'huge'
        if sq_blocker > 0 or sq_critical > 0:
            quality_level = 'critical'
        elif sq_major > 0:
            quality_level = 'major'
        elif sq_minor > 0:
            quality_level = 'minor'
        else:
            quality_level = 'clean'
        commit_date = updated_time.date()
        commit_year = updated_time.year
        commit_month = updated_time.month
        commit_week = updated_time.isocalendar()[1]
        commit_hour = updated_time.hour
        issues_json = {
            "current": {
                "BLOCKER": sq_blocker,
                "CRITICAL": sq_critical,
                "MAJOR": sq_major,
                "MINOR": sq_minor,
                "INFO": sq_info
            },
            "resolved": {
                "BLOCKER": resolved_issues.get("BLOCKER", 0),
                "CRITICAL": resolved_issues.get("CRITICAL", 0),
                "MAJOR": resolved_issues.get("MAJOR", 0),
                "MINOR": resolved_issues.get("MINOR", 0),
                "INFO": resolved_issues.get("INFO", 0)
            }
        }
        
        # 数据库操作
        conn = get_db_connection()
        if not conn:
            logger.error("无法连接到数据库")
            return False
        try:
            # 检查是否已存在
            check_result = conn.run(
                "SELECT 1 FROM commit_metrics WHERE commit_id = :commit_id",
                commit_id=commit_id
            )
            record_exists = len(check_result) > 0
            if record_exists:
                logger.info("将执行UPDATE操作")
                conn.run("""
                    UPDATE commit_metrics SET
                        change_id = :change_id,
                        change_id_short = :change_id_short,
                        patch_set = :patch_set,
                        gerrit_project = :gerrit_project,
                        branch = :branch,
                        subject = :subject,
                        status = :status,
                        commit_time = :commit_time,
                        created = :created,
                        updated = :updated,
                        author = :author,
                        owner = :owner,
                        number = :number,
                        url = :url,
                        reviewers = :reviewers,
                        changed_lines = :changed_lines,
                        insertions = :insertions,
                        deletions = :deletions,
                        patchset_count = :patchset_count,
                        repo_path = :repo_path,
                        sonar_project = :sonar_project,
                        sonar_key = :sonar_key,
                        sonar_creation_date = :sonar_creation_date,
                        sq_blocker = :sq_blocker,
                        sq_critical = :sq_critical,
                        sq_major = :sq_major,
                        sq_minor = :sq_minor,
                        sq_info = :sq_info,
                        sq_resolved_blocker = :sq_resolved_blocker,
                        sq_resolved_critical = :sq_resolved_critical,
                        sq_resolved_major = :sq_resolved_major,
                        sq_resolved_minor = :sq_resolved_minor,
                        sq_resolved_info = :sq_resolved_info,
                        gerrit_raw = :gerrit_raw,
                        sonar_issues = :sonar_issues,
                        ncloc = :ncloc,
                        statements = :statements,
                        functions = :functions,
                        files = :files,
                        comment_lines = :comment_lines,
                        comment_lines_density = :comment_lines_density,
                        complexity = :complexity,
                        duplicated_lines_density = :duplicated_lines_density,
                        duplicated_lines = :duplicated_lines,
                        duplicated_blocks = :duplicated_blocks,
                        duplicated_files = :duplicated_files,
                        commit_date = :commit_date,
                        commit_year = :commit_year,
                        commit_month = :commit_month,
                        commit_week = :commit_week,
                        commit_hour = :commit_hour,
                        total_issues = :total_issues,
                        total_resolved_issues = :total_resolved_issues,
                        critical_issues = :critical_issues,
                        issue_density = :issue_density,
                        change_size_category = :change_size_category,
                        quality_level = :quality_level,
                        updated_at = NOW()
                    WHERE commit_id = :commit_id
                """,
                change_id=change_id,
                change_id_short=change_id_short,
                patch_set=patch_set,
                gerrit_project=gerrit_project,
                branch=branch,
                subject=subject,
                status=status,
                commit_time=updated_time,
                created=created_time,
                updated=updated_time,
                author=author,
                owner=owner,
                number=number,
                url=url,
                reviewers=reviewers,
                changed_lines=changed_lines,
                insertions=insertions,
                deletions=deletions,
                patchset_count=patchset_count,
                repo_path=repo_path,
                sonar_project=sonar_project_name,
                sonar_key=sonar_key,
                sonar_creation_date=sonar_creation_date,
                sq_blocker=sq_blocker,
                sq_critical=sq_critical,
                sq_major=sq_major,
                sq_minor=sq_minor,
                sq_info=sq_info,
                sq_resolved_blocker=resolved_issues.get("BLOCKER", 0),
                sq_resolved_critical=resolved_issues.get("CRITICAL", 0),
                sq_resolved_major=resolved_issues.get("MAJOR", 0),
                sq_resolved_minor=resolved_issues.get("MINOR", 0),
                sq_resolved_info=resolved_issues.get("INFO", 0),
                gerrit_raw=json.dumps(gerrit_data),
                sonar_issues=json.dumps(issues_json),
                ncloc=ncloc,
                statements=statements,
                functions=functions,
                files=files,
                comment_lines=comment_lines,
                comment_lines_density=comment_lines_density,
                complexity=complexity,
                duplicated_lines_density=duplicated_lines_density,
                duplicated_lines=duplicated_lines,
                duplicated_blocks=duplicated_blocks,
                duplicated_files=duplicated_files,
                commit_date=commit_date,
                commit_year=commit_year,
                commit_month=commit_month,
                commit_week=commit_week,
                commit_hour=commit_hour,
                total_issues=total_issues,
                total_resolved_issues=total_resolved_issues,
                critical_issues=critical_issues,
                issue_density=issue_density,
                change_size_category=change_size_category,
                quality_level=quality_level,
                commit_id=commit_id
                )
            else:
                logger.info("将执行INSERT操作")
                conn.run("""
                    INSERT INTO commit_metrics (
                        commit_id, change_id, change_id_short, patch_set, gerrit_project, branch, subject, status, commit_time, created, updated, author, owner, number, url, reviewers, changed_lines, insertions, deletions, patchset_count, repo_path, sonar_project, sonar_key, sonar_creation_date, sq_blocker, sq_critical, sq_major, sq_minor, sq_info, sq_resolved_blocker, sq_resolved_critical, sq_resolved_major, sq_resolved_minor, sq_resolved_info, gerrit_raw, sonar_issues, ncloc, statements, functions, files, comment_lines, comment_lines_density, complexity, duplicated_lines_density, duplicated_lines, duplicated_blocks, duplicated_files, commit_date, commit_year, commit_month, commit_week, commit_hour, total_issues, total_resolved_issues, critical_issues, issue_density, change_size_category, quality_level
                    ) VALUES (
                        :commit_id, :change_id, :change_id_short, :patch_set, :gerrit_project, :branch, :subject, :status, :commit_time, :created, :updated, :author, :owner, :number, :url, :reviewers, :changed_lines, :insertions, :deletions, :patchset_count, :repo_path, :sonar_project, :sonar_key, :sonar_creation_date, :sq_blocker, :sq_critical, :sq_major, :sq_minor, :sq_info, :sq_resolved_blocker, :sq_resolved_critical, :sq_resolved_major, :sq_resolved_minor, :sq_resolved_info, :gerrit_raw, :sonar_issues, :ncloc, :statements, :functions, :files, :comment_lines, :comment_lines_density, :complexity, :duplicated_lines_density, :duplicated_lines, :duplicated_blocks, :duplicated_files, :commit_date, :commit_year, :commit_month, :commit_week, :commit_hour, :total_issues, :total_resolved_issues, :critical_issues, :issue_density, :change_size_category, :quality_level
                    )
                """,
                commit_id=commit_id,
                change_id=change_id,
                change_id_short=change_id_short,
                patch_set=patch_set,
                gerrit_project=gerrit_project,
                branch=branch,
                subject=subject,
                status=status,
                commit_time=updated_time,
                created=created_time,
                updated=updated_time,
                author=author,
                owner=owner,
                number=number,
                url=url,
                reviewers=reviewers,
                changed_lines=changed_lines,
                insertions=insertions,
                deletions=deletions,
                patchset_count=patchset_count,
                repo_path=repo_path,
                sonar_project=sonar_project_name,
                sonar_key=sonar_key,
                sonar_creation_date=sonar_creation_date,
                sq_blocker=sq_blocker,
                sq_critical=sq_critical,
                sq_major=sq_major,
                sq_minor=sq_minor,
                sq_info=sq_info,
                sq_resolved_blocker=resolved_issues.get("BLOCKER", 0),
                sq_resolved_critical=resolved_issues.get("CRITICAL", 0),
                sq_resolved_major=resolved_issues.get("MAJOR", 0),
                sq_resolved_minor=resolved_issues.get("MINOR", 0),
                sq_resolved_info=resolved_issues.get("INFO", 0),
                gerrit_raw=json.dumps(gerrit_data),
                sonar_issues=json.dumps(issues_json),
                ncloc=ncloc,
                statements=statements,
                functions=functions,
                files=files,
                comment_lines=comment_lines,
                comment_lines_density=comment_lines_density,
                complexity=complexity,
                duplicated_lines_density=duplicated_lines_density,
                duplicated_lines=duplicated_lines,
                duplicated_blocks=duplicated_blocks,
                duplicated_files=duplicated_files,
                commit_date=commit_date,
                commit_year=commit_year,
                commit_month=commit_month,
                commit_week=commit_week,
                commit_hour=commit_hour,
                total_issues=total_issues,
                total_resolved_issues=total_resolved_issues,
                critical_issues=critical_issues,
                issue_density=issue_density,
                change_size_category=change_size_category,
                quality_level=quality_level
                )
            logger.info(f"成功存储提交度量数据: {commit_id}")
            return True
        except Exception as e:
            logger.error(f"存储提交度量数据失败: {e}")
            logger.exception("详细异常信息:")
            return False
    except Exception as e:
        logger.error(f"处理提交度量数据异常: {e}")
        return False

def main():
    import argparse
    parser = argparse.ArgumentParser(description="SonarQube项目查找和度量数据收集工具（以commit_id为主键）")
    parser.add_argument("commit_id", help="Gerrit Commit ID")
    parser.add_argument("--find-only", action="store_true", help="仅查找SonarQube项目，不收集度量数据")
    parser.add_argument("--project-key", help="指定SonarQube项目键，跳过查找步骤")
    args = parser.parse_args()
    commit_id = args.commit_id
    logger.info(f"开始处理Commit ID: {commit_id}")
    if args.find_only:
        # 仅查找SonarQube项目
        change_id_short = commit_id[:8] if len(commit_id) >= 8 else commit_id
        logger.info(f"搜索与Commit ID {commit_id}相关的SonarQube项目")
        projects = search_sonar_projects(change_id_short)
        if not projects:
            logger.info(f"未找到与Commit ID {commit_id}相关的SonarQube项目")
            return
        logger.info(f"找到 {len(projects)} 个匹配项目:")
        for i, project in enumerate(projects, 1):
            key = project.get("key", "")
            name = project.get("name", "")
            print(f"{i}. 项目键: {key}")
            print(f"   项目名称: {name}")
    else:
        # 收集度量数据
        project_key = args.project_key
        success = store_commit_metrics(commit_id, project_key)
        if success:
            logger.info(f"成功处理Commit ID: {commit_id}")
        else:
            logger.error(f"处理Commit ID失败: {commit_id}")

if __name__ == "__main__":
    main()

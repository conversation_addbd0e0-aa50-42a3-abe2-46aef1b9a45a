{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  COUNT(*) as \"总审查数\",\n  COUNT(CASE WHEN status = 'MERGED' THEN 1 END) as \"已合并数\",\n  ROUND(COUNT(CASE WHEN status = 'MERGED' THEN 1 END)::float / COUNT(*) * 100, 2) as \"合并率%\",\n  ROUND(AVG(patchset_count), 1) as \"平均修订次数\",\n  ROUND(AVG(EXTRACT(EPOCH FROM (updated - created))/3600), 1) as \"平均审查时长(小时)\",\n  COUNT(CASE WHEN patchset_count = 1 THEN 1 END) as \"一次通过数\"\nFROM commit_metrics \nWHERE commit_time >= NOW() - INTERVAL '30 days'", "refId": "A"}], "title": "代码审查效率关键指标 (最近30天)", "type": "stat"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "time_series", "rawQuery": true, "rawSql": "SELECT \n  commit_date as time,\n  COUNT(*) as \"每日审查数\",\n  COUNT(CASE WHEN status = 'MERGED' THEN 1 END) as \"每日合并数\",\n  ROUND(AVG(patchset_count), 1) as \"平均修订次数\"\nFROM commit_metrics \nWHERE commit_date >= CURRENT_DATE - INTERVAL '30 days'\nGROUP BY commit_date\nORDER BY commit_date", "refId": "A"}], "title": "每日审查活动趋势", "type": "timeseries"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6}, "id": 3, "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "pieType": "pie", "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  CASE \n    WHEN patchset_count = 1 THEN '一次通过'\n    WHEN patchset_count = 2 THEN '二次修订'\n    WHEN patchset_count = 3 THEN '三次修订'\n    WHEN patchset_count BETWEEN 4 AND 5 THEN '4-5次修订'\n    ELSE '6次以上修订'\n  END as metric,\n  COUNT(*) as value\nFROM commit_metrics\nWHERE commit_time >= NOW() - INTERVAL '30 days'\nGROUP BY \n  CASE \n    WHEN patchset_count = 1 THEN '一次通过'\n    WHEN patchset_count = 2 THEN '二次修订'\n    WHEN patchset_count = 3 THEN '三次修订'\n    WHEN patchset_count BETWEEN 4 AND 5 THEN '4-5次修订'\n    ELSE '6次以上修订'\n  END\nORDER BY COUNT(*) DESC", "refId": "A"}], "title": "修订次数分布", "type": "piechart"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "合并率%"}, "properties": [{"id": "unit", "value": "percent"}, {"id": "custom.displayMode", "value": "color-background"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 70}, {"color": "green", "value": 90}]}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 14}, "id": 4, "options": {"showHeader": true}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  author as \"开发者\",\n  COUNT(*) as \"提交数\",\n  COUNT(CASE WHEN status = 'MERGED' THEN 1 END) as \"合并数\",\n  ROUND(COUNT(CASE WHEN status = 'MERGED' THEN 1 END)::float / COUNT(*) * 100, 2) as \"合并率%\",\n  ROUND(AVG(patchset_count), 1) as \"平均修订次数\",\n  COUNT(CASE WHEN patchset_count = 1 THEN 1 END) as \"一次通过数\",\n  ROUND(AVG(EXTRACT(EPOCH FROM (updated - created))/3600), 1) as \"平均审查时长(小时)\"\nFROM commit_metrics \nWHERE commit_time >= NOW() - INTERVAL '30 days'\nGROUP BY author\nHAVING COUNT(*) >= 3\nORDER BY \"合并率%\" DESC, \"提交数\" DESC\nLIMIT 15", "refId": "A"}], "title": "开发者审查效率排行", "type": "table"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 14}, "id": 5, "options": {"showHeader": true}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  gerrit_project as \"项目名称\",\n  COUNT(*) as \"审查数\",\n  COUNT(CASE WHEN status = 'MERGED' THEN 1 END) as \"合并数\",\n  ROUND(COUNT(CASE WHEN status = 'MERGED' THEN 1 END)::float / COUNT(*) * 100, 2) as \"合并率%\",\n  ROUND(AVG(patchset_count), 1) as \"平均修订次数\",\n  COUNT(CASE WHEN patchset_count = 1 THEN 1 END) as \"一次通过数\",\n  COUNT(DISTINCT author) as \"参与开发者数\"\nFROM commit_metrics \nWHERE commit_time >= NOW() - INTERVAL '30 days'\nGROUP BY gerrit_project\nHAVING COUNT(*) >= 5\nORDER BY \"合并率%\" DESC, \"审查数\" DESC\nLIMIT 15", "refId": "A"}], "title": "项目审查效率排行", "type": "table"}], "refresh": "5m", "schemaVersion": 36, "style": "dark", "tags": ["代码审查", "效率分析"], "templating": {"list": []}, "time": {"from": "now-30d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "代码审查效率分析仪表板", "uid": "code-review-efficiency", "version": 1, "weekStart": ""}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import streamlit as st
from config import get_database_engine

class DatabaseManager:
    def __init__(self):
        self.engine = get_database_engine()
    
    @st.cache_data(ttl=300)  # 缓存5分钟
    def execute_query(_self, query, params=None):
        """执行SQL查询并返回DataFrame"""
        try:
            return pd.read_sql(query, _self.engine, params=params)
        except Exception as e:
            st.error(f"数据库查询错误: {e}")
            return pd.DataFrame()
    
    def get_overview_stats(self, days=30):
        """获取概览统计数据"""
        query = """
        SELECT 
            COUNT(*) as total_commits,
            COUNT(DISTINCT author) as total_authors,
            COUNT(DISTINCT gerrit_project) as total_projects,
            SUM(changed_lines) as total_changed_lines,
            SUM(total_issues) as total_issues,
            SUM(critical_issues) as total_critical_issues,
            ROUND(AVG(issue_density)::numeric, 2) as avg_issue_density,
            COUNT(CASE WHEN quality_level = 'clean' THEN 1 END) as clean_commits,
            ROUND((COUNT(CASE WHEN quality_level = 'clean' THEN 1 END)::float / NULLIF(COUNT(*), 0) * 100)::numeric, 2) as clean_rate
        FROM commit_metrics 
        WHERE commit_time >= NOW() - INTERVAL '%s days'
        """
        return self.execute_query(query % days)
    
    def get_daily_trends(self, days=30):
        """获取每日趋势数据"""
        query = """
        SELECT 
            commit_date,
            total_commits,
            total_issues,
            total_critical_issues,
            avg_issue_density
        FROM daily_commit_stats 
        WHERE commit_date >= CURRENT_DATE - INTERVAL '%s days'
        ORDER BY commit_date
        """
        return self.execute_query(query % days)
    
    def get_monthly_trends(self, months=12):
        """获取月度趋势数据"""
        query = """
        SELECT 
            month_start,
            total_commits,
            unique_authors,
            total_issues,
            clean_rate_percent
        FROM monthly_trend_stats 
        WHERE month_start >= CURRENT_DATE - INTERVAL '%s months'
        ORDER BY month_start
        """
        return self.execute_query(query % months)
    
    def get_quality_distribution(self):
        """获取质量等级分布"""
        query = """
        SELECT 
            quality_level,
            commit_count,
            percentage
        FROM quality_level_distribution
        ORDER BY 
            CASE quality_level 
                WHEN 'clean' THEN 1 
                WHEN 'minor' THEN 2 
                WHEN 'major' THEN 3 
                WHEN 'critical' THEN 4 
            END
        """
        return self.execute_query(query)
    
    def get_severity_distribution(self, days=30):
        """获取问题严重程度分布"""
        query = """
        SELECT 
            'Blocker' as severity, SUM(sq_blocker) as count
        FROM commit_metrics 
        WHERE commit_time >= NOW() - INTERVAL '%s days'
        UNION ALL
        SELECT 'Critical', SUM(sq_critical)
        FROM commit_metrics 
        WHERE commit_time >= NOW() - INTERVAL '%s days'
        UNION ALL
        SELECT 'Major', SUM(sq_major)
        FROM commit_metrics 
        WHERE commit_time >= NOW() - INTERVAL '%s days'
        UNION ALL
        SELECT 'Minor', SUM(sq_minor)
        FROM commit_metrics 
        WHERE commit_time >= NOW() - INTERVAL '%s days'
        UNION ALL
        SELECT 'Info', SUM(sq_info)
        FROM commit_metrics 
        WHERE commit_time >= NOW() - INTERVAL '%s days'
        """
        return self.execute_query(query % (days, days, days, days, days))
    
    def get_author_stats(self, limit=20):
        """获取开发者统计"""
        query = """
        SELECT 
            author,
            total_commits,
            total_changed_lines,
            avg_changed_lines,
            total_issues,
            critical_issues,
            avg_issue_density,
            clean_rate_percent
        FROM author_contribution_stats 
        WHERE total_commits >= 3
        ORDER BY total_commits DESC 
        LIMIT %s
        """
        return self.execute_query(query % limit)
    
    def get_project_stats(self, limit=15):
        """获取项目统计"""
        query = """
        SELECT 
            gerrit_project,
            total_commits,
            unique_authors,
            total_changed_lines,
            total_issues,
            critical_issues,
            avg_issue_density,
            avg_comment_density,
            avg_duplication_density,
            clean_rate_percent
        FROM project_quality_stats 
        WHERE total_commits >= 5
        ORDER BY total_commits DESC 
        LIMIT %s
        """
        return self.execute_query(query % limit)
    
    def get_change_size_analysis(self, days=30):
        """获取变更大小分析"""
        query = """
        SELECT 
            changed_lines,
            total_issues,
            issue_density,
            quality_level,
            change_size_category,
            author,
            gerrit_project
        FROM commit_metrics 
        WHERE commit_time >= NOW() - INTERVAL '%s days'
        ORDER BY changed_lines
        """
        return self.execute_query(query % days)
    
    def get_high_risk_commits(self, days=7):
        """获取高风险提交"""
        query = """
        SELECT 
            commit_id,
            change_id_short,
            author,
            gerrit_project,
            subject,
            changed_lines,
            total_issues,
            critical_issues,
            quality_level,
            commit_time
        FROM commit_metrics 
        WHERE (sq_blocker > 0 OR sq_critical > 0)
          AND commit_time >= NOW() - INTERVAL '%s days'
        ORDER BY critical_issues DESC, commit_time DESC
        LIMIT 20
        """
        return self.execute_query(query % days)
    
    def get_complexity_analysis(self):
        """获取复杂度分析"""
        query = """
        SELECT 
            CASE 
                WHEN complexity <= 10 THEN '0-10'
                WHEN complexity <= 50 THEN '11-50'
                WHEN complexity <= 100 THEN '51-100'
                WHEN complexity <= 200 THEN '101-200'
                ELSE '200+'
            END as complexity_range,
            COUNT(*) as count,
            ROUND(AVG(total_issues)::numeric, 2) as avg_issues
        FROM commit_metrics 
        WHERE complexity IS NOT NULL
        GROUP BY complexity_range
        ORDER BY 
            CASE complexity_range
                WHEN '0-10' THEN 1
                WHEN '11-50' THEN 2
                WHEN '51-100' THEN 3
                WHEN '101-200' THEN 4
                WHEN '200+' THEN 5
            END
        """
        return self.execute_query(query)

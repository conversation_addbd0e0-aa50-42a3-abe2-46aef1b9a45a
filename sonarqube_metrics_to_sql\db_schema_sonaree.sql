-- SonarQube 项目指标表 - sonaree_wr02
CREATE TABLE IF NOT EXISTS sonaree_wr02 (
    -- 基础标识字段
    id SERIAL PRIMARY KEY, -- 自增主键，唯一标识每条记录
    analysis_date TIMESTAMPTZ NOT NULL, -- 分析时间，精确到秒
    project_name VARCHAR(200) NOT NULL, -- SonarQube项目名称
    sonar_key VARCHAR(200) NOT NULL, -- SonarQube项目Key
    branch VARCHAR(100) NOT NULL, -- 分支名
    build_number VARCHAR(50), -- 构建编号
    status VARCHAR(20) DEFAULT 'active', -- 记录状态
    created_at TIMESTAMPTZ DEFAULT NOW(), -- 记录创建时间
    updated_at TIMESTAMPTZ DEFAULT NOW(), -- 记录最后更新时间

    -- SonarQube 问题指标
    bugs INTEGER DEFAULT 0, -- Bug数量
    vulnerabilities INTEGER DEFAULT 0, -- 安全漏洞数量
    code_smells INTEGER DEFAULT 0, -- 代码异味数量
    sq_blocker INTEGER DEFAULT 0, -- 阻断级别问题数量
    sq_critical INTEGER DEFAULT 0, -- 严重级别问题数量
    sq_major INTEGER DEFAULT 0, -- 主要级别问题数量
    sq_minor INTEGER DEFAULT 0, -- 次要级别问题数量
    sq_info INTEGER DEFAULT 0, -- 信息级别问题数量
    security_hotspots INTEGER DEFAULT 0, -- 安全热点数量
    security_reviewed_hotspots INTEGER DEFAULT 0, -- 已评审的安全热点数量
    open_issues INTEGER DEFAULT 0, -- 未解决问题总数
    resolved_issues INTEGER DEFAULT 0, -- 已解决问题总数

    -- SonarQube 代码质量指标
    coverage FLOAT DEFAULT 0, -- 单元测试覆盖率(%)
    line_coverage FLOAT DEFAULT 0, -- 行覆盖率(%)
    branch_coverage FLOAT DEFAULT 0, -- 分支覆盖率(%)
    duplicated_lines_density FLOAT DEFAULT 0, -- 代码重复率(%)
    comment_lines_density FLOAT DEFAULT 0, -- 注释行密度(%)
    complexity INTEGER DEFAULT 0, -- 代码圈复杂度总和
    cognitive_complexity INTEGER DEFAULT 0, -- 认知复杂度

    -- 代码规模指标
    ncloc INTEGER DEFAULT 0, -- 非注释代码行数
    statements INTEGER DEFAULT 0, -- 语句数
    functions INTEGER DEFAULT 0, -- 函数/方法数
    files INTEGER DEFAULT 0, -- 文件数
    directories INTEGER DEFAULT 0, -- 目录数
    lines INTEGER DEFAULT 0, -- 总行数
    comment_lines INTEGER DEFAULT 0, -- 注释行数
    duplicated_lines INTEGER DEFAULT 0, -- 重复行数
    duplicated_blocks INTEGER DEFAULT 0, -- 重复块数
    duplicated_files INTEGER DEFAULT 0, -- 重复文件数

    -- 新增：数据分析增强字段
    analysis_date_only DATE, -- 仅日期部分
    analysis_year INT, -- 分析年份
    analysis_month INT, -- 分析月份
    analysis_week INT, -- 分析周数
    total_issues INT, -- 总问题数
    critical_issues INT, -- 严重问题数(阻断+严重)
    issue_density FLOAT, -- 问题密度(每千行代码的问题数)
    quality_level VARCHAR(20), -- 代码质量等级

    -- 原始数据备份
    raw_json JSONB, -- SonarQube原始指标数据的JSON备份
    description TEXT -- 备注或描述信息
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_sonaree_wr02_analysis_date ON sonaree_wr02(analysis_date);
CREATE INDEX IF NOT EXISTS idx_sonaree_wr02_project_name ON sonaree_wr02(project_name);
CREATE INDEX IF NOT EXISTS idx_sonaree_wr02_sonar_key ON sonaree_wr02(sonar_key);
CREATE INDEX IF NOT EXISTS idx_sonaree_wr02_branch ON sonaree_wr02(branch);
CREATE INDEX IF NOT EXISTS idx_sonaree_wr02_status ON sonaree_wr02(status);
CREATE INDEX IF NOT EXISTS idx_sonaree_wr02_analysis_date_only ON sonaree_wr02(analysis_date_only);
CREATE INDEX IF NOT EXISTS idx_sonaree_wr02_analysis_year ON sonaree_wr02(analysis_year);
CREATE INDEX IF NOT EXISTS idx_sonaree_wr02_analysis_month ON sonaree_wr02(analysis_month);
CREATE INDEX IF NOT EXISTS idx_sonaree_wr02_analysis_week ON sonaree_wr02(analysis_week);
CREATE INDEX IF NOT EXISTS idx_sonaree_wr02_total_issues ON sonaree_wr02(total_issues);
CREATE INDEX IF NOT EXISTS idx_sonaree_wr02_critical_issues ON sonaree_wr02(critical_issues);
CREATE INDEX IF NOT EXISTS idx_sonaree_wr02_quality_level ON sonaree_wr02(quality_level);
CREATE INDEX IF NOT EXISTS idx_sonaree_wr02_raw_json ON sonaree_wr02 USING GIN(raw_json jsonb_path_ops);

-- 添加自动更新updated_at的触发器
CREATE OR REPLACE FUNCTION update_sonaree_wr02_modtime()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建计算字段的触发器函数
CREATE OR REPLACE FUNCTION calculate_sonaree_wr02_derived_fields()
RETURNS TRIGGER AS $$
BEGIN
    -- 计算时间维度字段
    NEW.analysis_date_only := NEW.analysis_date::date;
    NEW.analysis_year := EXTRACT(YEAR FROM NEW.analysis_date);
    NEW.analysis_month := EXTRACT(MONTH FROM NEW.analysis_date);
    NEW.analysis_week := EXTRACT(WEEK FROM NEW.analysis_date);

    -- 计算统计字段
    NEW.total_issues := NEW.sq_blocker + NEW.sq_critical + NEW.sq_major + NEW.sq_minor + NEW.sq_info;
    NEW.critical_issues := NEW.sq_blocker + NEW.sq_critical;

    -- 计算问题密度
    IF NEW.ncloc > 0 THEN
        NEW.issue_density := (NEW.sq_blocker + NEW.sq_critical + NEW.sq_major + NEW.sq_minor + NEW.sq_info)::float / NEW.ncloc * 1000;
    ELSE
        NEW.issue_density := 0;
    END IF;

    -- 计算质量等级
    IF NEW.sq_blocker > 0 OR NEW.sq_critical > 0 THEN
        NEW.quality_level := 'critical';
    ELSIF NEW.sq_major > 0 THEN
        NEW.quality_level := 'major';
    ELSIF NEW.sq_minor > 0 THEN
        NEW.quality_level := 'minor';
    ELSE
        NEW.quality_level := 'clean';
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
CREATE TRIGGER update_sonaree_wr02_modtime_trigger
BEFORE UPDATE ON sonaree_wr02
FOR EACH ROW
EXECUTE FUNCTION update_sonaree_wr02_modtime();

CREATE TRIGGER calculate_sonaree_wr02_derived_fields_trigger
BEFORE INSERT OR UPDATE ON sonaree_wr02
FOR EACH ROW
EXECUTE FUNCTION calculate_sonaree_wr02_derived_fields();


-- SonarQube 项目指标表 - sonaree_bg03
CREATE TABLE IF NOT EXISTS sonaree_bg03 (
    -- 基础标识字段
    id SERIAL PRIMARY KEY, -- 自增主键，唯一标识每条记录
    analysis_date TIMESTAMPTZ NOT NULL, -- 分析时间，精确到秒
    project_name VARCHAR(200) NOT NULL, -- SonarQube项目名称
    sonar_key VARCHAR(200) NOT NULL, -- SonarQube项目Key
    branch VARCHAR(100) NOT NULL, -- 分支名
    build_number VARCHAR(50), -- 构建编号
    status VARCHAR(20) DEFAULT 'active', -- 记录状态
    created_at TIMESTAMPTZ DEFAULT NOW(), -- 记录创建时间
    updated_at TIMESTAMPTZ DEFAULT NOW(), -- 记录最后更新时间

    -- SonarQube 问题指标
    bugs INTEGER DEFAULT 0, -- Bug数量
    vulnerabilities INTEGER DEFAULT 0, -- 安全漏洞数量
    code_smells INTEGER DEFAULT 0, -- 代码异味数量
    sq_blocker INTEGER DEFAULT 0, -- 阻断级别问题数量
    sq_critical INTEGER DEFAULT 0, -- 严重级别问题数量
    sq_major INTEGER DEFAULT 0, -- 主要级别问题数量
    sq_minor INTEGER DEFAULT 0, -- 次要级别问题数量
    sq_info INTEGER DEFAULT 0, -- 信息级别问题数量
    security_hotspots INTEGER DEFAULT 0, -- 安全热点数量
    security_reviewed_hotspots INTEGER DEFAULT 0, -- 已评审的安全热点数量
    open_issues INTEGER DEFAULT 0, -- 未解决问题总数
    resolved_issues INTEGER DEFAULT 0, -- 已解决问题总数

    -- SonarQube 代码质量指标
    coverage FLOAT DEFAULT 0, -- 单元测试覆盖率(%)
    line_coverage FLOAT DEFAULT 0, -- 行覆盖率(%)
    branch_coverage FLOAT DEFAULT 0, -- 分支覆盖率(%)
    duplicated_lines_density FLOAT DEFAULT 0, -- 代码重复率(%)
    comment_lines_density FLOAT DEFAULT 0, -- 注释行密度(%)
    complexity INTEGER DEFAULT 0, -- 代码圈复杂度总和
    cognitive_complexity INTEGER DEFAULT 0, -- 认知复杂度

    -- 代码规模指标
    ncloc INTEGER DEFAULT 0, -- 非注释代码行数
    statements INTEGER DEFAULT 0, -- 语句数
    functions INTEGER DEFAULT 0, -- 函数/方法数
    files INTEGER DEFAULT 0, -- 文件数
    directories INTEGER DEFAULT 0, -- 目录数
    lines INTEGER DEFAULT 0, -- 总行数
    comment_lines INTEGER DEFAULT 0, -- 注释行数
    duplicated_lines INTEGER DEFAULT 0, -- 重复行数
    duplicated_blocks INTEGER DEFAULT 0, -- 重复块数
    duplicated_files INTEGER DEFAULT 0, -- 重复文件数

    -- 新增：数据分析增强字段
    analysis_date_only DATE, -- 仅日期部分
    analysis_year INT, -- 分析年份
    analysis_month INT, -- 分析月份
    analysis_week INT, -- 分析周数
    total_issues INT, -- 总问题数
    critical_issues INT, -- 严重问题数(阻断+严重)
    issue_density FLOAT, -- 问题密度(每千行代码的问题数)
    quality_level VARCHAR(20), -- 代码质量等级

    -- 原始数据备份
    raw_json JSONB, -- SonarQube原始指标数据的JSON备份
    description TEXT -- 备注或描述信息
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_sonaree_bg03_analysis_date ON sonaree_bg03(analysis_date);
CREATE INDEX IF NOT EXISTS idx_sonaree_bg03_project_name ON sonaree_bg03(project_name);
CREATE INDEX IF NOT EXISTS idx_sonaree_bg03_sonar_key ON sonaree_bg03(sonar_key);
CREATE INDEX IF NOT EXISTS idx_sonaree_bg03_branch ON sonaree_bg03(branch);
CREATE INDEX IF NOT EXISTS idx_sonaree_bg03_status ON sonaree_bg03(status);
CREATE INDEX IF NOT EXISTS idx_sonaree_bg03_analysis_date_only ON sonaree_bg03(analysis_date_only);
CREATE INDEX IF NOT EXISTS idx_sonaree_bg03_analysis_year ON sonaree_bg03(analysis_year);
CREATE INDEX IF NOT EXISTS idx_sonaree_bg03_analysis_month ON sonaree_bg03(analysis_month);
CREATE INDEX IF NOT EXISTS idx_sonaree_bg03_analysis_week ON sonaree_bg03(analysis_week);
CREATE INDEX IF NOT EXISTS idx_sonaree_bg03_total_issues ON sonaree_bg03(total_issues);
CREATE INDEX IF NOT EXISTS idx_sonaree_bg03_critical_issues ON sonaree_bg03(critical_issues);
CREATE INDEX IF NOT EXISTS idx_sonaree_bg03_quality_level ON sonaree_bg03(quality_level);
CREATE INDEX IF NOT EXISTS idx_sonaree_bg03_raw_json ON sonaree_bg03 USING GIN(raw_json jsonb_path_ops);

-- 添加自动更新updated_at的触发器
CREATE OR REPLACE FUNCTION update_sonaree_bg03_modtime()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建计算字段的触发器函数
CREATE OR REPLACE FUNCTION calculate_sonaree_bg03_derived_fields()
RETURNS TRIGGER AS $$
BEGIN
    -- 计算时间维度字段
    NEW.analysis_date_only := NEW.analysis_date::date;
    NEW.analysis_year := EXTRACT(YEAR FROM NEW.analysis_date);
    NEW.analysis_month := EXTRACT(MONTH FROM NEW.analysis_date);
    NEW.analysis_week := EXTRACT(WEEK FROM NEW.analysis_date);

    -- 计算统计字段
    NEW.total_issues := NEW.sq_blocker + NEW.sq_critical + NEW.sq_major + NEW.sq_minor + NEW.sq_info;
    NEW.critical_issues := NEW.sq_blocker + NEW.sq_critical;

    -- 计算问题密度
    IF NEW.ncloc > 0 THEN
        NEW.issue_density := (NEW.sq_blocker + NEW.sq_critical + NEW.sq_major + NEW.sq_minor + NEW.sq_info)::float / NEW.ncloc * 1000;
    ELSE
        NEW.issue_density := 0;
    END IF;

    -- 计算质量等级
    IF NEW.sq_blocker > 0 OR NEW.sq_critical > 0 THEN
        NEW.quality_level := 'critical';
    ELSIF NEW.sq_major > 0 THEN
        NEW.quality_level := 'major';
    ELSIF NEW.sq_minor > 0 THEN
        NEW.quality_level := 'minor';
    ELSE
        NEW.quality_level := 'clean';
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
CREATE TRIGGER update_sonaree_bg03_modtime_trigger
BEFORE UPDATE ON sonaree_bg03
FOR EACH ROW
EXECUTE FUNCTION update_sonaree_bg03_modtime();

CREATE TRIGGER calculate_sonaree_bg03_derived_fields_trigger
BEFORE INSERT OR UPDATE ON sonaree_bg03
FOR EACH ROW
EXECUTE FUNCTION calculate_sonaree_bg03_derived_fields();
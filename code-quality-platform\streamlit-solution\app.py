#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from streamlit_option_menu import option_menu
from config import PAGE_CONFIG, COLORS, TIME_RANGES
from database import DatabaseManager

# 页面配置
st.set_page_config(**PAGE_CONFIG)

# 初始化数据库管理器
@st.cache_resource
def get_db_manager():
    return DatabaseManager()

db = get_db_manager()

# 侧边栏
with st.sidebar:
    selected = option_menu(
        "代码质量分析",
        ["总览", "趋势分析", "开发者分析", "项目分析", "质量分析", "风险监控"],
        icons=['house', 'graph-up', 'people', 'folder', 'shield-check', 'exclamation-triangle'],
        menu_icon="cast",
        default_index=0,
    )
    
    # 时间范围选择
    time_range = st.selectbox("时间范围", list(TIME_RANGES.keys()), index=1)
    days = TIME_RANGES[time_range]

# 主页面
st.title("🚀 代码质量效能分析平台")

if selected == "总览":
    st.header("📊 总览仪表板")
    
    # 获取概览数据
    overview_data = db.get_overview_stats(days)
    
    if not overview_data.empty:
        row = overview_data.iloc[0]
        
        # 关键指标
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                label="总提交数",
                value=f"{row['total_commits']:,}",
                delta=None
            )
        
        with col2:
            st.metric(
                label="活跃开发者",
                value=f"{row['total_authors']:,}",
                delta=None
            )
        
        with col3:
            st.metric(
                label="代码变更行数",
                value=f"{row['total_changed_lines']:,}",
                delta=None
            )
        
        with col4:
            st.metric(
                label="清洁代码率",
                value=f"{row['clean_rate']:.1f}%",
                delta=None
            )
        
        # 第二行指标
        col5, col6, col7, col8 = st.columns(4)
        
        with col5:
            st.metric(
                label="总问题数",
                value=f"{row['total_issues']:,}",
                delta=None
            )
        
        with col6:
            st.metric(
                label="严重问题数",
                value=f"{row['total_critical_issues']:,}",
                delta=None
            )
        
        with col7:
            st.metric(
                label="平均问题密度",
                value=f"{row['avg_issue_density']:.2f}",
                delta=None
            )
        
        with col8:
            st.metric(
                label="活跃项目数",
                value=f"{row['total_projects']:,}",
                delta=None
            )
    
    # 图表区域
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📈 每日提交趋势")
        daily_data = db.get_daily_trends(days)
        if not daily_data.empty:
            fig = px.line(
                daily_data, 
                x='commit_date', 
                y=['total_commits', 'total_issues'],
                title="每日提交数与问题数趋势"
            )
            fig.update_layout(height=400)
            st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        st.subheader("🎯 质量等级分布")
        quality_data = db.get_quality_distribution()
        if not quality_data.empty:
            fig = px.pie(
                quality_data,
                values='commit_count',
                names='quality_level',
                title="代码质量等级分布",
                color='quality_level',
                color_discrete_map=COLORS['quality_levels']
            )
            fig.update_layout(height=400)
            st.plotly_chart(fig, use_container_width=True)

elif selected == "趋势分析":
    st.header("📈 趋势分析")

    # 月度趋势
    st.subheader("月度趋势分析")
    monthly_data = db.get_monthly_trends(12)

    if not monthly_data.empty:
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('提交数趋势', '开发者数趋势', '问题数趋势', '清洁代码率趋势'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )

        # 提交数趋势
        fig.add_trace(
            go.Scatter(x=monthly_data['month_start'], y=monthly_data['total_commits'],
                      name='提交数', line=dict(color=COLORS['primary'])),
            row=1, col=1
        )

        # 开发者数趋势
        fig.add_trace(
            go.Scatter(x=monthly_data['month_start'], y=monthly_data['unique_authors'],
                      name='开发者数', line=dict(color=COLORS['success'])),
            row=1, col=2
        )

        # 问题数趋势
        fig.add_trace(
            go.Scatter(x=monthly_data['month_start'], y=monthly_data['total_issues'],
                      name='问题数', line=dict(color=COLORS['danger'])),
            row=2, col=1
        )

        # 清洁代码率趋势
        fig.add_trace(
            go.Scatter(x=monthly_data['month_start'], y=monthly_data['clean_rate_percent'],
                      name='清洁代码率', line=dict(color=COLORS['info'])),
            row=2, col=2
        )

        fig.update_layout(height=600, showlegend=False)
        st.plotly_chart(fig, use_container_width=True)

    # 每日详细趋势
    st.subheader("每日详细趋势")
    daily_data = db.get_daily_trends(days)

    if not daily_data.empty:
        # 选择要显示的指标
        metrics = st.multiselect(
            "选择要显示的指标",
            ['total_commits', 'total_issues', 'total_critical_issues', 'avg_issue_density'],
            default=['total_commits', 'total_issues']
        )

        if metrics:
            fig = px.line(
                daily_data,
                x='commit_date',
                y=metrics,
                title="每日趋势详情"
            )
            fig.update_layout(height=500)
            st.plotly_chart(fig, use_container_width=True)

elif selected == "开发者分析":
    st.header("👨‍💻 开发者分析")

    # 获取开发者数据
    author_data = db.get_author_stats(50)

    if not author_data.empty:
        # 开发者选择
        selected_authors = st.multiselect(
            "选择开发者进行对比分析",
            author_data['author'].tolist(),
            default=author_data['author'].head(5).tolist()
        )

        if selected_authors:
            filtered_data = author_data[author_data['author'].isin(selected_authors)]

            # 开发者对比雷达图
            st.subheader("开发者能力雷达图")

            fig = go.Figure()

            for author in selected_authors:
                author_row = filtered_data[filtered_data['author'] == author].iloc[0]

                # 标准化数据（转换为0-100的分数）
                max_commits = author_data['total_commits'].max()
                max_lines = author_data['total_changed_lines'].max()

                values = [
                    (author_row['total_commits'] / max_commits) * 100,  # 提交活跃度
                    100 - min(author_row['avg_issue_density'] * 10, 100),  # 代码质量（问题密度越低越好）
                    author_row['clean_rate_percent'],  # 清洁代码率
                    (author_row['total_changed_lines'] / max_lines) * 100,  # 代码贡献量
                ]

                fig.add_trace(go.Scatterpolar(
                    r=values + [values[0]],  # 闭合图形
                    theta=['提交活跃度', '代码质量', '清洁代码率', '代码贡献量', '提交活跃度'],
                    fill='toself',
                    name=author
                ))

            fig.update_layout(
                polar=dict(
                    radialaxis=dict(
                        visible=True,
                        range=[0, 100]
                    )),
                showlegend=True,
                height=500
            )
            st.plotly_chart(fig, use_container_width=True)

            # 开发者详细数据表
            st.subheader("开发者详细数据")
            st.dataframe(
                filtered_data[['author', 'total_commits', 'total_changed_lines',
                              'total_issues', 'clean_rate_percent', 'avg_issue_density']],
                use_container_width=True
            )

elif selected == "项目分析":
    st.header("📁 项目分析")

    # 获取项目数据
    project_data = db.get_project_stats(30)

    if not project_data.empty:
        # 项目质量矩阵
        st.subheader("项目质量矩阵")

        fig = px.scatter(
            project_data,
            x='avg_issue_density',
            y='clean_rate_percent',
            size='total_commits',
            color='avg_comment_density',
            hover_name='gerrit_project',
            title="项目质量矩阵 (问题密度 vs 清洁代码率)",
            labels={
                'avg_issue_density': '平均问题密度',
                'clean_rate_percent': '清洁代码率 (%)',
                'avg_comment_density': '注释密度'
            }
        )
        fig.update_layout(height=500)
        st.plotly_chart(fig, use_container_width=True)

        # 项目对比分析
        st.subheader("项目对比分析")

        selected_projects = st.multiselect(
            "选择项目进行对比",
            project_data['gerrit_project'].tolist(),
            default=project_data['gerrit_project'].head(3).tolist()
        )

        if selected_projects:
            filtered_projects = project_data[project_data['gerrit_project'].isin(selected_projects)]

            # 多指标对比柱状图
            metrics_to_compare = ['total_commits', 'unique_authors', 'avg_issue_density', 'clean_rate_percent']

            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=('提交数对比', '开发者数对比', '问题密度对比', '清洁代码率对比')
            )

            for i, metric in enumerate(metrics_to_compare):
                row = (i // 2) + 1
                col = (i % 2) + 1

                fig.add_trace(
                    go.Bar(
                        x=filtered_projects['gerrit_project'],
                        y=filtered_projects[metric],
                        name=metric,
                        showlegend=False
                    ),
                    row=row, col=col
                )

            fig.update_layout(height=600)
            st.plotly_chart(fig, use_container_width=True)

            # 项目详细数据表
            st.subheader("项目详细数据")
            st.dataframe(filtered_projects, use_container_width=True)

elif selected == "质量分析":
    st.header("🛡️ 质量分析")

    # 问题严重程度分布
    st.subheader("问题严重程度分布")
    severity_data = db.get_severity_distribution(days)

    if not severity_data.empty and severity_data['count'].sum() > 0:
        col1, col2 = st.columns(2)

        with col1:
            # 饼图
            fig = px.pie(
                severity_data,
                values='count',
                names='severity',
                title="问题严重程度分布",
                color='severity',
                color_discrete_map=COLORS['severity_levels']
            )
            st.plotly_chart(fig, use_container_width=True)

        with col2:
            # 柱状图
            fig = px.bar(
                severity_data,
                x='severity',
                y='count',
                title="问题数量统计",
                color='severity',
                color_discrete_map=COLORS['severity_levels']
            )
            st.plotly_chart(fig, use_container_width=True)

    # 变更大小与质量关系分析
    st.subheader("变更大小与质量关系分析")
    change_data = db.get_change_size_analysis(days)

    if not change_data.empty:
        fig = px.scatter(
            change_data,
            x='changed_lines',
            y='total_issues',
            color='quality_level',
            size='issue_density',
            hover_data=['author', 'gerrit_project'],
            title="变更大小与问题数关系",
            color_discrete_map=COLORS['quality_levels']
        )
        fig.update_layout(height=500)
        st.plotly_chart(fig, use_container_width=True)

    # 复杂度分析
    st.subheader("代码复杂度分析")
    complexity_data = db.get_complexity_analysis()

    if not complexity_data.empty:
        fig = px.bar(
            complexity_data,
            x='complexity_range',
            y='count',
            title="代码复杂度分布",
            text='count'
        )
        fig.update_traces(texttemplate='%{text}', textposition='outside')
        fig.update_layout(height=400)
        st.plotly_chart(fig, use_container_width=True)

elif selected == "风险监控":
    st.header("⚠️ 风险监控")

    # 高风险提交
    st.subheader("高风险提交监控")
    high_risk_data = db.get_high_risk_commits(7)

    if not high_risk_data.empty:
        st.warning(f"发现 {len(high_risk_data)} 个高风险提交")

        # 高风险提交表格
        st.dataframe(
            high_risk_data[['change_id_short', 'author', 'gerrit_project', 'subject',
                           'changed_lines', 'total_issues', 'critical_issues', 'commit_time']],
            use_container_width=True
        )

        # 高风险提交分布
        col1, col2 = st.columns(2)

        with col1:
            # 按作者分布
            author_risk = high_risk_data.groupby('author')['critical_issues'].sum().reset_index()
            fig = px.bar(
                author_risk.sort_values('critical_issues', ascending=False).head(10),
                x='critical_issues',
                y='author',
                orientation='h',
                title="高风险提交作者分布"
            )
            st.plotly_chart(fig, use_container_width=True)

        with col2:
            # 按项目分布
            project_risk = high_risk_data.groupby('gerrit_project')['critical_issues'].sum().reset_index()
            fig = px.bar(
                project_risk.sort_values('critical_issues', ascending=False).head(10),
                x='critical_issues',
                y='gerrit_project',
                orientation='h',
                title="高风险提交项目分布"
            )
            st.plotly_chart(fig, use_container_width=True)
    else:
        st.success("✅ 近期无高风险提交")

    # 质量趋势告警
    st.subheader("质量趋势告警")
    daily_data = db.get_daily_trends(30)

    if not daily_data.empty:
        # 计算最近7天的平均问题密度
        recent_density = daily_data.tail(7)['avg_issue_density'].mean()
        previous_density = daily_data.head(-7).tail(7)['avg_issue_density'].mean()

        if recent_density > previous_density * 1.2:
            st.error(f"⚠️ 问题密度上升告警：最近7天平均问题密度 {recent_density:.2f}，比前7天增长 {((recent_density/previous_density-1)*100):.1f}%")
        elif recent_density < previous_density * 0.8:
            st.success(f"✅ 质量改善：最近7天平均问题密度 {recent_density:.2f}，比前7天降低 {((1-recent_density/previous_density)*100):.1f}%")
        else:
            st.info(f"ℹ️ 质量稳定：最近7天平均问题密度 {recent_density:.2f}")

# 页脚
st.markdown("---")
st.markdown("🚀 **代码质量效能分析平台** | 基于 Gerrit + SonarQube 数据")

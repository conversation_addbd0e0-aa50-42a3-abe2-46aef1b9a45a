pipeline {
    agent { label 'WinBuild' }
    options {
        timestamps()
        disableConcurrentBuilds()
        timeout(time: 1, unit: 'HOURS')
        buildDiscarder(logRotator(numToKeepStr: '30'))
    }
    environment {
        // 项目基础配置
        PROJECT_NAME = "wr02"
        PROJECT_TYPE = "daily"

        // 构建信息 - 在环境变量定义时生成时间戳
        BUILD_TIMESTAMP = sh(script: 'date +%Y%m%d%H%M', returnStdout: true).trim()
        YM_TIMESTAMP = sh(script: 'date +%Y%m', returnStdout: true).trim()
        D_TIMESTAMP = sh(script: 'date +%d', returnStdout: true).trim()
        HH_TIMESTAMP = sh(script: 'date +%H%M', returnStdout: true).trim()
        ARTIFACT_NAME = "wr02-daily-${YM_TIMESTAMP}"
        
        // 仓库配置
        REPO_URL = "ssh://jenkinsadmin@********:29418/wr02"
        REPO_MANIFEST = "wearable-sifli-wr02-develop.xml"
        REPO_BRANCH = "develop"
        
        // 制品库配置
        NEXUS_URL = "*********:8089"
        NEXUS_REPO = "wearable-build"
        NEXUS_GROUP_ID = "wearable-wr02-${PROJECT_TYPE}"
        
        MANI_NAME = "daily_manifest_${BUILD_TIMESTAMP}"
        
        // 系统路径配置
        //PATH = "/home/<USER>/bin:/usr/local/bin:/usr/bin:/usr/bin:/sbin:/usr/local/bin:/home/<USER>/bin:/cygdrive/c/Program Files/Common Files/Oracle/Java/javapath:/cygdrive/c/App/OpenSSH-Win64:/cygdrive/c/Windows/system32:/cygdrive/c/Windows:/cygdrive/c/Windows/System32/Wbem:/cygdrive/c/Windows/System32/WindowsPowerShell/v1.0:/cygdrive/c/Windows:/cygdrive/c/App/curl/bin:/cygdrive/c/App/GnuWin32/bin:/cygdrive/c/App/7-Zip:/cygdrive/c/Program Files/dotnet:/cygdrive/c/Keil_v5/ARM/ARMCC/bin:/cygdrive/c/Users/<USER>/bin:/cygdrive/c/Users/<USER>/.vscode/extensions/cl.eide-3.20.0/res/tools/win32/unify_builder:/cygdrive/c/Users/<USER>/.vscode/extensions/cl.eide-3.20.1/res/tools/win32/unify_builder:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python37/Scripts:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python37:/cygdrive/c/Program Files (x86)/GNU Arm Embedded Toolchain/10 2021.10/bin:/cygdrive/c/Users/<USER>/AppData/Local/Microsoft/WindowsApps:/cygdrive/c/App/Microsoft VS Code/bin"
    }
    stages {
        stage('清理构建缓存') {
            steps {
                sh '''
                echo "清理工作空间: CIBuildScript,CIBuildArtifacts";
                rm -rf CIBuild*;
                mkdir CIBuildArtifacts;
                '''
                //git branch: 'develop', credentialsId: 'sonarde', url: 'http://********:8081/wr02'
            }
        }
        stage('下载代码') {
            options {
                timeout(time: 10, unit: 'MINUTES')
            }
            steps {
                // 使用环境变量而不是参数
                sh """
                if [ ! -d ".repo" ]; then
                    echo "首次初始化repo";
                    repo init -u ${env.REPO_URL} -m ${env.REPO_MANIFEST} -b ${env.REPO_BRANCH};
                    repo sync -c --no-clone-bundle;
                else
                    echo "已存在repo，回退所有仓库到基线并同步"
                    repo start --all ${env.REPO_BRANCH};
                    repo forall -c "git gc --prune=now";
                    repo forall -c "git reset --hard HEAD^";
                    repo forall -c "git clean -dfx -e .clangd  .eide.usr.ctx.json  eide.json";
                    repo sync -c --no-clone-bundle;
                fi
                unix2dos tools/upgrade_tools/*.bat;
                cmd.exe /c 'icacls  tools/upgrade_tools/bin_update_src/qw_file_packerbg1.exe  /grant Everyone:F'
                """
                // 错误处理
                script {
                    def exitCode = sh(script: "if [ -d '.repo' ]; then echo 'success'; else echo 'fail'; fi", returnStdout: true).trim()
                    if (exitCode == 'fail') {
                        error "代码同步失败，请检查网络或仓库配置"
                    }
                }
            }
        }
        stage('打标记') {
            steps {
                sh '''
                repo manifest -r -o ${MANI_NAME}.xml;
                mv ${MANI_NAME}.xml CIBuildArtifacts/;
                echo "BuildURL: $BUILD_URL" > jenkinsinfo;
                mv jenkinsinfo CIBuildArtifacts/
                '''
            }
        }
        stage('生成编译配置文件') {
            steps {
                sh '''
                cp -r /home/<USER>/CIBuildScript ./;
                sh CIBuildScript/builder_boot/getbuilder_bootv32.sh version=PIPELINE workdir=Ddwr02;
                sh CIBuildScript/builder_lcpu/getbuilder_lcpuv32.sh version=PIPELINE workdir=Ddwr02;
                sh CIBuildScript/builder_app/getbuilder_appv32.sh version=PIPELINE workdir=Ddwr02;
                '''
                
                // 错误处理
                script {
                    def configFiles = sh(script: "find CIBuildScript -name '*.params' | wc -l", returnStdout: true).trim()
                    if (configFiles == '0') {
                        error "编译配置文件生成失败，请检查CIBuildScript路径"
                    }
                }
            }
        }
        stage('编译BOOT') {
            options {
                timeout(time: 5, unit: 'MINUTES')
            }
            steps {
                sh '''
                echo "编译BOOT"
                unify_builder -p CIBuildScript/builder_boot/builder_boot.params --rebuild
                '''
                
                // 错误处理
                script {
                    def bootBuildSuccess = sh(script: "find . -name '*boot*.bin' | wc -l", returnStdout: true).trim()
                    if (bootBuildSuccess == '0') {
                        error "BOOT编译失败，请检查编译日志"
                    }
                }
            }
        }
        stage('编译LCPU') {
            options {
                timeout(time: 5, unit: 'MINUTES')
            }
            steps {
                sh '''
                echo "编译LCPU"
                unify_builder -p CIBuildScript/builder_lcpu/builder_lcpu.params --rebuild
                '''
                
                // 错误处理
                script {
                    def lcpuBuildSuccess = sh(script: "find . -name '*lcpu*.bin' | wc -l", returnStdout: true).trim()
                    if (lcpuBuildSuccess == '0') {
                        error "LCPU编译失败，请检查编译日志"
                    }
                }
            }
        }
        stage('编译APP') {
            options {
                timeout(time: 10, unit: 'MINUTES')
            }
            steps {
                sh '''
                echo "编译APP"
                unify_builder -p CIBuildScript/builder_app/builder_app.params --rebuild
                '''
                
                // 错误处理
                script {
                    def appBuildSuccess = sh(script: "find . -name '*app*.bin' | wc -l", returnStdout: true).trim()
                    if (appBuildSuccess == '0') {
                        error "APP编译失败，请检查编译日志"
                    }
                }
            }
        }
        stage('执行打包BAT') {
            steps {
                script {
                    bat """
                        cd "tools\\upgrade_tools"
                        call WR02_OTA_Upgrade.bat standard
                            
                    """
                }
            }
        }
        stage('拷贝制品') {
            steps {
                sh '''
                echo "拷贝制品"
                cp -r tools/upgrade_tools CIBuildArtifacts/
                rm -rf CIBuildArtifacts/upgrade_tools/Asset
                rm -rf CIBuildArtifacts/upgrade_tools/bin_update_src
                7z a -tzip CIBuildArtifacts.zip CIBuildArtifacts
                '''
            }
        }
        stage('制品备份归档至jenkins') {
            steps {
                archiveArtifacts artifacts: '*.zip', fingerprint: true, onlyIfSuccessful: true
            }
        }
        stage('制品推送至nexus') {
            steps {
                script {
                    nexusArtifactUploader artifacts: [[artifactId: "${ARTIFACT_NAME}", 
                                                    classifier: "", 
                                                    file: "CIBuildArtifacts.zip", 
                                                    type: "zip"]], 
                                    credentialsId: "nexus", 
                                    groupId: "${NEXUS_GROUP_ID}", 
                                    nexusUrl: "${NEXUS_URL}", 
                                    nexusVersion: "nexus3", 
                                    protocol: "http", 
                                    repository: "${NEXUS_REPO}", 
                                    version: "${D_TIMESTAMP}.${HH_TIMESTAMP}"
                }
            }
        }
        stage('创建禅道版本') {
            steps {
                sh '''
                PATH="/home/<USER>/bin:/usr/local/bin:/usr/bin:/usr/bin:/sbin:/usr/local/bin:/home/<USER>/bin:/cygdrive/c/Program Files/Common Files/Oracle/Java/javapath:/cygdrive/c/App/OpenSSH-Win64:/cygdrive/c/Windows/system32:/cygdrive/c/Windows:/cygdrive/c/Windows/System32/Wbem:/cygdrive/c/Windows/System32/WindowsPowerShell/v1.0:/cygdrive/c/Windows:/cygdrive/c/App/curl/bin:/cygdrive/c/App/GnuWin32/bin:/cygdrive/c/App/7-Zip:/cygdrive/c/Program Files/dotnet:/cygdrive/c/Keil_v5/ARM/ARMCC/bin:/cygdrive/c/Users/<USER>/bin:/cygdrive/c/Users/<USER>/.vscode/extensions/cl.eide-3.20.0/res/tools/win32/unify_builder:/cygdrive/c/Users/<USER>/.vscode/extensions/cl.eide-3.20.1/res/tools/win32/unify_builder:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python37/Scripts:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python37:/cygdrive/c/Program Files (x86)/GNU Arm Embedded Toolchain/10 2021.10/bin:/cygdrive/c/Users/<USER>/AppData/Local/Microsoft/WindowsApps:/cygdrive/c/App/Microsoft VS Code/bin:$PATH";
                #注意 #./create_build.sh 851 64 932 "test" "admin" ""  "2024-04-10" "" "" "info"
                # 851表示项目id,64表示产品,932表示迭代执行,"test"表示版本名称，"admin"表示创建人，""表示分支,"2024-04-10"表示日期，"inof"表示备注。
                #sh CIBuildScript/zentao/create_builder.sh  851 64 932 "WR02_Daily_${dataDir}_${dayTime}.${dayVersion}" "lijiajing" "" "${zentaoTime}" "" "http://*********:8089/#browse/browse:wearable-build:wearable-wr02-daily%2FWR02-Daily-${dataDir}%2F${dayTime}.${dayVersion}%2FWR02-Daily-${dataDir}-${dayTime}.${dayVersion}.zip" "info:${BUILD_URL}";
                '''
            }
        }
    }
    post {
        success {
            echo "构建成功"
        }
        failure {
            echo "构建失败"
        }
        always {
            // 无论成功失败都需清理的临时文件
            sh '''
            echo "清理临时文件"
            rm -rf .repo/repo_cache
            '''
        }
    }
}

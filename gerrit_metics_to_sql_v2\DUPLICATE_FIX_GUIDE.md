# 🔧 解决重复数据问题指南

## 🎯 问题描述

当前问题：Jenkins Gerrit Trigger插件在同一个Change的多个Patch Set时会多次触发数据采集脚本，导致数据库中存在同仓库、同分支、同change_id的多条记录。

**期望结果**：每个Change只保留一条最新的记录，中间版本通过数据刷新的方式更新。

## 📋 解决方案对比

| 方案 | 优势 | 缺点 | 推荐度 |
|------|------|------|--------|
| **方案1: 修改唯一性逻辑** | 彻底解决问题，性能好 | 需要修改代码 | ⭐⭐⭐⭐⭐ |
| **方案2: 数据库约束** | 数据库层面保证唯一性 | 需要清理历史数据 | ⭐⭐⭐⭐ |
| **方案3: 定期清理脚本** | 不影响现有流程 | 治标不治本 | ⭐⭐⭐ |
| **方案4: Jenkins配置优化** | 从源头解决 | 可能影响其他功能 | ⭐⭐⭐⭐ |

## 🚀 推荐实施步骤

### 第一步：数据库清理和约束添加

```bash
# 1. 备份数据库
pg_dump -h 10.0.0.199 -p 5434 -U admin mydatabase > backup_$(date +%Y%m%d).sql

# 2. 执行清理脚本
psql -h 10.0.0.199 -p 5434 -U admin -d mydatabase -f cleanup_duplicates.sql
```

### 第二步：修改数据采集脚本

#### 选项A：替换整个保存函数（推荐）

```python
# 在 find_and_collect.py 中找到 save_commit_metrics 函数
# 将其替换为 patch_save_function.py 中的 save_commit_metrics_improved 函数

# 1. 备份原文件
cp find_and_collect.py find_and_collect.py.backup

# 2. 替换函数（手动操作）
# 将 patch_save_function.py 中的函数复制到 find_and_collect.py 中
```

#### 选项B：修改现有逻辑

```python
# 在现有的 save_commit_metrics 函数中，将：
check_result = conn.run(
    "SELECT 1 FROM commit_metrics WHERE commit_id = :commit_id",
    commit_id=commit_id
)

# 修改为：
check_result = conn.run("""
    SELECT commit_id, patch_set, updated_at 
    FROM commit_metrics 
    WHERE gerrit_project = :gerrit_project 
      AND branch = :branch 
      AND change_id_short = :change_id_short
""", 
gerrit_project=gerrit_project,
branch=branch,
change_id_short=change_id_short
)
```

### 第三步：验证修复效果

```sql
-- 1. 检查是否还有重复数据
SELECT 
    gerrit_project,
    branch,
    change_id_short,
    COUNT(*) as count
FROM commit_metrics
GROUP BY gerrit_project, branch, change_id_short
HAVING COUNT(*) > 1;

-- 2. 监控新数据
SELECT 
    gerrit_project,
    change_id_short,
    patch_set,
    created_at,
    updated_at
FROM commit_metrics
WHERE created_at >= CURRENT_DATE
ORDER BY updated_at DESC
LIMIT 20;
```

## 🔍 详细实施说明

### 1. 数据库层面解决方案

#### 添加唯一约束
```sql
-- 确保 (gerrit_project, branch, change_id_short) 组合唯一
ALTER TABLE commit_metrics 
ADD CONSTRAINT uk_commit_metrics_unique 
UNIQUE (gerrit_project, branch, change_id_short);
```

#### 清理重复数据
```sql
-- 保留最新的patch set，删除旧版本
WITH ranked_commits AS (
    SELECT 
        commit_id,
        ROW_NUMBER() OVER (
            PARTITION BY gerrit_project, branch, change_id_short 
            ORDER BY patch_set DESC, updated_at DESC
        ) as rn
    FROM commit_metrics
)
DELETE FROM commit_metrics 
WHERE commit_id IN (
    SELECT commit_id 
    FROM ranked_commits 
    WHERE rn > 1
);
```

### 2. 应用层面解决方案

#### 修改唯一性判断逻辑
```python
def is_record_outdated(conn, gerrit_project, branch, change_id_short, current_patch_set):
    """检查当前记录是否过时"""
    result = conn.run("""
        SELECT patch_set 
        FROM commit_metrics 
        WHERE gerrit_project = :gerrit_project 
          AND branch = :branch 
          AND change_id_short = :change_id_short
    """, 
    gerrit_project=gerrit_project,
    branch=branch,
    change_id_short=change_id_short
    )
    
    if not result:
        return False  # 没有记录，不是过时
    
    existing_patch_set = result[0][0]
    return current_patch_set < existing_patch_set  # 当前版本小于已存在版本
```

#### 使用UPSERT操作
```python
# 使用 PostgreSQL 的 ON CONFLICT 语法
upsert_sql = """
    INSERT INTO commit_metrics (...) 
    VALUES (...)
    ON CONFLICT (gerrit_project, branch, change_id_short) 
    DO UPDATE SET
        patch_set = EXCLUDED.patch_set,
        -- 其他字段...
        updated_at = NOW()
    WHERE commit_metrics.patch_set <= EXCLUDED.patch_set
"""
```

### 3. Jenkins配置优化（可选）

#### 修改Gerrit Trigger配置
```groovy
// 在Jenkins Job配置中，可以添加条件判断
// 只在特定条件下触发（如patch set为最新版本）

if (env.GERRIT_PATCHSET_NUMBER == env.GERRIT_PATCHSET_REVISION) {
    // 执行数据采集
    sh "python3 find_and_collect.py"
}
```

## 📊 监控和维护

### 创建监控视图
```sql
-- 重复数据监控视图
CREATE OR REPLACE VIEW v_duplicate_monitor AS
SELECT 
    gerrit_project,
    branch,
    change_id_short,
    COUNT(*) as record_count,
    ARRAY_AGG(patch_set ORDER BY patch_set) as patch_sets,
    MAX(updated_at) as last_updated
FROM commit_metrics
GROUP BY gerrit_project, branch, change_id_short
HAVING COUNT(*) > 1;

-- 每日数据质量报告
CREATE OR REPLACE VIEW v_daily_data_quality AS
SELECT 
    CURRENT_DATE as report_date,
    COUNT(*) as total_records,
    COUNT(DISTINCT gerrit_project || '~' || branch || '~' || change_id_short) as unique_changes,
    COUNT(*) - COUNT(DISTINCT gerrit_project || '~' || branch || '~' || change_id_short) as duplicate_count,
    ROUND(
        (COUNT(DISTINCT gerrit_project || '~' || branch || '~' || change_id_short)::float / COUNT(*) * 100)::numeric, 
        2
    ) as data_quality_score
FROM commit_metrics;
```

### 定期清理脚本
```bash
#!/bin/bash
# cleanup_duplicates.sh

echo "开始清理重复数据..."

psql -h 10.0.0.199 -p 5434 -U admin -d mydatabase -c "
WITH ranked_commits AS (
    SELECT 
        commit_id,
        ROW_NUMBER() OVER (
            PARTITION BY gerrit_project, branch, change_id_short 
            ORDER BY patch_set DESC, updated_at DESC
        ) as rn
    FROM commit_metrics
    WHERE created_at >= CURRENT_DATE - INTERVAL '1 day'
)
DELETE FROM commit_metrics 
WHERE commit_id IN (
    SELECT commit_id 
    FROM ranked_commits 
    WHERE rn > 1
);
"

echo "清理完成"
```

## 🎯 验收标准

修复完成后，应该满足以下条件：

1. ✅ **数据唯一性**：每个 (gerrit_project, branch, change_id_short) 组合只有一条记录
2. ✅ **数据完整性**：保留的是最新的patch set数据
3. ✅ **性能优化**：查询性能不受影响，甚至有所提升
4. ✅ **监控完善**：有监控视图可以及时发现问题
5. ✅ **向后兼容**：现有的分析查询和仪表板正常工作

## 🚨 注意事项

1. **备份数据**：在执行任何修改前，务必备份数据库
2. **测试环境**：建议先在测试环境验证修改效果
3. **分步实施**：建议分步骤实施，每步验证后再进行下一步
4. **监控告警**：设置监控告警，及时发现新的重复数据问题
5. **文档更新**：更新相关文档和操作手册

---

🎉 **按照以上步骤实施后，您的重复数据问题将得到彻底解决！**

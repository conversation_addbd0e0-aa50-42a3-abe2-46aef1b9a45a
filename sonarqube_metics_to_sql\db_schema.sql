-- SonarQube 项目通用指标表（每个项目一个表，表名为项目名）
CREATE TABLE IF NOT EXISTS wearable_wr02_daily_sonarde (
    id SERIAL PRIMARY KEY, -- 自增主键，唯一标识每条记录。例如：1, 2, 3
    analysis_date TIMESTAMPTZ NOT NULL, -- 分析时间，记录SonarQube项目分析的实际时间，精确到秒。例如：2025-07-01 05:17:39+08
    project_name VARCHAR(200) NOT NULL, -- SonarQube项目名称。例如：wearable-wr02-daily-sonarde
    bugs INTEGER DEFAULT 0, -- 代码中的Bug数量。示例：5
    vulnerabilities INTEGER DEFAULT 0, -- 代码中的安全漏洞数量。示例：2
    code_smells INTEGER DEFAULT 0, -- 代码异味（Code Smells）数量。示例：10
    coverage FLOAT DEFAULT 0, -- 单元测试覆盖率（百分比）。示例：85.5
    duplicated_lines_density FLOAT DEFAULT 0, -- 代码重复率（百分比）。示例：3.2
    ncloc INTEGER DEFAULT 0, -- 非注释代码行数（NCLOC）。示例：12000
    sq_blocker INTEGER DEFAULT 0, -- 阻断级别问题数量（Blocker）。示例：1
    sq_critical INTEGER DEFAULT 0, -- 严重级别问题数量（Critical）。示例：2
    sq_major INTEGER DEFAULT 0, -- 主要级别问题数量（Major）。示例：3
    sq_minor INTEGER DEFAULT 0, -- 次要级别问题数量（Minor）。示例：4
    sq_info INTEGER DEFAULT 0, -- 信息级别问题数量（Info）。示例：0
    comment_lines_density FLOAT DEFAULT 0, -- 注释行密度（百分比），即注释行占比。示例：12.5
    complexity INTEGER DEFAULT 0, -- 代码圈复杂度总和。示例：150
    files INTEGER DEFAULT 0, -- 文件数。示例：50
    functions INTEGER DEFAULT 0, -- 函数/方法数。示例：300
    statements INTEGER DEFAULT 0, -- 语句数。示例：8000
    duplicated_lines INTEGER DEFAULT 0, -- 重复行数，代码中被检测为重复的行数。示例：120
    duplicated_blocks INTEGER DEFAULT 0, -- 重复块数，代码中被检测为重复的代码块数量。示例：5
    duplicated_files INTEGER DEFAULT 0, -- 重复文件数，包含重复代码的文件数量。示例：2
    created_at TIMESTAMPTZ DEFAULT NOW(), -- 记录创建时间，自动生成。
    updated_at TIMESTAMPTZ DEFAULT NOW(), -- 记录最后更新时间，自动更新。
    raw_json JSONB, -- SonarQube原始指标数据的JSON备份，便于后续分析和溯源。
    status VARCHAR(20) DEFAULT 'active', -- 记录状态，业务用途（如active/archived等）。NEEDS CLARIFICATION
    description TEXT, -- 备注或描述信息，业务自定义。NEEDS CLARIFICATION
    position INTEGER -- 业务排序字段，具体用途待补充。NEEDS CLARIFICATION
);

CREATE INDEX IF NOT EXISTS idx_wearable_wr02_daily_sonarde_analysis_date ON wearable_wr02_daily_sonarde(analysis_date);
CREATE INDEX IF NOT EXISTS idx_wearable_wr02_daily_sonarde_status ON wearable_wr02_daily_sonarde(status);
CREATE INDEX IF NOT EXISTS idx_wearable_wr02_daily_sonarde_created_at ON wearable_wr02_daily_sonarde(created_at);

-- 自动更新时间戳触发器
CREATE OR REPLACE FUNCTION update_wearable_wr02_daily_sonarde_modtime()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_wearable_wr02_daily_sonarde_modtime
BEFORE UPDATE ON wearable_wr02_daily_sonarde
FOR EACH ROW
EXECUTE FUNCTION update_wearable_wr02_daily_sonarde_modtime(); 
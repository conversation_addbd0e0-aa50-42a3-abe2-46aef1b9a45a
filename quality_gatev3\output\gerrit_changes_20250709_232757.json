[{"change_id": "I282e59416a5016d1cb3b898cbd89349030f7fc18", "project": "sifli", "branch": "develop", "subject": "gpio2: force clear gpio2 isr", "status": "MERGED", "created": 1751194312, "updated": 1751196075, "mergeable": false, "number": 5444, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/5444", "patchset_count": 3, "commit_id": "bea823b15e9341feb2b9e962200b9ccb19668404", "insertions": 24, "deletions": 3}, {"change_id": "I7ebf6db4211b438c4fd6de81e76722b8cc9fbcc0", "project": "app", "branch": "wr02_release", "subject": "gpio_irq: disable gpio irq before detach it", "status": "MERGED", "created": 1751192039, "updated": 1751192693, "mergeable": false, "number": 5394, "owner": "linjizhao", "url": "http://********:8081/c/app/+/5394", "patchset_count": 1, "commit_id": "f595b33226c9c120f53c66b57d74f24d2bc12686", "insertions": 14, "deletions": 2}, {"change_id": "I7ebf6db4211b438c4fd6de81e76722b8cc9fbcc0", "project": "sifli", "branch": "wr02_release", "subject": "gpio_irq: disable gpio irq before detach it", "status": "MERGED", "created": 1751192023, "updated": 1751192693, "mergeable": false, "number": 5393, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/5393", "patchset_count": 1, "commit_id": "2de9132047f8a2594d36475c7d6a1e63dee1deba", "insertions": 28, "deletions": 22}, {"change_id": "I949dbb8fb33611971693b220f6b052ed001e5800", "project": "sifli", "branch": "develop", "subject": "ant: debug ant irq abort", "status": "ABANDONED", "created": 1751101836, "updated": 1751192239, "mergeable": false, "number": 5436, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/5436", "patchset_count": 4, "commit_id": "4d98212752c475c09e01f4fe3535ebb5f1883b65", "insertions": 22, "deletions": 19}, {"change_id": "I7ebf6db4211b438c4fd6de81e76722b8cc9fbcc0", "project": "sifli", "branch": "develop", "subject": "gpio_irq: disable gpio irq before detach it", "status": "MERGED", "created": 1751191443, "updated": 1751192024, "mergeable": false, "number": 5443, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/5443", "patchset_count": 1, "commit_id": "222544cd644bbb1cb09f93f42b64af6855184506", "insertions": 28, "deletions": 22}, {"change_id": "I7ebf6db4211b438c4fd6de81e76722b8cc9fbcc0", "project": "app", "branch": "develop", "subject": "gpio_irq: disable gpio irq before detach it", "status": "MERGED", "created": 1751191317, "updated": 1751192024, "mergeable": false, "number": 5442, "owner": "linjizhao", "url": "http://********:8081/c/app/+/5442", "patchset_count": 2, "commit_id": "0c5404bed5d8d27bf0844e5d2ea420eca8d602bb", "insertions": 13, "deletions": 2}, {"change_id": "Ic1f25ddc22a3fd713514405c5fd9447a664d5b4b", "project": "qw_platform", "branch": "develop", "subject": "ppg: support DVT2-LEDV3 daily config", "status": "MERGED", "created": 1751112989, "updated": 1751113627, "mergeable": false, "number": 5441, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5441", "patchset_count": 2, "commit_id": "09d471bbb9134687a0a692d38a45c215f1085302", "insertions": 745, "deletions": 152}, {"change_id": "Ib20b416379dda971771db3a92c3e4021eeafe3bd", "project": "cycle/sifli", "branch": "bg2_develop", "subject": "[Fix]: 新增gps配置, 调整LCD支持最低刷新率", "status": "MERGED", "created": 1751103916, "updated": 1751107591, "mergeable": false, "number": 5438, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/sifli/+/5438", "patchset_count": 1, "commit_id": "c9e50814dd817517426756bca3beba478a1c69c9", "insertions": 56, "deletions": 11}, {"change_id": "I88432245f15c030e4bce49f4a5299e7fcf7d1ced", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 调整LCD无操作时最低刷新率降低功耗", "status": "MERGED", "created": 1751104120, "updated": 1751107571, "mergeable": false, "number": 5439, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/5439", "patchset_count": 2, "commit_id": "adc361584794251e03ec908469bf8f71b8da8ba7", "insertions": 19, "deletions": 1}, {"change_id": "I5bc31e134158f856129f18535cefde40fddfe841", "project": "qw_platform", "branch": "develop", "subject": "ppg: ppg && acc time diff limit 80ms", "status": "MERGED", "created": 1751104386, "updated": 1751104916, "mergeable": false, "number": 5440, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5440", "patchset_count": 2, "commit_id": "709fa3200dab1807a7dbacc53a494a0b24afaf1e", "insertions": 11, "deletions": 1}, {"change_id": "Ief7753929ca7fa0a25e1a687015a9e2731695479", "project": "app", "branch": "develop", "subject": "ble: update min connect interval", "status": "MERGED", "created": 1751101918, "updated": 1751103396, "mergeable": false, "number": 5437, "owner": "linjizhao", "url": "http://********:8081/c/app/+/5437", "patchset_count": 2, "commit_id": "ae2691243354892ce82221aece31daf951d9de4f", "insertions": 12, "deletions": 2}, {"change_id": "Id997446a092b468d950f873e03afb6af208f849a", "project": "app", "branch": "develop", "subject": "ble: set ble max connect interval to 30ms", "status": "ABANDONED", "created": 1751098908, "updated": 1751100237, "mergeable": false, "number": 5435, "owner": "linjizhao", "url": "http://********:8081/c/app/+/5435", "patchset_count": 1, "commit_id": "67b7ad203e98a0cab594f42e0ed2d686b0f1d141", "insertions": 12, "deletions": 2}, {"change_id": "Ie6425df187dec6b085f40864950091b1388346c1", "project": "qw_platform", "branch": "develop", "subject": "ppg: add dump acc log", "status": "MERGED", "created": 1751093851, "updated": 1751098821, "mergeable": false, "number": 5431, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5431", "patchset_count": 5, "commit_id": "4e7fbc44d658ac7f32bafaeca8930fbee2cc6af9", "insertions": 35, "deletions": 15}, {"change_id": "Ic8557c1d0bea64da923c8ce66ae350fd24765acf", "project": "app", "branch": "develop", "subject": "[algo]: 解决速度不出值的问题 lottie动画死机的问题", "status": "MERGED", "created": 1751095442, "updated": 1751095846, "mergeable": false, "number": 5433, "owner": "lixin", "url": "http://********:8081/c/app/+/5433", "patchset_count": 1, "commit_id": "597ba373a4a5a7fdba5d1cb40861ae4d04679fc5", "insertions": 98, "deletions": 89}, {"change_id": "Ic8557c1d0bea64da923c8ce66ae350fd24765acf", "project": "qw_platform", "branch": "develop", "subject": "[sonar]: 回滚一些修正", "status": "MERGED", "created": 1751095394, "updated": 1751095846, "mergeable": false, "number": 5432, "owner": "lixin", "url": "http://********:8081/c/qw_platform/+/5432", "patchset_count": 2, "commit_id": "526a90e0ab32e86695a358d311e644aaa2a9a957", "insertions": 14, "deletions": 6}, {"change_id": "I1b0e69893d6c625b7db934712f89aecba2f2bb72", "project": "qw_platform", "branch": "develop", "subject": "[sys_trace]: 完善异常信息打印", "status": "MERGED", "created": 1751083176, "updated": 1751092145, "mergeable": false, "number": 5428, "owner": "lixiaolong", "url": "http://********:8081/c/qw_platform/+/5428", "patchset_count": 2, "commit_id": "d6a314a0bda67dbfea6490b3ce1c20a0b1b67333", "insertions": 20, "deletions": 7}, {"change_id": "I7593580940bf3cece515f74063876ef2d2714f49", "project": "sifli", "branch": "develop", "subject": "[pm]: 只对standby进行打点", "status": "MERGED", "created": 1751083184, "updated": 1751092131, "mergeable": false, "number": 5429, "owner": "lixiaolong", "url": "http://********:8081/c/sifli/+/5429", "patchset_count": 1, "commit_id": "cea662f0de3b9f48a762f95007531b34788c448e", "insertions": 36, "deletions": 8}, {"change_id": "I082cc9b1de3582022ba1462052821f13867fd849", "project": "sifli", "branch": "develop", "subject": "[wdt]: 修改pm_wdt初始化位置", "status": "MERGED", "created": 1751077842, "updated": 1751092130, "mergeable": false, "number": 5425, "owner": "lixiaolong", "url": "http://********:8081/c/sifli/+/5425", "patchset_count": 1, "commit_id": "72bfb4a1ce92f9775ebd1ac0f810a2e0b24b5912", "insertions": 50, "deletions": 39}, {"change_id": "I60806b854e29164c654eabf92b951c53f7f4aa9a", "project": "qw_platform", "branch": "develop", "subject": "[GUI]: 修复模拟器编译问题。", "status": "MERGED", "created": 1751075319, "updated": 1751089795, "mergeable": false, "number": 5421, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5421", "patchset_count": 2, "commit_id": "538b7735fa7482b8c7c40d6d49fbe38e7afdaa6a", "insertions": 15, "deletions": 0}, {"change_id": "I382c8fb3a4741fba0060de7af4cfab49cfa85bbc", "project": "qw_platform", "branch": "develop", "subject": "algo_capture: include qw_user_debug.h", "status": "MERGED", "created": 1751084526, "updated": 1751085195, "mergeable": false, "number": 5430, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5430", "patchset_count": 2, "commit_id": "7dbcff80eb9fe02975cb0502b33d4b00610fcb7d", "insertions": 11, "deletions": 0}, {"change_id": "I36eca51a57eab4b6f6c5890e807a5cebb59045d0", "project": "qw_platform", "branch": "develop", "subject": "ppg&&acc: set acc batch to 400ms.", "status": "MERGED", "created": 1751076792, "updated": 1751079235, "mergeable": false, "number": 5424, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5424", "patchset_count": 2, "commit_id": "eadd0b176c94b4bcf27eb0ef7fc7f01b616e26d1", "insertions": 21, "deletions": 6}, {"change_id": "I3070e5c52fc0fada9cf7205b747ff59da62a201c", "project": "sifli", "branch": "wr02_release", "subject": "pm: disable sleep if capture ppg", "status": "MERGED", "created": 1751076696, "updated": 1751077129, "mergeable": false, "number": 5391, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/5391", "patchset_count": 1, "commit_id": "82ff204ef337da3c0f0e3da5bb50290ba85a639a", "insertions": 20, "deletions": 8}, {"change_id": "I3070e5c52fc0fada9cf7205b747ff59da62a201c", "project": "sifli", "branch": "develop", "subject": "pm: disable sleep if capture ppg", "status": "MERGED", "created": 1751075839, "updated": 1751076709, "mergeable": false, "number": 5422, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/5422", "patchset_count": 1, "commit_id": "3ce3041efebee6eb8e8f0d44529376b77f6e23d4", "insertions": 20, "deletions": 8}, {"change_id": "Ic1194d0c519e97adc0cc94b093b1ae86640b7b73", "project": "qw_platform", "branch": "develop", "subject": "ppg: sync acc time event", "status": "MERGED", "created": 1751037359, "updated": 1751037883, "mergeable": false, "number": 5420, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5420", "patchset_count": 2, "commit_id": "227103e32c2dd1cc87fe90be246a10de243ff78f", "insertions": 89, "deletions": 40}, {"change_id": "I497c567c05a7e3ccb7d0938147c97af88d5fb4f1", "project": "qw_platform", "branch": "wr02_release", "subject": "[Gps]: EPO数据注入方式改为RTCRAM", "status": "MERGED", "created": 1751021965, "updated": 1751022555, "mergeable": false, "number": 5390, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5390", "patchset_count": 1, "commit_id": "556590f0d9d730c4c2e42ff8b5aef49b9aff1865", "insertions": 12, "deletions": 1}, {"change_id": "Ie61f4eca7b6451edcc8de544e7fcf93922ff9d5c", "project": "sifli", "branch": "wr02_release", "subject": "[Log]: Assert时打印func提前", "status": "MERGED", "created": 1751021756, "updated": 1751022162, "mergeable": false, "number": 5389, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/5389", "patchset_count": 1, "commit_id": "75b2d447c62fc587fe6d3cb990ea90532ddd5fe3", "insertions": 11, "deletions": 3}, {"change_id": "I3fb45f0660d7119afe8dbfb55b2fcd1006b0978a", "project": "qw_platform", "branch": "wr02_release", "subject": "[GPS]: 解决编译告警", "status": "MERGED", "created": 1750941951, "updated": 1751021953, "mergeable": false, "number": 5359, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5359", "patchset_count": 3, "commit_id": "afb4d054037a95bdbd7cdf240b81360c5ba8a2cb", "insertions": 15, "deletions": 2}, {"change_id": "I497c567c05a7e3ccb7d0938147c97af88d5fb4f1", "project": "qw_platform", "branch": "develop", "subject": "[Gps]: EPO数据注入方式改为RTCRAM", "status": "MERGED", "created": 1751017360, "updated": 1751021947, "mergeable": false, "number": 5418, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5418", "patchset_count": 2, "commit_id": "43dbb8843aac711ac837368ef57cc53c4a12a2ba", "insertions": 11, "deletions": 1}, {"change_id": "I3fb45f0660d7119afe8dbfb55b2fcd1006b0978a", "project": "qw_platform", "branch": "develop", "subject": "[GPS]: 解决编译告警", "status": "MERGED", "created": 1750941918, "updated": 1751021938, "mergeable": false, "number": 5362, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5362", "patchset_count": 3, "commit_id": "12d95ddc42cf5f7cfb72bc9b6a492df9262716ab", "insertions": 15, "deletions": 2}, {"change_id": "Ie61f4eca7b6451edcc8de544e7fcf93922ff9d5c", "project": "sifli", "branch": "develop", "subject": "[Log]: Assert时打印func提前", "status": "MERGED", "created": 1751004682, "updated": 1751021759, "mergeable": false, "number": 5407, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/sifli/+/5407", "patchset_count": 2, "commit_id": "b860a329c798eb0330cccf397e4de24f7bbe08e0", "insertions": 11, "deletions": 3}, {"change_id": "I64da26141bbe82cc703802143fb0af16a94c776f", "project": "app", "branch": "develop", "subject": "[gui]: 删除多余代码，并修改eide不在编译videoWeight", "status": "MERGED", "created": 1751007940, "updated": 1751017708, "mergeable": false, "number": 5412, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5412", "patchset_count": 2, "commit_id": "4561161d6bce69b8458303755a5e8a1a45481798", "insertions": 14, "deletions": 62}, {"change_id": "Ic02eb9de6105ba63194c7954d71a21f91c04f260", "project": "sifli", "branch": "wr02_release", "subject": "ipc: only assert check user ipc channal", "status": "MERGED", "created": 1751015307, "updated": 1751017313, "mergeable": false, "number": 5387, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/5387", "patchset_count": 1, "commit_id": "9a1f629018999ba3ea9a0f671b23e65d9609a7b3", "insertions": 18, "deletions": 4}, {"change_id": "I31e29c97b1dc3b1a54bdd1084a80c480c8562b56", "project": "app", "branch": "develop", "subject": "ble: force update connect interval as 7.5ms in goodix capture", "status": "MERGED", "created": 1751015225, "updated": 1751015673, "mergeable": false, "number": 5416, "owner": "linjizhao", "url": "http://********:8081/c/app/+/5416", "patchset_count": 1, "commit_id": "75b4fa14d1892953452b70c5e805a44bf1bc537c", "insertions": 23, "deletions": 10}, {"change_id": "Ic02eb9de6105ba63194c7954d71a21f91c04f260", "project": "sifli", "branch": "develop", "subject": "ipc: only assert check user ipc channal", "status": "MERGED", "created": 1751014943, "updated": 1751015663, "mergeable": false, "number": 5415, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/5415", "patchset_count": 2, "commit_id": "94038d6f1294f804da30e7def646ce67f8618132", "insertions": 18, "deletions": 4}, {"change_id": "I5fd2448c8596b6928f7fa96b04a588840dc702de", "project": "app", "branch": "wr02_algosim", "subject": "alg_fmk:修改版本名称为sim_1.1_0626", "status": "MERGED", "created": 1751014471, "updated": 1751014533, "mergeable": false, "number": 5414, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/app/+/5414", "patchset_count": 1, "commit_id": "4981a9f7c604b2c75efb8e4a67f0f87beb0e2e31", "insertions": 13, "deletions": 1}, {"change_id": "If04e83a4436abecf9441e938f53ed3f9dadb0d13", "project": "ci/firmware_update", "branch": "develop", "subject": "ota: 更新补丁添加功能 添加蓝牙和GPS升级的方法 修改rsc包存在的目录", "status": "MERGED", "created": 1751013853, "updated": 1751014107, "mergeable": false, "number": 5250, "owner": "jinx<PERSON><PERSON>", "url": "http://********:8081/c/ci/firmware_update/+/5250", "patchset_count": 1, "commit_id": "f18d01af44891e7aa85c44fcac290e2ea593ddcb", "insertions": 3853, "deletions": 0}, {"change_id": "I378f35e999f786856451e7c1194a44bd44220b5a", "project": "app", "branch": "develop", "subject": "lcpu_eide: enable goodix sdk v4300", "status": "MERGED", "created": 1750952179, "updated": 1751008273, "mergeable": false, "number": 5367, "owner": "linjizhao", "url": "http://********:8081/c/app/+/5367", "patchset_count": 3, "commit_id": "d827825f519c1484d13843660b0aa2a681454384", "insertions": 319, "deletions": 45}, {"change_id": "I378f35e999f786856451e7c1194a44bd44220b5a", "project": "qw_platform", "branch": "develop", "subject": "ppg: adaptation goodix algorithm SDK 4300", "status": "MERGED", "created": 1750951672, "updated": 1751008273, "mergeable": false, "number": 5366, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5366", "patchset_count": 5, "commit_id": "cefac397509b6fe46321f378f803a5db581c141c", "insertions": 1769, "deletions": 976}, {"change_id": "Ic13fcefa1f5752ba0706922e56861ea8b98aa14a", "project": "tools", "branch": "develop", "subject": "[QTrace]: 支持订阅计数打点显示", "status": "MERGED", "created": 1750986541, "updated": 1751007872, "mergeable": false, "number": 5370, "owner": "lixiaolong", "url": "http://********:8081/c/tools/+/5370", "patchset_count": 2, "commit_id": "7bc788c455757be3179040bbe99807e429ccda0a", "insertions": 1218, "deletions": 1056}, {"change_id": "Iece278a0e299cc5e71988e974710c0593411b2cd", "project": "sifli", "branch": "develop", "subject": "[sys_trace]: 开启pm打点", "status": "MERGED", "created": 1750986566, "updated": 1751007867, "mergeable": false, "number": 5371, "owner": "lixiaolong", "url": "http://********:8081/c/sifli/+/5371", "patchset_count": 2, "commit_id": "0083a7d82603a468291f4457054d72c78ba183e3", "insertions": 16, "deletions": 13}, {"change_id": "Iecf07a95ff343407d768a6104d1b8e4e6147e164", "project": "app", "branch": "develop", "subject": "[sys_trace]: 支持订阅状态打点", "status": "MERGED", "created": 1750986682, "updated": 1751007861, "mergeable": false, "number": 5373, "owner": "lixiaolong", "url": "http://********:8081/c/app/+/5373", "patchset_count": 2, "commit_id": "fbfc9b57bdf6ed9503277ebc3273491baa531263", "insertions": 48, "deletions": 0}, {"change_id": "Iecf07a95ff343407d768a6104d1b8e4e6147e164", "project": "qw_platform", "branch": "develop", "subject": "[sys_trace]: 增加订阅计数", "status": "MERGED", "created": 1750986588, "updated": 1751007861, "mergeable": false, "number": 5372, "owner": "lixiaolong", "url": "http://********:8081/c/qw_platform/+/5372", "patchset_count": 2, "commit_id": "e2597ea8c2fd316940b40d61b962e3b907025dd1", "insertions": 77, "deletions": 8}, {"change_id": "I8e31cd9fac8825d313687f62a8ba30ca4554f2f9", "project": "qw_platform", "branch": "develop", "subject": "[gui]: 修改sonarqube", "status": "ABANDONED", "created": 1751006921, "updated": 1751007786, "mergeable": false, "number": 5409, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5409", "patchset_count": 1, "commit_id": "d9b1825a0b0f319dce126664616e435030f51586", "insertions": 34, "deletions": 20}, {"change_id": "Ic900a6a5cbaedb93488d55d657d0db1475ab9d83", "project": "app", "branch": "wr02_release", "subject": "alg_fmk:igp_WR02_1.0.28_20250626 Release", "status": "MERGED", "created": 1750940281, "updated": 1751007467, "mergeable": false, "number": 5358, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/app/+/5358", "patchset_count": 2, "commit_id": "5eabc936b6a92d6927ff6d0740157508ce992855", "insertions": 26, "deletions": 3}, {"change_id": "I7779435eb97a96e8ff66576e5d4ab5e593d454e5", "project": "qw_platform", "branch": "develop", "subject": "[gui]: 修改训练课程卡片", "status": "MERGED", "created": 1750996841, "updated": 1751006844, "mergeable": false, "number": 5404, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5404", "patchset_count": 2, "commit_id": "65c0fa6a1eb5fae9c44e7cc8ee2564b6e899ead6", "insertions": 15, "deletions": 4}, {"change_id": "I7779435eb97a96e8ff66576e5d4ab5e593d454e5", "project": "sifli", "branch": "develop", "subject": "[gui]: 修改压缩buff大小，有1/2屏扩大至2/3屏，解决EXDMA死机，遗留问题EXDMA报错，屏幕出现横线", "status": "MERGED", "created": 1750996834, "updated": 1751006844, "mergeable": false, "number": 5403, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/sifli/+/5403", "patchset_count": 1, "commit_id": "fec4a9e78209b63e62fcdd96b6240a51c351f89d", "insertions": 12, "deletions": 1}, {"change_id": "I7779435eb97a96e8ff66576e5d4ab5e593d454e5", "project": "app", "branch": "develop", "subject": "[gui]: 修改聚焦背景图bin文件，修改运动准备页进入AOD模式，禁用lcd最小buf刷新", "status": "MERGED", "created": 1750996852, "updated": 1751006843, "mergeable": false, "number": 5405, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5405", "patchset_count": 2, "commit_id": "87aa3b9bf2d83bb65731120cc94feba90297293c", "insertions": 16, "deletions": 3}, {"change_id": "I88f233d253fd7024f378bda91185544cf5aa2585", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 新增gps枚举指令", "status": "MERGED", "created": 1751006213, "updated": 1751006630, "mergeable": false, "number": 5408, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/5408", "patchset_count": 2, "commit_id": "611b38062a048bf6f82280ac3df1e313fdc22708", "insertions": 14, "deletions": 0}, {"change_id": "I8136fee7dca1cf84e2a9f986e4c0e2b53e2ea151", "project": "app", "branch": "wr02_release", "subject": "SERVICE: modify the update logic for resting heart rate", "status": "MERGED", "created": 1751005542, "updated": 1751006277, "mergeable": false, "number": 5384, "owner": "hongxing", "url": "http://********:8081/c/app/+/5384", "patchset_count": 1, "commit_id": "85ed5d7380a7c0c1a9cb9812650a898b0b992577", "insertions": 23, "deletions": 7}, {"change_id": "I8136fee7dca1cf84e2a9f986e4c0e2b53e2ea151", "project": "app", "branch": "develop", "subject": "SERVICE: modify the update logic for resting heart rate", "status": "MERGED", "created": 1751003621, "updated": 1751005517, "mergeable": false, "number": 5406, "owner": "hongxing", "url": "http://********:8081/c/app/+/5406", "patchset_count": 3, "commit_id": "f48ab0ff04f035cb07f183c9e4defcaacebd979a", "insertions": 22, "deletions": 7}, {"change_id": "I8d233d41ebff43cdf974938cfff2f2366095712f", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 解决UI内存泄漏问题", "status": "MERGED", "created": 1750987660, "updated": 1751005337, "mergeable": false, "number": 5376, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/5376", "patchset_count": 3, "commit_id": "c830c7b0a466704301b2feb6ad56ec2934432434", "insertions": 29, "deletions": 3}, {"change_id": "I071c6ffa6815a5b7d541196198eaca8028e5cd72", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Build]: 更新版本至v1.18_beta1", "status": "MERGED", "created": 1750987660, "updated": 1751005330, "mergeable": false, "number": 5375, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/5375", "patchset_count": 3, "commit_id": "8f70de9714310175a0573f36a822ab0b0803af4e", "insertions": 12, "deletions": 2}, {"change_id": "I5da85075b8f9e5c8cad07bbd226c4f8e92602e1a", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Feature]: 新增转向预警", "status": "MERGED", "created": 1750988927, "updated": 1751005217, "mergeable": false, "number": 5402, "owner": "liquan", "url": "http://********:8081/c/cycle/app/+/5402", "patchset_count": 2, "commit_id": "1660e112adefbb002bcc1dbe95a2cb5571204295", "insertions": 42, "deletions": 5}, {"change_id": "Ied11d0937048381d1028c6d2e307ae699c09cb18", "project": "qw_platform", "branch": "develop", "subject": "ota: OTA单文件MD5校验加上重试机制。", "status": "MERGED", "created": 1750931470, "updated": 1750994567, "mergeable": false, "number": 5248, "owner": "jinx<PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5248", "patchset_count": 3, "commit_id": "e734975600902629cfa132e22309c07c7e351db9", "insertions": 118, "deletions": 46}, {"change_id": "I099a87c3a4ee03991bf25c35b35199eb38c25d7d", "project": "tools", "branch": "develop", "subject": "ota: 增加拷贝脚本", "status": "MERGED", "created": 1750993545, "updated": 1750994542, "mergeable": false, "number": 5249, "owner": "jinx<PERSON><PERSON>", "url": "http://********:8081/c/tools/+/5249", "patchset_count": 1, "commit_id": "f266f817e5140b9e0ca9cc217d56e6f32c4fed4d", "insertions": 217, "deletions": 0}, {"change_id": "I0300dc1463d982bd2368faa5ae2c6c70519c6690", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Feature]: 更新GPS信号图标，无信号时闪烁", "status": "MERGED", "created": 1750988927, "updated": 1750993477, "mergeable": false, "number": 5401, "owner": "liquan", "url": "http://********:8081/c/cycle/app/+/5401", "patchset_count": 2, "commit_id": "a1687fbde083d5018d56837c370b38c8e5b9fe37", "insertions": 610, "deletions": 552}, {"change_id": "I12c28d457c9e79de188f4d82276c1980b2699726", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Optimize]: 优化坡度分级，优化网格页图形化显示效果", "status": "MERGED", "created": 1750988927, "updated": 1750993447, "mergeable": false, "number": 5380, "owner": "liquan", "url": "http://********:8081/c/cycle/app/+/5380", "patchset_count": 2, "commit_id": "21add00d012fa835b3424ede61b2e9bf31b67f23", "insertions": 444, "deletions": 86}, {"change_id": "I2351969f7668f6d4f72b6d6d36e65ac844be300e", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Feature]: 新增导航转向提示控制逻辑", "status": "MERGED", "created": 1750988927, "updated": 1750993436, "mergeable": false, "number": 5379, "owner": "liquan", "url": "http://********:8081/c/cycle/app/+/5379", "patchset_count": 1, "commit_id": "0dda45f02612e1d82bd8c4175954647f3d749cd0", "insertions": 1076, "deletions": 28}, {"change_id": "I7bd26401cc52508ca8f22010772f3f4ae58a5cd8", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Feature]: 新增工厂界面检测RTC", "status": "MERGED", "created": 1750988927, "updated": 1750993421, "mergeable": false, "number": 5378, "owner": "liquan", "url": "http://********:8081/c/cycle/app/+/5378", "patchset_count": 1, "commit_id": "eefb7118155c925763bd0ebee20ade55e00c3052", "insertions": 114, "deletions": 13}, {"change_id": "I8eaff2bb9b6ec70e4711d9f02e48d409ecc34b86", "project": "app", "branch": "develop", "subject": "[GUI]: 更新模拟器字库文件", "status": "MERGED", "created": 1750987582, "updated": 1750990221, "mergeable": false, "number": 5374, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5374", "patchset_count": 2, "commit_id": "e500b8f184f21c982daff983cbc3a0c2b86cf97d", "insertions": 10, "deletions": 0}, {"change_id": "I0d615f13890b1ba93efcd34647902370e103d724", "project": "qw_platform", "branch": "develop", "subject": "ui:修复导航文件名称漏字", "status": "MERGED", "created": 1750923885, "updated": 1750990220, "mergeable": false, "number": 5320, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5320", "patchset_count": 2, "commit_id": "ea2355ab66872b056bf4dc9f62031e0dc6cb8929", "insertions": 17, "deletions": 5}, {"change_id": "I0d615f13890b1ba93efcd34647902370e103d724", "project": "app", "branch": "develop", "subject": "ui:修复导航文件名称漏字", "status": "MERGED", "created": 1750923836, "updated": 1750990219, "mergeable": false, "number": 5319, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5319", "patchset_count": 2, "commit_id": "7312dadd6181b0f21db68d3996d85b8179502eaf", "insertions": 16, "deletions": 2}, {"change_id": "I9d02d549170e9dc60f48c6a3ace4d96a6680edf5", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Feature]: 新增根据APP是否支持偏航规划适配UI及离线规划逻辑", "status": "MERGED", "created": 1750988927, "updated": 1750990144, "mergeable": false, "number": 5377, "owner": "liquan", "url": "http://********:8081/c/cycle/app/+/5377", "patchset_count": 1, "commit_id": "e5a02ea1ad9c853ca5cd428fc6ce43c63fab514b", "insertions": 202, "deletions": 38}, {"change_id": "I14d625b3d7dffb2533da28db3b9dd09d14a7708d", "project": "app", "branch": "wr02_release", "subject": "factory : add factory ble ant+ menu.", "status": "ABANDONED", "created": 1750955412, "updated": 1750956056, "mergeable": false, "number": 5368, "owner": "gaoxing", "url": "http://********:8081/c/app/+/5368", "patchset_count": 1, "commit_id": "15c4103e34029319d11e11f21f9e21faa1851d1a", "insertions": 7070, "deletions": 60}, {"change_id": "I14d625b3d7dffb2533da28db3b9dd09d14a7708d", "project": "qw_platform", "branch": "wr02_release", "subject": "factory : add factory ble ant+ menu.", "status": "ABANDONED", "created": 1750955423, "updated": 1750956040, "mergeable": false, "number": 5369, "owner": "gaoxing", "url": "http://********:8081/c/qw_platform/+/5369", "patchset_count": 1, "commit_id": "bbd0b753a57e1a2b2c8cbd06c45a896cfa6aeb80", "insertions": 54, "deletions": 0}, {"change_id": "I158c5b55c091fbb6e4d40a27b0b305ea26030282", "project": "qw_platform", "branch": "wr02_release", "subject": "gh3020: add gh3020 v4300 sdk", "status": "MERGED", "created": 1750955224, "updated": 1750955650, "mergeable": false, "number": 5383, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5383", "patchset_count": 1, "commit_id": "53860bc0ec39918768ff9d6ee6a17dc9ccaa5ced", "insertions": 49857, "deletions": 0}, {"change_id": "I158c5b55c091fbb6e4d40a27b0b305ea26030282", "project": "qw_platform", "branch": "develop", "subject": "gh3020: add gh3020 v4300 sdk", "status": "MERGED", "created": 1750943516, "updated": 1750948049, "mergeable": false, "number": 5363, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5363", "patchset_count": 1, "commit_id": "722fc56b6a5ef69558bf9702a97e446a4aafc439", "insertions": 49856, "deletions": 0}, {"change_id": "Icae136c79e5c90ec738855296392cdd75c2608cd", "project": "app", "branch": "wr02_algosim", "subject": "alg_fmk:数据倒灌更新igp_WR02_1.0.28_20250626 Release", "status": "MERGED", "created": 1750940878, "updated": 1750943569, "mergeable": false, "number": 5338, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/app/+/5338", "patchset_count": 1, "commit_id": "4a571521516eb6c21051436bd5994e49c86f9cdd", "insertions": 12, "deletions": 0}, {"change_id": "I64ba1eaa0426eb7ec2d59973e7a7a2cc99ec3c71", "project": "app", "branch": "develop", "subject": "[GM]: 数据大小使用错误，有溢出的风险", "status": "MERGED", "created": 1750941847, "updated": 1750943303, "mergeable": false, "number": 5361, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5361", "patchset_count": 1, "commit_id": "59e9a1ea65e0ff0da34b0b538fcc73fa0877a8df", "insertions": 14, "deletions": 4}, {"change_id": "I64ba1eaa0426eb7ec2d59973e7a7a2cc99ec3c71", "project": "app", "branch": "wr02_release", "subject": "[GM]: 数据大小使用错误，有溢出的风险", "status": "MERGED", "created": 1750941961, "updated": 1750943301, "mergeable": false, "number": 5360, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5360", "patchset_count": 1, "commit_id": "dc82f36cae39078ac2f04d979e2f4470a1b38a39", "insertions": 12, "deletions": 2}, {"change_id": "I3a6095cb4aa35a3b1339cec332ac066af095444a", "project": "app", "branch": "wr02_release", "subject": "develop : factory gps signal menu merge fix.", "status": "ABANDONED", "created": 1750941391, "updated": 1750942000, "mergeable": false, "number": 5339, "owner": "gaoxing", "url": "http://********:8081/c/app/+/5339", "patchset_count": 1, "commit_id": "d36923d5e62c51856b14f00e14cbb2c4200ac3fd", "insertions": 241, "deletions": 20}, {"change_id": "I4aab9d69098fb6dd18e70df0c79082e572f6e8d5", "project": "app", "branch": "wr02_algosim", "subject": "alg_fmk:睡眠算法数据倒灌实现", "status": "MERGED", "created": 1750650870, "updated": 1750940544, "mergeable": false, "number": 5169, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/app/+/5169", "patchset_count": 1, "commit_id": "f85af0f4c449ab087f94884567b3e2ae0a8e83ee", "insertions": 60, "deletions": 4}, {"change_id": "Ic900a6a5cbaedb93488d55d657d0db1475ab9d83", "project": "app", "branch": "develop", "subject": "alg_fmk:igp_WR02_1.0.28_20250626 Release", "status": "MERGED", "created": 1750939571, "updated": 1750940199, "mergeable": false, "number": 5337, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/app/+/5337", "patchset_count": 3, "commit_id": "937eb8d63c5138233157c032bc825e98e102172c", "insertions": 25, "deletions": 3}, {"change_id": "I827159a775b1ce1f22b4840840dea542091f0f50", "project": "app", "branch": "develop", "subject": "[algo]: 改写运动算法逻辑中的日志打印", "status": "MERGED", "created": 1750936042, "updated": 1750938579, "mergeable": false, "number": 5336, "owner": "lixin", "url": "http://********:8081/c/app/+/5336", "patchset_count": 3, "commit_id": "1484cf2f56a0dba07a4824fbebaedabd3ffe0f8c", "insertions": 3140, "deletions": 3506}, {"change_id": "I5f140fd428065e466dc620c1df4101ef78d03766", "project": "qw_platform", "branch": "develop", "subject": "[dev]: 新增EPO自动同步申请相关代码的总体控制宏", "status": "MERGED", "created": 1750928133, "updated": 1750938405, "mergeable": false, "number": 5326, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5326", "patchset_count": 2, "commit_id": "4311944b8d41937e2ead85f44292f44f96de1ebe", "insertions": 11, "deletions": 1}, {"change_id": "I5f140fd428065e466dc620c1df4101ef78d03766", "project": "app", "branch": "develop", "subject": "[dev]: 接入EPO定时2min自动申请同步的开发者选项", "status": "MERGED", "created": 1750929042, "updated": 1750938404, "mergeable": false, "number": 5329, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5329", "patchset_count": 3, "commit_id": "0a24172bade017e8c77e29ff29dc4e1447b6a5e9", "insertions": 235, "deletions": 13}, {"change_id": "I68d9e3a1f2407c261e407643d560eea23d735826", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 解决内存分配失败在开机动画反复重启问题", "status": "MERGED", "created": 1750919441, "updated": 1750936904, "mergeable": false, "number": 5315, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/5315", "patchset_count": 2, "commit_id": "b1dc25beaf374e7930240a6931c7e6ade9814121", "insertions": 69, "deletions": 33}, {"change_id": "I9126d8e045368458aeb60244d4ea6808102b30f0", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 解决schedule编码和fit存储可能存在冲突的问题", "status": "MERGED", "created": 1750839661, "updated": 1750936669, "mergeable": false, "number": 5270, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/5270", "patchset_count": 1, "commit_id": "7fb6efac3b09a19c913e8b79553de7714717daac", "insertions": 12, "deletions": 1}, {"change_id": "I5b759c0df692c95ba093d12658ac55134f53702e", "project": "app", "branch": "wr02_release", "subject": "SERVICE: the way to modify the data subscription method for resting heart rate", "status": "MERGED", "created": 1750935843, "updated": 1750936584, "mergeable": false, "number": 5355, "owner": "hongxing", "url": "http://********:8081/c/app/+/5355", "patchset_count": 1, "commit_id": "0f6a6e624283ab2762c4820cb7d931cd6f1757e3", "insertions": 19, "deletions": 15}, {"change_id": "Ia6097ace9359655f93626d4fd905b527f80282c6", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 修正裁剪区域计算问题", "status": "MERGED", "created": 1750770765, "updated": 1750936544, "mergeable": false, "number": 5231, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/5231", "patchset_count": 1, "commit_id": "aa74228fba276d97b01032a6a0b1a6e00dc54912", "insertions": 30, "deletions": 10}, {"change_id": "Ib437fd5d65881c521f04a070276887ba4959a3f3", "project": "qw_platform", "branch": "develop", "subject": "qw_platform : 工模添加外设传感器界面", "status": "ABANDONED", "created": 1749467112, "updated": 1750936445, "mergeable": false, "number": 4817, "owner": "gaoxing", "url": "http://********:8081/c/qw_platform/+/4817", "patchset_count": 3, "commit_id": "5013078fde92039c2a936a8c8fc40040e2caba5c", "insertions": 54, "deletions": 0}, {"change_id": "I7c75b4aac1a4b5578ba9282a5efb804ee9faf14a", "project": "qw_platform", "branch": "develop", "subject": "[GUI]: 更新字库翻译", "status": "MERGED", "created": 1750928424, "updated": 1750936243, "mergeable": false, "number": 5328, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5328", "patchset_count": 2, "commit_id": "527bab60ab12bbec7b38e75fc57811a4b09e0ffe", "insertions": 257, "deletions": 72}, {"change_id": "I7c75b4aac1a4b5578ba9282a5efb804ee9faf14a", "project": "app", "branch": "develop", "subject": "[GUI]:更新字库版本。更新HRV翻译KEY。", "status": "MERGED", "created": 1750928414, "updated": 1750936242, "mergeable": false, "number": 5327, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5327", "patchset_count": 2, "commit_id": "fdac02a3b7f436df8e790008f9464f6d80e8e6cc", "insertions": 19, "deletions": 3}, {"change_id": "I65cf35f8b460d6f366ccdd3c214a0a4fd65c8b5c", "project": "qw_platform", "branch": "develop", "subject": "[sonar]: 解决sonar bug", "status": "MERGED", "created": 1750935189, "updated": 1750935834, "mergeable": false, "number": 5335, "owner": "lixin", "url": "http://********:8081/c/qw_platform/+/5335", "patchset_count": 1, "commit_id": "8a55813f36750920b639ad182e9c09d2603e54e1", "insertions": 21, "deletions": 3}, {"change_id": "I9d7b873ef2ddbdf190ecf755cc29461a775e09d8", "project": "app", "branch": "wr02_release", "subject": "SERVICE: modify the HRV data reporting logic", "status": "MERGED", "created": 1750931545, "updated": 1750935809, "mergeable": false, "number": 5352, "owner": "hongxing", "url": "http://********:8081/c/app/+/5352", "patchset_count": 1, "commit_id": "7abfec17e864637cd5c177ba98e4db94f1b86575", "insertions": 87, "deletions": 208}, {"change_id": "I55067e62c03aa373229b3c7cbc4360d0a2ec6355", "project": "app", "branch": "develop", "subject": "develop : fix burn test bug.", "status": "MERGED", "created": 1750925449, "updated": 1750932248, "mergeable": false, "number": 5321, "owner": "gaoxing", "url": "http://********:8081/c/app/+/5321", "patchset_count": 5, "commit_id": "6a81c1055ed56b16ed83d7c592fac0b8f66ed9e8", "insertions": 52, "deletions": 34}, {"change_id": "I9d7b873ef2ddbdf190ecf755cc29461a775e09d8", "project": "app", "branch": "develop", "subject": "SERVICE: modify the HRV data reporting logic", "status": "MERGED", "created": 1750920568, "updated": 1750931528, "mergeable": false, "number": 5317, "owner": "hongxing", "url": "http://********:8081/c/app/+/5317", "patchset_count": 2, "commit_id": "74dc1bf5b03203e7337fc7f94219e6832ee67489", "insertions": 86, "deletions": 208}, {"change_id": "I3af744d690cce6a20c9f9d7ae685bebfbd79d6a9", "project": "app", "branch": "wr02_release", "subject": "SERVICE: increase and expand the SensorHub channels to prevent data overflow", "status": "MERGED", "created": 1750930277, "updated": 1750931053, "mergeable": false, "number": 5351, "owner": "hongxing", "url": "http://********:8081/c/app/+/5351", "patchset_count": 2, "commit_id": "a1b4d6b5c7095be1a4a4ee9667ab94eb12a72235", "insertions": 43, "deletions": 33}, {"change_id": "I3af744d690cce6a20c9f9d7ae685bebfbd79d6a9", "project": "sifli", "branch": "wr02_release", "subject": "SIFLI: add an IPC channel for sensorhub", "status": "MERGED", "created": 1750930262, "updated": 1750931052, "mergeable": false, "number": 5350, "owner": "hongxing", "url": "http://********:8081/c/sifli/+/5350", "patchset_count": 1, "commit_id": "8c2308ab3463a16dabadf648a335fec9bd0d8360", "insertions": 33, "deletions": 2}, {"change_id": "I55067e62c03aa373229b3c7cbc4360d0a2ec6355", "project": "app", "branch": "wr02_release", "subject": "develop : fix burn test bug.", "status": "MERGED", "created": 1750925529, "updated": 1750930900, "mergeable": false, "number": 5299, "owner": "gaoxing", "url": "http://********:8081/c/app/+/5299", "patchset_count": 3, "commit_id": "8428455dbfe2900cd7b5845433289bf986d6a878", "insertions": 52, "deletions": 34}, {"change_id": "Ifb09fc42d48146d09f722d245a2a06847195ff87", "project": "app", "branch": "wr02_release", "subject": "SERVICE: modify the acquisition method of photosensitive raw data", "status": "MERGED", "created": 1750929059, "updated": 1750930224, "mergeable": false, "number": 5349, "owner": "hongxing", "url": "http://********:8081/c/app/+/5349", "patchset_count": 1, "commit_id": "5c8116f312114da31bb9dc25b08f85f642c2f83f", "insertions": 39, "deletions": 32}, {"change_id": "Ifb09fc42d48146d09f722d245a2a06847195ff87", "project": "qw_platform", "branch": "wr02_release", "subject": "PLATFORM: add the function to write the photosensitive raw data into the shared memory", "status": "MERGED", "created": 1750926805, "updated": 1750930223, "mergeable": false, "number": 5343, "owner": "hongxing", "url": "http://********:8081/c/qw_platform/+/5343", "patchset_count": 2, "commit_id": "45a16897625e5d2cb098ed638b9392b13ac5c31b", "insertions": 50, "deletions": 0}, {"change_id": "Id996c4fd919399a0269b9e63f8673cc14817eec7", "project": "app", "branch": "wr02_release", "subject": "app:自动亮度优化", "status": "MERGED", "created": 1750928177, "updated": 1750929033, "mergeable": false, "number": 5348, "owner": "hongxing", "url": "http://********:8081/c/app/+/5348", "patchset_count": 2, "commit_id": "b2c70faaefb026e3cd80c70e1abb9e9345236690", "insertions": 226, "deletions": 215}, {"change_id": "I091cbaa235dcf6bfa94f304e489bcd5e5d6d5aa8", "project": "qw_platform", "branch": "wr02_release", "subject": "gh3020: delete gh3020 v4100 sdk", "status": "MERGED", "created": 1750927907, "updated": 1750928472, "mergeable": false, "number": 5347, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5347", "patchset_count": 1, "commit_id": "1ab2946993933875ca4342a1a8b257457dc3f256", "insertions": 11, "deletions": 30012}, {"change_id": "I091cbaa235dcf6bfa94f304e489bcd5e5d6d5aa8", "project": "app", "branch": "wr02_release", "subject": "lcpu_eide: delete gh3020 v4100 sdk files.", "status": "MERGED", "created": 1750927897, "updated": 1750928472, "mergeable": false, "number": 5346, "owner": "linjizhao", "url": "http://********:8081/c/app/+/5346", "patchset_count": 2, "commit_id": "41ff46d644e460256650861fc0d4da20814b9b40", "insertions": 10, "deletions": 108}, {"change_id": "I8af6f5b2e85f2cf8e1d99c1bc7d374dbae5ba594", "project": "app", "branch": "wr02_release", "subject": "auto_light:优化自动亮度初值检测、精简计算步骤", "status": "MERGED", "created": 1750927132, "updated": 1750928108, "mergeable": false, "number": 5345, "owner": "hongxing", "url": "http://********:8081/c/app/+/5345", "patchset_count": 1, "commit_id": "be3a73aa92e6714ee1b4b20b4544b710761b419b", "insertions": 196, "deletions": 240}, {"change_id": "I091cbaa235dcf6bfa94f304e489bcd5e5d6d5aa8", "project": "app", "branch": "develop", "subject": "lcpu_eide: delete gh3020 v4100 sdk files.", "status": "MERGED", "created": 1750926923, "updated": 1750927899, "mergeable": false, "number": 5325, "owner": "linjizhao", "url": "http://********:8081/c/app/+/5325", "patchset_count": 2, "commit_id": "756b1f496682f40618902ce8f53800299c6ed848", "insertions": 10, "deletions": 108}, {"change_id": "I091cbaa235dcf6bfa94f304e489bcd5e5d6d5aa8", "project": "qw_platform", "branch": "develop", "subject": "gh3020: delete gh3020 v4100 sdk", "status": "MERGED", "created": 1750926833, "updated": 1750927898, "mergeable": false, "number": 5324, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5324", "patchset_count": 3, "commit_id": "a24ec5a459e48de92174421092defabb4f2637ff", "insertions": 10, "deletions": 30012}, {"change_id": "Iff30bd002db602155dfa635c02796de8e6d27190", "project": "qw_platform", "branch": "develop", "subject": "ota: 添加OTA状态/错误 反馈接口", "status": "MERGED", "created": 1750922411, "updated": 1750927354, "mergeable": false, "number": 5246, "owner": "jinx<PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5246", "patchset_count": 3, "commit_id": "0f962389844e34b8569bd2c0d52bba104621b836", "insertions": 501, "deletions": 12}, {"change_id": "Iff30bd002db602155dfa635c02796de8e6d27190", "project": "app", "branch": "develop", "subject": "ota: 添加OTA状态/错误 反馈接口", "status": "MERGED", "created": 1750922465, "updated": 1750927353, "mergeable": false, "number": 5247, "owner": "jinx<PERSON><PERSON>", "url": "http://********:8081/c/app/+/5247", "patchset_count": 2, "commit_id": "5f44107fe9d5d701894ed7b20d4258bb91f5a627", "insertions": 49, "deletions": 47}, {"change_id": "Ib437fd5d65881c521f04a070276887ba4959a3f3", "project": "app", "branch": "develop", "subject": "app : 工模添加外设传感器界面", "status": "ABANDONED", "created": 1749467103, "updated": 1750926663, "mergeable": false, "number": 4816, "owner": "gaoxing", "url": "http://********:8081/c/app/+/4816", "patchset_count": 6, "commit_id": "623bd7102c8e004abd63263d5b26a44d3f87ad54", "insertions": 7193, "deletions": 357}, {"change_id": "I600092e4906d7e643bd3db9b7f8af3446d69a3b1", "project": "app", "branch": "wr02_release", "subject": "mem_map: set mailbox memory to 8K", "status": "MERGED", "created": 1750925946, "updated": 1750926568, "mergeable": false, "number": 5341, "owner": "hongxing", "url": "http://********:8081/c/app/+/5341", "patchset_count": 1, "commit_id": "1f6cb12abaf6860fe27f720c65332abed7a230dd", "insertions": 11, "deletions": 12}, {"change_id": "I600092e4906d7e643bd3db9b7f8af3446d69a3b1", "project": "sifli", "branch": "wr02_release", "subject": "mem_map: set mailbox memory to 8K", "status": "MERGED", "created": 1750925781, "updated": 1750926568, "mergeable": false, "number": 5300, "owner": "hongxing", "url": "http://********:8081/c/sifli/+/5300", "patchset_count": 2, "commit_id": "e50821aa10165b495d4351e7239fb5da77326ef4", "insertions": 15, "deletions": 4}, {"change_id": "Ife84984ba017d648af42dc2ebd3cff2cdb9f8e98", "project": "app", "branch": "wr02_release", "subject": "alg_fmk:睡眠期间静息上报异常修改", "status": "MERGED", "created": 1750921541, "updated": 1750922689, "mergeable": false, "number": 5298, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/app/+/5298", "patchset_count": 1, "commit_id": "d3fa56b19dcf193b43904863f9a4bf021c99b888", "insertions": 78, "deletions": 9}, {"change_id": "Ife84984ba017d648af42dc2ebd3cff2cdb9f8e98", "project": "app", "branch": "develop", "subject": "alg_fmk:睡眠期间静息上报异常修改", "status": "MERGED", "created": 1750921380, "updated": 1750922687, "mergeable": false, "number": 5318, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/app/+/5318", "patchset_count": 2, "commit_id": "affac2abf7de13c6c59db7d1a3b62114c7aec457", "insertions": 78, "deletions": 9}, {"change_id": "Ie1dbfe116f4772e9dd703a0ea78d90ed90e161b4", "project": "app", "branch": "develop", "subject": "app:开机后恢复专注任务", "status": "MERGED", "created": 1750915950, "updated": 1750920613, "mergeable": false, "number": 5314, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5314", "patchset_count": 3, "commit_id": "a68f28219e548b50cabc6200ed04a5a5cecea403", "insertions": 59, "deletions": 41}, {"change_id": "I58fb8f6d170e8e8203f7818d609c072f8430933b", "project": "app", "branch": "develop", "subject": "GUI: 解决点测心率血氧压力不出值后弹提醒弹窗导致死机问题", "status": "MERGED", "created": 1750919441, "updated": 1750920312, "mergeable": false, "number": 5316, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5316", "patchset_count": 1, "commit_id": "c10b46815b68e4461a34279b0c9ef1959bc14311", "insertions": 22, "deletions": 8}, {"change_id": "Ie4c53ecb556f06aceec5fec94ffb26c08da603e0", "project": "app", "branch": "develop", "subject": "app : 修复老化版本重复开关卡死问题，优化逻辑:1.在老化重启后，进入工模直接跳转到老化测试界面，继续老化测试;2.在老化过程中不容许退出老化界面，只有关闭老化测试才能（老化时间长防止意外误操作退出老化）", "status": "MERGED", "created": 1750910990, "updated": 1750916628, "mergeable": false, "number": 5313, "owner": "gaoxing", "url": "http://********:8081/c/app/+/5313", "patchset_count": 1, "commit_id": "6d3c4a6c747aa9988d14687e108b33a46c8e59e5", "insertions": 78, "deletions": 26}, {"change_id": "I285f8316f16abb93a806e5dee0ede0153f5e878b", "project": "cycle/20250607_fc06", "branch": "develop", "subject": "[Feature]: 1.骑行事件初步处理 2.参数调整", "status": "MERGED", "created": 1750899921, "updated": 1750915947, "mergeable": false, "number": 5280, "owner": "hancheng", "url": "http://********:8081/c/cycle/20250607_fc06/+/5280", "patchset_count": 2, "commit_id": "75eed3b07796d6916e74d15c5db4c6634b69e7e4", "insertions": 81701, "deletions": 77772}, {"change_id": "I8693f9241b38e5cbe0c92b26b53cdcb2fea632a0", "project": "cycle/20250607_fc06", "branch": "develop", "subject": "[Feature]: 记录页相关", "status": "MERGED", "created": 1750840699, "updated": 1750915941, "mergeable": false, "number": 5271, "owner": "hancheng", "url": "http://********:8081/c/cycle/20250607_fc06/+/5271", "patchset_count": 2, "commit_id": "7d99c9a353924b0143e7842e7b90e8439b1f5f95", "insertions": 540, "deletions": 141}, {"change_id": "Ibbea310bf0aeb14ad2d7f64d84f7cf75157baf1d", "project": "app", "branch": "develop", "subject": "app : 工模修改ACC界面", "status": "ABANDONED", "created": 1749284783, "updated": 1750912078, "mergeable": false, "number": 4765, "owner": "gaoxing", "url": "http://********:8081/c/app/+/4765", "patchset_count": 4, "commit_id": "77e405c7e2b3542cbe4c10b59f5e787152639730", "insertions": 258, "deletions": 42}, {"change_id": "I2f388e853da6a65292177cb1da441989c7b592c7", "project": "app", "branch": "develop", "subject": "app : 调整马达蜂鸣器的任务优先级", "status": "ABANDONED", "created": 1749788658, "updated": 1750912059, "mergeable": false, "number": 4933, "owner": "gaoxing", "url": "http://********:8081/c/app/+/4933", "patchset_count": 3, "commit_id": "6549571be02e9d5058b1ef4fdb1baa13cd90331c", "insertions": 16, "deletions": 6}, {"change_id": "Ic75e4f4bcc045562c301f24d0bf958c1a3470b8e", "project": "sifli", "branch": "develop", "subject": "sifli : 将马达RST脚初始化提前，在RESET前拉高VDD，添加支付宝IC复位测试和使用测试", "status": "ABANDONED", "created": 1749788166, "updated": 1750912039, "mergeable": false, "number": 4932, "owner": "gaoxing", "url": "http://********:8081/c/sifli/+/4932", "patchset_count": 3, "commit_id": "8d6466ca233ca98580ee686b15e9e7c82af24633", "insertions": 62, "deletions": 4}, {"change_id": "Ie3022c0d6ed96d36ad4ea6a29c4b664fd432191c", "project": "qw_platform", "branch": "develop", "subject": "qw_platform : 工厂模式宏开后默认跳转到工厂固件", "status": "MERGED", "created": 1750905910, "updated": 1750911998, "mergeable": false, "number": 5304, "owner": "gaoxing", "url": "http://********:8081/c/qw_platform/+/5304", "patchset_count": 2, "commit_id": "8d17810a2c99784f3a5367ddcd2d27aff59db9d5", "insertions": 14, "deletions": 0}, {"change_id": "Ie3022c0d6ed96d36ad4ea6a29c4b664fd432191c", "project": "qw_platform", "branch": "wr02_release", "subject": "qw_platform : 工厂模式宏开后默认跳转到工厂固件", "status": "MERGED", "created": 1750906047, "updated": 1750911997, "mergeable": false, "number": 5294, "owner": "gaoxing", "url": "http://********:8081/c/qw_platform/+/5294", "patchset_count": 2, "commit_id": "93d252045d9e54bddf8574104c5e1a9c2d2dd35b", "insertions": 14, "deletions": 0}, {"change_id": "Ie4c53ecb556f06aceec5fec94ffb26c08da603e0", "project": "app", "branch": "wr02_release", "subject": "app : 修复老化版本重复开关卡死问题，优化逻辑:1.在老化重启后，进入工模直接跳转到老化测试界面，继续老化测试;2.在老化过程中不容许退出老化界面，只有关闭老化测试才能（老化时间长防止意外误操作退出老化）", "status": "MERGED", "created": 1750911065, "updated": 1750911792, "mergeable": false, "number": 5296, "owner": "gaoxing", "url": "http://********:8081/c/app/+/5296", "patchset_count": 1, "commit_id": "1f874bb83e2108ab1e882c6cfa9f8cd038427e8a", "insertions": 78, "deletions": 26}, {"change_id": "I0345526c026f20bef2e22dcadbb831661d2aad0e", "project": "qw_platform", "branch": "wr02_release", "subject": "qwplatform : release版本添加专用宏", "status": "MERGED", "created": 1750907664, "updated": 1750910401, "mergeable": false, "number": 5310, "owner": "gaoxing", "url": "http://********:8081/c/qw_platform/+/5310", "patchset_count": 1, "commit_id": "e6d06f42f39a57e049d5f6d53a0a5e1ffc235f07", "insertions": 13, "deletions": 0}, {"change_id": "I3af744d690cce6a20c9f9d7ae685bebfbd79d6a9", "project": "app", "branch": "develop", "subject": "SERVICE: increase and expand the SensorHub channels to prevent data overflow", "status": "MERGED", "created": 1750909386, "updated": 1750909812, "mergeable": false, "number": 5312, "owner": "hongxing", "url": "http://********:8081/c/app/+/5312", "patchset_count": 1, "commit_id": "dfc9c1b540a732197eac6acacc51addbe0975994", "insertions": 42, "deletions": 33}, {"change_id": "I3af744d690cce6a20c9f9d7ae685bebfbd79d6a9", "project": "sifli", "branch": "develop", "subject": "SIFLI: add an IPC channel for sensorhub", "status": "MERGED", "created": 1750909237, "updated": 1750909812, "mergeable": false, "number": 5311, "owner": "hongxing", "url": "http://********:8081/c/sifli/+/5311", "patchset_count": 2, "commit_id": "03bdefc38b954f345fa0cb5c00e0653c785ee169", "insertions": 32, "deletions": 2}, {"change_id": "I0a29624beb9f4b174cc5bf605d0856035d371bf3", "project": "app", "branch": "wr02_release", "subject": "alg_fmk:导入静息心率修改，解决与脚本处理不一致问题", "status": "MERGED", "created": 1750908955, "updated": 1750909781, "mergeable": false, "number": 5295, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/app/+/5295", "patchset_count": 1, "commit_id": "b576a1a705900f8fa333ad781f18b283d86c868d", "insertions": 45, "deletions": 30}, {"change_id": "I1a477fd58430304d6096be5a4b170ec2ac04ebf1", "project": "sifli", "branch": "develop", "subject": "[GUI]: 18336 内容超长末尾没有“...”", "status": "MERGED", "created": 1750906878, "updated": 1750909242, "mergeable": false, "number": 5309, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/sifli/+/5309", "patchset_count": 1, "commit_id": "47b65a9138930fd7c7e6f50fdee16b2c77489809", "insertions": 11, "deletions": 1}, {"change_id": "I1a477fd58430304d6096be5a4b170ec2ac04ebf1", "project": "app", "branch": "develop", "subject": "[GUI]: 18336 内容超长末尾没有“...”", "status": "MERGED", "created": 1750906858, "updated": 1750909242, "mergeable": false, "number": 5307, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5307", "patchset_count": 2, "commit_id": "477267e68c2427f2c9fbd2fc0ac937898d3d2272", "insertions": 97, "deletions": 31}, {"change_id": "Ie0d5f76229d1999e8c5595425b9af7e9a628d44d", "project": "app", "branch": "wr02_release", "subject": "alg_fmk:1、导入静息心率自研算法 2、将hrv范围调整到[0,130]", "status": "MERGED", "created": 1750904209, "updated": 1750908926, "mergeable": false, "number": 5290, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/app/+/5290", "patchset_count": 3, "commit_id": "392a538efad5d8b976684eb8e5c50816fd2567f1", "insertions": 224, "deletions": 109}, {"change_id": "I90517553af827e3710fc6f667deb3e7355589c2e", "project": "app", "branch": "develop", "subject": "release: add CONFIG_RELEASE_VERSION marco", "status": "ABANDONED", "created": 1750906870, "updated": 1750907979, "mergeable": false, "number": 5308, "owner": "linjizhao", "url": "http://********:8081/c/app/+/5308", "patchset_count": 2, "commit_id": "dcfdf7559a3d3ab9807c1e2315c98d7f0ca7bed1", "insertions": 12, "deletions": 1}, {"change_id": "Ifb09fc42d48146d09f722d245a2a06847195ff87", "project": "app", "branch": "develop", "subject": "SERVICE: modify the acquisition method of photosensitive raw data", "status": "MERGED", "created": 1750906038, "updated": 1750907473, "mergeable": false, "number": 5305, "owner": "hongxing", "url": "http://********:8081/c/app/+/5305", "patchset_count": 2, "commit_id": "53fea0c96e264d6b04f0e7f3f8e80763281d3de9", "insertions": 38, "deletions": 32}, {"change_id": "Ifb09fc42d48146d09f722d245a2a06847195ff87", "project": "qw_platform", "branch": "develop", "subject": "PLATFORM: add the function to write the photosensitive raw data into the shared memory", "status": "MERGED", "created": 1750906047, "updated": 1750907472, "mergeable": false, "number": 5306, "owner": "hongxing", "url": "http://********:8081/c/qw_platform/+/5306", "patchset_count": 2, "commit_id": "b73f79bafbf55c15166c119e2dffa1891b4c4e93", "insertions": 49, "deletions": 0}, {"change_id": "I0ae3ae29494f6626a4f51a49732a9d1ddcbb966b", "project": "qw_platform", "branch": "wr02_release", "subject": "ppg: ppg irq io set to nopull in A3 && A4", "status": "MERGED", "created": 1750905895, "updated": 1750907035, "mergeable": false, "number": 5293, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5293", "patchset_count": 3, "commit_id": "59fa5da447741300a82750a959c855b1e93906db", "insertions": 13, "deletions": 4}, {"change_id": "I0ae3ae29494f6626a4f51a49732a9d1ddcbb966b", "project": "qw_platform", "branch": "develop", "subject": "ppg: ppg irq io set to nopull in A3 && A4", "status": "MERGED", "created": 1750905823, "updated": 1750907034, "mergeable": false, "number": 5303, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5303", "patchset_count": 2, "commit_id": "143c1bc3ad566174ee0150746525a52fd330b50b", "insertions": 13, "deletions": 4}, {"change_id": "Id3737a6f8d4d2dd21e31b6321315656d23a15efc", "project": "app", "branch": "develop", "subject": "[GUI]: 增加EPO timer enable test", "status": "ABANDONED", "created": 1750830243, "updated": 1750906992, "mergeable": false, "number": 5262, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5262", "patchset_count": 1, "commit_id": "ffc8e5ebd9bf9cb3543d76f64a0a1e6794a2174b", "insertions": 29, "deletions": 0}, {"change_id": "I3692176e206beaa8b58af7ee0f28b8c5b1c7d829", "project": "qw_platform", "branch": "wr02_release", "subject": "alg_fmk:acc和陀螺仪发布适配batch", "status": "MERGED", "created": 1750905801, "updated": 1750906366, "mergeable": false, "number": 5292, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5292", "patchset_count": 1, "commit_id": "2215ebd12e8bf3459eb6dcb28615d1aeefbcedb4", "insertions": 14, "deletions": 1}, {"change_id": "I3692176e206beaa8b58af7ee0f28b8c5b1c7d829", "project": "app", "branch": "wr02_release", "subject": "alg_fmk:acc和陀螺仪发布适配batch", "status": "MERGED", "created": 1750905749, "updated": 1750906365, "mergeable": false, "number": 5291, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/app/+/5291", "patchset_count": 2, "commit_id": "ccb3cfb1d003ca93cae9ac842e4b1c5a63419eda", "insertions": 306, "deletions": 45}, {"change_id": "I0a29624beb9f4b174cc5bf605d0856035d371bf3", "project": "app", "branch": "develop", "subject": "alg_fmk:导入静息心率修改，解决与脚本处理不一致问题", "status": "MERGED", "created": 1750842197, "updated": 1750906315, "mergeable": false, "number": 5273, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/app/+/5273", "patchset_count": 4, "commit_id": "35987a08c7d0ebecc6e96128cccca975e20f0727", "insertions": 44, "deletions": 30}, {"change_id": "I6b0ebf22a5c6efd9bc102018c969ca3329496b5d", "project": "app", "branch": "wr02_release", "subject": "gm_collect: set gomore collect buffer to psram", "status": "MERGED", "created": 1750903913, "updated": 1750905025, "mergeable": false, "number": 5289, "owner": "linjizhao", "url": "http://********:8081/c/app/+/5289", "patchset_count": 3, "commit_id": "0ed067d082842e45b2e04ac9b2788f6877d9e5d3", "insertions": 20, "deletions": 2}, {"change_id": "I9e114ade3cfa30364666f045b4c4069828076c2b", "project": "app", "branch": "wr02_release", "subject": "factory:优化传感器自检页面布局", "status": "MERGED", "created": 1750903305, "updated": 1750904385, "mergeable": false, "number": 5288, "owner": "linjizhao", "url": "http://********:8081/c/app/+/5288", "patchset_count": 2, "commit_id": "e888e22d093fea8f9e2291273614c3c3792d597e", "insertions": 50, "deletions": 8}, {"change_id": "I66099618bb69987318a584e91367c859ec65da67", "project": "sifli", "branch": "wr02_release", "subject": "sifli : 支付宝TP开机自检结果绑定", "status": "MERGED", "created": 1750903187, "updated": 1750903625, "mergeable": false, "number": 5287, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/5287", "patchset_count": 2, "commit_id": "09db8d0a839c03a683f2d52a4b5efafc0642959d", "insertions": 34, "deletions": 3}, {"change_id": "I902770c63030192d04df4aaa2128c145c2dac1dc", "project": "platform/external/proto", "branch": "master", "subject": "[proto]: 新增向APP请求app是否支持某项功能协议", "status": "MERGED", "created": 1750901435, "updated": 1750903569, "mergeable": false, "number": 5301, "owner": "liquan", "url": "http://********:8081/c/platform/external/proto/+/5301", "patchset_count": 2, "commit_id": "a0909177a7cf4df6ddcead8f64069dbb32bbafbd", "insertions": 18, "deletions": 2}, {"change_id": "I958f3d016848c2d19993046091100fab8025a336", "project": "platform/external/proto", "branch": "master", "subject": "[proto]: 新增传感器类型协议", "status": "MERGED", "created": 1749630209, "updated": 1750903556, "mergeable": false, "number": 4883, "owner": "liquan", "url": "http://********:8081/c/platform/external/proto/+/4883", "patchset_count": 2, "commit_id": "1ebd0af63bb9c7e7e8e55ec8fa58ccd6e76790d9", "insertions": 12, "deletions": 0}, {"change_id": "I4692ab639c34352c44128e32bb8411d23ed492c0", "project": "app", "branch": "wr02_release", "subject": "[GM]: 优化log存储", "status": "MERGED", "created": 1750900963, "updated": 1750903430, "mergeable": false, "number": 5286, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5286", "patchset_count": 2, "commit_id": "782ec1a99eb9e4fd223edcd48f54aea6dc4a036b", "insertions": 293, "deletions": 151}, {"change_id": "I4692ab639c34352c44128e32bb8411d23ed492c0", "project": "sifli", "branch": "wr02_release", "subject": "[GM]: 优化log存储", "status": "MERGED", "created": 1750900948, "updated": 1750903430, "mergeable": false, "number": 5285, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/sifli/+/5285", "patchset_count": 1, "commit_id": "cc6f53b860f631f8cf266fd53124e58775493250", "insertions": 35, "deletions": 24}, {"change_id": "I66099618bb69987318a584e91367c859ec65da67", "project": "sifli", "branch": "develop", "subject": "sifli : 支付宝TP开机自检结果绑定", "status": "MERGED", "created": 1749899039, "updated": 1750903400, "mergeable": false, "number": 4963, "owner": "gaoxing", "url": "http://********:8081/c/sifli/+/4963", "patchset_count": 3, "commit_id": "2e9f42cace0df4b0414ba3d8ebdf160cb73cc649", "insertions": 34, "deletions": 3}, {"change_id": "I9c3974320d8f3d28a4f581194ea85f7f841e74b5", "project": "qw_platform", "branch": "wr02_release", "subject": "[Mag]: 收集五次数据后再发，降低通信负载", "status": "MERGED", "created": 1750856615, "updated": 1750903339, "mergeable": false, "number": 5120, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5120", "patchset_count": 2, "commit_id": "a7d587d769558c01629d4b8a5cb40893e311121f", "insertions": 29, "deletions": 7}, {"change_id": "If870623a15db63142ece265898ce6844f6996876", "project": "qw_platform", "branch": "wr02_release", "subject": "sonarqube: fix cff.c bugs && add QW_ASSERT", "status": "MERGED", "created": 1750899523, "updated": 1750903285, "mergeable": false, "number": 5284, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5284", "patchset_count": 1, "commit_id": "5061bca77bab5808e27256436aadd57f9f6701e1", "insertions": 19, "deletions": 13}, {"change_id": "Ia026193310999534bc551ac6d31e088b7dbd0b11", "project": "tools", "branch": "develop", "subject": "ota: 修改位打包选择脚本，自动打包字库,蓝牙,GPS 添加字体文件到打包里面去", "status": "MERGED", "created": 1750901212, "updated": 1750902015, "mergeable": false, "number": 5245, "owner": "jinx<PERSON><PERSON>", "url": "http://********:8081/c/tools/+/5245", "patchset_count": 1, "commit_id": "29e63300c039f8e524437eeb68c141f12d0c2e30", "insertions": 621, "deletions": 256}, {"change_id": "I0b8050c0182876d6b53c54824db998850840ffd1", "project": "tools", "branch": "develop", "subject": "ota: 修改位打包选择脚本，自动打包字库,蓝牙,GPS", "status": "ABANDONED", "created": 1750846519, "updated": 1750901157, "mergeable": false, "number": 5244, "owner": "jinx<PERSON><PERSON>", "url": "http://********:8081/c/tools/+/5244", "patchset_count": 2, "commit_id": "89edc88b9121bca45b368d9dec93e79858d76086", "insertions": 355, "deletions": 164}, {"change_id": "I764cec09665717e856dd58934db63563d465e7ab", "project": "tools", "branch": "develop", "subject": "ota: 增加字库文件自动打包流程", "status": "ABANDONED", "created": 1750846519, "updated": 1750901157, "mergeable": false, "number": 5243, "owner": "jinx<PERSON><PERSON>", "url": "http://********:8081/c/tools/+/5243", "patchset_count": 2, "commit_id": "298127474376b19a5ff9a89811cdc5fe1bcad880", "insertions": 126, "deletions": 198}, {"change_id": "I473aa68f313f1b33e68d3c2882046ce062796c57", "project": "tools", "branch": "develop", "subject": "tool: 添加字体文件到打包里面去", "status": "ABANDONED", "created": 1750404978, "updated": 1750901157, "mergeable": false, "number": 4877, "owner": "jinx<PERSON><PERSON>", "url": "http://********:8081/c/tools/+/4877", "patchset_count": 2, "commit_id": "c22abfed3474e3e6393686a178dd24ad06125537", "insertions": 269, "deletions": 4}, {"change_id": "Ifc007802229dd2de3de93481e17f61f5a00c12fc", "project": "qw_platform", "branch": "wr02_release", "subject": "ppg: set ppg irq pulse cold time width to 20ms", "status": "MERGED", "created": 1750859360, "updated": 1750859849, "mergeable": false, "number": 5283, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5283", "patchset_count": 1, "commit_id": "c112fc9227f87740d8e9dab18f508f5f8ff91f25", "insertions": 1190, "deletions": 3}, {"change_id": "Ifc007802229dd2de3de93481e17f61f5a00c12fc", "project": "qw_platform", "branch": "develop", "subject": "ppg: set ppg irq pulse cold time width to 20ms", "status": "MERGED", "created": 1750859348, "updated": 1750859848, "mergeable": false, "number": 5279, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5279", "patchset_count": 1, "commit_id": "f7022955c1b36cbe94eb336d5c48b1f69fe2c841", "insertions": 1190, "deletions": 3}, {"change_id": "I6fdc6e90127dab0fd8cccb4dcc7fa0fff30dd17b", "project": "qw_platform", "branch": "wr02_release", "subject": "ppg: set ppg task prority to 9", "status": "MERGED", "created": 1750858361, "updated": 1750858963, "mergeable": false, "number": 5282, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5282", "patchset_count": 1, "commit_id": "bd6b0248bac9bbfbb14b2c30f6abed3f0c603596", "insertions": 83, "deletions": 6}, {"change_id": "I6fdc6e90127dab0fd8cccb4dcc7fa0fff30dd17b", "project": "qw_platform", "branch": "develop", "subject": "ppg: set ppg task prority to 9", "status": "MERGED", "created": 1750853900, "updated": 1750858363, "mergeable": false, "number": 5278, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5278", "patchset_count": 2, "commit_id": "a82f5e526d2d19fd8e01a680ec499cab88b689f6", "insertions": 83, "deletions": 6}, {"change_id": "I209641e94291bce3b1eb9a8412c7d21f5a1b47f2", "project": "sifli", "branch": "wr02_release", "subject": "gpio: don't set ppg gpio when resume && remove nordic irq monitor", "status": "MERGED", "created": 1750856340, "updated": 1750857072, "mergeable": false, "number": 5119, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/5119", "patchset_count": 1, "commit_id": "63fe3d4e5bbc3d626b2332e3f0e2c4d78d118e83", "insertions": 15, "deletions": 6}, {"change_id": "Id600fe781b07742bf7c53e741d56ed7056110c08", "project": "sifli", "branch": "develop", "subject": "pm: safe release pm", "status": "ABANDONED", "created": 1750748344, "updated": 1750856512, "mergeable": false, "number": 5203, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/5203", "patchset_count": 3, "commit_id": "187891a61dbcdd473805154b325916fa79922875", "insertions": 28, "deletions": 2}, {"change_id": "I209641e94291bce3b1eb9a8412c7d21f5a1b47f2", "project": "sifli", "branch": "develop", "subject": "gpio: don't set ppg gpio when resume && remove nordic irq monitor", "status": "MERGED", "created": 1750852845, "updated": 1750856308, "mergeable": false, "number": 5277, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/5277", "patchset_count": 4, "commit_id": "f6fe815ab01830cee348572977ea6893ba848cba", "insertions": 14, "deletions": 6}, {"change_id": "I3766e6066ba426633b2c811ddc71d766747345bb", "project": "qw_platform", "branch": "develop", "subject": "Revert \"sonarqube: fix some misra-c blocking issues\"", "status": "ABANDONED", "created": 1750842014, "updated": 1750854394, "mergeable": false, "number": 5118, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5118", "patchset_count": 2, "commit_id": "cf966831270ef183b9afb2a3376deec8d761a7ef", "insertions": 101, "deletions": 95}, {"change_id": "I1b3d6136e999fb58f673dc6104a7032d3d337488", "project": "app", "branch": "develop", "subject": "ui:更改运动区间、体力恢复时间页面", "status": "MERGED", "created": 1750845904, "updated": 1750847853, "mergeable": false, "number": 5276, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5276", "patchset_count": 1, "commit_id": "f72bf0b7ebe75ddb2814ec5b713c56498a79fe0f", "insertions": 158, "deletions": 139}, {"change_id": "I4026bb4c4282fbccd00250ab336d9c68d6527229", "project": "app", "branch": "develop", "subject": "[gui]: 修改弹窗中动画的buff使用来源", "status": "MERGED", "created": 1750844780, "updated": 1750845479, "mergeable": false, "number": 5275, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5275", "patchset_count": 1, "commit_id": "1e8db27ead8c002431b06a3f7987714afbbd4b6b", "insertions": 29, "deletions": 48}, {"change_id": "I4026bb4c4282fbccd00250ab336d9c68d6527229", "project": "qw_platform", "branch": "develop", "subject": "[gui]: 修改ezip_gif的buff来源", "status": "MERGED", "created": 1750844712, "updated": 1750845478, "mergeable": false, "number": 5274, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5274", "patchset_count": 2, "commit_id": "7553234813692a6ce52a26a61c9b8c6d08062624", "insertions": 89, "deletions": 67}, {"change_id": "I9e114ade3cfa30364666f045b4c4069828076c2b", "project": "app", "branch": "develop", "subject": "factory:优化传感器自检页面布局", "status": "MERGED", "created": 1750834783, "updated": 1750842768, "mergeable": false, "number": 5266, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5266", "patchset_count": 2, "commit_id": "024d8d523f33cd05f6d4c473035d7b55c7262b08", "insertions": 49, "deletions": 8}, {"change_id": "I7a6b05bfe946c145f487a730a10b38316c201ceb", "project": "app", "branch": "develop", "subject": "GUI: 修复由于buf分配问题进summary导致的死机问题", "status": "MERGED", "created": 1750842021, "updated": 1750842762, "mergeable": false, "number": 5272, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5272", "patchset_count": 2, "commit_id": "3c24cc7a7f5eab5a2ef5ef8c38ad72a15c33dd30", "insertions": 18, "deletions": 4}, {"change_id": "Ibb1aa7a77801c8b947d16379e4770a30e28cad55", "project": "app", "branch": "develop", "subject": "[GUI]: 解决禅道bug", "status": "MERGED", "created": 1750837362, "updated": 1750842754, "mergeable": false, "number": 5268, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5268", "patchset_count": 2, "commit_id": "43923e52e915401d6cbca66bb1c5ec67541a2073", "insertions": 70, "deletions": 8}, {"change_id": "Ic4962be285e70e67e5d5080b233eb895c9a7d984", "project": "app", "branch": "develop", "subject": "GUI: 替换表盘缩略图图片", "status": "MERGED", "created": 1750836830, "updated": 1750842753, "mergeable": false, "number": 5267, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5267", "patchset_count": 2, "commit_id": "f459e4f623c786ae7d7ae1256b7e85cf2cc819f5", "insertions": 10, "deletions": 0}, {"change_id": "I2ec0e059d12faa7720cf5e06eccff6b2004526d0", "project": "qw_platform", "branch": "develop", "subject": "sonarqube: fix some misra-c blocking issues", "status": "MERGED", "created": 1749186720, "updated": 1750842014, "mergeable": false, "number": 4734, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/4734", "patchset_count": 2, "commit_id": "5e1462270f6e6919aae60d2031fb76d69365aa27", "insertions": 107, "deletions": 87}, {"change_id": "Iaee3fc96cde78283a0d222b496f5233d8e3725fa", "project": "cycle/20250607_fc06", "branch": "develop", "subject": "[Feature]: 1.设置页相关 2.模拟器按键适配", "status": "MERGED", "created": 1750744499, "updated": 1750840561, "mergeable": false, "number": 5197, "owner": "hancheng", "url": "http://********:8081/c/cycle/20250607_fc06/+/5197", "patchset_count": 2, "commit_id": "9b75ff85b3755a25ebd721063c80276e77be6686", "insertions": 1726, "deletions": 353}, {"change_id": "I3692176e206beaa8b58af7ee0f28b8c5b1c7d829", "project": "qw_platform", "branch": "develop", "subject": "alg_fmk:acc和陀螺仪发布适配batch", "status": "MERGED", "created": 1750815581, "updated": 1750839493, "mergeable": false, "number": 5236, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5236", "patchset_count": 2, "commit_id": "4b52fe0df5153a53c9ab645e09b1fae6e97ca4b1", "insertions": 13, "deletions": 1}, {"change_id": "I3692176e206beaa8b58af7ee0f28b8c5b1c7d829", "project": "app", "branch": "develop", "subject": "alg_fmk:acc和陀螺仪发布适配batch", "status": "MERGED", "created": 1750815409, "updated": 1750839493, "mergeable": false, "number": 5235, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/app/+/5235", "patchset_count": 2, "commit_id": "307de7580acecc254d757f05764f39d56a77a90c", "insertions": 305, "deletions": 45}, {"change_id": "Ib056531a7a2dbf3945976095c66eec6e1c1e331d", "project": "app", "branch": "develop", "subject": "GUI: 1.新增睡眠得分判断逻辑，2.去掉清醒次数显示", "status": "MERGED", "created": 1750831172, "updated": 1750834707, "mergeable": false, "number": 5263, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5263", "patchset_count": 3, "commit_id": "209e35959cd912b16d31e49e91162787e9e7cea2", "insertions": 35, "deletions": 39}, {"change_id": "I8a744080a41d1c970a9b32113c988a07f3512b45", "project": "qw_platform", "branch": "develop", "subject": "bug:修补bug", "status": "MERGED", "created": 1750831544, "updated": 1750834673, "mergeable": false, "number": 5265, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5265", "patchset_count": 2, "commit_id": "7d436fbc65075ef3e84ce5ea01b934bca27abd91", "insertions": 16, "deletions": 2}, {"change_id": "I8a744080a41d1c970a9b32113c988a07f3512b45", "project": "app", "branch": "develop", "subject": "bug:修补bug", "status": "MERGED", "created": 1750831458, "updated": 1750834672, "mergeable": false, "number": 5264, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5264", "patchset_count": 3, "commit_id": "178dc432a44dc9dddbe013a1de7a1cc85dd0d776", "insertions": 72, "deletions": 297}, {"change_id": "I76bf30fb67a5106ed9b26b07554dec0ac7180920", "project": "qw_platform", "branch": "develop", "subject": "[sonar]: 解决sonar告警", "status": "MERGED", "created": 1750768999, "updated": 1750834437, "mergeable": false, "number": 5228, "owner": "lixiaolong", "url": "http://********:8081/c/qw_platform/+/5228", "patchset_count": 6, "commit_id": "c853ab9cda8957934be9dd43aec2945659f60a02", "insertions": 19, "deletions": 2}, {"change_id": "I887929d09ebc8804036e2ff969410d9ae15d0e42", "project": "qw_platform", "branch": "develop", "subject": "[sys_trace]: 所有事件均需经过消息发送到队列里操作文件,统一事件类型", "status": "MERGED", "created": 1750768999, "updated": 1750833348, "mergeable": false, "number": 5227, "owner": "lixiaolong", "url": "http://********:8081/c/qw_platform/+/5227", "patchset_count": 2, "commit_id": "89e00fc4b6fe80860937d997ac3a47a2bcd48989", "insertions": 91, "deletions": 92}, {"change_id": "I887929d09ebc8804036e2ff969410d9ae15d0e42", "project": "app", "branch": "develop", "subject": "[sys_trace]: 统一trace名称", "status": "MERGED", "created": 1750768990, "updated": 1750833347, "mergeable": false, "number": 5226, "owner": "lixiaolong", "url": "http://********:8081/c/app/+/5226", "patchset_count": 2, "commit_id": "2485854bff8e23fe49f4fcb787e4e3ef0a87b977", "insertions": 12, "deletions": 2}, {"change_id": "I366b9c86bd763e9503748e8904c67d9fcef7c8a3", "project": "tools", "branch": "develop", "subject": "[QTrace]: 支持鼠标悬浮显示坐标值", "status": "MERGED", "created": 1750769019, "updated": 1750833333, "mergeable": false, "number": 5230, "owner": "lixiaolong", "url": "http://********:8081/c/tools/+/5230", "patchset_count": 1, "commit_id": "906e1061559e8e2c6e86fbd5841bb06c0b6a1a5f", "insertions": 185, "deletions": 2}, {"change_id": "If6634ff72889ca957471d3a317dd025466df50c9", "project": "tools", "branch": "develop", "subject": "[QTrace]: 统一事件类型处理", "status": "MERGED", "created": 1750769019, "updated": 1750833332, "mergeable": false, "number": 5229, "owner": "lixiaolong", "url": "http://********:8081/c/tools/+/5229", "patchset_count": 1, "commit_id": "23e934b19d13fea1b891b17e940888518566671b", "insertions": 94, "deletions": 111}, {"change_id": "I62bf16e7b6711d1b44ad52e8c86610c2af1d1e9a", "project": "qw_platform", "branch": "develop", "subject": "GUI: 新增留白box接口", "status": "MERGED", "created": 1750822469, "updated": 1750833173, "mergeable": false, "number": 5238, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5238", "patchset_count": 2, "commit_id": "9b94d1a25127ed6bb82c2792b72e992df56297f7", "insertions": 21, "deletions": 0}, {"change_id": "I62bf16e7b6711d1b44ad52e8c86610c2af1d1e9a", "project": "app", "branch": "develop", "subject": "GUI: 新增运动结算页记圈页面最后留白处理", "status": "MERGED", "created": 1750822575, "updated": 1750833172, "mergeable": false, "number": 5239, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5239", "patchset_count": 3, "commit_id": "f13a0a49123fd62bc80c9ce1f17d8a48bcef2ae9", "insertions": 51, "deletions": 13}, {"change_id": "I91da9ba25dbea7b8db51d5823da3bb7faa242235", "project": "app", "branch": "develop", "subject": "[gui]: 修改24位色深", "status": "MERGED", "created": 1749180205, "updated": 1750832964, "mergeable": false, "number": 4730, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4730", "patchset_count": 10, "commit_id": "651cc9d3e15ab6bbc12ad0cf36c8b7312d1444b0", "insertions": 3295, "deletions": 3065}, {"change_id": "I91da9ba25dbea7b8db51d5823da3bb7faa242235", "project": "sifli", "branch": "develop", "subject": "[gui]: 修改24位色深", "status": "MERGED", "created": 1749180229, "updated": 1750832963, "mergeable": false, "number": 4732, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/sifli/+/4732", "patchset_count": 4, "commit_id": "dce5e627c38e12c4a1dedd32d6fa40518f616ab2", "insertions": 6617, "deletions": 521}, {"change_id": "I91da9ba25dbea7b8db51d5823da3bb7faa242235", "project": "qw_platform", "branch": "develop", "subject": "[gui]: 修改24位色深", "status": "MERGED", "created": 1749180223, "updated": 1750832963, "mergeable": false, "number": 4731, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4731", "patchset_count": 7, "commit_id": "40c4c63ec75154c730fc34e05ff14160bbef5d5a", "insertions": 1037, "deletions": 308}, {"change_id": "I3590f310d5d6eb901f6520277f75946877d920ae", "project": "qw_platform", "branch": "develop", "subject": "[GUI]: 去掉无用的打印。", "status": "MERGED", "created": 1750820900, "updated": 1750832142, "mergeable": false, "number": 5237, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5237", "patchset_count": 2, "commit_id": "a2dacdce7aade55a2289f0dd83a95db2a6e24529", "insertions": 10, "deletions": 1}, {"change_id": "I9cc8ac392e68f597af771420862a169e37a76949", "project": "qw_platform", "branch": "develop", "subject": "[fix]: 解决运动界面下，通过SDK发起解绑无法停止运动并保存活动文件的问题", "status": "MERGED", "created": 1750768871, "updated": 1750832128, "mergeable": false, "number": 5224, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5224", "patchset_count": 3, "commit_id": "b987ca6b0c669dbf721d39eb4f2ba5b9cbf699ed", "insertions": 28, "deletions": 1}, {"change_id": "I9cc8ac392e68f597af771420862a169e37a76949", "project": "app", "branch": "develop", "subject": "[fix]: 解决运动界面下，通过SDK发起解绑无法停止运动并保存活动文件的问题", "status": "MERGED", "created": 1750768982, "updated": 1750832127, "mergeable": false, "number": 5225, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5225", "patchset_count": 2, "commit_id": "175aa002cb9b659bddc2af47ef33a5a7f398a710", "insertions": 13, "deletions": 0}, {"change_id": "Ifbe2d8dc8887d195cee30568f4f646de2590ad5a", "project": "chip/airoha/3353", "branch": "master", "subject": "[Init]: Init SDK", "status": "MERGED", "created": 1750731045, "updated": 1750829865, "mergeable": false, "number": 4880, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/chip/airoha/3353/+/4880", "patchset_count": 1, "commit_id": "0181cc7fe53941252a31a124770b58438436434d", "insertions": 2982915, "deletions": 0}, {"change_id": "I5c2035c9a80868ac051133435814ef5dee704b9c", "project": "app", "branch": "develop", "subject": "sleep: sleep split time as 300ms", "status": "ABANDONED", "created": 1749898297, "updated": 1750825233, "mergeable": false, "number": 4960, "owner": "linjizhao", "url": "http://********:8081/c/app/+/4960", "patchset_count": 1, "commit_id": "8152cde5a15e5578af380f37185d1a6901da15f0", "insertions": 19, "deletions": 9}, {"change_id": "I23d0a86a2e9198df9261741b9a20cddf355a78ed", "project": "qw_platform", "branch": "develop", "subject": "ota: 精简ota模块的更新", "status": "MERGED", "created": 1750766820, "updated": 1750816236, "mergeable": false, "number": 5241, "owner": "jinx<PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5241", "patchset_count": 2, "commit_id": "211c07699cda78e877d8722c87875b08f6e5381e", "insertions": 76, "deletions": 1862}, {"change_id": "I8a294baa3657afcd8d0978655b6ef9105e62a263", "project": "app", "branch": "develop", "subject": "[GUI]: fix sonar qube bug", "status": "MERGED", "created": 1750767500, "updated": 1750815036, "mergeable": false, "number": 5223, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5223", "patchset_count": 2, "commit_id": "0f17adf5c068c4d1d10878d3774d93ba38006fb9", "insertions": 23, "deletions": 3}, {"change_id": "I2eaae045c4e502512148d1f41f2f69956713c876", "project": "sifli", "branch": "develop", "subject": "sifli : 修复未接FPC小板光旋钮开机过程中I2C报错", "status": "NEW", "created": 1750164830, "updated": 1750806133, "mergeable": false, "number": 5022, "owner": "gaoxing", "url": "http://********:8081/c/sifli/+/5022", "patchset_count": 2, "commit_id": "cbd6bb9712bbaba4556e3897213cb8feb4dc4cca", "insertions": 18, "deletions": 7}, {"change_id": "I00464a238387a220fa446e3e93c899f60e22547d", "project": "app", "branch": "develop", "subject": "app : 关闭GPS信号和GPS启动测试工模两个界面合并修改", "status": "MERGED", "created": 1750773924, "updated": 1750774425, "mergeable": false, "number": 5234, "owner": "gaoxing", "url": "http://********:8081/c/app/+/5234", "patchset_count": 1, "commit_id": "4584b7c0be41fd74d3e46eb41c2bc168a79b40bb", "insertions": 12, "deletions": 1}, {"change_id": "Ifd0fb2ce2901e97a9bfc7099b09df03e0e25dd0b", "project": "qw_platform", "branch": "wr02_release", "subject": "gh3020: set gh3x2x protocal data to psram", "status": "MERGED", "created": 1750772720, "updated": 1750773590, "mergeable": false, "number": 5116, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5116", "patchset_count": 1, "commit_id": "f797c3f0f8e359955e925e1b729350ba05ea77fc", "insertions": 14, "deletions": 1}, {"change_id": "I6b0ebf22a5c6efd9bc102018c969ca3329496b5d", "project": "app", "branch": "develop", "subject": "gm_collect: set gomore collect buffer to psram", "status": "MERGED", "created": 1750772771, "updated": 1750773425, "mergeable": false, "number": 5233, "owner": "linjizhao", "url": "http://********:8081/c/app/+/5233", "patchset_count": 1, "commit_id": "bf09645bfd4aea37a9ecc137bef2ade931021c52", "insertions": 18, "deletions": 2}, {"change_id": "Ifd0fb2ce2901e97a9bfc7099b09df03e0e25dd0b", "project": "qw_platform", "branch": "develop", "subject": "gh3020: set gh3x2x protocal data to psram", "status": "MERGED", "created": 1750772574, "updated": 1750772995, "mergeable": false, "number": 5232, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5232", "patchset_count": 1, "commit_id": "51e9b4b8df89a953298ddce0bd1542936982da07", "insertions": 14, "deletions": 1}, {"change_id": "Ic7c965acb11cfd4542e337a46894880ca9a47fc5", "project": "qw_platform", "branch": "develop", "subject": "GUI: 优化高度弹窗退出窗口逻辑", "status": "MERGED", "created": 1750764011, "updated": 1750769426, "mergeable": false, "number": 5222, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5222", "patchset_count": 2, "commit_id": "fcc1a939a6a8eec3e3f70a67e4bc81eb924a792b", "insertions": 15, "deletions": 5}, {"change_id": "I38243789902030d8f08bc6fcdc9349e182459773", "project": "app", "branch": "develop", "subject": "GUI: 重构高度UI", "status": "MERGED", "created": 1750763969, "updated": 1750769421, "mergeable": false, "number": 5221, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5221", "patchset_count": 2, "commit_id": "152c06ce69d043450bf28204bce8fd55cd9c64b2", "insertions": 479, "deletions": 294}, {"change_id": "Ib9cef498ddb18d4ec113187df93010022129fca4", "project": "qw_platform", "branch": "develop", "subject": "FT_ASSERT: set FT_ASSERT as RT_ASSERT in smiulator", "status": "MERGED", "created": 1750763504, "updated": 1750765531, "mergeable": false, "number": 5220, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5220", "patchset_count": 5, "commit_id": "926956a870fc151652788632a44eef9cfcd67b31", "insertions": 16, "deletions": 1}, {"change_id": "I16d7a40e4497cef1c4587340df7a36256be19951", "project": "qw_platform", "branch": "develop", "subject": "[sonar]: 解决sonar bug", "status": "MERGED", "created": 1750761125, "updated": 1750763752, "mergeable": false, "number": 5218, "owner": "lixin", "url": "http://********:8081/c/qw_platform/+/5218", "patchset_count": 2, "commit_id": "cd716511b64beb8ebdf96c5b0e5ed4b3629873e5", "insertions": 33, "deletions": 18}, {"change_id": "I16d7a40e4497cef1c4587340df7a36256be19951", "project": "app", "branch": "develop", "subject": "[sonar]: 解决sonar bug", "status": "MERGED", "created": 1750761162, "updated": 1750763751, "mergeable": false, "number": 5219, "owner": "lixin", "url": "http://********:8081/c/app/+/5219", "patchset_count": 1, "commit_id": "74e86579dca2e681bd12f6c4241312359c4b79b4", "insertions": 190, "deletions": 113}, {"change_id": "If870623a15db63142ece265898ce6844f6996876", "project": "qw_platform", "branch": "develop", "subject": "sonarqube: fix cff.c bugs && add QW_ASSERT", "status": "MERGED", "created": 1750756999, "updated": 1750762841, "mergeable": false, "number": 5217, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5217", "patchset_count": 3, "commit_id": "e95daa29b123f977331cba3c018a084bce36c7bb", "insertions": 18, "deletions": 13}, {"change_id": "Ibf459b42e93f45b0bdfa272fd6bbd3dbdc17c0fb", "project": "qw_platform", "branch": "develop", "subject": "[GUI]:运动中修改计圈字段，计圈页保留原来的计圈字段", "status": "MERGED", "created": 1750756588, "updated": 1750758866, "mergeable": false, "number": 5215, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5215", "patchset_count": 3, "commit_id": "52ad40d5af07fdfa1ac504c744d423710eac643c", "insertions": 22, "deletions": 1}, {"change_id": "If5c2a92105aa6bed075869dec60eb0590f045e49", "project": "qw_platform", "branch": "develop", "subject": "SERVICE: modify the data logic of stress pile driving", "status": "MERGED", "created": 1750756946, "updated": 1750757780, "mergeable": false, "number": 5216, "owner": "hongxing", "url": "http://********:8081/c/qw_platform/+/5216", "patchset_count": 1, "commit_id": "031de9d52aba0174341b5e6e73b3a9146162afd0", "insertions": 20, "deletions": 6}, {"change_id": "Iae3c6e4b9c310eb368119796ff66b7f4e5a8da02", "project": "app", "branch": "develop", "subject": "[ble]: 删除无用文件，暂时关闭HID服务", "status": "MERGED", "created": 1750752492, "updated": 1750755210, "mergeable": false, "number": 5210, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5210", "patchset_count": 2, "commit_id": "bc134ffcc0fbcd76971c301e66c86fe8cc8838d9", "insertions": 12, "deletions": 2918}, {"change_id": "I2aa1e863f110429112675258345d896149bb51ea", "project": "qw_platform", "branch": "develop", "subject": "[fix]: 解决sonar校验问题", "status": "MERGED", "created": 1750751679, "updated": 1750755150, "mergeable": false, "number": 5209, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5209", "patchset_count": 2, "commit_id": "8a5b034850c71b4accd7cb34f12acc90d1ff8730", "insertions": 20, "deletions": 0}, {"change_id": "I8df045b5c1b4a9c49e59b006fddb2311f63d2eed", "project": "qw_platform", "branch": "develop", "subject": "[GUI]: 运动中计圈没有值也显示单位。", "status": "MERGED", "created": 1750753815, "updated": 1750754941, "mergeable": false, "number": 5214, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5214", "patchset_count": 2, "commit_id": "f4f228b9f5cb5b1126da0ed85d3ef0e6e47355f1", "insertions": 239, "deletions": 198}, {"change_id": "I8df045b5c1b4a9c49e59b006fddb2311f63d2eed", "project": "app", "branch": "develop", "subject": "[GUI]: 训练步骤结束响铃", "status": "MERGED", "created": 1750753803, "updated": 1750754941, "mergeable": false, "number": 5213, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5213", "patchset_count": 2, "commit_id": "e5b049200c71e725455bf276bf3f2cd2bf039208", "insertions": 97, "deletions": 49}, {"change_id": "Ic94d6a965b95520fb385b4672914342ae7888a95", "project": "app", "branch": "develop", "subject": "[gui]: 修改gps定位倒计时有一个初始圆弧", "status": "MERGED", "created": 1750749807, "updated": 1750754840, "mergeable": false, "number": 5207, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5207", "patchset_count": 3, "commit_id": "0ab38eace719a9ef46258db4af9c266e0a9494e7", "insertions": 17, "deletions": 6}, {"change_id": "Id635b6ff0e37a1aa492d47aca103c7ae6a658d02", "project": "app", "branch": "wr02_release", "subject": "piling: add spo2 piling", "status": "MERGED", "created": 1750751646, "updated": 1750754770, "mergeable": false, "number": 5115, "owner": "linjizhao", "url": "http://********:8081/c/app/+/5115", "patchset_count": 1, "commit_id": "ae8a68f9f1bd372736fffb4892299d5c9845441c", "insertions": 15, "deletions": 0}, {"change_id": "I027fc5ea7f4b500aaf4079d75c1c04f2d43fe731", "project": "app", "branch": "develop", "subject": "[sys_trace]: 开始结束运动状态打点", "status": "MERGED", "created": 1750749201, "updated": 1750754646, "mergeable": false, "number": 5206, "owner": "lixiaolong", "url": "http://********:8081/c/app/+/5206", "patchset_count": 2, "commit_id": "374e75f4a11e7d3cce80a3a45b42e28e4e80c8f8", "insertions": 14, "deletions": 0}, {"change_id": "I027fc5ea7f4b500aaf4079d75c1c04f2d43fe731", "project": "qw_platform", "branch": "develop", "subject": "[sys_trace]: 添加运动状态定义", "status": "MERGED", "created": 1750749189, "updated": 1750754646, "mergeable": false, "number": 5205, "owner": "lixiaolong", "url": "http://********:8081/c/qw_platform/+/5205", "patchset_count": 2, "commit_id": "13bba6326253b79d75b5faab5d0c9a265c911414", "insertions": 12, "deletions": 1}, {"change_id": "I027fc5ea7f4b500aaf4079d75c1c04f2d43fe731", "project": "tools", "branch": "develop", "subject": "[QTrace]: 添加运动状态显示", "status": "MERGED", "created": 1750749180, "updated": 1750754646, "mergeable": false, "number": 5204, "owner": "lixiaolong", "url": "http://********:8081/c/tools/+/5204", "patchset_count": 1, "commit_id": "64a9c901ab48c3bdf1fbca8e1fa228773c79cd0b", "insertions": 186, "deletions": 5}, {"change_id": "Id3f529f7bd351ccfa91db7f89a1d75efc936d642", "project": "app", "branch": "develop", "subject": "[fitfile]: 修正文件列表的问题", "status": "MERGED", "created": 1750753379, "updated": 1750754216, "mergeable": false, "number": 5212, "owner": "lixin", "url": "http://********:8081/c/app/+/5212", "patchset_count": 1, "commit_id": "563b6aad7f68580c72b21bc0a92dc05bc695ca52", "insertions": 25, "deletions": 4}, {"change_id": "Id3f529f7bd351ccfa91db7f89a1d75efc936d642", "project": "qw_platform", "branch": "develop", "subject": "[grid]: 减少不必要的log 修正一些bug", "status": "MERGED", "created": 1750753203, "updated": 1750754216, "mergeable": false, "number": 5211, "owner": "lixin", "url": "http://********:8081/c/qw_platform/+/5211", "patchset_count": 1, "commit_id": "cb40cc7ab69bca033c85c203a671235d233b7dc5", "insertions": 19, "deletions": 9}, {"change_id": "Id635b6ff0e37a1aa492d47aca103c7ae6a658d02", "project": "app", "branch": "develop", "subject": "piling: add spo2 piling", "status": "MERGED", "created": 1750751177, "updated": 1750751639, "mergeable": false, "number": 5208, "owner": "linjizhao", "url": "http://********:8081/c/app/+/5208", "patchset_count": 1, "commit_id": "a8f3727d613c8873e39dcbcd63a0edf723f58635", "insertions": 14, "deletions": 0}, {"change_id": "I8d703a67a278436ebe1ae7f180dbe8fc6a60cc73", "project": "qw_platform", "branch": "develop", "subject": "[sys_trace]: 修改电流单位为uA, 修改battery结构体数据类型", "status": "MERGED", "created": 1750744829, "updated": 1750748854, "mergeable": false, "number": 5198, "owner": "lixiaolong", "url": "http://********:8081/c/qw_platform/+/5198", "patchset_count": 2, "commit_id": "8ffc282562133693b1b7e519c360555b07349fda", "insertions": 88, "deletions": 38}, {"change_id": "Ifb92363b07c8274f879c7835df09b202de9a06d2", "project": "tools", "branch": "develop", "subject": "[QTrace]: 更新QTrace上位机工具,适配battery数据结构修改", "status": "MERGED", "created": 1750744845, "updated": 1750748809, "mergeable": false, "number": 5199, "owner": "lixiaolong", "url": "http://********:8081/c/tools/+/5199", "patchset_count": 2, "commit_id": "4e79b59cf1949f12b15982b32d596e44e926dba0", "insertions": 834, "deletions": 73}, {"change_id": "I9c3974320d8f3d28a4f581194ea85f7f841e74b5", "project": "qw_platform", "branch": "develop", "subject": "[Mag]: 收集五次数据后再发，降低通信负载", "status": "MERGED", "created": 1750747561, "updated": 1750747959, "mergeable": false, "number": 5202, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5202", "patchset_count": 2, "commit_id": "4b9c1c3c4cdcc63102a30bfcb5b5b1b03b70ef23", "insertions": 28, "deletions": 7}, {"change_id": "I5b759c0df692c95ba093d12658ac55134f53702e", "project": "app", "branch": "develop", "subject": "SERVICE: the way to modify the data subscription method for resting heart rate", "status": "MERGED", "created": 1750744489, "updated": 1750747854, "mergeable": false, "number": 5196, "owner": "hongxing", "url": "http://********:8081/c/app/+/5196", "patchset_count": 3, "commit_id": "353fdb80cbe0536e70bce1688ff86d0f14318f6e", "insertions": 18, "deletions": 15}, {"change_id": "I269f245a2ef036720a769bb68e0295124f8e234e", "project": "chip/airoha/3335m", "branch": "master", "subject": "[Init]: 忽略out目录", "status": "MERGED", "created": 1750730141, "updated": 1750747606, "mergeable": false, "number": 4879, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/chip/airoha/3335m/+/4879", "patchset_count": 2, "commit_id": "d3998283a0bde9af7419ce313effd7bffe9076a2", "insertions": 11, "deletions": 0}, {"change_id": "Id996c4fd919399a0269b9e63f8673cc14817eec7", "project": "app", "branch": "develop", "subject": "app:自动亮度优化", "status": "MERGED", "created": 1750737237, "updated": 1750745314, "mergeable": false, "number": 5191, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5191", "patchset_count": 2, "commit_id": "4c6767bb6066d2278eecfb134825e2572d210a1b", "insertions": 225, "deletions": 215}, {"change_id": "I815b9c9f8cf811ad6c94f8008e57e239e6311476", "project": "qw_platform", "branch": "wr02_release", "subject": "piling: fix bat piling bug", "status": "MERGED", "created": 1750743653, "updated": 1750744286, "mergeable": false, "number": 5113, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5113", "patchset_count": 1, "commit_id": "695e67773c484e3ec81222d7d1443e41a9cd60b6", "insertions": 17, "deletions": 6}, {"change_id": "I815b9c9f8cf811ad6c94f8008e57e239e6311476", "project": "qw_platform", "branch": "develop", "subject": "piling: fix bat piling bug", "status": "MERGED", "created": 1750742057, "updated": 1750743639, "mergeable": false, "number": 5193, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5193", "patchset_count": 5, "commit_id": "b7997a58ae53437591bfc6cbe12425a2e2db62e3", "insertions": 16, "deletions": 6}, {"change_id": "I600092e4906d7e643bd3db9b7f8af3446d69a3b1", "project": "app", "branch": "develop", "subject": "mem_map: set mailbox memory to 8K", "status": "MERGED", "created": 1750742399, "updated": 1750742951, "mergeable": false, "number": 5195, "owner": "linjizhao", "url": "http://********:8081/c/app/+/5195", "patchset_count": 1, "commit_id": "d2995a4671dac780dc8779d3bc6c1b8d39a50be2", "insertions": 10, "deletions": 12}, {"change_id": "I600092e4906d7e643bd3db9b7f8af3446d69a3b1", "project": "sifli", "branch": "develop", "subject": "mem_map: set mailbox memory to 8K", "status": "MERGED", "created": 1750742320, "updated": 1750742951, "mergeable": false, "number": 5194, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/5194", "patchset_count": 2, "commit_id": "0bab9a2525506ec9809ee6b7eef0f1099a166bfd", "insertions": 14, "deletions": 4}, {"change_id": "Ifb05340001d3f74ad7ea0eae293102200a5ba58a", "project": "sifli", "branch": "develop", "subject": "pm: add pm_request lose event debug", "status": "MERGED", "created": 1750737426, "updated": 1750742014, "mergeable": false, "number": 5192, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/5192", "patchset_count": 2, "commit_id": "6a1aba85f9e4eaa883342ba7adb6c668f9f59b56", "insertions": 13, "deletions": 0}, {"change_id": "Ifb60c64c74508ef9e96b66eeda2d6aaa409ac6e0", "project": "app", "branch": "develop", "subject": "bug:运动中去除骑行台", "status": "MERGED", "created": 1750732227, "updated": 1750737158, "mergeable": false, "number": 5190, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5190", "patchset_count": 1, "commit_id": "4372b52eb1e9e9b58b80c8f0f41900cc37c00efc", "insertions": 23, "deletions": 10}, {"change_id": "Id2b8ebfddc7afda986b28f3c9e3d9bcd70632624", "project": "cycle/20250607_fc06", "branch": "develop", "subject": "[Feature]: 1.新增除LCD驱动 2.新增进入BOOT接口", "status": "MERGED", "created": 1750683342, "updated": 1750734239, "mergeable": false, "number": 5188, "owner": "<PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/20250607_fc06/+/5188", "patchset_count": 2, "commit_id": "100f073f64aa76f412646bb880d2d6bb2b4645d5", "insertions": 202496, "deletions": 5119}, {"change_id": "Id3e3f40e2ec4bddf4fa961067236adce5d4128c7", "project": "chip/airoha/3335m", "branch": "master", "subject": "[SDK]: Init", "status": "MERGED", "created": 1750729648, "updated": 1750729837, "mergeable": false, "number": 4878, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/chip/airoha/3335m/+/4878", "patchset_count": 2, "commit_id": "eac91cb4b22689c5a083de957f5d9c64304a2fbf", "insertions": 2834509, "deletions": 0}, {"change_id": "I0672b9b7f2a2eef6d3773bac51fe84954052689f", "project": "qw_platform", "branch": "develop", "subject": "[Log]: 解决模拟器编译错误", "status": "MERGED", "created": 1750658662, "updated": 1750727723, "mergeable": false, "number": 5171, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5171", "patchset_count": 2, "commit_id": "7b7da5eeade5030370f49e294d6b4d747610ef2f", "insertions": 13, "deletions": 3}, {"change_id": "I4692ab639c34352c44128e32bb8411d23ed492c0", "project": "app", "branch": "develop", "subject": "[GM]: 优化log存储", "status": "MERGED", "created": 1750075747, "updated": 1750727694, "mergeable": false, "number": 4995, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4995", "patchset_count": 7, "commit_id": "21992ebb8745030247b0c1a45e416cca1f271314", "insertions": 292, "deletions": 151}, {"change_id": "I4692ab639c34352c44128e32bb8411d23ed492c0", "project": "sifli", "branch": "develop", "subject": "[GM]: 优化log存储", "status": "MERGED", "created": 1750075790, "updated": 1750727693, "mergeable": false, "number": 4996, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/sifli/+/4996", "patchset_count": 3, "commit_id": "69409cfe394010bf1e263297b26d4339ba5e85dd", "insertions": 34, "deletions": 24}, {"change_id": "I453e1c0248bbc640d69089512e34bba1cd1726da", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Optimize]: 添加对爬坡点数量的判断，避免越界", "status": "MERGED", "created": 1750686169, "updated": 1750686680, "mergeable": false, "number": 5189, "owner": "liquan", "url": "http://********:8081/c/cycle/app/+/5189", "patchset_count": 1, "commit_id": "20f285823c279d85f6b48f0ffa0f6198216db8e2", "insertions": 139, "deletions": 20}, {"change_id": "I441360ed433db9441a8385126362179ab48079f7", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 解决骑行台速度显示不连续问题", "status": "MERGED", "created": 1750667407, "updated": 1750682292, "mergeable": false, "number": 5176, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/5176", "patchset_count": 2, "commit_id": "cf6153cc1a25bb1a3daf8536db66d905c32f7684", "insertions": 16, "deletions": 5}, {"change_id": "Idba2c7e990f4db577984b4ed7ece2d3887277fa1", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 解决计圈弹窗距离不显示的问题", "status": "MERGED", "created": 1750679396, "updated": 1750682269, "mergeable": false, "number": 5187, "owner": "wang<PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/5187", "patchset_count": 2, "commit_id": "eb1c41eaae144a8ab5c96ea6d1028c6a0b4d4a46", "insertions": 19, "deletions": 9}, {"change_id": "If6080f7da211d64007425d05b1786fcf9665cec0", "project": "qw_platform", "branch": "develop", "subject": "NUMBER_NO_32_B_FONT 增加支持字符r，R", "status": "MERGED", "created": 1750672636, "updated": 1750675446, "mergeable": false, "number": 5184, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5184", "patchset_count": 2, "commit_id": "2862675270aaf4867a56b6f96f6dafc52478f376", "insertions": 183, "deletions": 116}, {"change_id": "I7432c642684e84c09b3bb9fc63280cb7d29bc548", "project": "app", "branch": "develop", "subject": "GUI: 运动结算页优化聚焦页逻辑，新增变量控件获取当前聚焦在第几页", "status": "MERGED", "created": 1750658600, "updated": 1750672066, "mergeable": false, "number": 5170, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5170", "patchset_count": 2, "commit_id": "dee4ef06a6747304e87be2c7f16d3a47bacb2251", "insertions": 95, "deletions": 10}, {"change_id": "I6791cd52c37beb60804af71be035ea4c81029a35", "project": "qw_platform", "branch": "develop", "subject": "[GUI]: 调整运动中计圈字体", "status": "MERGED", "created": 1750666588, "updated": 1750670865, "mergeable": false, "number": 5174, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5174", "patchset_count": 2, "commit_id": "e87536352fd538fb07f69caf602237cc514687f5", "insertions": 54, "deletions": 14}, {"change_id": "I43ffae722d833e5ac2f9a328a0af658dff76535a", "project": "app", "branch": "develop", "subject": "Revert \"GUI: test\"", "status": "ABANDONED", "created": 1750669218, "updated": 1750669237, "mergeable": false, "number": 5110, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5110", "patchset_count": 1, "commit_id": "36b2c3e7754a29c04f4adf3e53da5b96ca406ff3", "insertions": 14, "deletions": 0}, {"change_id": "I1952c18f36f517d24b9591f62c8bb70537b9cf61", "project": "app", "branch": "wr02_release_test", "subject": "GUI: test", "status": "ABANDONED", "created": 1750666373, "updated": 1750669233, "mergeable": false, "number": 5173, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5173", "patchset_count": 2, "commit_id": "0c230e12d82cc7efdc9047107d7e3077aa749f9f", "insertions": 12, "deletions": 1}, {"change_id": "I1952c18f36f517d24b9591f62c8bb70537b9cf61", "project": "app", "branch": "develop", "subject": "GUI: test", "status": "MERGED", "created": 1750666742, "updated": 1750669218, "mergeable": false, "number": 5108, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5108", "patchset_count": 1, "commit_id": "d8ec9baec28ba6f1c2c238074c79ee2753986eb9", "insertions": 10, "deletions": 0}, {"change_id": "Idf920da3bee8381003c378955d840568bc52628f", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 解决骑行台速度显示不连续问题", "status": "ABANDONED", "created": 1750666776, "updated": 1750667247, "mergeable": false, "number": 5175, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/5175", "patchset_count": 1, "commit_id": "04bedad278a60fa2275a57fc7183e8925418282f", "insertions": 18, "deletions": 7}, {"change_id": "Ic91db3c1d3ba1f5d2e1308360cdf388e7c5c70c9", "project": "app", "branch": "develop", "subject": "Revert \"GUI: test\"", "status": "MERGED", "created": 1750666933, "updated": 1750666946, "mergeable": false, "number": 5109, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5109", "patchset_count": 1, "commit_id": "61f3bad9fc18bc478b1427f6403f0af68fabd5ed", "insertions": 14, "deletions": 0}, {"change_id": "I7a465eb9c4b6755be34c7f398567dfef56fca811", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 1. 清理部分编译警告；", "status": "MERGED", "created": 1750645062, "updated": 1750666897, "mergeable": false, "number": 5163, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/5163", "patchset_count": 2, "commit_id": "4fb63098994e35aab87745178c65d76ddf2c6fca", "insertions": 686, "deletions": 614}, {"change_id": "Idf46a9fa340ce72d6024387911aada5858f39e3c", "project": "cycle/c202310_bg1", "branch": "Develop", "subject": "[Fix]: 解决骑行台速度显示不连续问题", "status": "MERGED", "created": 1750665523, "updated": 1750666859, "mergeable": false, "number": 5172, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/c202310_bg1/+/5172", "patchset_count": 1, "commit_id": "4430ce65894569f143bba35f22fc3639d4f4f697", "insertions": 18, "deletions": 7}, {"change_id": "Ie9fa50f85fabbd05e7d6632b81d2d7f2c2ed9c09", "project": "cycle/c202310_bg1", "branch": "Develop", "subject": "[Fix]: 新增ANT连接过程失败超时处理", "status": "MERGED", "created": 1750400111, "updated": 1750666856, "mergeable": false, "number": 5127, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/c202310_bg1/+/5127", "patchset_count": 2, "commit_id": "3e64587e1b1b304e194df57d26cd4d90e8244f97", "insertions": 91, "deletions": 3}, {"change_id": "I23b89546de57c16ad727a66d65a72f9e30e2f211", "project": "cycle/c202310_bg1", "branch": "Develop", "subject": "[Fix]: 新增速度选择逻辑", "status": "MERGED", "created": 1750400111, "updated": 1750666853, "mergeable": false, "number": 5126, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/c202310_bg1/+/5126", "patchset_count": 2, "commit_id": "203dbde80356439885d8c2448fca3ee5c6e04e6d", "insertions": 74, "deletions": 0}, {"change_id": "Ie0d5f76229d1999e8c5595425b9af7e9a628d44d", "project": "app", "branch": "develop", "subject": "alg_fmk:1、导入静息心率自研算法 2、将hrv范围调整到[0,130]", "status": "MERGED", "created": 1750403890, "updated": 1750662660, "mergeable": false, "number": 5134, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/app/+/5134", "patchset_count": 12, "commit_id": "f62a776267bde699f4034cc167508ed7ec3617c9", "insertions": 224, "deletions": 109}, {"change_id": "Ic2fdfac62842b4b1e614cd8f21b732b77dcb0649", "project": "qw_platform", "branch": "develop", "subject": "[GUI]: 运动中自动计圈页计圈字段", "status": "MERGED", "created": 1750649015, "updated": 1750657950, "mergeable": false, "number": 5165, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5165", "patchset_count": 2, "commit_id": "0fee33e6db5d14421d7853e469c1e1b01ecf9a44", "insertions": 66, "deletions": 27}, {"change_id": "Ic2fdfac62842b4b1e614cd8f21b732b77dcb0649", "project": "app", "branch": "develop", "subject": "[GUI]:运动中计圈页面支持配置计圈字段。", "status": "MERGED", "created": 1750649005, "updated": 1750657950, "mergeable": false, "number": 5164, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5164", "patchset_count": 3, "commit_id": "48005a2db6ff22fe76c8da633b225464a19a1b3f", "insertions": 1674, "deletions": 331}, {"change_id": "Ibc06ceec6b3640c218e0fd8d941cb51f13672ca3", "project": "qw_platform", "branch": "develop", "subject": "GUI: 优化运动结算页记圈页面逻辑", "status": "MERGED", "created": 1750641792, "updated": 1750657941, "mergeable": false, "number": 5159, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5159", "patchset_count": 4, "commit_id": "381bec95a2a258a648ac487d0e872ed5edcc3795", "insertions": 1856, "deletions": 51}, {"change_id": "Ibc06ceec6b3640c218e0fd8d941cb51f13672ca3", "project": "app", "branch": "develop", "subject": "GUI: 优化运动结算页中记圈页面逻辑", "status": "MERGED", "created": 1750644526, "updated": 1750657940, "mergeable": false, "number": 5162, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5162", "patchset_count": 4, "commit_id": "faf838a952ac10b8ebb51dbddc6e97bf6b8ad5c0", "insertions": 1085, "deletions": 81}, {"change_id": "I42c92da5595c68e63184382f16f657a590b6ba20", "project": "app", "branch": "develop", "subject": "[img]: 调整内部图片占用空间 将不常用的图片移到外部", "status": "MERGED", "created": 1750650570, "updated": 1750651120, "mergeable": false, "number": 5167, "owner": "lixin", "url": "http://********:8081/c/app/+/5167", "patchset_count": 2, "commit_id": "74a85d00be79e7840bc3bca77b261f7287d7b5b1", "insertions": 450, "deletions": 1355}, {"change_id": "I42c92da5595c68e63184382f16f657a590b6ba20", "project": "qw_platform", "branch": "develop", "subject": "[GUI]: 按照图片修改调整代码", "status": "MERGED", "created": 1750650523, "updated": 1750651119, "mergeable": false, "number": 5166, "owner": "lixin", "url": "http://********:8081/c/qw_platform/+/5166", "patchset_count": 1, "commit_id": "d17c1cda33580c4fe0284f2e0fbd87a659ec3c87", "insertions": 41, "deletions": 29}, {"change_id": "I4aab9d69098fb6dd18e70df0c79082e572f6e8d5", "project": "app", "branch": "develop", "subject": "alg_fmk:睡眠算法倒灌实现", "status": "ABANDONED", "created": 1750650582, "updated": 1750650885, "mergeable": false, "number": 5168, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/app/+/5168", "patchset_count": 1, "commit_id": "15c38ff90c8d269e893113e7630a51bdce2e5137", "insertions": 60, "deletions": 4}, {"change_id": "I8af6f5b2e85f2cf8e1d99c1bc7d374dbae5ba594", "project": "app", "branch": "develop", "subject": "auto_light:优化自动亮度初值检测、精简计算步骤", "status": "MERGED", "created": 1750407886, "updated": 1750650824, "mergeable": false, "number": 5136, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5136", "patchset_count": 3, "commit_id": "54d0bfbc074de0e40e9f15b954eb6b38106c6739", "insertions": 195, "deletions": 240}, {"change_id": "I5a1f6d639fd3a62abffededd08dda77140658397", "project": "app", "branch": "develop", "subject": "GUI: 优化运动结算页中记圈页面逻辑", "status": "ABANDONED", "created": 1750644478, "updated": 1750644664, "mergeable": false, "number": 5161, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5161", "patchset_count": 1, "commit_id": "ed7bbb1f9f079c7a095465419f5e85dab119e696", "insertions": 1081, "deletions": 81}, {"change_id": "I9426f527b89820fdb73a4d28d1a8c6f7012f2c64", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 解决应用sonar-bug", "status": "MERGED", "created": 1750643051, "updated": 1750643475, "mergeable": false, "number": 5160, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/5160", "patchset_count": 1, "commit_id": "062c4d121786765bdf3afdae301c3a9322fcffb2", "insertions": 14, "deletions": 2}, {"change_id": "I2f5df5a3153e0177ad2da9d1565ff3f9c10f6c11", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Build]: 更新版本至v1.17_beta1", "status": "MERGED", "created": 1750500180, "updated": 1750643468, "mergeable": false, "number": 5154, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/5154", "patchset_count": 1, "commit_id": "61687d48239b6dd4d5bd2e81ff32c667da084686", "insertions": 12, "deletions": 2}, {"change_id": "I14faeb01a5d712a0dffbbcc5b9911ea9365b389f", "project": "app", "branch": "develop", "subject": "[gui]: 修改所有菜单的聚焦保存机制", "status": "MERGED", "created": 1750503051, "updated": 1750504651, "mergeable": false, "number": 5158, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5158", "patchset_count": 2, "commit_id": "eae57680e03df6f5c2434ec76b2e0bdcf054646f", "insertions": 966, "deletions": 838}, {"change_id": "I14faeb01a5d712a0dffbbcc5b9911ea9365b389f", "project": "qw_platform", "branch": "develop", "subject": "[gui]: 基于PageBase增加页面聚焦项保存", "status": "MERGED", "created": 1750503042, "updated": 1750504651, "mergeable": false, "number": 5157, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5157", "patchset_count": 2, "commit_id": "fda8df469e268e3ed6cace8d2f2f58f150697e64", "insertions": 441, "deletions": 165}, {"change_id": "Ic9d71b09e7ac108ccf5f89c64f02f5747c0a39f2", "project": "app", "branch": "wr02_release", "subject": "boot: set compile option to fast", "status": "ABANDONED", "created": 1750397714, "updated": 1750503337, "mergeable": false, "number": 5104, "owner": "linjizhao", "url": "http://********:8081/c/app/+/5104", "patchset_count": 1, "commit_id": "d8cd9efa7749e276a2603318dd453111cbf2dae6", "insertions": 12, "deletions": 1}, {"change_id": "Iaff16a956821d192fcbf40905784a8ae29a81c38", "project": "app", "branch": "develop", "subject": "[Log]: Crash前日志存储", "status": "MERGED", "created": 1750333815, "updated": 1750503209, "mergeable": false, "number": 5081, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5081", "patchset_count": 3, "commit_id": "0fb766da0bb49f5410d6ee1de9cf752afb147fc1", "insertions": 11, "deletions": 1}, {"change_id": "Iaff16a956821d192fcbf40905784a8ae29a81c38", "project": "sifli", "branch": "develop", "subject": "[Log]: Crash前日志存储", "status": "MERGED", "created": 1750333867, "updated": 1750503208, "mergeable": false, "number": 5084, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/sifli/+/5084", "patchset_count": 4, "commit_id": "55e9e4781f67b228a8ab289c8def6f50f4bbec67", "insertions": 73, "deletions": 26}, {"change_id": "Iaff16a956821d192fcbf40905784a8ae29a81c38", "project": "qw_platform", "branch": "develop", "subject": "[Log]: Crash前日志存储", "status": "MERGED", "created": 1750333851, "updated": 1750503207, "mergeable": false, "number": 5083, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5083", "patchset_count": 6, "commit_id": "cae784563a9e95e7cd1bd5d536c1ff47bdebb8ea", "insertions": 79, "deletions": 7}, {"change_id": "I5de1596cde16b6fc4f0003139a343d5bcdd5b6c1", "project": "app", "branch": "wr02_release", "subject": "[GPS]: 更新位置策略", "status": "ABANDONED", "created": 1750324024, "updated": 1750503179, "mergeable": false, "number": 4795, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4795", "patchset_count": 1, "commit_id": "e0addcd0391bd7d85ca0620465e91bcf0643015e", "insertions": 31, "deletions": 17}, {"change_id": "I5de1596cde16b6fc4f0003139a343d5bcdd5b6c1", "project": "qw_platform", "branch": "wr02_release", "subject": "[GPS]: 更新位置策略", "status": "ABANDONED", "created": 1750324013, "updated": 1750503173, "mergeable": false, "number": 4794, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4794", "patchset_count": 1, "commit_id": "88e8f2d5ce1f28389e3bb90dc41e5392a291a6de", "insertions": 15, "deletions": 2}, {"change_id": "I5de1596cde16b6fc4f0003139a343d5bcdd5b6c1", "project": "sifli", "branch": "wr02_release", "subject": "[GPS]: 更新位置策略", "status": "ABANDONED", "created": 1750324002, "updated": 1750503156, "mergeable": false, "number": 4793, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/sifli/+/4793", "patchset_count": 1, "commit_id": "944260ad51d953e10c6b172eb21953133e419df8", "insertions": 19, "deletions": 27}, {"change_id": "I1b4e7561a49f6186253ea91315ba427ae8599151", "project": "qw_platform", "branch": "develop", "subject": "[gui]: 增加gps圆弧组件的接口", "status": "MERGED", "created": 1750501813, "updated": 1750502558, "mergeable": false, "number": 5155, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5155", "patchset_count": 1, "commit_id": "2ddf8a523a6d18cd84e7bd95562294278b5b8bf9", "insertions": 25, "deletions": 5}, {"change_id": "I1b4e7561a49f6186253ea91315ba427ae8599151", "project": "app", "branch": "develop", "subject": "[gui]: 修改运动开始gps圆弧", "status": "MERGED", "created": 1750501822, "updated": 1750502557, "mergeable": false, "number": 5156, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5156", "patchset_count": 1, "commit_id": "3c341cf88778c1082d37ab2ebc02b0dd4949b784", "insertions": 26, "deletions": 7}, {"change_id": "I9b2557658e26a395a6147dd3f18519d8e09f617d", "project": "qw_platform", "branch": "develop", "subject": "OTA: 重构OTA模块.", "status": "MERGED", "created": 1750319849, "updated": 1750498898, "mergeable": false, "number": 4875, "owner": "jinx<PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4875", "patchset_count": 9, "commit_id": "e79042dd9c773f7dee7256fed8e32381a29433b1", "insertions": 10261, "deletions": 1484}, {"change_id": "I9b2557658e26a395a6147dd3f18519d8e09f617d", "project": "sifli", "branch": "develop", "subject": "OTA: 重构OTA模块.", "status": "MERGED", "created": 1750319858, "updated": 1750498897, "mergeable": false, "number": 4876, "owner": "jinx<PERSON><PERSON>", "url": "http://********:8081/c/sifli/+/4876", "patchset_count": 4, "commit_id": "e24cff425994b5db978be4fc660090cf7ebeb537", "insertions": 126, "deletions": 53}, {"change_id": "I9b2557658e26a395a6147dd3f18519d8e09f617d", "project": "app", "branch": "develop", "subject": "OTA: 重构OTA模块.", "status": "MERGED", "created": 1750319807, "updated": 1750498896, "mergeable": false, "number": 4874, "owner": "jinx<PERSON><PERSON>", "url": "http://********:8081/c/app/+/4874", "patchset_count": 15, "commit_id": "32a4e9d2f433ba8de339646125d382ba90db8a47", "insertions": 1022, "deletions": 290}, {"change_id": "Ied00fe4a9a8993ddfbc2d2d84c71216d1217806c", "project": "app", "branch": "develop", "subject": "ble: enable high speed && tx power for ppg test", "status": "MERGED", "created": 1750493203, "updated": 1750498092, "mergeable": false, "number": 5150, "owner": "linjizhao", "url": "http://********:8081/c/app/+/5150", "patchset_count": 1, "commit_id": "911bdf3f5e95f6dd1c832cb046fd994900a46bda", "insertions": 209, "deletions": 160}, {"change_id": "Iea8c8b57bb4717d4769ab8c18c6a24bf7d5719f5", "project": "qw_platform", "branch": "develop", "subject": "ppg: ppg test timer as 10ms", "status": "MERGED", "created": 1750493261, "updated": 1750497612, "mergeable": false, "number": 5151, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5151", "patchset_count": 1, "commit_id": "9484b601080a134c47256f9299c3488223a97891", "insertions": 11, "deletions": 1}, {"change_id": "I53a8cbc9d9f51333eace8c678fa4d20aaf250431", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 修复sonar提示bug", "status": "MERGED", "created": 1750492692, "updated": 1750497605, "mergeable": false, "number": 5149, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/5149", "patchset_count": 4, "commit_id": "cae2536307078f945ff696721c4b07c80e9e74f3", "insertions": 70, "deletions": 18}, {"change_id": "Iac7840715b5939ccb5c85019e6438f7747efdd3a", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Feature]: 1. 启用和完善GoMore系统日志；", "status": "MERGED", "created": 1750493677, "updated": 1750497591, "mergeable": false, "number": 5152, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/5152", "patchset_count": 1, "commit_id": "e3f6e369ff48860ef7d5ef61d5583236deb2c993", "insertions": 90, "deletions": 11}, {"change_id": "Ieb0c7cff86b8638c887a12057b32cbf2b4e703ff", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 解决boot-lcpu_img不匹配问题", "status": "MERGED", "created": 1750485655, "updated": 1750492766, "mergeable": false, "number": 5145, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/5145", "patchset_count": 2, "commit_id": "cecaf83e9ffcb293f40f013361547793c72c1cae", "insertions": 3157, "deletions": 3100}, {"change_id": "I5c0bce9a60da940e0057b8797e2c03458561a697", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 新增速度选择逻辑", "status": "MERGED", "created": 1750405959, "updated": 1750492759, "mergeable": false, "number": 5135, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/5135", "patchset_count": 2, "commit_id": "8cc85770876bf4d6da2aafae17f054ae3441d301", "insertions": 74, "deletions": 0}, {"change_id": "Ia0d6c62a58a6508ae6a080804d3e899af7f0753b", "project": "cycle/sifli", "branch": "bg2_develop", "subject": "[Fix]: 解决sonar-Bug", "status": "MERGED", "created": 1750490475, "updated": 1750492715, "mergeable": false, "number": 5148, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/sifli/+/5148", "patchset_count": 1, "commit_id": "abe460d540e178711ee1a61cc639e492b13f78d8", "insertions": 22, "deletions": 3}, {"change_id": "I49a84e6fbd2ec543ba03a1cf192a1b3ce324ad42", "project": "app", "branch": "develop", "subject": "[hrm image]: 修正心率订阅异常问题 添加一些图片", "status": "MERGED", "created": 1750488860, "updated": 1750489617, "mergeable": false, "number": 5146, "owner": "lixin", "url": "http://********:8081/c/app/+/5146", "patchset_count": 1, "commit_id": "ebd70bdbfb7afd51d28c6d7018a55419cb21add2", "insertions": 17390, "deletions": 16473}, {"change_id": "I49a84e6fbd2ec543ba03a1cf192a1b3ce324ad42", "project": "qw_platform", "branch": "develop", "subject": "[image]: 添加图片声明", "status": "MERGED", "created": 1750488990, "updated": 1750489616, "mergeable": false, "number": 5147, "owner": "lixin", "url": "http://********:8081/c/qw_platform/+/5147", "patchset_count": 1, "commit_id": "bda2829644f46138912ecb91f4680bbf989f6e35", "insertions": 116, "deletions": 143}, {"change_id": "I45617bda1282dee5e5b59f8353b37a9446f9eb04", "project": "qw_platform", "branch": "develop", "subject": "Revert \"[gui]: 增加菜单选中项保存\"", "status": "MERGED", "created": 1750487698, "updated": 1750487864, "mergeable": false, "number": 5106, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5106", "patchset_count": 1, "commit_id": "457df0c125b143f6d2985a1e05205e6b124d03cc", "insertions": 179, "deletions": 431}, {"change_id": "I048ea739a48987acdc87f61db09b964c2942a8d2", "project": "app", "branch": "develop", "subject": "Revert \"[gui]: 统一修改聚焦选中机制\"", "status": "MERGED", "created": 1750487750, "updated": 1750487832, "mergeable": false, "number": 5107, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5107", "patchset_count": 1, "commit_id": "64ce74626de006be020e5cd55b4967ef1359d357", "insertions": 852, "deletions": 956}, {"change_id": "I934661b4c1d07b11988e2aee9edd70d900679dd7", "project": "app", "branch": "develop", "subject": "[gui]: 统一修改聚焦选中机制", "status": "MERGED", "created": 1750413051, "updated": 1750487750, "mergeable": false, "number": 5142, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5142", "patchset_count": 3, "commit_id": "b32c466cd2c93438c67bf3b101513c5a57ebe617", "insertions": 966, "deletions": 838}, {"change_id": "I934661b4c1d07b11988e2aee9edd70d900679dd7", "project": "qw_platform", "branch": "develop", "subject": "[gui]: 增加菜单选中项保存", "status": "MERGED", "created": 1750410344, "updated": 1750487698, "mergeable": false, "number": 5140, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5140", "patchset_count": 3, "commit_id": "63587268f9488d35989cce1e158d065ff8e02461", "insertions": 441, "deletions": 165}, {"change_id": "I485a46d5e2f01cf0cecbc93f28cc1fd1fcdcade1", "project": "qw_platform", "branch": "develop", "subject": "[fit]: 添加bug 18485的监控日志", "status": "MERGED", "created": 1750414904, "updated": 1750415391, "mergeable": false, "number": 5144, "owner": "lixin", "url": "http://********:8081/c/qw_platform/+/5144", "patchset_count": 1, "commit_id": "77194665dfa120509fbe70a70169e3e2fc28d3e2", "insertions": 21, "deletions": 1}, {"change_id": "Ic35c7e0decf4c0dff9d90f9e6f83ee9e9aa08693", "project": "app", "branch": "develop", "subject": "[gui]: 修改运动菜单翻转时，按start键死机", "status": "ABANDONED", "created": 1750413051, "updated": 1750415149, "mergeable": false, "number": 5143, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5143", "patchset_count": 2, "commit_id": "f3507269db8a80a200ce359f8034dd2caffff757", "insertions": 21, "deletions": 1}, {"change_id": "If6c89d4684f785127ee0ed3e70e43a237e2efdb2", "project": "app", "branch": "develop", "subject": "SERVICE: expand the scope of HRV weekly data to solve the HRV data display issue", "status": "MERGED", "created": 1750411846, "updated": 1750412316, "mergeable": false, "number": 5141, "owner": "hongxing", "url": "http://********:8081/c/app/+/5141", "patchset_count": 2, "commit_id": "1bd104006f1dd13082fbfed221646f2922d2735d", "insertions": 19, "deletions": 6}, {"change_id": "Icfebd8f66cb69d781b6710b3531cbc7299753596", "project": "qw_platform", "branch": "develop", "subject": "[pc]: 修正模拟器编译错误", "status": "MERGED", "created": 1750410062, "updated": 1750410531, "mergeable": false, "number": 5139, "owner": "lixin", "url": "http://********:8081/c/qw_platform/+/5139", "patchset_count": 1, "commit_id": "a717c5a6d5add407b425a84ff784ee779c5be1f3", "insertions": 14, "deletions": 0}, {"change_id": "Idf5777056f3751de78f40d8b95c5d84915af6f86", "project": "app", "branch": "develop", "subject": "[alt]: 修正bug单自测错误内容", "status": "MERGED", "created": 1750408364, "updated": 1750409262, "mergeable": false, "number": 5138, "owner": "lixin", "url": "http://********:8081/c/app/+/5138", "patchset_count": 1, "commit_id": "73d534bad5f1a59f0fb26471c1acd9ab6b5567f4", "insertions": 80, "deletions": 35}, {"change_id": "Idf5777056f3751de78f40d8b95c5d84915af6f86", "project": "qw_platform", "branch": "develop", "subject": "[msg]: 修正高度校准弹窗事件处理", "status": "MERGED", "created": 1750408252, "updated": 1750409262, "mergeable": false, "number": 5137, "owner": "lixin", "url": "http://********:8081/c/qw_platform/+/5137", "patchset_count": 1, "commit_id": "b57704b22df7cdb41cd6a8c48bd05626b50c73b2", "insertions": 14, "deletions": 1}, {"change_id": "Ia8a07ab18f34a6f564b6b105e50654e25b6e813e", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 解决主题一卡片缩略图显示错误问题", "status": "MERGED", "created": 1750403128, "updated": 1750405989, "mergeable": false, "number": 5131, "owner": "liquan", "url": "http://********:8081/c/cycle/app/+/5131", "patchset_count": 1, "commit_id": "21bf7c7729039b92298c4b58619b2dbaa7d3aa5b", "insertions": 13, "deletions": 0}, {"change_id": "I6b1e16e626a0acc0d2a63e135ca079ca079320ca", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 解决Sscnf阻断", "status": "MERGED", "created": 1750327076, "updated": 1750405984, "mergeable": false, "number": 5076, "owner": "liquan", "url": "http://********:8081/c/cycle/app/+/5076", "patchset_count": 2, "commit_id": "30ace561371f441f99ed4ba4834d700870c98ef8", "insertions": 16, "deletions": 9}, {"change_id": "I7155749ee53402e57d2665e8f0cc78f482be98c2", "project": "app", "branch": "develop", "subject": "[GUI]: 解决禅道bug", "status": "MERGED", "created": 1750403785, "updated": 1750405915, "mergeable": false, "number": 5133, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5133", "patchset_count": 4, "commit_id": "f2efe2534aa4b412a631e39b686cdb6f0d135374", "insertions": 102, "deletions": 36}, {"change_id": "I7155749ee53402e57d2665e8f0cc78f482be98c2", "project": "qw_platform", "branch": "develop", "subject": "[GUI]:解决禅道bug", "status": "MERGED", "created": 1750403775, "updated": 1750405915, "mergeable": false, "number": 5132, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5132", "patchset_count": 2, "commit_id": "0e8dc73b57d596f77f485716e3ed7de5b4f3dea5", "insertions": 25, "deletions": 3}, {"change_id": "I88ca8daaf5bf907b48a32564400ba0882b4e27f6", "project": "qw_platform", "branch": "develop", "subject": "[gui]: 修改菜单收缩时死机问题", "status": "MERGED", "created": 1750402603, "updated": 1750403729, "mergeable": false, "number": 5130, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5130", "patchset_count": 2, "commit_id": "cb106b3e1a0504e777852be28a019530501dc991", "insertions": 15, "deletions": 5}, {"change_id": "I6a91682c3582908f41f0a863b97a17af902af028", "project": "app", "branch": "develop", "subject": "[gui]: 修改结算页最大摄氧量只取当次运动", "status": "MERGED", "created": 1750402596, "updated": 1750403719, "mergeable": false, "number": 5129, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5129", "patchset_count": 2, "commit_id": "eaecfca09572e72d04b6b665304d72703a00998f", "insertions": 12, "deletions": 2}, {"change_id": "Ic5419b2e9b7544c806cf94127b3099a72515615a", "project": "app", "branch": "develop", "subject": "[gui]: 修改运动提醒菜单的副标题", "status": "MERGED", "created": 1750390815, "updated": 1750403704, "mergeable": false, "number": 5123, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5123", "patchset_count": 2, "commit_id": "783e78a191d857a9c8d71d739083c0ad1a7ecb97", "insertions": 19, "deletions": 3}, {"change_id": "I89315556e7d0624fd06d53406765a20b10ce1271", "project": "cycle/c202310_bg1", "branch": "Develop", "subject": "[Fix]: 修改赛段缩略图显示异常，解决导航结束弹窗常驻问题，解决游标箭头滑动区域错误问题", "status": "MERGED", "created": 1750401317, "updated": 1750403239, "mergeable": false, "number": 5128, "owner": "liquan", "url": "http://********:8081/c/cycle/c202310_bg1/+/5128", "patchset_count": 2, "commit_id": "7437ec9fba7935321b5eab4c2ad9f80636a1de22", "insertions": 57, "deletions": 14}, {"change_id": "Iec29d2d22b953545dfe6bf24803f0a726f92fdb1", "project": "sifli", "branch": "wr02_release", "subject": "debug: remove into hard_fault function debug", "status": "MERGED", "created": 1750400796, "updated": 1750402549, "mergeable": false, "number": 5105, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/5105", "patchset_count": 2, "commit_id": "3f65d0159b8b65965b6c83f6b644c3c79584f726", "insertions": 19, "deletions": 5}, {"change_id": "Iec29d2d22b953545dfe6bf24803f0a726f92fdb1", "project": "sifli", "branch": "develop", "subject": "debug: remove into hard_fault function debug", "status": "MERGED", "created": 1750390666, "updated": 1750400797, "mergeable": false, "number": 5122, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/5122", "patchset_count": 5, "commit_id": "ade1914005815574ce2c67af2c3675bcfe520f2b", "insertions": 14, "deletions": 4}, {"change_id": "Ic9d71b09e7ac108ccf5f89c64f02f5747c0a39f2", "project": "app", "branch": "develop", "subject": "boot: set compile option to fast", "status": "MERGED", "created": 1750393799, "updated": 1750397704, "mergeable": false, "number": 5125, "owner": "linjizhao", "url": "http://********:8081/c/app/+/5125", "patchset_count": 3, "commit_id": "483249ac6b76fd2e5ce7b3c0bcfb3eadc44eab78", "insertions": 11, "deletions": 1}, {"change_id": "I60b4154a3f2f294908a73d2b3fdc67d2fdc6d1a8", "project": "app", "branch": "develop", "subject": "SERVICE: add size validation logic for sleep blood oxygen and daily activity data files", "status": "MERGED", "created": 1750391826, "updated": 1750397504, "mergeable": false, "number": 5124, "owner": "hongxing", "url": "http://********:8081/c/app/+/5124", "patchset_count": 4, "commit_id": "b288791de1fea9959d1740f05da8a53b3a04fe34", "insertions": 49, "deletions": 10}, {"change_id": "I76c43e8bd4f6b6f627db9a946b53bcbe4b8e80dc", "project": "app", "branch": "develop", "subject": "[font]: 上传font缓存文件", "status": "MERGED", "created": 1750390504, "updated": 1750391243, "mergeable": false, "number": 5121, "owner": "lixin", "url": "http://********:8081/c/app/+/5121", "patchset_count": 2, "commit_id": "3a73b82e243f989e345a07802fc6eab530693fa7", "insertions": 12, "deletions": 2}, {"change_id": "Ief0e1d07a3a5826b760059964536b076fb03c371", "project": "qw_platform", "branch": "develop", "subject": "[translate]: 关闭测试宏", "status": "MERGED", "created": 1750388703, "updated": 1750389143, "mergeable": false, "number": 5100, "owner": "lixin", "url": "http://********:8081/c/qw_platform/+/5100", "patchset_count": 1, "commit_id": "0a7b8c2a02bb18ccd7adf4ab74fb5f6c0749c54a", "insertions": 13, "deletions": 1}, {"change_id": "I0e3a994a197f4a61680669980863afb25372e78a", "project": "cycle/20250607_fc06", "branch": "develop", "subject": "[Feature]: 工厂模式相关", "status": "MERGED", "created": 1750249867, "updated": 1750385039, "mergeable": false, "number": 5048, "owner": "hancheng", "url": "http://********:8081/c/cycle/20250607_fc06/+/5048", "patchset_count": 2, "commit_id": "674b4038c4fc9f878eaf0be060862f73710b3578", "insertions": 732, "deletions": 886}, {"change_id": "If041d71229280c3ea2c8443d0b5656ced134e2ad", "project": "app", "branch": "develop", "subject": "SERVICE: Add the logic for reading daily activity reminder files", "status": "MERGED", "created": 1750382666, "updated": 1750383193, "mergeable": false, "number": 5098, "owner": "hongxing", "url": "http://********:8081/c/app/+/5098", "patchset_count": 1, "commit_id": "173d12724d251fecd1dd07870cf93cd3e48236b8", "insertions": 23, "deletions": 10}, {"change_id": "Id30cf19e74f5f24feee2a9b81d974561c0bcc3ba", "project": "app", "branch": "develop", "subject": "Revert \"[pm]: 亮屏期间允许大核进light sleep，工作电流可减小2mA\"", "status": "ABANDONED", "created": 1744099025, "updated": 1750380966, "mergeable": false, "number": 3203, "owner": "linjizhao", "url": "http://********:8081/c/app/+/3203", "patchset_count": 1, "commit_id": "5de0ab77033cf593be7cb7cc421a89896865b8f4", "insertions": 14, "deletions": 1}, {"change_id": "I2c18ee148fce4a2eae951348097d2d8154fbc465", "project": "app", "branch": "develop", "subject": "[burn]: 添加老化测试功能", "status": "MERGED", "created": 1750214172, "updated": 1750380947, "mergeable": false, "number": 5026, "owner": "lixiaolong", "url": "http://********:8081/c/app/+/5026", "patchset_count": 3, "commit_id": "fa08c47052088093ef1b2c015d12909a6846d3aa", "insertions": 587, "deletions": 1}, {"change_id": "I720f4da8bf1b03940c2f55127a921133da38e521", "project": "sifli", "branch": "wr02_release", "subject": "nordic: power down nordic52832 if crash happened", "status": "MERGED", "created": 1750338304, "updated": 1750380887, "mergeable": false, "number": 4800, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/4800", "patchset_count": 1, "commit_id": "07e96ef154ec30dc81a0d635a3cc16adc4d6b0c6", "insertions": 40, "deletions": 2}, {"change_id": "I720f4da8bf1b03940c2f55127a921133da38e521", "project": "qw_platform", "branch": "wr02_release", "subject": "nordic: power down nordic52832 if crash happened", "status": "MERGED", "created": 1750337450, "updated": 1750380887, "mergeable": false, "number": 4799, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/4799", "patchset_count": 1, "commit_id": "837264748d7ce2297a70f1a9c6e110ad7bae1d6a", "insertions": 15, "deletions": 0}, {"change_id": "I026fe3789c036c16003fefc96d0fd8ed4a4a9824", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 解决计圈页和团骑队友页列表底部无法拖动的问题", "status": "MERGED", "created": 1750331284, "updated": 1750343101, "mergeable": false, "number": 5079, "owner": "wang<PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/5079", "patchset_count": 2, "commit_id": "a33a52affeba0e9242b0434cdb6a320f66d17829", "insertions": 18, "deletions": 3}, {"change_id": "I3c70fab7cddb3a0d8f5d5ef4460f3c9b15284d08", "project": "app", "branch": "develop", "subject": "[ver]: 修改版本号", "status": "MERGED", "created": 1750342517, "updated": 1750343040, "mergeable": false, "number": 5097, "owner": "lixin", "url": "http://********:8081/c/app/+/5097", "patchset_count": 1, "commit_id": "2e1e6c98de594f2c1b46a62f1eef780e380e9884", "insertions": 13, "deletions": 1}, {"change_id": "I28fd207cdee2cd019ff566b3de1e2743ab27f838", "project": "app", "branch": "develop", "subject": "[alt]: 修复bug", "status": "MERGED", "created": 1750340257, "updated": 1750342335, "mergeable": false, "number": 5096, "owner": "lixin", "url": "http://********:8081/c/app/+/5096", "patchset_count": 3, "commit_id": "ae7be09ef2ffc3c79d98b806ba92d4e1376d950d", "insertions": 211, "deletions": 75}, {"change_id": "I28fd207cdee2cd019ff566b3de1e2743ab27f838", "project": "qw_platform", "branch": "develop", "subject": "[tran]: 更新翻译 解决bug", "status": "MERGED", "created": 1750339952, "updated": 1750342335, "mergeable": false, "number": 5094, "owner": "lixin", "url": "http://********:8081/c/qw_platform/+/5094", "patchset_count": 2, "commit_id": "b5584cee2a0bbb4fd008ed8b392508815f3c0f74", "insertions": 860, "deletions": 571}, {"change_id": "I017b50e30b717854d80faa3650db434c48385041", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]：解决阻断问题", "status": "MERGED", "created": 1750331389, "updated": 1750340452, "mergeable": false, "number": 5080, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/5080", "patchset_count": 3, "commit_id": "54cf4eb16fe6e068ae7808a661ada8e64e752dab", "insertions": 25, "deletions": 2}, {"change_id": "I254dc511eebd693bbbf7dd0e05635fd15078d247", "project": "app", "branch": "develop", "subject": "[alt]: 修复bug", "status": "ABANDONED", "created": 1750340168, "updated": 1750340342, "mergeable": false, "number": 5095, "owner": "lixin", "url": "http://********:8081/c/app/+/5095", "patchset_count": 1, "commit_id": "8041e39b2c5931d642132e8438d3d9c8422e7b1b", "insertions": 163, "deletions": 26}, {"change_id": "I87227c0bca5fb686c957e8d416be0c0e802bfc59", "project": "app", "branch": "develop", "subject": "sports: 修复BUG", "status": "MERGED", "created": 1750333905, "updated": 1750339997, "mergeable": false, "number": 5087, "owner": "yukai", "url": "http://********:8081/c/app/+/5087", "patchset_count": 4, "commit_id": "ac9f6c2e6d843a5672f4a8bc28b7093f3c2561c0", "insertions": 462, "deletions": 130}, {"change_id": "I87227c0bca5fb686c957e8d416be0c0e802bfc59", "project": "qw_platform", "branch": "develop", "subject": "navi: 增加偏航箭头绘制 fit: 优化fit字段定义 解决BUG: 17688【功能测试】【导航-线路】导航路书线路未显示行进箭头", "status": "MERGED", "created": 1750333884, "updated": 1750339992, "mergeable": false, "number": 5085, "owner": "yukai", "url": "http://********:8081/c/qw_platform/+/5085", "patchset_count": 2, "commit_id": "2d1fd78c59e83b8fd30e330b742b6d8dae11f3b4", "insertions": 107, "deletions": 47}, {"change_id": "I720f4da8bf1b03940c2f55127a921133da38e521", "project": "qw_platform", "branch": "develop", "subject": "nordic: power down nordic52832 if crash happened", "status": "MERGED", "created": 1750335175, "updated": 1750337438, "mergeable": false, "number": 5091, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5091", "patchset_count": 2, "commit_id": "e7654840442f61690563ea1c22f4ad2813a54ca8", "insertions": 14, "deletions": 0}, {"change_id": "I720f4da8bf1b03940c2f55127a921133da38e521", "project": "sifli", "branch": "develop", "subject": "nordic: power down nordic52832 if crash happened", "status": "MERGED", "created": 1750335224, "updated": 1750337437, "mergeable": false, "number": 5092, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/5092", "patchset_count": 3, "commit_id": "73cb2452a54af899dbe3a603b3e9fdcd74d32899", "insertions": 39, "deletions": 2}, {"change_id": "Ie0690e2fd89a9b996ebb7433b89ede7fe7bddc37", "project": "qw_platform", "branch": "develop", "subject": "navi:导航转向弹窗实时更新距离", "status": "MERGED", "created": 1750333884, "updated": 1750336595, "mergeable": false, "number": 5086, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5086", "patchset_count": 2, "commit_id": "fd45b56eb12a3de09aee41d55a6430b207103ba3", "insertions": 43, "deletions": 50}, {"change_id": "Ie0690e2fd89a9b996ebb7433b89ede7fe7bddc37", "project": "app", "branch": "develop", "subject": "navi:导航转向弹窗实时更新距离", "status": "MERGED", "created": 1750334049, "updated": 1750336594, "mergeable": false, "number": 5088, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5088", "patchset_count": 3, "commit_id": "21baa42b22bf84b57d19ccc52a4ecb4482e1faab", "insertions": 37, "deletions": 2}, {"change_id": "I22caf240e45f38a7c747fc76b01a7e177a0b6b5c", "project": "app", "branch": "develop", "subject": "[gui]: 修改结算页最大摄氧量", "status": "MERGED", "created": 1750335729, "updated": 1750336394, "mergeable": false, "number": 5093, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5093", "patchset_count": 2, "commit_id": "e61f0eb5ce1a821aa80c78d361dc1d5f6cf98a29", "insertions": 26, "deletions": 8}, {"change_id": "I5de1596cde16b6fc4f0003139a343d5bcdd5b6c1", "project": "app", "branch": "develop", "subject": "[GPS]: 更新位置策略", "status": "MERGED", "created": 1750320058, "updated": 1750336255, "mergeable": false, "number": 5063, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5063", "patchset_count": 5, "commit_id": "1446ef4d3451e5146b5f349ccb6980ced8683cf5", "insertions": 31, "deletions": 17}, {"change_id": "I5de1596cde16b6fc4f0003139a343d5bcdd5b6c1", "project": "sifli", "branch": "develop", "subject": "[GPS]: 更新位置策略", "status": "MERGED", "created": 1750320119, "updated": 1750336254, "mergeable": false, "number": 5065, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/sifli/+/5065", "patchset_count": 2, "commit_id": "9f315f5960a86a1ce542af3b264d708d3dad3588", "insertions": 19, "deletions": 27}, {"change_id": "I5de1596cde16b6fc4f0003139a343d5bcdd5b6c1", "project": "qw_platform", "branch": "develop", "subject": "[GPS]: 更新位置策略", "status": "MERGED", "created": 1750320097, "updated": 1750336254, "mergeable": false, "number": 5064, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5064", "patchset_count": 2, "commit_id": "8b8e05d85c7835ed41363b85429291ec3dd7bbaa", "insertions": 15, "deletions": 2}, {"change_id": "I8988d050bb54d45c76f5bec4877a521289f042b7", "project": "app", "branch": "develop", "subject": "SERVICE[BUG-001]: fix the issue where the blood oxygen data synchronization file size is incorrect", "status": "MERGED", "created": 1750335104, "updated": 1750336089, "mergeable": false, "number": 5090, "owner": "hongxing", "url": "http://********:8081/c/app/+/5090", "patchset_count": 4, "commit_id": "be289adf3d875cd957e896c391ecfc224502c807", "insertions": 20, "deletions": 5}, {"change_id": "I050ebe1f01309280f8cfb8feea4b07202ff2d0f6", "project": "app", "branch": "develop", "subject": "[GUI]: 17774 消息通知详情的标题超长应该显示..", "status": "MERGED", "created": 1750334737, "updated": 1750335754, "mergeable": false, "number": 5089, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5089", "patchset_count": 1, "commit_id": "e04f8fe818bcb81746de0736591db23e63184baa", "insertions": 25, "deletions": 0}, {"change_id": "I0f85d908b60f1bb93825a286ec6b2c98f93b6166", "project": "app", "branch": "develop", "subject": "navi:导航转向弹窗实时更新距离", "status": "ABANDONED", "created": 1750333837, "updated": 1750334520, "mergeable": false, "number": 5082, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5082", "patchset_count": 1, "commit_id": "7231eab620f9af37a25a9d5f847aa1841b3e014b", "insertions": 37, "deletions": 2}, {"change_id": "Ifdfa5d505df173ccdfce6d215a06a1ce61c4996b", "project": "app", "branch": "develop", "subject": "bug:间歇菜单导航栏显示间歇内容", "status": "MERGED", "created": 1750324635, "updated": 1750332075, "mergeable": false, "number": 5070, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5070", "patchset_count": 4, "commit_id": "bae07958e0ca229ac42a800bf6eb3b5f1a26f759", "insertions": 70, "deletions": 26}, {"change_id": "Ifdfa5d505df173ccdfce6d215a06a1ce61c4996b", "project": "qw_platform", "branch": "develop", "subject": "bug:间歇菜单导航栏显示间歇内容", "status": "MERGED", "created": 1750324688, "updated": 1750332074, "mergeable": false, "number": 5071, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5071", "patchset_count": 2, "commit_id": "a5fa87fb347dacb55b5217fd432e5f3931b9556b", "insertions": 54, "deletions": 0}, {"change_id": "I88fc229ba85369a8807f7ae842a9035b46b347df", "project": "qw_algo/navigation", "branch": "bg2_develop", "subject": "[Fix]: 1. 修复导航模块剩余阻断问题；", "status": "MERGED", "created": 1750326803, "updated": 1750331434, "mergeable": false, "number": 5075, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_algo/navigation/+/5075", "patchset_count": 1, "commit_id": "d930065bbee6d6242f8fb714e6c15b2de1ebb4a6", "insertions": 23, "deletions": 5}, {"change_id": "I404d04db50b4364c6122ccd8c2fdf007b8c618df", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 1. 修复导航接入模块阻断问题；", "status": "MERGED", "created": 1750326190, "updated": 1750331424, "mergeable": false, "number": 5074, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/5074", "patchset_count": 1, "commit_id": "3e75cdede8f96bcb88219039f50b6944c7ff9499", "insertions": 46, "deletions": 11}, {"change_id": "I8967fdcc050062df3fec60691f9c5a519a312b56", "project": "app", "branch": "develop", "subject": "app : 修复关机后小核异常唤醒大核退出关机状态错误：添加关闭唤醒源", "status": "MERGED", "created": 1750329438, "updated": 1750330170, "mergeable": false, "number": 5077, "owner": "gaoxing", "url": "http://********:8081/c/app/+/5077", "patchset_count": 1, "commit_id": "70f9c2e7e309fd81c37da3020f432544cb84be38", "insertions": 34, "deletions": 17}, {"change_id": "I8967fdcc050062df3fec60691f9c5a519a312b56", "project": "qw_platform", "branch": "develop", "subject": "qw_platform : 修复关机后小核异常唤醒大核退出关机状态错误：添加关闭唤醒源", "status": "MERGED", "created": 1750329446, "updated": 1750330169, "mergeable": false, "number": 5078, "owner": "gaoxing", "url": "http://********:8081/c/qw_platform/+/5078", "patchset_count": 1, "commit_id": "61db3038047b78ec345d86d3008a35359bc40bb2", "insertions": 30, "deletions": 0}, {"change_id": "I165329e7e42300a8e87027775a31af64462fe190", "project": "qw_platform", "branch": "develop", "subject": "[GUI]:18277 训练课程中的运动类型不支持步行，手表中跑步最大心率测量课程显示有步行", "status": "MERGED", "created": 1750325786, "updated": 1750327199, "mergeable": false, "number": 5073, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5073", "patchset_count": 2, "commit_id": "7e184bc18dbed9fea552fa1f030a677cb2aee796", "insertions": 11, "deletions": 4}, {"change_id": "Ie7016b6694dbf0115cbfee8b6190db6628299655", "project": "app", "branch": "develop", "subject": "18277 训练课程中的运动类型不支持步行，手表中跑步最大心率测量课程显示有步行", "status": "MERGED", "created": 1750324785, "updated": 1750327189, "mergeable": false, "number": 5072, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5072", "patchset_count": 1, "commit_id": "e7cb14fae1a18870f003986fad076504f0f4f3a2", "insertions": 16, "deletions": 2}, {"change_id": "Ic2f31410a7b160e3497d767b67dd859a4cc67309", "project": "app", "branch": "wr02_release", "subject": "alg_fmk:暂时注释掉自动暂停恢复算法（影响到跑步算法正常执行） 解扫描阻断问题", "status": "MERGED", "created": 1750324988, "updated": 1750327154, "mergeable": false, "number": 4798, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/app/+/4798", "patchset_count": 2, "commit_id": "1a9257963f620ac2fc1b4ff612959bb20be319b5", "insertions": 29, "deletions": 7}, {"change_id": "I9a6c5f0fb6a597929b6dd722775dbc70bf24fe3e", "project": "app", "branch": "wr02_release", "subject": "alg_fmk:1、解决手动查看睡眠，抬腕失效的问题 2、仿真添加睡眠时间有效性判断 3、ppi备份清空，防止异常输入", "status": "MERGED", "created": 1750324937, "updated": 1750327152, "mergeable": false, "number": 4797, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/app/+/4797", "patchset_count": 1, "commit_id": "df9742e7c50fb82bd9326ef86ffcdba84e053599", "insertions": 37, "deletions": 2}, {"change_id": "I5a9ba49a946e821c7c2a65f75482c889742b36fb", "project": "tools", "branch": "wr02_release", "subject": "alg_fmk:静息心率采集计算工具（滑窗：时间30min和点数300）", "status": "MERGED", "created": 1750324887, "updated": 1750327145, "mergeable": false, "number": 4796, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/tools/+/4796", "patchset_count": 2, "commit_id": "c8bb7e973f08d661b8c3b3ec7cc240d00638cf8c", "insertions": 187, "deletions": 0}, {"change_id": "I9910b6329c84e385570bfc3507ad6bbf3e40e191", "project": "qw_platform", "branch": "develop", "subject": "ppg: enable spo2 algo", "status": "MERGED", "created": 1750320670, "updated": 1750325952, "mergeable": false, "number": 5066, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5066", "patchset_count": 5, "commit_id": "81509403400e84a6c801e9e6d4f3e1c6535a4e5f", "insertions": 61, "deletions": 23}, {"change_id": "I49af466ca0e09778ab6ab0adc586f3b711a090d7", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 1. 修复离线规划库阻断问题；", "status": "MERGED", "created": 1750324160, "updated": 1750325877, "mergeable": false, "number": 5069, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/5069", "patchset_count": 1, "commit_id": "53ae592bc5bd2ff9a12616d7f60bedceb17939f4", "insertions": 48, "deletions": 19}, {"change_id": "I86eb3d77b2bce53048466c071473c4cca324c813", "project": "qw_algo/navigation", "branch": "bg2_develop", "subject": "[Fix]: 1. 修复导航模块阻断问题；", "status": "MERGED", "created": 1750313751, "updated": 1750325858, "mergeable": false, "number": 5055, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_algo/navigation/+/5055", "patchset_count": 3, "commit_id": "be81237db8e1be4650392b99b173172488f16a29", "insertions": 21, "deletions": 13}, {"change_id": "Ife4ffafdd3b20f47e92cd563cdddc91fa8211a14", "project": "qw_platform", "branch": "develop", "subject": "[fix]: 修复模拟器编译", "status": "MERGED", "created": 1750321952, "updated": 1750322907, "mergeable": false, "number": 5068, "owner": "lixiaolong", "url": "http://********:8081/c/qw_platform/+/5068", "patchset_count": 2, "commit_id": "7a39c44d62ab1de6baf3ad7ca43717ac8e169cd3", "insertions": 11, "deletions": 0}, {"change_id": "Ic2f31410a7b160e3497d767b67dd859a4cc67309", "project": "app", "branch": "develop", "subject": "alg_fmk:暂时注释掉自动暂停恢复算法（影响到跑步算法正常执行） 解扫描阻断问题", "status": "MERGED", "created": 1750321165, "updated": 1750322597, "mergeable": false, "number": 5067, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/app/+/5067", "patchset_count": 2, "commit_id": "1c287d53782cba3990d4d2bffb65293ce940877d", "insertions": 28, "deletions": 7}, {"change_id": "I06425dc427404dce32f5ef38fd94d27314c60832", "project": "app", "branch": "develop", "subject": "[gui]: 修改bug", "status": "MERGED", "created": 1750314063, "updated": 1750322079, "mergeable": false, "number": 5057, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5057", "patchset_count": 2, "commit_id": "72b316ca14176dd084333e4fc38a21fa7ce3ee43", "insertions": 699, "deletions": 172}, {"change_id": "I06425dc427404dce32f5ef38fd94d27314c60832", "project": "qw_platform", "branch": "develop", "subject": "[gui]: 修改注释", "status": "MERGED", "created": 1750314055, "updated": 1750322079, "mergeable": false, "number": 5056, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5056", "patchset_count": 2, "commit_id": "1267e32cd505321e3db0cb5d7f60ada44116a086", "insertions": 13, "deletions": 3}, {"change_id": "I61c7a92b567d7f54af17dee39c69ce0a7f26b7d5", "project": "qw_platform", "branch": "develop", "subject": "ppgtest: disbale hrv in dvtv2", "status": "ABANDONED", "created": 1749119246, "updated": 1750321059, "mergeable": false, "number": 4715, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/4715", "patchset_count": 10, "commit_id": "20100541910f6d644e6b3ded4691950727a4e527", "insertions": 43, "deletions": 4}, {"change_id": "I8b042da7df9ad91dbeeadb553e93f103c294049f", "project": "app", "branch": "develop", "subject": "SERVICE[BUG-001]: add the logic to synchronize blood oxygen data to the app sdk", "status": "MERGED", "created": 1750318505, "updated": 1750320857, "mergeable": false, "number": 5061, "owner": "hongxing", "url": "http://********:8081/c/app/+/5061", "patchset_count": 3, "commit_id": "6b9c0e55ee8d9a0d3f38228f02544e589164f832", "insertions": 482, "deletions": 16}, {"change_id": "I8b042da7df9ad91dbeeadb553e93f103c294049f", "project": "qw_platform", "branch": "develop", "subject": "PLATFORM[BUG-001]: add the function to package blood oxygen synchronization data into FIT format", "status": "MERGED", "created": 1750318512, "updated": 1750320856, "mergeable": false, "number": 5062, "owner": "hongxing", "url": "http://********:8081/c/qw_platform/+/5062", "patchset_count": 2, "commit_id": "73353bc46aa019c84feb8099eddeb1f4fb01d42a", "insertions": 170, "deletions": 1}, {"change_id": "I3175ed014adae99929822502b582298a605e4ccb", "project": "qw_platform", "branch": "develop", "subject": "bug:修复文件加载弹窗失败时退出", "status": "MERGED", "created": 1750315975, "updated": 1750320229, "mergeable": false, "number": 5060, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5060", "patchset_count": 2, "commit_id": "576aa0f5d1d887420a5685b6b48288674d6b57e0", "insertions": 17, "deletions": 7}, {"change_id": "Ic0bc64b215c80df9edc4953113c21956da37d832", "project": "app", "branch": "develop", "subject": "navi:导航预览增加高度数据线性插值，计算平均上坡度", "status": "MERGED", "created": 1750315885, "updated": 1750320224, "mergeable": false, "number": 5059, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5059", "patchset_count": 2, "commit_id": "8950c272bd8ac1c574505e7516ee6a89a7bd8c71", "insertions": 158, "deletions": 45}, {"change_id": "I8cd757d8dabb59ff1c89f66ffa5417af6146f38f", "project": "qw_platform", "branch": "develop", "subject": "[GPS]: 修改GPS升级接口", "status": "ABANDONED", "created": 1747969054, "updated": 1750320191, "mergeable": false, "number": 4434, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4434", "patchset_count": 1, "commit_id": "8ab915d537c48fcdd95e45114ec51214a80b517a", "insertions": 58, "deletions": 1}, {"change_id": "I8cd757d8dabb59ff1c89f66ffa5417af6146f38f", "project": "sifli", "branch": "develop", "subject": "[GPS]: 修改GPS升级接口", "status": "ABANDONED", "created": 1747969026, "updated": 1750320186, "mergeable": false, "number": 4433, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/sifli/+/4433", "patchset_count": 4, "commit_id": "518d5077afe1982a74a5a893900114f297c272bb", "insertions": 11, "deletions": 1}, {"change_id": "I3cf1df924f0be0c572e1f058e72758ef74a2b30c", "project": "qw_platform", "branch": "develop", "subject": "navi:删除路线时，同步更新应用卡片中导航的数据", "status": "MERGED", "created": 1750229803, "updated": 1750320027, "mergeable": false, "number": 5040, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5040", "patchset_count": 3, "commit_id": "d7963d97bdaa563ac03b094ed1c88ab610db4b89", "insertions": 44, "deletions": 10}, {"change_id": "I3cf1df924f0be0c572e1f058e72758ef74a2b30c", "project": "app", "branch": "develop", "subject": "navi:删除路线时，同步更新应用卡片中导航的数据", "status": "MERGED", "created": 1750229756, "updated": 1750320027, "mergeable": false, "number": 5039, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5039", "patchset_count": 4, "commit_id": "c26edc071d07e16fed38ff2350cde181441d68a3", "insertions": 11, "deletions": 1}, {"change_id": "I1f99e62cff4cdd1332f698c9813545714848d59a", "project": "qw_platform", "branch": "develop", "subject": "dev: 添加跨核shell，用于光感校准的触发。", "status": "ABANDONED", "created": 1750073470, "updated": 1750318388, "mergeable": false, "number": 4873, "owner": "jinx<PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4873", "patchset_count": 7, "commit_id": "71270447f0bc3f448305420134154d880856a3bd", "insertions": 2260, "deletions": 795}, {"change_id": "I1f99e62cff4cdd1332f698c9813545714848d59a", "project": "app", "branch": "develop", "subject": "dev: 添加跨核shell，用于光感校准的触发。", "status": "ABANDONED", "created": 1750073450, "updated": 1750318388, "mergeable": false, "number": 4872, "owner": "jinx<PERSON><PERSON>", "url": "http://********:8081/c/app/+/4872", "patchset_count": 5, "commit_id": "7b67e70b2aadef837e2342b87c4059fd403eb359", "insertions": 125, "deletions": 17}, {"change_id": "Ia4f4c65c8967d6b4a80885596526a4d2087c1529", "project": "sifli", "branch": "develop", "subject": "ota: ota模块提交.", "status": "ABANDONED", "created": 1749891551, "updated": 1750318388, "mergeable": false, "number": 4871, "owner": "jinx<PERSON><PERSON>", "url": "http://********:8081/c/sifli/+/4871", "patchset_count": 3, "commit_id": "26b75169d93595534fa6fde380742ebf8fb4d09c", "insertions": 112, "deletions": 46}, {"change_id": "Ia4f4c65c8967d6b4a80885596526a4d2087c1529", "project": "qw_platform", "branch": "develop", "subject": "ota: ota模块提交.", "status": "ABANDONED", "created": 1749891541, "updated": 1750318388, "mergeable": false, "number": 4870, "owner": "jinx<PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4870", "patchset_count": 6, "commit_id": "e3902acfeb77fc0caee1bf565c6f600e59af9e31", "insertions": 8383, "deletions": 577}, {"change_id": "Ia4f4c65c8967d6b4a80885596526a4d2087c1529", "project": "app", "branch": "develop", "subject": "ota: ota模块提交.", "status": "ABANDONED", "created": 1749891527, "updated": 1750318388, "mergeable": false, "number": 4869, "owner": "jinx<PERSON><PERSON>", "url": "http://********:8081/c/app/+/4869", "patchset_count": 6, "commit_id": "518f04b54813b9a3d2af7be0ce11c14fe7abb1f7", "insertions": 926, "deletions": 276}, {"change_id": "Ia903a88eb4d146a9cc4f5fb72ee33fbe09d91b8b", "project": "app", "branch": "develop", "subject": "SERVICE[BUG-001]: add a check for the HRV file size to prevent the presence of corrupted files", "status": "MERGED", "created": 1750300850, "updated": 1750318081, "mergeable": false, "number": 5052, "owner": "hongxing", "url": "http://********:8081/c/app/+/5052", "patchset_count": 5, "commit_id": "c11d2bb13d4553ef2f0d364db0884a6492213c79", "insertions": 74, "deletions": 23}, {"change_id": "I44a23b206eb8b532fc0eea3d523fef81fc9ec091", "project": "qw_platform", "branch": "develop", "subject": "[A+G]: 时间戳直接使用RTC时间，不补偿, 提升boot time获取安全性", "status": "MERGED", "created": 1750304626, "updated": 1750317986, "mergeable": false, "number": 5053, "owner": "lixiaolong", "url": "http://********:8081/c/qw_platform/+/5053", "patchset_count": 4, "commit_id": "62a70e376728fb9079ef70573656549bd8a9bf34", "insertions": 40, "deletions": 20}, {"change_id": "I67aabdcd41151a9ac4a7cf6788ed602fb0d01a87", "project": "app", "branch": "develop", "subject": "[GUI]: 个人成就记录新类型", "status": "MERGED", "created": 1750314591, "updated": 1750316900, "mergeable": false, "number": 5058, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5058", "patchset_count": 2, "commit_id": "9239b6044335273893395fa7c861c35d46c5eae5", "insertions": 3185, "deletions": 0}, {"change_id": "I68c9e5f79be126ea3751cce42720c97ecd515ad6", "project": "sifli", "branch": "develop", "subject": "[RTC]: 解决RTC时间读取不准问题", "status": "MERGED", "created": 1750304714, "updated": 1750311216, "mergeable": false, "number": 5054, "owner": "lixiaolong", "url": "http://********:8081/c/sifli/+/5054", "patchset_count": 1, "commit_id": "86e487441efa7aebcfa7bb2fce28ad45b5b8922c", "insertions": 15, "deletions": 1}, {"change_id": "I8126bdf5d207307a6df7ad83a957a0d139a08d45", "project": "app", "branch": "develop", "subject": "[weather]: 修正天气页面bug", "status": "MERGED", "created": 1750248565, "updated": 1750302726, "mergeable": false, "number": 5047, "owner": "lixin", "url": "http://********:8081/c/app/+/5047", "patchset_count": 3, "commit_id": "2bb37f49095535e89a2cb2da7f96b565606294c9", "insertions": 785, "deletions": 647}, {"change_id": "I8126bdf5d207307a6df7ad83a957a0d139a08d45", "project": "qw_platform", "branch": "develop", "subject": "[fit]: 添加无效活动文件的判断接口", "status": "MERGED", "created": 1750248335, "updated": 1750302726, "mergeable": false, "number": 5046, "owner": "lixin", "url": "http://********:8081/c/qw_platform/+/5046", "patchset_count": 2, "commit_id": "8578383d917805142d7c2873b24da43f93de25b2", "insertions": 428, "deletions": 434}, {"change_id": "I897cda4f1bb736dc7c381f930dd0baf653faeedb", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 根据代码审查结果优化代码", "status": "MERGED", "created": 1750258202, "updated": 1750299343, "mergeable": false, "number": 5051, "owner": "wang<PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/5051", "patchset_count": 2, "commit_id": "bdb3ee8d3c262d7f66d830a84f219d6f45d7fc1a", "insertions": 207, "deletions": 434}, {"change_id": "I9aab9e5aafb66ab5e3f286e61963ca6b2634b6a6", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 优化继续骑行逻辑", "status": "MERGED", "created": 1750254095, "updated": 1750299035, "mergeable": false, "number": 5050, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/5050", "patchset_count": 1, "commit_id": "3ee302cd1aaf46b9026974a0d93d9ee45cfc8f14", "insertions": 76, "deletions": 56}, {"change_id": "I463153f465bbc52ac7cd0e07d084d2671a57dc02", "project": "app", "branch": "develop", "subject": "[GUI]: 应用卡片训练计划支持自动刷新显示", "status": "MERGED", "created": 1749896085, "updated": 1750295060, "mergeable": false, "number": 4959, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4959", "patchset_count": 3, "commit_id": "ed6718af733f6f497852da46b08625b7fc17d772", "insertions": 116, "deletions": 43}, {"change_id": "I5d93ea2267058379c47bdeb000a5b980acbd2c79", "project": "cycle/c202310_bg1", "branch": "Develop", "subject": "[Fix]: 解决继续骑行判断逻辑问题", "status": "MERGED", "created": 1750253223, "updated": 1750255454, "mergeable": false, "number": 5049, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/c202310_bg1/+/5049", "patchset_count": 1, "commit_id": "f78dd2a4113242e6f24d7aa7aa01c732d97518cc", "insertions": 103, "deletions": 83}, {"change_id": "Ic19cb52628ac34cee48b354928042179f4fd4dd6", "project": "cycle/20250607_fc06", "branch": "develop", "subject": "[Feature]: 实现宏隔离IGS_DEV 和 SIMULATOR", "status": "MERGED", "created": 1750146100, "updated": 1750249695, "mergeable": false, "number": 5010, "owner": "hancheng", "url": "http://********:8081/c/cycle/20250607_fc06/+/5010", "patchset_count": 3, "commit_id": "a39797a019329fd1712a0db9859ccec9234f7392", "insertions": 430, "deletions": 879}, {"change_id": "I5248f9f69abc4c108b0fa972092eaf9c30b7f0fa", "project": "cycle/sifli", "branch": "bg2_develop", "subject": "[Fix]: 解决sifli_sdk阻断", "status": "MERGED", "created": 1750247859, "updated": 1750249549, "mergeable": false, "number": 5045, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/sifli/+/5045", "patchset_count": 1, "commit_id": "8f1d4b3ddde85329ed623175227806da31d8e0c3", "insertions": 30, "deletions": 11}, {"change_id": "Ib89f69f96902a7c34ffe84bdfbaa53d1f76a79e5", "project": "app", "branch": "develop", "subject": "[image]: 更换折线图页面图片", "status": "MERGED", "created": 1750231615, "updated": 1750249162, "mergeable": false, "number": 5041, "owner": "lixin", "url": "http://********:8081/c/app/+/5041", "patchset_count": 3, "commit_id": "5611a9a0a0b51630a8e980000a47085558cc135f", "insertions": 276, "deletions": 724}, {"change_id": "Id98e950f1ea6ed5dadc3c1096594b490cd9e9f96", "project": "app", "branch": "develop", "subject": "GUI: 优化高度，气压Ui显示", "status": "MERGED", "created": 1750234725, "updated": 1750248651, "mergeable": false, "number": 5042, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5042", "patchset_count": 1, "commit_id": "6b4d9d196b6dcd0d415348aa383cb6b894fbfab5", "insertions": 69, "deletions": 18}, {"change_id": "Ib771b7a5b7f52062491f5aad25d8dc0974d36fdb", "project": "qw_platform", "branch": "develop", "subject": "GUI: 修复QwVerticalSwipeContainer没有写子控件click事件导致运动结算页划不动问题", "status": "MERGED", "created": 1750240429, "updated": 1750248647, "mergeable": false, "number": 5043, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5043", "patchset_count": 2, "commit_id": "e533fcb6a372f69ca063fe6bac1cf9cb0435cd8c", "insertions": 25, "deletions": 2}, {"change_id": "I5ba8668ed99582614d4cdf15eab34e5d3fdea1fe", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 解决应用阻断", "status": "MERGED", "created": 1750244295, "updated": 1750248640, "mergeable": false, "number": 5044, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/5044", "patchset_count": 2, "commit_id": "9e1501d5f43faeb4cadc25d08e3114f28aaf599e", "insertions": 250, "deletions": 151}, {"change_id": "I3db35bafe86ca12385086e0bd0f343284b80d9c5", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 解决继续骑行数据结构变更引起的数据错位问题", "status": "MERGED", "created": 1750215172, "updated": 1750244531, "mergeable": false, "number": 5028, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/5028", "patchset_count": 2, "commit_id": "a5e9fd74bcac0790a41a3e1507c57b8691d749ee", "insertions": 29, "deletions": 8}, {"change_id": "I9a6c5f0fb6a597929b6dd722775dbc70bf24fe3e", "project": "app", "branch": "develop", "subject": "alg_fmk:1、解决手动查看睡眠，抬腕失效的问题 2、仿真添加睡眠时间有效性判断 3、ppi备份清空，防止异常输入", "status": "MERGED", "created": 1750150716, "updated": 1750233422, "mergeable": false, "number": 5013, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/app/+/5013", "patchset_count": 7, "commit_id": "5db8b6ba1213324b423f2a21ba3a0b9c189385ce", "insertions": 36, "deletions": 2}, {"change_id": "If274552ce8ab68569fbe568cd38a1bcf27a97968", "project": "app", "branch": "develop", "subject": "Fix: 修复高度相关问题", "status": "MERGED", "created": 1750228481, "updated": 1750233308, "mergeable": false, "number": 5036, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5036", "patchset_count": 4, "commit_id": "a0b6a68fbe5d146dee6fe1da07c3015e9f29dac6", "insertions": 24, "deletions": 3}, {"change_id": "I0df9a786e40d1019ce897a55cfbba74c32595676", "project": "app", "branch": "develop", "subject": "GUI: 添加锁屏打桩日志", "status": "MERGED", "created": 1750225894, "updated": 1750233307, "mergeable": false, "number": 5033, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5033", "patchset_count": 3, "commit_id": "730dd2fc71b4a2e908a407ceac298a07760dc62e", "insertions": 14, "deletions": 0}, {"change_id": "I0fcfda877e405f33acda6b170db59f407fd49304", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 解决Lib_New中workout和segment阻断", "status": "MERGED", "created": 1750215093, "updated": 1750232789, "mergeable": false, "number": 5027, "owner": "hancheng", "url": "http://********:8081/c/cycle/app/+/5027", "patchset_count": 3, "commit_id": "226a806eecf5480b62adab2e7b13f7781dee571e", "insertions": 57, "deletions": 18}, {"change_id": "I12e67315f3b10d472f439a473cd845b8fbb56c69", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 解决sscanf相关警告", "status": "MERGED", "created": 1750228440, "updated": 1750232664, "mergeable": false, "number": 5035, "owner": "liquan", "url": "http://********:8081/c/cycle/app/+/5035", "patchset_count": 1, "commit_id": "fb9a9206c29545b9eb7877ef0ff28db5d1268029", "insertions": 86, "deletions": 34}, {"change_id": "I3036d0d59bdff35c60cf7238d4d85c488a81f9c2", "project": "app", "branch": "develop", "subject": "beep: fix beep_param local varible address reference", "status": "MERGED", "created": 1750227823, "updated": 1750232322, "mergeable": false, "number": 5034, "owner": "linjizhao", "url": "http://********:8081/c/app/+/5034", "patchset_count": 2, "commit_id": "ea59b1c8f2abeb060bb80e402680f67b05a8e73f", "insertions": 17, "deletions": 15}, {"change_id": "I4ddb34ea750af9966a9917656cbf91579ebe31c1", "project": "sifli", "branch": "develop", "subject": "Revert \"sleep: disable lcpu enter sleep in A4 && A3\"", "status": "MERGED", "created": 1750142786, "updated": 1750232267, "mergeable": false, "number": 4789, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/4789", "patchset_count": 2, "commit_id": "1e68c9f59c4dee57db0a99bc42fa7da1c0beaabd", "insertions": 24, "deletions": 10}, {"change_id": "I18da56be08b8de30638decd2586955f52276dbe1", "project": "qw_platform", "branch": "develop", "subject": "[grid]: 删除无用的注释代码", "status": "MERGED", "created": 1750228851, "updated": 1750230757, "mergeable": false, "number": 5038, "owner": "lixin", "url": "http://********:8081/c/qw_platform/+/5038", "patchset_count": 1, "commit_id": "acce758fcf14b630c63b5a7ca00260194c259312", "insertions": 12, "deletions": 221}, {"change_id": "I18da56be08b8de30638decd2586955f52276dbe1", "project": "app", "branch": "develop", "subject": "[cfg]: 添加个人成就纪录新项", "status": "MERGED", "created": 1750228759, "updated": 1750230757, "mergeable": false, "number": 5037, "owner": "lixin", "url": "http://********:8081/c/app/+/5037", "patchset_count": 1, "commit_id": "81efbc67247da0464e57a367e90ed5cbfb5ac320", "insertions": 273, "deletions": 203}, {"change_id": "I3d4a421a4237eb545a4610b48bd478944e67ff9f", "project": "app", "branch": "develop", "subject": "GUI: 新增app下发使用表盘指令，表盘立刻发生变化功能", "status": "MERGED", "created": 1750217870, "updated": 1750228096, "mergeable": false, "number": 5031, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5031", "patchset_count": 2, "commit_id": "68c6657980b137a5b7365ee8d06cd72e31f75012", "insertions": 16, "deletions": 2}, {"change_id": "I3d4a421a4237eb545a4610b48bd478944e67ff9f", "project": "qw_platform", "branch": "develop", "subject": "GUI: 表盘与app操作事件新增使用事件", "status": "MERGED", "created": 1750217715, "updated": 1750228096, "mergeable": false, "number": 5030, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5030", "patchset_count": 2, "commit_id": "d509ccc0d75cd97a622c7debee2bf34df135d75b", "insertions": 11, "deletions": 0}, {"change_id": "I482a75b8ce3ab32ba70b4582ff48a2f6d40cf96e", "project": "app", "branch": "develop", "subject": "[sonar]: 解决sonar bug", "status": "MERGED", "created": 1750153216, "updated": 1750228036, "mergeable": false, "number": 5016, "owner": "lixin", "url": "http://********:8081/c/app/+/5016", "patchset_count": 3, "commit_id": "8e8638464c7924eaf9e407a6fd64d3ad2b24647c", "insertions": 42, "deletions": 17}, {"change_id": "I4beccc33273e9570f7b2babdb5b986d1b25d5dc2", "project": "qw_platform", "branch": "develop", "subject": "app:增加间歇详情数据接口", "status": "MERGED", "created": 1750142327, "updated": 1750228006, "mergeable": false, "number": 5008, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5008", "patchset_count": 2, "commit_id": "9da73786adfffe94db8daf4899977780a512a0c8", "insertions": 12, "deletions": 2}, {"change_id": "I4beccc33273e9570f7b2babdb5b986d1b25d5dc2", "project": "app", "branch": "develop", "subject": "app:增加间歇详情数据接口", "status": "MERGED", "created": 1750142266, "updated": 1750228006, "mergeable": false, "number": 5007, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5007", "patchset_count": 7, "commit_id": "7e667f3f27f68f20b3dd69ca2b60a0b0464eb172", "insertions": 291, "deletions": 169}, {"change_id": "I995a08e9af1d0365ff206690a9dba6f0cb8fbd2a", "project": "wearable/hr50", "branch": "develop", "subject": "[HR50]:修复佳明蓝牙连接问题", "status": "NEW", "created": 1750211601, "updated": 1750218175, "mergeable": false, "number": 5025, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/wearable/hr50/+/5025", "patchset_count": 3, "commit_id": "e807be6bc50b57111910fc71c4c53bf17c2aa287", "insertions": 11352, "deletions": 190}, {"change_id": "I165c773b9117bc9b3d440bac1676407e86445f99", "project": "app", "branch": "develop", "subject": "bug:17646 进入秒表时死机", "status": "ABANDONED", "created": 1750162938, "updated": 1750218159, "mergeable": false, "number": 5018, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5018", "patchset_count": 1, "commit_id": "e487ae6e727cb86d252b2c69df77391bf41f6964", "insertions": 20, "deletions": 83}, {"change_id": "I1b019c3440d9d2c422490e81b164e0373448b522", "project": "qw_platform", "branch": "develop", "subject": "GUI: 优化运动网格页滑动卡顿问题，注释截图动作", "status": "MERGED", "created": 1750162527, "updated": 1750217600, "mergeable": false, "number": 5017, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5017", "patchset_count": 2, "commit_id": "ab132f1eea528ccc10bf987f61b691df94810bdb", "insertions": 20, "deletions": 10}, {"change_id": "I248f50df9636010e91169012c83a055af4c099b2", "project": "app", "branch": "develop", "subject": "FIx: 解决表盘编辑数据页面骑行、跑步最大摄氧量显示不对问题", "status": "MERGED", "created": 1750164733, "updated": 1750217590, "mergeable": false, "number": 5021, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5021", "patchset_count": 1, "commit_id": "da702c2a4f0a871976a192569f9ab4febb29d4c2", "insertions": 82, "deletions": 36}, {"change_id": "I37933dc45cb977dacfea0f87b6b82caffc38539f", "project": "cycle/c202310_bg1", "branch": "Develop", "subject": "[Fix]: 解决继续骑行数据结构变更引起是数据错误问题", "status": "MERGED", "created": 1750215508, "updated": 1750215611, "mergeable": false, "number": 5029, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/c202310_bg1/+/5029", "patchset_count": 1, "commit_id": "4c7a11642a53a411aa5bfadfb1793c1960a15da5", "insertions": 29, "deletions": 10}, {"change_id": "Iaf06c60e315a6388981d470c9b4baadf70b37c46", "project": "cycle/20250607_fc06", "branch": "develop", "subject": "[featurn]: 1.PB应用层加入编译", "status": "MERGED", "created": 1750077350, "updated": 1750212629, "mergeable": false, "number": 4999, "owner": "<PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/20250607_fc06/+/4999", "patchset_count": 2, "commit_id": "296a73f7a4fd3c8285597d749746384cf7c5e8a9", "insertions": 339, "deletions": 109}, {"change_id": "I5616a95031b97d1cba78fb758140813a5a12e196", "project": "wearable/hr50", "branch": "develop", "subject": "Revert \"hr50_led:重构led模块，优化状态机处理逻辑\"", "status": "ABANDONED", "created": 1750158062, "updated": 1750209349, "mergeable": false, "number": 4790, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/wearable/hr50/+/4790", "patchset_count": 1, "commit_id": "e76e5372544edbd5ad8719bf24fdb1a948e5f028", "insertions": 257, "deletions": 260}, {"change_id": "I72621b55316b313402fc4cceeaadcde8d15b4942", "project": "wearable/hr50", "branch": "develop", "subject": "Revert \"hr50_led:重构led模块，优化状态机处理逻辑\"", "status": "MERGED", "created": 1750208661, "updated": 1750209245, "mergeable": false, "number": 4792, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/wearable/hr50/+/4792", "patchset_count": 1, "commit_id": "6c19add4bfd454c6c113c86410028dbc728eee2f", "insertions": 257, "deletions": 260}, {"change_id": "I5cad13088e79365796454781cd365a0d06f3ca12", "project": "wearable/hr50", "branch": "develop", "subject": "hr50_led:重构led模块，优化状态机处理逻辑", "status": "MERGED", "created": 1749438837, "updated": 1750208661, "mergeable": false, "number": 4803, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/wearable/hr50/+/4803", "patchset_count": 1, "commit_id": "0be6875d6548bac77bc6119cd0348f4c1901f0ef", "insertions": 270, "deletions": 243}, {"change_id": "I87d39bfef248f83a39a166a82c865063d5622b4f", "project": "qw_platform", "branch": "develop", "subject": "offbody: force onbody for debug", "status": "NEW", "created": 1749543789, "updated": 1750201326, "mergeable": false, "number": 4832, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/4832", "patchset_count": 1, "commit_id": "0792c319d8ccdceaef253e6d9dd04612bc41c347", "insertions": 11, "deletions": 1}, {"change_id": "I361a4d91b1e7c549c9b539982a23d026fb7b542e", "project": "qw_platform", "branch": "develop", "subject": "onbody: force onbody && disable sleep", "status": "NEW", "created": 1749455602, "updated": 1750201325, "mergeable": false, "number": 4806, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/4806", "patchset_count": 4, "commit_id": "63fbab381f3b97ca199633be081ee8b32265b08b", "insertions": 13, "deletions": 2}, {"change_id": "I75a9d2254c2b35101a550793868113760ed43cdf", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 解决qwos相关警告", "status": "MERGED", "created": 1750168688, "updated": 1750171647, "mergeable": false, "number": 5024, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/5024", "patchset_count": 2, "commit_id": "a269972787b72351cc97cb7d5f36597b9ffcace7", "insertions": 220, "deletions": 438}, {"change_id": "If941ab262a2306b2e28fb7de96e6b5d884ffa857", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 优化UI跟手延迟", "status": "MERGED", "created": 1750168688, "updated": 1750169577, "mergeable": false, "number": 5023, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/5023", "patchset_count": 1, "commit_id": "1082d0409e4bd3cf2eef2483470327f55d0e239e", "insertions": 22, "deletions": 2}, {"change_id": "If88b528ded5536d7ff1a01d35ab9a08cabb4b0e9", "project": "cycle/sifli", "branch": "bg2_develop", "subject": "[Fix]: 解决sifli阻断", "status": "MERGED", "created": 1750148317, "updated": 1750168801, "mergeable": false, "number": 5011, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/sifli/+/5011", "patchset_count": 3, "commit_id": "ed12f915a1cefaa40339d3feaef903a6600dce5b", "insertions": 20, "deletions": 23}, {"change_id": "I193e4d18d55e7b203babfd3aaf0153d6eead9500", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 优化传感器连接(减少ANT/BLE一起搜索时影响)", "status": "MERGED", "created": 1749903257, "updated": 1750167695, "mergeable": false, "number": 4966, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/4966", "patchset_count": 3, "commit_id": "36a157e0dd2126366012cfe2059de1a8b07e0ecc", "insertions": 130, "deletions": 29}, {"change_id": "I959a0cdbf2a23c5ee2af521197472e8e0ea64fcd", "project": "qw_platform", "branch": "develop", "subject": "ppg: add ppg && acc event timestamp", "status": "MERGED", "created": 1750143441, "updated": 1750165510, "mergeable": false, "number": 5009, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/5009", "patchset_count": 3, "commit_id": "fcb51190556d4f3e4d2b8b2657195de5f65586dd", "insertions": 236, "deletions": 226}, {"change_id": "If16026f3c41074f203d6e27ce91cfae786d5076c", "project": "cycle/c202310_bg1", "branch": "Develop", "subject": "[Feature]: 新增工厂界面检测GM算法", "status": "MERGED", "created": 1750163100, "updated": 1750163250, "mergeable": false, "number": 5020, "owner": "liquan", "url": "http://********:8081/c/cycle/c202310_bg1/+/5020", "patchset_count": 1, "commit_id": "c4d0cedff4ec1339f119fd4d858842ef97503bd1", "insertions": 85, "deletions": 2}, {"change_id": "If2840fb05e5d59ebdb49b4e10e708f6ad36775f3", "project": "cycle/c202310_bg1", "branch": "Develop", "subject": "[Fix]: 1. 解决保存活动摘要页为数字问题 2.导航首页缩略图距离爬升显示错误问题", "status": "MERGED", "created": 1750163100, "updated": 1750163245, "mergeable": false, "number": 5019, "owner": "liquan", "url": "http://********:8081/c/cycle/c202310_bg1/+/5019", "patchset_count": 1, "commit_id": "69d1c68659ec26f8736092e7879fcae5706a0ec9", "insertions": 19, "deletions": 6}, {"change_id": "I12e49fa40d12345cd8d67a4244980a4020b9afa8", "project": "qw_platform", "branch": "develop", "subject": "PLATFORM[BUG-001]: add interfaces for setting and getting the activity status in shared memory", "status": "MERGED", "created": 1750152299, "updated": 1750157537, "mergeable": false, "number": 5015, "owner": "hongxing", "url": "http://********:8081/c/qw_platform/+/5015", "patchset_count": 2, "commit_id": "ee98733a0546f54aff0e7a57531a62affc4ac9bd", "insertions": 48, "deletions": 0}, {"change_id": "I12e49fa40d12345cd8d67a4244980a4020b9afa8", "project": "app", "branch": "develop", "subject": "SERVICE[BUG-001]: modify the acquisition method of activity status", "status": "MERGED", "created": 1750152292, "updated": 1750157537, "mergeable": false, "number": 5014, "owner": "hongxing", "url": "http://********:8081/c/app/+/5014", "patchset_count": 2, "commit_id": "9d7f69a1864dd2608ed08976433112cfaee8b9e0", "insertions": 24, "deletions": 6}, {"change_id": "Ibef5d3a5b3565170330b867310295590c387d531", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Optimize]: 删除旧导航模块代码", "status": "MERGED", "created": 1750150497, "updated": 1750152857, "mergeable": false, "number": 5012, "owner": "liquan", "url": "http://********:8081/c/cycle/app/+/5012", "patchset_count": 2, "commit_id": "c93b95d93568af0da009ed77a4f23fcf9226cc9c", "insertions": 42, "deletions": 33139}, {"change_id": "Ief5c9553c9c1858cc9d00753d24da764a431c892", "project": "sifli", "branch": "develop", "subject": "sleep: disable lcpu enter sleep in A4 && A3", "status": "MERGED", "created": 1749779883, "updated": 1750142786, "mergeable": false, "number": 4926, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/4926", "patchset_count": 1, "commit_id": "34145bf921a401dce9c735a9e9c61092674d9d92", "insertions": 20, "deletions": 10}, {"change_id": "I7f62f8eef12fc3105d972a74e3608259d3a2ddcf", "project": "app", "branch": "develop", "subject": "[sonar]: 解决sonar bug", "status": "MERGED", "created": 1750139816, "updated": 1750142515, "mergeable": false, "number": 5005, "owner": "lixin", "url": "http://********:8081/c/app/+/5005", "patchset_count": 2, "commit_id": "dcff1c42a624c30bf3f3630622f41ab005832052", "insertions": 187, "deletions": 122}, {"change_id": "I5a9ba49a946e821c7c2a65f75482c889742b36fb", "project": "tools", "branch": "wr02_algosim", "subject": "alg_fmk:静息心率采集计算工具（滑窗：时间30min和点数300）", "status": "MERGED", "created": 1750142269, "updated": 1750142371, "mergeable": false, "number": 4787, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/tools/+/4787", "patchset_count": 1, "commit_id": "4947554b377ae511220b4a126c26a9d8fe3fea75", "insertions": 186, "deletions": 0}, {"change_id": "I5a9ba49a946e821c7c2a65f75482c889742b36fb", "project": "tools", "branch": "develop", "subject": "alg_fmk:静息心率采集计算工具（滑窗：时间30min和点数300）", "status": "MERGED", "created": 1750141706, "updated": 1750142365, "mergeable": false, "number": 5006, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/tools/+/5006", "patchset_count": 1, "commit_id": "d2e82f74732ac99265e7b9cef8779fc2a4a4e88d", "insertions": 186, "deletions": 0}, {"change_id": "I5338fffdd2fe98fa989271b6fb35934684b4e8fe", "project": "app", "branch": "develop", "subject": "[sonar]: 解决sonar bug", "status": "MERGED", "created": 1750131795, "updated": 1750133780, "mergeable": false, "number": 5003, "owner": "lixin", "url": "http://********:8081/c/app/+/5003", "patchset_count": 3, "commit_id": "d9f8aefa62ab8a15de951cb693296616f8482e39", "insertions": 89, "deletions": 57}, {"change_id": "I04b0e55551ffc56a362a3b26e4ce616c98d9ea5a", "project": "qw_platform", "branch": "develop", "subject": "[GUI]:训练按步骤计圈，不自动计圈", "status": "MERGED", "created": 1750128377, "updated": 1750131159, "mergeable": false, "number": 5001, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/5001", "patchset_count": 2, "commit_id": "f7b04667ebaf7d309f0eccd98a1b03a82dde8944", "insertions": 14, "deletions": 1}, {"change_id": "I04b0e55551ffc56a362a3b26e4ce616c98d9ea5a", "project": "app", "branch": "develop", "subject": "[GUI]: 训练按步骤计圈，不自动计圈", "status": "MERGED", "created": 1750128359, "updated": 1750131159, "mergeable": false, "number": 5000, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/5000", "patchset_count": 1, "commit_id": "311d931956f18c5b64beef8a8e48c1f59b45516b", "insertions": 111, "deletions": 33}, {"change_id": "I09e241f8f7d6a9fb6bbed249f8368577527f71bb", "project": "qw_platform", "branch": "develop", "subject": "SERVICE[BUG-001]: fix the incorrect UI of the sedentary reminder pop-up window", "status": "MERGED", "created": 1750129167, "updated": 1750131150, "mergeable": false, "number": 5002, "owner": "hongxing", "url": "http://********:8081/c/qw_platform/+/5002", "patchset_count": 1, "commit_id": "fd96dc7eb4ecac5eb4cb4fb74a0c44899b3e5e0a", "insertions": 14, "deletions": 1}, {"change_id": "I8e256cb5e40b06337f2b04850ace4fdb8d05c5b8", "project": "cycle/sifli", "branch": "bg2_develop", "subject": "[Fix]: 解决nrf52832小概率消息延迟问题", "status": "MERGED", "created": 1750038543, "updated": 1750124052, "mergeable": false, "number": 4970, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/sifli/+/4970", "patchset_count": 1, "commit_id": "3f97fd6243cabfa02820344efbc0a25c59efc1c6", "insertions": 12, "deletions": 9}, {"change_id": "I28b2b94ef4c26e991af98be9d773e674e7804543", "project": "sifli", "branch": "wr02_release", "subject": "[fix]: 修正sonar代码扫描阻断问题", "status": "ABANDONED", "created": 1746666793, "updated": 1750123314, "mergeable": false, "number": 4024, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/4024", "patchset_count": 1, "commit_id": "d479995f8b0a9bc00e9044b0d15fbc6be5e07243", "insertions": 21, "deletions": 13}, {"change_id": "Ib758ab7ca22fdd6ed20c92a298d7a86d93d55472", "project": "sifli", "branch": "wr02_release", "subject": "sonarqube: fix null pointer reference", "status": "ABANDONED", "created": 1747374803, "updated": 1750123307, "mergeable": false, "number": 4234, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/4234", "patchset_count": 1, "commit_id": "2769339176688181da7c7e12fd41326dbce026a0", "insertions": 28, "deletions": 8}, {"change_id": "I639e51fb6e491c2854de61dac9fd3d55e346c655", "project": "cycle/c202310_bg1", "branch": "Develop", "subject": "[Fix]: 1. 适配双行显示规则 2. 解决回程导航没有航向箭头问题", "status": "MERGED", "created": 1750076438, "updated": 1750077168, "mergeable": false, "number": 4998, "owner": "liquan", "url": "http://********:8081/c/cycle/c202310_bg1/+/4998", "patchset_count": 1, "commit_id": "f07cb677f4c958ffcc48ee43515555335ae9d958", "insertions": 93, "deletions": 27}, {"change_id": "I77124857bacdb521c0636d9f38a420c7f702f122", "project": "cycle/c202310_bg1", "branch": "Develop", "subject": "[Fix]: 同步解决保存/放弃活动地图页会存在导航结束弹窗问题", "status": "MERGED", "created": 1750076438, "updated": 1750077165, "mergeable": false, "number": 4997, "owner": "liquan", "url": "http://********:8081/c/cycle/c202310_bg1/+/4997", "patchset_count": 1, "commit_id": "0a4b9a06d86f69e48db83fef35e4f776edf79e03", "insertions": 21, "deletions": 4}, {"change_id": "I2a22b5c62f3abbf62935766524f50865ef79caf1", "project": "app", "branch": "develop", "subject": "SERVICE[BUG-001]: add simulated data for strength activity duration to the piling version", "status": "MERGED", "created": 1750075657, "updated": 1750076828, "mergeable": false, "number": 4993, "owner": "hongxing", "url": "http://********:8081/c/app/+/4993", "patchset_count": 2, "commit_id": "4be5ba02cc4d5015cfd34ffb20b40224989ae1c4", "insertions": 17, "deletions": 1}, {"change_id": "I2a22b5c62f3abbf62935766524f50865ef79caf1", "project": "qw_platform", "branch": "develop", "subject": "PLATFORM[BUG-001]: modify the pressure piling data in the piling version", "status": "MERGED", "created": 1750075665, "updated": 1750076827, "mergeable": false, "number": 4994, "owner": "hongxing", "url": "http://********:8081/c/qw_platform/+/4994", "patchset_count": 2, "commit_id": "2eabe207b93399d57808282c6570510232e368ee", "insertions": 20, "deletions": 4}, {"change_id": "Ia7f4b75be800013e32ab8160f22d9c117c17eeb9", "project": "app", "branch": "develop", "subject": "Fix: 修复日常健康字体高度不对导致的英文隐藏问题", "status": "MERGED", "created": 1750074349, "updated": 1750076097, "mergeable": false, "number": 4992, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4992", "patchset_count": 3, "commit_id": "87210b434e610a73783c0b96575e8a1653e97490", "insertions": 13, "deletions": 1}, {"change_id": "Icbf269233a5633c56413982d9fbc13665ca7b432", "project": "app", "branch": "develop", "subject": "[gui]: 修正记组类运动页面", "status": "MERGED", "created": 1750072217, "updated": 1750076073, "mergeable": false, "number": 4991, "owner": "lixin", "url": "http://********:8081/c/app/+/4991", "patchset_count": 2, "commit_id": "480596469313bae85014fdf82b2ab13bd76ab200", "insertions": 1903, "deletions": 1707}, {"change_id": "Icbf269233a5633c56413982d9fbc13665ca7b432", "project": "qw_platform", "branch": "develop", "subject": "[gird]: 修改记组运动类型的页面跳转逻辑", "status": "MERGED", "created": 1750072175, "updated": 1750076072, "mergeable": false, "number": 4990, "owner": "lixin", "url": "http://********:8081/c/qw_platform/+/4990", "patchset_count": 1, "commit_id": "f6b88e4d0ea84171011d44f20bf3ffe2a709e135", "insertions": 571, "deletions": 531}, {"change_id": "Ief86832bd4a943a9ecaf2e2fbe7bfa7567e2614e", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 1. 解决变速齿轮图形化不刷新问题 2. 解决工厂界面BOOT版本号位置文本显示APP问题", "status": "MERGED", "created": 1750043754, "updated": 1750071097, "mergeable": false, "number": 4973, "owner": "liquan", "url": "http://********:8081/c/cycle/app/+/4973", "patchset_count": 2, "commit_id": "da3b0015cc21c668c9fd908677307a056b0a8aab", "insertions": 13, "deletions": 4}, {"change_id": "I13ed84d83f397e9780e16a4677c32ef0ec988a23", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 解决FTP测量步骤时长显示错误", "status": "MERGED", "created": 1749648593, "updated": 1750071081, "mergeable": false, "number": 4899, "owner": "hancheng", "url": "http://********:8081/c/cycle/app/+/4899", "patchset_count": 3, "commit_id": "762ee62df90de2658cadcd79e97b3f4e57ab6df0", "insertions": 19, "deletions": 3}, {"change_id": "I0b18d1a99d7cc803a6a19f0c9a75a0c53ab46b91", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: agps更新弹窗逻辑优化", "status": "MERGED", "created": 1750056293, "updated": 1750071047, "mergeable": false, "number": 4980, "owner": "wang<PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/4980", "patchset_count": 2, "commit_id": "a9b3d882c40fe32850784472b7dfc8aee776548c", "insertions": 52, "deletions": 19}, {"change_id": "Ib112f1c8b7aabc535e38031c9c9b775e0c165987", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 优化AGPS弹窗显示样式", "status": "MERGED", "created": 1749895232, "updated": 1750071026, "mergeable": false, "number": 4958, "owner": "wang<PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/4958", "patchset_count": 2, "commit_id": "ad4e73720965c77cabc172726af5534ea9888af7", "insertions": 11, "deletions": 1}, {"change_id": "I0c35bf28b2c805c5dab5b6ed080ad8d5237fe48b", "project": "cycle/20250607_fc06", "branch": "develop", "subject": "[feature]: 1. 新增cscs_c服务 2.新增透传数据处理模块", "status": "MERGED", "created": 1749816788, "updated": 1750066391, "mergeable": false, "number": 4948, "owner": "<PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/20250607_fc06/+/4948", "patchset_count": 4, "commit_id": "0c6b3d668ba8ba74f435b8a2e4934a870d2f25e5", "insertions": 1249, "deletions": 175}, {"change_id": "If41bc61475dceeec18957de2aab4580438ba11b1", "project": "cycle/c202310_bg1", "branch": "Develop", "subject": "[Fix]: 解决相机回连慢问题", "status": "MERGED", "created": 1750065111, "updated": 1750066253, "mergeable": false, "number": 4988, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/c202310_bg1/+/4988", "patchset_count": 1, "commit_id": "401517982e151f05f0220684046d7c9af1b8a25a", "insertions": 73, "deletions": 25}, {"change_id": "Ic9032f6533753867e892158e2cd6aae3cc4969b0", "project": "cycle/c202310_bg1", "branch": "Develop", "subject": "[Fix]: 解决隧道补偿时间异常问题", "status": "MERGED", "created": 1750065111, "updated": 1750066249, "mergeable": false, "number": 4987, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/c202310_bg1/+/4987", "patchset_count": 1, "commit_id": "2ac45c31a31f124e3eeee1415fffcf883bd5b8d5", "insertions": 69, "deletions": 47}, {"change_id": "I333e684658ef2f0f583c14c11fc5277c5ad20f1d", "project": "cycle/20250607_fc06", "branch": "develop", "subject": "[feature]: 1.同步删除文件", "status": "ABANDONED", "created": 1750063625, "updated": 1750065881, "mergeable": false, "number": 4986, "owner": "<PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/20250607_fc06/+/4986", "patchset_count": 2, "commit_id": "dff1ecdadf1fb512231b3544c0780dfb9517986e", "insertions": 17, "deletions": 10146}, {"change_id": "Id074334706f9d6a8ea314c3f8ede38fd144d773e", "project": "app", "branch": "develop", "subject": "app:导航和训练同时开启", "status": "MERGED", "created": 1750059111, "updated": 1750063491, "mergeable": false, "number": 4983, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4983", "patchset_count": 5, "commit_id": "b4b786b5ccfd1d81c597df8fdaa241d3aee68009", "insertions": 87, "deletions": 90}, {"change_id": "I2eb0b0c2a586f1dc0ef0a82b63925478bd86d1eb", "project": "qw_platform", "branch": "develop", "subject": "ui:应用卡片翻译后超长问题", "status": "MERGED", "created": 1750054328, "updated": 1750063482, "mergeable": false, "number": 4979, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4979", "patchset_count": 2, "commit_id": "11713f1b0b9652f89b2ee8b7a73103c3e6af2970", "insertions": 13, "deletions": 2}, {"change_id": "I2eb0b0c2a586f1dc0ef0a82b63925478bd86d1eb", "project": "app", "branch": "develop", "subject": "ui:应用卡片翻译后超长问题", "status": "MERGED", "created": 1750054272, "updated": 1750063481, "mergeable": false, "number": 4978, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4978", "patchset_count": 2, "commit_id": "c9c4bf2e21cc70bc53b88499bf9735b79f4a39d3", "insertions": 13, "deletions": 3}, {"change_id": "I2d0747736f8dc4799fea8ba43325f2430f2d6862", "project": "tools", "branch": "develop", "subject": "alg_fmk:解决跑步分析工具计算MAPE异常的问题 1、以大于0的条件洗数据 2、对输入做时间戳对齐", "status": "MERGED", "created": 1750061109, "updated": 1750062371, "mergeable": false, "number": 4985, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/tools/+/4985", "patchset_count": 3, "commit_id": "32f15d0cba7f9bafa3783919abb6e7b0a2bdc20c", "insertions": 24, "deletions": 7}, {"change_id": "I2d0747736f8dc4799fea8ba43325f2430f2d6862", "project": "tools", "branch": "wr02_algosim", "subject": "alg_fmk:解决跑步分析工具计算MAPE异常的问题 1、以大于0的条件洗数据 2、对输入做时间戳对齐", "status": "MERGED", "created": 1750062300, "updated": 1750062366, "mergeable": false, "number": 4785, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/tools/+/4785", "patchset_count": 1, "commit_id": "f6f837dc89359fadc438be5599bb4730543439fe", "insertions": 24, "deletions": 7}, {"change_id": "Ia27a2a451b5342ffb44a0f6783d52c942eb9d349", "project": "cycle/20250607_fc06", "branch": "develop", "subject": "[Fix]: 补充提交项目文件", "status": "MERGED", "created": 1750058922, "updated": 1750060211, "mergeable": false, "number": 4982, "owner": "hancheng", "url": "http://********:8081/c/cycle/20250607_fc06/+/4982", "patchset_count": 1, "commit_id": "9ca99bef814270bf6ef50c6a332262ed11ab8249", "insertions": 43, "deletions": 5}, {"change_id": "Ib891caeda7f9f3088eea3269ae662d1fa1c90391", "project": "app", "branch": "develop", "subject": "[GUI]: 支持运动中设置训练课程。", "status": "MERGED", "created": 1750057097, "updated": 1750058621, "mergeable": false, "number": 4981, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4981", "patchset_count": 2, "commit_id": "f09e339789a01643dd13a78bc19e8bad45e28e79", "insertions": 89, "deletions": 41}, {"change_id": "If3c15dee1913c0ad2686aaa8f6e4f093a2b9f161", "project": "qw_platform", "branch": "develop", "subject": "GUI: 新增fit文件删除时删除对应缩略图功能", "status": "MERGED", "created": 1750053824, "updated": 1750058611, "mergeable": false, "number": 4977, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4977", "patchset_count": 2, "commit_id": "b1eebe252627d7f3f554de26ebde5cb8bf1115d6", "insertions": 22, "deletions": 3}, {"change_id": "If3c15dee1913c0ad2686aaa8f6e4f093a2b9f161", "project": "app", "branch": "develop", "subject": "GUI: 新增删除目录内容及目录本身接口", "status": "MERGED", "created": 1750053720, "updated": 1750058610, "mergeable": false, "number": 4975, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4975", "patchset_count": 2, "commit_id": "1f9c6e8ad5f2232784cad2b7e5d48c4e21ab3995", "insertions": 30, "deletions": 0}, {"change_id": "I5ee9c6974e065bb4a959e9c0053ff4d9a504b149", "project": "app", "branch": "develop", "subject": "app:运动时，旋钮可以打破AOD", "status": "MERGED", "created": 1750038907, "updated": 1750058605, "mergeable": false, "number": 4971, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4971", "patchset_count": 2, "commit_id": "c5089d246d53ba67bcd185fa820dc6d3819dd07c", "insertions": 31, "deletions": 13}, {"change_id": "Ie4ace4c3606d1fdeb5f5a689643526487a08617e", "project": "qw_platform", "branch": "develop", "subject": "GUI: 优化健康达标提醒弹窗逻辑", "status": "MERGED", "created": 1750041768, "updated": 1750058546, "mergeable": false, "number": 4972, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4972", "patchset_count": 3, "commit_id": "d7fb4edd9515d8f412105e366a94549ff10c702f", "insertions": 61, "deletions": 157}, {"change_id": "I9e4e8e146ff5da61a8e4871ca28da57a98bfdc56", "project": "cycle/20250607_fc06", "branch": "develop", "subject": "[Feature]: 参数相关", "status": "MERGED", "created": 1749893532, "updated": 1750057211, "mergeable": false, "number": 4956, "owner": "hancheng", "url": "http://********:8081/c/cycle/20250607_fc06/+/4956", "patchset_count": 6, "commit_id": "7e8fe5e412b38a5f18641bef5663cb3782851e73", "insertions": 7549, "deletions": 77732}, {"change_id": "I1c2e1353c6558d2fa437288e68fdbdd28d51fcca", "project": "app", "branch": "develop", "subject": "alg_fmk:[GUI]: 解决禅道bug", "status": "MERGED", "created": 1750053789, "updated": 1750055846, "mergeable": false, "number": 4976, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/app/+/4976", "patchset_count": 1, "commit_id": "055d851b246d54f13925e846ffeccaece89905f0", "insertions": 22, "deletions": 0}, {"change_id": "I5979e6f5b2dbb9f98c60267973ea8dbce0306c2d", "project": "app", "branch": "develop", "subject": "alg_fmk:[GUI]: 解决禅道bug", "status": "MERGED", "created": **********, "updated": **********, "mergeable": false, "number": 4947, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/app/+/4947", "patchset_count": 5, "commit_id": "404d24d2c9cca64d59063bf9721ec820ebc3dd4a", "insertions": 34, "deletions": 2}, {"change_id": "I2d565de2abc689277fe763e86503798ef957000f", "project": "app", "branch": "develop", "subject": "[health]: 小核给大核发送睡眠数据时，禁止大核休眠", "status": "MERGED", "created": **********, "updated": **********, "mergeable": false, "number": 4967, "owner": "lixiaolong", "url": "http://********:8081/c/app/+/4967", "patchset_count": 3, "commit_id": "d520dcd9b23fbdfbe849c0699ef841a0f391e4dc", "insertions": 15, "deletions": 1}, {"change_id": "I5a587eca07b6fd305a8a38499924d8c4f2ed0fe9", "project": "sifli", "branch": "develop", "subject": "[ipc]: IPC超时后ASSERT，方便问题定位", "status": "MERGED", "created": **********, "updated": **********, "mergeable": false, "number": 4968, "owner": "lixiaolong", "url": "http://********:8081/c/sifli/+/4968", "patchset_count": 4, "commit_id": "2f560ab913cd71776cecc5b37a96a4910bccea69", "insertions": 12, "deletions": 1}, {"change_id": "I8c9547caa046ef8d2c81463bee4992633163904c", "project": "qw_platform", "branch": "develop", "subject": "factory:老化测试页面", "status": "MERGED", "created": **********, "updated": 1750038410, "mergeable": false, "number": 4955, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4955", "patchset_count": 3, "commit_id": "1be09c48f5ebcfe93bdc6dda5d12062521a2ce4e", "insertions": 12, "deletions": 0}, {"change_id": "I8c9547caa046ef8d2c81463bee4992633163904c", "project": "app", "branch": "develop", "subject": "factory:老化测试页面", "status": "MERGED", "created": 1749890681, "updated": 1750038410, "mergeable": false, "number": 4954, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4954", "patchset_count": 5, "commit_id": "3556a2b221fca2980f36116eaab782155eda4dcc", "insertions": 734, "deletions": 3}, {"change_id": "I844fb6876fec96598a2e844089f58c7faad0ab12", "project": "app", "branch": "develop", "subject": "GUI: 优化summary显示逻辑", "status": "MERGED", "created": 1749898756, "updated": 1750038400, "mergeable": false, "number": 4962, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4962", "patchset_count": 4, "commit_id": "2122c7d64fc1bd857c08845d16b64447db5e0a45", "insertions": 275, "deletions": 37}, {"change_id": "I844fb6876fec96598a2e844089f58c7faad0ab12", "project": "qw_platform", "branch": "develop", "subject": "fit: 新增设置，获取当前正造操作的fit文件时间戳接口", "status": "MERGED", "created": 1749898499, "updated": 1750038400, "mergeable": false, "number": 4961, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4961", "patchset_count": 1, "commit_id": "f226fb9bc412c6fe01d70b97b3dd48a73e114262", "insertions": 32, "deletions": 2}, {"change_id": "I0580e6db8158ccef3776e70f506c17c471468875", "project": "tools", "branch": "develop", "subject": "alg_fmk:备份数据分析工具-v3.0", "status": "MERGED", "created": 1749808366, "updated": 1750038134, "mergeable": false, "number": 4944, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/tools/+/4944", "patchset_count": 2, "commit_id": "5f702ba1b1368c36a1998baf6dc89ba54de3b202", "insertions": 12, "deletions": 0}, {"change_id": "I0580e6db8158ccef3776e70f506c17c471468875", "project": "tools", "branch": "wr02_algosim", "subject": "alg_fmk:备份数据分析工具-v3.0", "status": "MERGED", "created": 1749812100, "updated": 1750038109, "mergeable": false, "number": 4783, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/tools/+/4783", "patchset_count": 1, "commit_id": "7bffcb3749a44e791be38f6e65ac5498201dafb1", "insertions": 12, "deletions": 0}, {"change_id": "Iea38cd2b54dc5d0412c854b87858903dbffe8d2e", "project": "app", "branch": "develop", "subject": "SERVICE[BUG-001]: no sedentary reminder pops up during sleep", "status": "MERGED", "created": 1750037035, "updated": 1750037739, "mergeable": false, "number": 4969, "owner": "hongxing", "url": "http://********:8081/c/app/+/4969", "patchset_count": 1, "commit_id": "b2d2b64fb23166a43ab7443bb600564d2c550736", "insertions": 15, "deletions": 1}, {"change_id": "Icd69d7f9609034bf729b964634577d192fec2fae", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 优化传感器连接(连接时关闭搜索减少延迟)", "status": "ABANDONED", "created": 1749902132, "updated": 1749903125, "mergeable": false, "number": 4965, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/4965", "patchset_count": 2, "commit_id": "29e306bb92007db02fd1f474768f741d0af63c8d", "insertions": 128, "deletions": 27}, {"change_id": "Ia170b64ddc4636a1abaf4ec3b565d7d453668ae2", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 解决camera回连慢问题", "status": "MERGED", "created": 1749902132, "updated": 1749902361, "mergeable": false, "number": 4964, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/4964", "patchset_count": 1, "commit_id": "6a604376cb1f37afe903948878c301d872dd99c6", "insertions": 13, "deletions": 2}, {"change_id": "I7dcb8132120c4f0aad5397ebd02702a51416eefe", "project": "qw_platform", "branch": "develop", "subject": "GUI: QwSwipePageBase去掉按键，触屏，tick ，assert修改", "status": "MERGED", "created": 1749893868, "updated": 1749894526, "mergeable": false, "number": 4957, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4957", "patchset_count": 1, "commit_id": "be27e0cdfa9ecf90e15521b84234acca4805446d", "insertions": 10, "deletions": 4}, {"change_id": "I8779d6801bc7b97ea7c2ce9e81b4eab2abb98a7b", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Feature]: 1.GPS设置页面AGPS有效日期显示;2.AGPS更新弹窗添加;3稍后继续场景增加 [Fix]: 1.优化tlap文件读取逻辑", "status": "MERGED", "created": 1749886989, "updated": 1749893795, "mergeable": false, "number": 4952, "owner": "wang<PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/4952", "patchset_count": 3, "commit_id": "5a952c3d478b2ff820dd972594eb52a6323cc719", "insertions": 12, "deletions": 4}, {"change_id": "Ib1ed261e9b23345d988b82a50295be1379060490", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]:1.优化tlap文件读取流程，添加互斥锁；2.优化计圈列表显示样式", "status": "MERGED", "created": 1749547520, "updated": 1749892567, "mergeable": false, "number": 4836, "owner": "wang<PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/4836", "patchset_count": 4, "commit_id": "82ff7b923d722430176998c8ed888ce08d8a84ed", "insertions": 891, "deletions": 362}, {"change_id": "I5dee3e7df91cd3b206fbe07f389cacdeb947e970", "project": "platform/external/proto", "branch": "master", "subject": "[LG09表情尾灯协议]: 新增表情尾灯协议文件，添加表情尾灯指令", "status": "MERGED", "created": 1749640873, "updated": 1749891627, "mergeable": false, "number": 4862, "owner": "wa<PERSON><PERSON>", "url": "http://********:8081/c/platform/external/proto/+/4862", "patchset_count": 1, "commit_id": "1b189e98ff01e7c81196ba13e0a798e96f41c695", "insertions": 166, "deletions": 3}, {"change_id": "If2b30d828b31567d176220b16b3ae953c5002716", "project": "tools", "branch": "develop", "subject": "tool: 减少默认ota项", "status": "MERGED", "created": 1749890840, "updated": 1749891547, "mergeable": false, "number": 4868, "owner": "jinx<PERSON><PERSON>", "url": "http://********:8081/c/tools/+/4868", "patchset_count": 1, "commit_id": "94fe2c630e7ec066e8ac86a610373e2251d884ac", "insertions": 22, "deletions": 10}, {"change_id": "Ideaa8420a9006a98df2bd9056f2d7aa78bba1e8b", "project": "sifli", "branch": "develop", "subject": "ota: ota模块提交", "status": "ABANDONED", "created": 1749890788, "updated": 1749891391, "mergeable": false, "number": 4867, "owner": "jinx<PERSON><PERSON>", "url": "http://********:8081/c/sifli/+/4867", "patchset_count": 1, "commit_id": "37b976bbc24cc69dae12b85cc7a5d6fbfec75614", "insertions": 114, "deletions": 48}, {"change_id": "Ideaa8420a9006a98df2bd9056f2d7aa78bba1e8b", "project": "qw_platform", "branch": "develop", "subject": "ota: ota模块提交", "status": "ABANDONED", "created": 1749890749, "updated": 1749891391, "mergeable": false, "number": 4865, "owner": "jinx<PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4865", "patchset_count": 1, "commit_id": "084f3519d7248480eec608ad853c6cd0cbbc729d", "insertions": 8685, "deletions": 574}, {"change_id": "Ideaa8420a9006a98df2bd9056f2d7aa78bba1e8b", "project": "app", "branch": "develop", "subject": "ota: ota模块提交", "status": "ABANDONED", "created": 1749890733, "updated": 1749891391, "mergeable": false, "number": 4863, "owner": "jinx<PERSON><PERSON>", "url": "http://********:8081/c/app/+/4863", "patchset_count": 1, "commit_id": "bfd6575bb7c8e2cdee555fab89713d1fedef7d35", "insertions": 896, "deletions": 322}, {"change_id": "I4d925ab1d4ff64e7ab3a5095882352e0dabfc9dd", "project": "qw_platform", "branch": "develop", "subject": "ota: 修复ota重启后进度异常的问题 将字库解析进度修改到ota模块内部", "status": "ABANDONED", "created": 1749890749, "updated": 1749891284, "mergeable": false, "number": 4866, "owner": "jinx<PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4866", "patchset_count": 1, "commit_id": "90394462a6d53704b79aec84ca6691a30ba6b233", "insertions": 40, "deletions": 350}, {"change_id": "I4d925ab1d4ff64e7ab3a5095882352e0dabfc9dd", "project": "app", "branch": "develop", "subject": "ota: 修复ota重启后进度异常的问题 将字库解析进度修改到ota模块内部", "status": "ABANDONED", "created": 1749890733, "updated": 1749891284, "mergeable": false, "number": 4864, "owner": "jinx<PERSON><PERSON>", "url": "http://********:8081/c/app/+/4864", "patchset_count": 1, "commit_id": "be082e16f4c38db592ac893ea13fb8643fbd35d3", "insertions": 98, "deletions": 9}, {"change_id": "I16aca2fce0b3d48817b5a539bf7c7253c04aa6f5", "project": "tools", "branch": "develop", "subject": "ota: 添加校验打包工具", "status": "ABANDONED", "created": 1749461595, "updated": 1749891175, "mergeable": false, "number": 4259, "owner": "jinx<PERSON><PERSON>", "url": "http://********:8081/c/tools/+/4259", "patchset_count": 2, "commit_id": "1b0a1ac558af7e196ad1fcb02f21171c21eec626", "insertions": 163, "deletions": 0}, {"change_id": "I7e7458b2a245e211c8be99711c3b068cbf27dd5a", "project": "qw_platform", "branch": "develop", "subject": "gui: 修复swipe控件导致的死机问题", "status": "MERGED", "created": 1749890026, "updated": 1749891041, "mergeable": false, "number": 4953, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4953", "patchset_count": 2, "commit_id": "56abd4d1438211f8bf20daaaf66afcfbc5f55eb2", "insertions": 70, "deletions": 91}, {"change_id": "Ic8a6a77b07090ae2fec4b602aaea33c93b197470", "project": "cycle/20250607_fc06", "branch": "develop", "subject": "[Feature]: 增加模拟器相关文件", "status": "MERGED", "created": 1749627743, "updated": 1749890894, "mergeable": false, "number": 4859, "owner": "hancheng", "url": "http://********:8081/c/cycle/20250607_fc06/+/4859", "patchset_count": 2, "commit_id": "2ff1d4ff928e2fbd106fb8422d6a16495960fc9d", "insertions": 205488, "deletions": 4}, {"change_id": "I45b78b0cdc6a19140f1561489939d43470a0f387", "project": "qw_platform", "branch": "develop", "subject": "GUI: 优化swipe控件更新流程，优化canvas逻辑，加快截图速度", "status": "MERGED", "created": 1749789970, "updated": 1749889915, "mergeable": false, "number": 4934, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4934", "patchset_count": 2, "commit_id": "8651eccc5aa5b05728b573192592ee957f977a87", "insertions": 65, "deletions": 8}, {"change_id": "Ia572907e2fbe39f895c61c1891c8d6726d4a7c8f", "project": "tools", "branch": "develop", "subject": "ota: 更新升级配置文件 添加生成MD5的脚本", "status": "MERGED", "created": 1749461595, "updated": 1749889775, "mergeable": false, "number": 4258, "owner": "jinx<PERSON><PERSON>", "url": "http://********:8081/c/tools/+/4258", "patchset_count": 2, "commit_id": "ca3e83ddc373c5cd5fac7797a19dc6e4aeaa85d6", "insertions": 168, "deletions": 36}, {"change_id": "I4c1bbad53f5951a57987acd5aa8f5b40156085fc", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Feature]: 1.完成计圈页列表滑动,实现滑动窗口翻页功能;2.新增计圈数据缓存文件控制逻辑，兼容稍后骑行", "status": "MERGED", "created": 1749476285, "updated": 1749886807, "mergeable": false, "number": 4820, "owner": "wang<PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/4820", "patchset_count": 3, "commit_id": "a6544bc26209fbfc7c5a84aa9323c7ba2e972ae4", "insertions": 772, "deletions": 213}, {"change_id": "I2968463c57a56d0b68139ccf9b07916f6aa07eff", "project": "app", "branch": "develop", "subject": "[GUI]: 下发训练课程跳转到运动列表。", "status": "MERGED", "created": 1749810523, "updated": 1749880301, "mergeable": false, "number": 4945, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4945", "patchset_count": 3, "commit_id": "2ee2069ddba44721bb8b5729a81032dbea6e4d9c", "insertions": 47, "deletions": 12}, {"change_id": "I2968463c57a56d0b68139ccf9b07916f6aa07eff", "project": "qw_platform", "branch": "develop", "subject": "[GUI]: 下发训练课程跳转到运动列表。", "status": "MERGED", "created": 1749810531, "updated": 1749880300, "mergeable": false, "number": 4946, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4946", "patchset_count": 3, "commit_id": "bcc0586d39ff3c28b0e991c5fde32c3428332f9a", "insertions": 58, "deletions": 15}, {"change_id": "I6c4d743caa0444da983762bfb69bbd1326a8aa6e", "project": "qw_platform", "branch": "develop", "subject": "key: press powerkey more than 3 seconds reboot if crash happened", "status": "MERGED", "created": 1749870215, "updated": 1749870989, "mergeable": false, "number": 4951, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/4951", "patchset_count": 1, "commit_id": "1bb0e3c0733f00e1101630c0b43eef5451d0d716", "insertions": 15, "deletions": 3}, {"change_id": "Ia8791c8168a993696b6fe49b65f6fa00e2b411a4", "project": "cycle/sifli", "branch": "bg2_develop", "subject": "[fix]: 新增调整scan窗口接口", "status": "MERGED", "created": 1749820714, "updated": 1749824271, "mergeable": false, "number": 4950, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/sifli/+/4950", "patchset_count": 1, "commit_id": "606789316adc4ebfeeb65aab347c794414d6876d", "insertions": 45, "deletions": 0}, {"change_id": "I384d5d52b77b7c25e6e5b36b9c14aa93ee395491", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Optimize]: 更新翻译", "status": "MERGED", "created": 1749806571, "updated": 1749817987, "mergeable": false, "number": 4943, "owner": "liquan", "url": "http://********:8081/c/cycle/app/+/4943", "patchset_count": 4, "commit_id": "7bc98772d406108d6848519c638f13542b54993f", "insertions": 368, "deletions": 210}, {"change_id": "I38188123b38b19333a3608866e8db930118629be", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Optimize]: 优化历史记录页面显示", "status": "MERGED", "created": 1749806571, "updated": 1749817986, "mergeable": false, "number": 4942, "owner": "liquan", "url": "http://********:8081/c/cycle/app/+/4942", "patchset_count": 5, "commit_id": "4bf5895da7c7836ab5bc6d35e808b422589d76d8", "insertions": 30, "deletions": 8}, {"change_id": "Iff47c1532cd0393e973985ca42d0b6d6b55ab9f8", "project": "qw_algo/navigation", "branch": "bg2_develop", "subject": "[Fix]: 1. 解决短距离路书回程导航数据异常的问题", "status": "MERGED", "created": 1749817374, "updated": 1749817961, "mergeable": false, "number": 4949, "owner": "liquan", "url": "http://********:8081/c/qw_algo/navigation/+/4949", "patchset_count": 1, "commit_id": "cb21220aeca26897691d0cf42ed65de279cdcb6a", "insertions": 15, "deletions": 0}, {"change_id": "Iecfb925eea88acac3d3a9c9b6ffe66df9127979a", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 解决保存活动地图页仍存在导航结束弹窗问题", "status": "MERGED", "created": 1749795208, "updated": 1749812956, "mergeable": false, "number": 4935, "owner": "liquan", "url": "http://********:8081/c/cycle/app/+/4935", "patchset_count": 2, "commit_id": "a14848f0e281908bb9bb887a1c97f9d335ef2833", "insertions": 14, "deletions": 0}, {"change_id": "Id7a4ce9d46f75df72536bd8c07bd1d7c36f7e4fa", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 解决DI2控制车灯异常问题", "status": "MERGED", "created": 1749785426, "updated": 1749812946, "mergeable": false, "number": 4931, "owner": "liquan", "url": "http://********:8081/c/cycle/app/+/4931", "patchset_count": 2, "commit_id": "340da40fc3470aff4dc6db27c7049f750e5e69f6", "insertions": 40, "deletions": 12}, {"change_id": "I751e10717f1790f3989d4e25b075942314fa93d0", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 优化代码逻辑", "status": "MERGED", "created": 1749785426, "updated": 1749812926, "mergeable": false, "number": 4930, "owner": "liquan", "url": "http://********:8081/c/cycle/app/+/4930", "patchset_count": 2, "commit_id": "d26c6fa26fc4d6458b0374ae9e89e8e390309d21", "insertions": 25, "deletions": 18}, {"change_id": "Ie9e37230a302d74fe8743419f9a045c22217549f", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Feature]: 新增APP传感器页面支持连接车灯、相机", "status": "MERGED", "created": 1749645317, "updated": 1749812906, "mergeable": false, "number": 4897, "owner": "liquan", "url": "http://********:8081/c/cycle/app/+/4897", "patchset_count": 6, "commit_id": "be59e80022189e7fd5c80825bf3eb58caf917e77", "insertions": 325, "deletions": 165}, {"change_id": "Id8dd550c4d287727faafb33579d9e7d045e67b96", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 地图触控模式适配雷达条, 爬坡开始弹窗可打断", "status": "MERGED", "created": 1749645317, "updated": 1749812871, "mergeable": false, "number": 4896, "owner": "liquan", "url": "http://********:8081/c/cycle/app/+/4896", "patchset_count": 6, "commit_id": "3bc3407d75d8c9e813ec9ceb5e9b1dec59e6ad3f", "insertions": 88, "deletions": 42}, {"change_id": "Ic2e2ba02861be52f47271b162baaa18071b990bb", "project": "tools", "branch": "wr02_algosim", "subject": "alg_fmk:跑步工具优化 1、增加数据项 2、计算MAE和MAPE 3、输出测试需要的表格 4、集成仿真和提供gm数据预处理", "status": "MERGED", "created": 1749808016, "updated": 1749812051, "mergeable": false, "number": 4782, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/tools/+/4782", "patchset_count": 1, "commit_id": "d059ddb027931db842af74ee98a2c46c4faa0dc6", "insertions": 784, "deletions": 95}, {"change_id": "I1331505c2984d89c3ef382f62b8ba4a938072ffb", "project": "app", "branch": "develop", "subject": "[GUI]:训练课程和导航列表调整。", "status": "MERGED", "created": 1749799038, "updated": 1749808681, "mergeable": false, "number": 4939, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4939", "patchset_count": 3, "commit_id": "1a670875f2957df67ee29bd9aebcebd098402c86", "insertions": 90, "deletions": 66}, {"change_id": "If5d9f8e066ffde5bd1e50780d2d35df174af2a26", "project": "qw_platform", "branch": "develop", "subject": "ui:菜单列表类增加设置标题超长处理方法", "status": "MERGED", "created": 1749797614, "updated": 1749808671, "mergeable": false, "number": 4937, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4937", "patchset_count": 1, "commit_id": "395ea836eeecaa46c0d3d4aa8f85363543d8e4a0", "insertions": 49, "deletions": 18}, {"change_id": "If5d9f8e066ffde5bd1e50780d2d35df174af2a26", "project": "app", "branch": "develop", "subject": "ui:菜单列表类增加设置标题超长处理方法", "status": "MERGED", "created": 1749797544, "updated": 1749808671, "mergeable": false, "number": 4936, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4936", "patchset_count": 2, "commit_id": "4034954072ebe63ac50568869bff1624fde7838c", "insertions": 11, "deletions": 1}, {"change_id": "I79c8e3d6c73d743e40e901437f00d8bcedce23e4", "project": "cycle/20250607_fc06", "branch": "develop", "subject": "Merge branch 'develop' of ssh://********:29418/cycle/20250607_fc06 into develop", "status": "ABANDONED", "created": 1749800009, "updated": 1749808372, "mergeable": false, "number": 4941, "owner": "<PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/20250607_fc06/+/4941", "patchset_count": 1, "commit_id": "fe83ce61561e17a69ebecbaed910c3015c8311b8", "insertions": 16, "deletions": 0}, {"change_id": "Ic2e2ba02861be52f47271b162baaa18071b990bb", "project": "tools", "branch": "develop", "subject": "alg_fmk:跑步工具优化 1、增加数据项 2、计算MAE和MAPE 3、输出测试需要的表格 4、集成仿真和提供gm数据预处理", "status": "MERGED", "created": 1749560604, "updated": 1749802210, "mergeable": false, "number": 4841, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/tools/+/4841", "patchset_count": 5, "commit_id": "db496f05d50bfbd78dae74f119032568d653858f", "insertions": 783, "deletions": 95}, {"change_id": "I20e1030e3044dfe77ab56c0c5d143730e62f52d3", "project": "cycle/20250607_fc06", "branch": "develop", "subject": "【新增功能】1.新增platform_dev驱动 2.fs接入(片上flash实现) 3.蓝牙基础服务添加", "status": "MERGED", "created": 1749800009, "updated": 1749801880, "mergeable": false, "number": 4940, "owner": "<PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/20250607_fc06/+/4940", "patchset_count": 2, "commit_id": "ec6597631b2e2fa9daa64cd2f2e9bff4193fd78a", "insertions": 69919, "deletions": 64129}, {"change_id": "Ifc6f7cef0768a37a865c7a62676968d6656a957c", "project": "app", "branch": "develop", "subject": "app:修复菜单列表副标题显示问题、加入全语言宏", "status": "MERGED", "created": 1749785393, "updated": 1749795561, "mergeable": false, "number": 4929, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4929", "patchset_count": 2, "commit_id": "cd6b4fa3e8372feba497d00729e000526defe1f0", "insertions": 109, "deletions": 73}, {"change_id": "Ifc6f7cef0768a37a865c7a62676968d6656a957c", "project": "qw_platform", "branch": "develop", "subject": "app:修复菜单列表副标题显示问题、加入全语言宏", "status": "MERGED", "created": 1749785332, "updated": 1749795561, "mergeable": false, "number": 4928, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4928", "patchset_count": 2, "commit_id": "4f30a7b1e42bf2cc861e37b3bde349a208df249e", "insertions": 89, "deletions": 32}, {"change_id": "I722cf9c3ff9b69062d43a8c87427122998eded47", "project": "app", "branch": "develop", "subject": "[GUI]: 更新个人资料翻译KEY", "status": "MERGED", "created": 1749778644, "updated": 1749795560, "mergeable": false, "number": 4925, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4925", "patchset_count": 2, "commit_id": "59bafcb07d780fee72cb00da4c00e41df80f48ff", "insertions": 11, "deletions": 1}, {"change_id": "I361a4d91b1e7c549c9b539982a23d026fb7b542e", "project": "sifli", "branch": "develop", "subject": "PM: disable lc<PERSON> enter sleep.", "status": "ABANDONED", "created": 1749455607, "updated": 1749792769, "mergeable": false, "number": 4807, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/4807", "patchset_count": 4, "commit_id": "9943ab5544834f76fcd2528c82df4bffda05b66c", "insertions": 20, "deletions": 8}, {"change_id": "I4812bc073fe542756364ee1165d353a9ac2d794d", "project": "app", "branch": "wr02_release", "subject": "sport: use common time ts diff interface", "status": "ABANDONED", "created": 1746586314, "updated": 1749792755, "mergeable": false, "number": 3874, "owner": "linjizhao", "url": "http://********:8081/c/app/+/3874", "patchset_count": 1, "commit_id": "cc80d1856634178d41aad757584445d93eb41241", "insertions": 94, "deletions": 30}, {"change_id": "I7911b32cbd3545e4ad37ab17a5c5a8ab4c5bd85e", "project": "qw_platform", "branch": "develop", "subject": "[翻译]:加入全语言启动宏", "status": "ABANDONED", "created": 1749731582, "updated": 1749784700, "mergeable": false, "number": 4921, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4921", "patchset_count": 2, "commit_id": "9b8333e98c30a0310baf4235a4a30931cd04d8c6", "insertions": 16498, "deletions": 16472}, {"change_id": "I7911b32cbd3545e4ad37ab17a5c5a8ab4c5bd85e", "project": "app", "branch": "develop", "subject": "[翻译]:加入全语言启动宏", "status": "ABANDONED", "created": 1749731654, "updated": 1749784694, "mergeable": false, "number": 4922, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4922", "patchset_count": 2, "commit_id": "d1a0db4a13fe40ef8a44eabb6c12ae7027f312ff", "insertions": 107, "deletions": 72}, {"change_id": "I40278dc0191a5378bec2387933626ec75eb885a7", "project": "app", "branch": "develop", "subject": "SERVICE[BUG-001]: adjust the priority of inter-core communication and add log triggering when an exception occurs to track related issues", "status": "MERGED", "created": 1749780015, "updated": 1749781044, "mergeable": false, "number": 4927, "owner": "hongxing", "url": "http://********:8081/c/app/+/4927", "patchset_count": 2, "commit_id": "7c4ecd7ac46482b4fb15fa9326a41520507e7139", "insertions": 49, "deletions": 28}, {"change_id": "I07931fa423b77ff1032cc2cbc71fd815de9cbb43", "project": "cycle/c202310_bg1", "branch": "Develop", "subject": "[Fix]: 解决历史记录摘要轨迹显示异常，离线规划不生效问题", "status": "MERGED", "created": 1749737351, "updated": 1749780547, "mergeable": false, "number": 4924, "owner": "liquan", "url": "http://********:8081/c/cycle/c202310_bg1/+/4924", "patchset_count": 3, "commit_id": "ca26a0471a47ee7657b6d7f62df97d1130a45461", "insertions": 44, "deletions": 23}, {"change_id": "If515a341f7221ade06011cc4e3ff254105acaf6f", "project": "cycle/c202310_bg1", "branch": "Develop", "subject": "[Fix]: 1、解决状态栏显示定位状态，地图页显示未定位问题 2、解决左滑删除异常问题", "status": "MERGED", "created": 1749645605, "updated": 1749780545, "mergeable": false, "number": 4898, "owner": "liquan", "url": "http://********:8081/c/cycle/c202310_bg1/+/4898", "patchset_count": 2, "commit_id": "c4c61f48feafa5f997af760e1e3e26e7289f046d", "insertions": 21, "deletions": 10}, {"change_id": "I8ddddaa34de485e10f44988afd84d9562e4b62d4", "project": "cycle/c202310_bg1", "branch": "Develop", "subject": "[Fix]: 解决APP下发名称相同路书，路书信息显示问题", "status": "MERGED", "created": 1749627961, "updated": 1749780543, "mergeable": false, "number": 4882, "owner": "liquan", "url": "http://********:8081/c/cycle/c202310_bg1/+/4882", "patchset_count": 2, "commit_id": "10975f6b0b1166c62dc9242f12c501c04eb85370", "insertions": 100, "deletions": 64}, {"change_id": "Ic4b4df0faf9bd536babf28cb0a2d264369995b31", "project": "cycle/c202310_bg1", "branch": "Develop", "subject": "[Optimize]: 更新翻译", "status": "MERGED", "created": 1749627961, "updated": 1749780540, "mergeable": false, "number": 4881, "owner": "liquan", "url": "http://********:8081/c/cycle/c202310_bg1/+/4881", "patchset_count": 1, "commit_id": "6ce1c43ac82fb9d48e08072d22af58133a330c92", "insertions": 187, "deletions": 283}, {"change_id": "I5a4a088d5fdc29f4cc58bbb332265aa44fe41ff5", "project": "cycle/c202310_bg1", "branch": "Develop", "subject": "[Optimize]: 优化地图页转向弹窗，爬坡进度弹窗", "status": "MERGED", "created": 1749627961, "updated": 1749780537, "mergeable": false, "number": 4860, "owner": "liquan", "url": "http://********:8081/c/cycle/c202310_bg1/+/4860", "patchset_count": 1, "commit_id": "b650f54f12443cb2ded88bb8b2b9e69c93fb52a6", "insertions": 154, "deletions": 52}, {"change_id": "I010e468b60739bafd4f38b00e897cc7fc6148000", "project": "sifli", "branch": "develop", "subject": "sifli : 修复误修改导致马达异常：开机和进入休眠后不振", "status": "MERGED", "created": 1749732340, "updated": 1749778519, "mergeable": false, "number": 4923, "owner": "gaoxing", "url": "http://********:8081/c/sifli/+/4923", "patchset_count": 9, "commit_id": "45633b98fdb9c065c6d04ec4b8eb4e79ffc76802", "insertions": 21, "deletions": 5}, {"change_id": "I44a49e1d0d19a7762f1ca4d6c977218299bdcfe7", "project": "qw_platform", "branch": "develop", "subject": "bug:导航返回提醒去掉标题", "status": "MERGED", "created": 1749719351, "updated": 1749776749, "mergeable": false, "number": 4916, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4916", "patchset_count": 3, "commit_id": "0e2bf922c4dbd67734bc03076b19229fe1c30867", "insertions": 11, "deletions": 1}, {"change_id": "Ic270967d727923313e7678bc4730e3e45d9ec404", "project": "qw_platform", "branch": "develop", "subject": "[GUI]: 解决禅道bug", "status": "MERGED", "created": 1749731154, "updated": 1749776734, "mergeable": false, "number": 4920, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4920", "patchset_count": 1, "commit_id": "1d77a77a6dbae1db315ee1e2e777f85b36897cb2", "insertions": 65, "deletions": 32}, {"change_id": "Ic270967d727923313e7678bc4730e3e45d9ec404", "project": "app", "branch": "develop", "subject": "[GUI]: 解决禅道bug", "status": "MERGED", "created": 1749726201, "updated": 1749776733, "mergeable": false, "number": 4917, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4917", "patchset_count": 4, "commit_id": "9fec430c9ccbfadca7638cebfa960c636f4708ef", "insertions": 71, "deletions": 17}, {"change_id": "I9996075a89ba65cbe403c1c10bd1631eebb357ce", "project": "app", "branch": "develop", "subject": "app:1、运动中且AOD时支持响应按键，2、修改bug及优化", "status": "MERGED", "created": 1749717245, "updated": 1749776719, "mergeable": false, "number": 4913, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4913", "patchset_count": 5, "commit_id": "64e5f4991ade1c3372c6a3b3fc198b404f9aa72e", "insertions": 105, "deletions": 52}, {"change_id": "Ia43f9491cddf1249db7697ddb1d3406d00353846", "project": "qw_platform", "branch": "develop", "subject": "offbody: force on body for debug", "status": "NEW", "created": 1749456018, "updated": 1749769322, "mergeable": false, "number": 4808, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/4808", "patchset_count": 2, "commit_id": "b69ca7eaf37c1e8ee3351d21900f145430255aea", "insertions": 13, "deletions": 1}, {"change_id": "I867dd10f7634f6687ac1e5d07e02d5382c364e65", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 修复骑行状态页跳转其他页面返回后雷达条显示问题", "status": "MERGED", "created": 1749730191, "updated": 1749734851, "mergeable": false, "number": 4919, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/4919", "patchset_count": 2, "commit_id": "71fa256cd6b88e846f3f4dc2cd74f7382c3c1d10", "insertions": 23, "deletions": 2}, {"change_id": "If4b133babb9214625789861c19cba0a471834ad4", "project": "sifli", "branch": "develop", "subject": "[wdt]: 修复A3板WDT超时问题", "status": "MERGED", "created": 1749728935, "updated": 1749733626, "mergeable": false, "number": 4918, "owner": "lixiaolong", "url": "http://********:8081/c/sifli/+/4918", "patchset_count": 2, "commit_id": "b2daa1ebf9c0f2534197cc4953dd7d86f0e91cce", "insertions": 68, "deletions": 19}, {"change_id": "I82094877f2fb1ca3a442a8c4668ba7c159203bff", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Optimize]: 1、新增APP辅助偏航规划及离线规划交互功能逻辑 2、优化转向弹窗显示", "status": "MERGED", "created": 1749645317, "updated": 1749722689, "mergeable": false, "number": 4894, "owner": "liquan", "url": "http://********:8081/c/cycle/app/+/4894", "patchset_count": 6, "commit_id": "289259b183b61670c29bb1609872c3651b7b1175", "insertions": 765, "deletions": 162}, {"change_id": "Ibbc27073ef1094fd992e8511589b070875e97578", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Feature]: 新增APP辅助偏航规划及离线规划功能逻辑", "status": "ABANDONED", "created": 1749645317, "updated": 1749720744, "mergeable": false, "number": 4895, "owner": "liquan", "url": "http://********:8081/c/cycle/app/+/4895", "patchset_count": 4, "commit_id": "0b01684415599a790c5499ac1e9b2a7e4ff46863", "insertions": 671, "deletions": 109}, {"change_id": "I708f2d8e91e057a8f4fed79e7e382abdead74430", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Feature]: 新增离线规划功能设置界面及交互逻辑", "status": "MERGED", "created": 1749645317, "updated": 1749720443, "mergeable": false, "number": 4893, "owner": "liquan", "url": "http://********:8081/c/cycle/app/+/4893", "patchset_count": 5, "commit_id": "d61d0f01fba0f84f8c6c884eaff01f8761e99d81", "insertions": 1043, "deletions": 27}, {"change_id": "If10ea9a554058a6126c81a9b07d60aa193de9475", "project": "app", "branch": "develop", "subject": "lv_draw_sw_polygon: lvgl库代码保持原状", "status": "MERGED", "created": 1749717553, "updated": 1749719882, "mergeable": false, "number": 4915, "owner": "linjizhao", "url": "http://********:8081/c/app/+/4915", "patchset_count": 2, "commit_id": "2c422b6726871bd9fbca37414ed6059cc703310c", "insertions": 16, "deletions": 10}, {"change_id": "If10ea9a554058a6126c81a9b07d60aa193de9475", "project": "qw_platform", "branch": "develop", "subject": "lv_draw_sw_polygon: lvgl库代码保持原状", "status": "MERGED", "created": 1749717352, "updated": 1749719882, "mergeable": false, "number": 4914, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/4914", "patchset_count": 1, "commit_id": "0e9570b1b1a7cfa59cd7a27dec891444ed14ec75", "insertions": 16, "deletions": 10}, {"change_id": "I3797e733ea1109483212a85a5b885be3a2343054", "project": "app", "branch": "develop", "subject": "[gps]: GPS唤醒源用时开启，不用关闭", "status": "MERGED", "created": 1749712423, "updated": 1749718984, "mergeable": false, "number": 4911, "owner": "lixiaolong", "url": "http://********:8081/c/app/+/4911", "patchset_count": 2, "commit_id": "48f910e3a9d8470142d38976729aa0224d1e7407", "insertions": 16, "deletions": 0}, {"change_id": "I3797e733ea1109483212a85a5b885be3a2343054", "project": "sifli", "branch": "develop", "subject": "[pm]: 重新封装唤醒源设置接口", "status": "MERGED", "created": 1749712415, "updated": 1749718984, "mergeable": false, "number": 4910, "owner": "lixiaolong", "url": "http://********:8081/c/sifli/+/4910", "patchset_count": 4, "commit_id": "6dfe4311710c415057e5abe90df6567889fdc935", "insertions": 112, "deletions": 52}, {"change_id": "Ifd9561af0809c59cf148593fdca02ef93c28c5ac", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Feature]: 新增Di2控制车灯功能", "status": "MERGED", "created": 1749645317, "updated": 1749718807, "mergeable": false, "number": 4891, "owner": "liquan", "url": "http://********:8081/c/cycle/app/+/4891", "patchset_count": 3, "commit_id": "9fd3d7c580a9991b9628271f677f12474123c8ec", "insertions": 634, "deletions": 53}, {"change_id": "Iec594772708f1e86f6f81ecbf1839685a61a6ad9", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Feature]: 新增APP辅助偏航规划协议", "status": "MERGED", "created": 1749645317, "updated": 1749718740, "mergeable": false, "number": 4892, "owner": "liquan", "url": "http://********:8081/c/cycle/app/+/4892", "patchset_count": 4, "commit_id": "ee1d4c0267dce1c2eeaa99c5b6f94c99e6011a6a", "insertions": 390, "deletions": 50}, {"change_id": "Idf6aee47062ce8e846a6c5d2263ddb6743fe63ca", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Optimize]: 1. 优化总升总降计算，和坡度解耦；", "status": "MERGED", "created": 1749641202, "updated": 1749718739, "mergeable": false, "number": 4890, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/4890", "patchset_count": 3, "commit_id": "08642dbb7547e11a2eed6c60663ce25e190a0461", "insertions": 313, "deletions": 134}, {"change_id": "Id972b8fee0acf3aed6816d98aca4203f2ab5d846", "project": "sifli", "branch": "develop", "subject": "i2c3: add i2c3 devices common interface", "status": "MERGED", "created": 1749692527, "updated": 1749717167, "mergeable": false, "number": 4901, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/4901", "patchset_count": 7, "commit_id": "d827479ee1953ed714c60fb43755fa08a2d11950", "insertions": 83, "deletions": 15}, {"change_id": "Id972b8fee0acf3aed6816d98aca4203f2ab5d846", "project": "qw_platform", "branch": "develop", "subject": "poweron_sync: set power on sync timeout to 30S", "status": "MERGED", "created": 1749716162, "updated": 1749717166, "mergeable": false, "number": 4912, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/4912", "patchset_count": 1, "commit_id": "11defa29ec1902f6f02a6ff2db2e921b89990e87", "insertions": 16, "deletions": 5}, {"change_id": "Id972b8fee0acf3aed6816d98aca4203f2ab5d846", "project": "app", "branch": "develop", "subject": "app : 添加引用路径", "status": "MERGED", "created": 1749701745, "updated": 1749717166, "mergeable": false, "number": 4907, "owner": "gaoxing", "url": "http://********:8081/c/app/+/4907", "patchset_count": 2, "commit_id": "14cff80ace68e823c6243af03457ac60479d5b86", "insertions": 13, "deletions": 0}, {"change_id": "I0651fc7b02a044183b58745b0de91d76d33a8db1", "project": "app", "branch": "develop", "subject": "[activity]: 调整fit保存时卡在动画的问题", "status": "MERGED", "created": 1749702180, "updated": 1749711765, "mergeable": false, "number": 4909, "owner": "lixin", "url": "http://********:8081/c/app/+/4909", "patchset_count": 1, "commit_id": "8c47c011f4e455ca99a51990c51884f2278ff4c7", "insertions": 25, "deletions": 7}, {"change_id": "I0c78426eea142458553722e28010bfab5a4abf3e", "project": "qw_platform", "branch": "develop", "subject": "ppg: keep ppg fifo irq as 200ms interval", "status": "MERGED", "created": 1749702132, "updated": 1749703024, "mergeable": false, "number": 4908, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/4908", "patchset_count": 1, "commit_id": "2371274eb7e28adacf3ab9a9c3f89b4655ad366f", "insertions": 111, "deletions": 34}, {"change_id": "I91095d0b6563c8a58ea482b1f43f8a7ea0ba3d67", "project": "app", "branch": "develop", "subject": "[GUI]: 修改默认应用列表,运动列表排序", "status": "MERGED", "created": 1749699603, "updated": 1749702009, "mergeable": false, "number": 4906, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4906", "patchset_count": 1, "commit_id": "e83d7e8df6abb3455eb57325821f4832e73a0475", "insertions": 44, "deletions": 34}, {"change_id": "Ifab700b74cf4d15cb2baee51ebafa866b200d959", "project": "app", "branch": "develop", "subject": "[GUI]: 支持单独下发路书不启用。修改测试代码中文字符。", "status": "MERGED", "created": 1749695681, "updated": 1749698466, "mergeable": false, "number": 4903, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4903", "patchset_count": 2, "commit_id": "7498d5f2f6b22e7b05209c15ab7133793596a838", "insertions": 48, "deletions": 11}, {"change_id": "If41dd190337826ee44b6bf496cef6c2d5e59e800", "project": "app", "branch": "develop", "subject": "GUI: 优化睡眠颜色值", "status": "MERGED", "created": 1749697455, "updated": 1749698208, "mergeable": false, "number": 4905, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4905", "patchset_count": 2, "commit_id": "c1fe9e9c12b9fa060aeab738833be222f253c5c2", "insertions": 14, "deletions": 4}, {"change_id": "I44a34e520664b32a5b19ee8d743a672454175faf", "project": "app", "branch": "develop", "subject": "GUI: 优化心率血氧压力hrv七天数据图表显示问题，中间没数据则断开", "status": "MERGED", "created": 1749696690, "updated": 1749698206, "mergeable": false, "number": 4904, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4904", "patchset_count": 2, "commit_id": "d1a79588227e048e60669bec284e188190868e54", "insertions": 22, "deletions": 20}, {"change_id": "Ibdee1beaf01df668c0d936d2d9c3b9415eaf7b52", "project": "app", "branch": "develop", "subject": "GUI: 修复卡路里步数上限不正确问题", "status": "MERGED", "created": 1749695058, "updated": 1749698205, "mergeable": false, "number": 4902, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4902", "patchset_count": 2, "commit_id": "38c0c75bd293c36a800a987801e9b3a74b4a1852", "insertions": 77, "deletions": 12}, {"change_id": "I97cbdbf1469f7275a38e620694408d578bbd679d", "project": "qw_platform", "branch": "develop", "subject": "app:更换睡眠提醒图片、支持路线列表刷新", "status": "MERGED", "created": 1749639320, "updated": 1749693324, "mergeable": false, "number": 4889, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4889", "patchset_count": 2, "commit_id": "fe71ec92decab203405d1a7850b6923abd6c2cd4", "insertions": 19, "deletions": 5}, {"change_id": "I97cbdbf1469f7275a38e620694408d578bbd679d", "project": "app", "branch": "develop", "subject": "app:更换睡眠提醒图片、支持路线列表刷新", "status": "MERGED", "created": 1749639278, "updated": 1749693324, "mergeable": false, "number": 4888, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4888", "patchset_count": 2, "commit_id": "2b13b5f2453058fe43b9a487deea88aa4c2dc5e5", "insertions": 946, "deletions": 26}, {"change_id": "Iaa1e6f3b42976e564bc813a5ecd3861ac701bd7b", "project": "app", "branch": "develop", "subject": "sleep: disable lc<PERSON> enter sleep", "status": "MERGED", "created": 1749650837, "updated": 1749692113, "mergeable": false, "number": 4900, "owner": "linjizhao", "url": "http://********:8081/c/app/+/4900", "patchset_count": 2, "commit_id": "835c5e009339f18197d4c5baf66f528c2c26b25d", "insertions": 11, "deletions": 1}, {"change_id": "I7d645e7e43955fbfee588b9f7433b782ed7bb814", "project": "qw_platform", "branch": "develop", "subject": "GUI: QwVerticalSwipeContainer控件新增动态销毁，生成功能", "status": "MERGED", "created": 1749633381, "updated": 1749650857, "mergeable": false, "number": 4884, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4884", "patchset_count": 3, "commit_id": "77b0800070d50b218f0a5ba927fc744a9ff85e15", "insertions": 489, "deletions": 8}, {"change_id": "I7d645e7e43955fbfee588b9f7433b782ed7bb814", "project": "app", "branch": "develop", "subject": "GUI: 运动结算页适配新swip控件", "status": "MERGED", "created": 1749633761, "updated": 1749650856, "mergeable": false, "number": 4885, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4885", "patchset_count": 2, "commit_id": "86438f0902b631404ee6e274ddc3909c39e319e4", "insertions": 56, "deletions": 10}, {"change_id": "I8038bc18805641946ff6e62ff96530666e892e5b", "project": "qw_platform", "branch": "develop", "subject": "[font]: 规范全局字体使用", "status": "MERGED", "created": 1749637321, "updated": 1749650267, "mergeable": false, "number": 4886, "owner": "lixin", "url": "http://********:8081/c/qw_platform/+/4886", "patchset_count": 1, "commit_id": "df62557a6869052aa9e548fe8f7c474f4e0cb833", "insertions": 145, "deletions": 137}, {"change_id": "I8038bc18805641946ff6e62ff96530666e892e5b", "project": "app", "branch": "develop", "subject": "[font]: 规范全局字体使用", "status": "MERGED", "created": 1749637355, "updated": 1749650266, "mergeable": false, "number": 4887, "owner": "lixin", "url": "http://********:8081/c/app/+/4887", "patchset_count": 1, "commit_id": "a94f1b0821e852529a04b7663e3e96fc9749e2cd", "insertions": 2202, "deletions": 2105}, {"change_id": "I0afd50dd79637f00abcfc1f50c0bc1833dff98e3", "project": "qw_platform", "branch": "develop", "subject": "[pcsim]: 解决PC模拟器编译问题", "status": "MERGED", "created": 1749623753, "updated": 1749634265, "mergeable": false, "number": 4858, "owner": "lixin", "url": "http://********:8081/c/qw_platform/+/4858", "patchset_count": 2, "commit_id": "240903abf0ea20f3ec9c58c3e7c538a1ab72379e", "insertions": 18, "deletions": 0}, {"change_id": "Iba6094e01655cc42d13dbc9cc8745dae598dd8f1", "project": "cycle/20250607_fc06", "branch": "develop", "subject": "[Feature]: 增加IGS_DEV宏定义", "status": "ABANDONED", "created": 1749608962, "updated": 1749627775, "mergeable": false, "number": 4848, "owner": "hancheng", "url": "http://********:8081/c/cycle/20250607_fc06/+/4848", "patchset_count": 1, "commit_id": "3dda497bca81276c04f35efdc356eba7249d7903", "insertions": 12, "deletions": 1}, {"change_id": "Id1232212e987679754a9c8a758ad703afcbe2212", "project": "app", "branch": "develop", "subject": "[GUI]: 历史记录改为可循环滚动，删除记录调整到最后面。", "status": "MERGED", "created": 1749622572, "updated": 1749624114, "mergeable": false, "number": 4857, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4857", "patchset_count": 2, "commit_id": "54a74ab139b9eff4ec9a6cc54a9c373bfe278378", "insertions": 21, "deletions": 7}, {"change_id": "I2c541defc1e5f24f5bc7785796ddaafec3be37ae", "project": "qw_platform", "branch": "develop", "subject": "app:去除网格页、输入器数据单位，运动暂停支持导航", "status": "MERGED", "created": 1749606324, "updated": 1749623985, "mergeable": false, "number": 4846, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4846", "patchset_count": 2, "commit_id": "0897bd8fd2d194dd74c577eea12712a21513ac00", "insertions": 77, "deletions": 59}, {"change_id": "I2c541defc1e5f24f5bc7785796ddaafec3be37ae", "project": "app", "branch": "develop", "subject": "app:去除网格页、输入器数据单位，运动暂停支持导航", "status": "MERGED", "created": 1749606262, "updated": 1749623985, "mergeable": false, "number": 4845, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4845", "patchset_count": 2, "commit_id": "6c2016f7406c3f5f12522714d052cc9fe9fb200d", "insertions": 316, "deletions": 338}, {"change_id": "Icb6c50de565dba4d7e298602d87e45a2ea9e528e", "project": "qw_platform", "branch": "develop", "subject": "[GUI]: 更新恢复出厂翻译", "status": "MERGED", "created": 1749603817, "updated": 1749623929, "mergeable": false, "number": 4844, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4844", "patchset_count": 2, "commit_id": "86decee29fa4149d45202178fc02d4ac406ab760", "insertions": 11, "deletions": 1}, {"change_id": "Id5e31914c79b0b52412e8978562a2b3189b0ffaa", "project": "qw_platform", "branch": "develop", "subject": "[charge]: 关机前使能charger插入中断，用于唤醒设备开机", "status": "MERGED", "created": 1749613026, "updated": 1749622714, "mergeable": false, "number": 4851, "owner": "lixiaolong", "url": "http://********:8081/c/qw_platform/+/4851", "patchset_count": 2, "commit_id": "67d06128883f6289e5dd5de7dfe0b2aad4faa05d", "insertions": 39, "deletions": 0}, {"change_id": "I19c03d373f7f316abad74b2e0b74700c5c764ee2", "project": "qw_platform", "branch": "develop", "subject": "ppg: disable spo2 algo", "status": "MERGED", "created": 1749617592, "updated": 1749621104, "mergeable": false, "number": 4852, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/4852", "patchset_count": 1, "commit_id": "a0bb2e6a75867490850964ce65a91028d6f03ea8", "insertions": 1070, "deletions": 3}, {"change_id": "I7f4a22e15b51ca69b1e66f46594f331f954352f5", "project": "tools", "branch": "develop", "subject": "capture_tools: add ppg_parse_tools", "status": "MERGED", "created": 1749620691, "updated": 1749621094, "mergeable": false, "number": 4853, "owner": "linjizhao", "url": "http://********:8081/c/tools/+/4853", "patchset_count": 2, "commit_id": "7ca4c17d452280a6fafb956f111c3b7f6d4448b1", "insertions": 264296, "deletions": 0}, {"change_id": "I0d7de5aad2eab2289407e3eb9efcd986d6f3e791", "project": "sifli", "branch": "develop", "subject": "gpio2_irq: add total gpio2 irq abonmal monitor", "status": "MERGED", "created": 1749609874, "updated": 1749610428, "mergeable": false, "number": 4850, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/4850", "patchset_count": 3, "commit_id": "32d1a49e95c7f4a65e3e65ccb8d6a8b407c8cad7", "insertions": 39, "deletions": 10}, {"change_id": "If7907d02fe8a30853ca6307eac9849fa7fede753", "project": "qw_platform", "branch": "develop", "subject": "battery: disable battery irq.", "status": "MERGED", "created": 1749609056, "updated": 1749609951, "mergeable": false, "number": 4849, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/4849", "patchset_count": 2, "commit_id": "560481f3a8ed065fc0c68cc81c5bcdff7b219bea", "insertions": 16, "deletions": 1}, {"change_id": "Ie2cdad1d12b6095c1ef00ae4e4ad8e6202ae01d3", "project": "qw_platform", "branch": "develop", "subject": "ppg: remove ecg code", "status": "MERGED", "created": 1749562255, "updated": 1749608918, "mergeable": false, "number": 4843, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/4843", "patchset_count": 4, "commit_id": "61081b599be06b5242e7cd19eaa15bde377e747d", "insertions": 34, "deletions": 236}, {"change_id": "I689cb3f598ee4b21f8d1bba5c8de62f8a5471cf7", "project": "cycle/20250607_fc06", "branch": "develop", "subject": "[Feature]: 增加.gitignore文件", "status": "MERGED", "created": 1749608627, "updated": 1749608693, "mergeable": false, "number": 4847, "owner": "hancheng", "url": "http://********:8081/c/cycle/20250607_fc06/+/4847", "patchset_count": 1, "commit_id": "37e3787ec0c2820adad9c8c36879c29dd22d6ae6", "insertions": 67, "deletions": 0}, {"change_id": "Ie6a014717c8c8162e2a9630e588ba7c541e57892", "project": "cycle/20250607_fc06", "branch": "develop", "subject": "[Feature]: 模拟器相关文件增加", "status": "ABANDONED", "created": 1749561615, "updated": 1749607984, "mergeable": false, "number": 4861, "owner": "hancheng", "url": "http://********:8081/c/cycle/20250607_fc06/+/4861", "patchset_count": 1, "commit_id": "53def7630344ec1c580c1a60a050ac737ff2ef95", "insertions": 204097, "deletions": 1}, {"change_id": "I5c7367b9a86f959a1463864cc1c793600b37d6bc", "project": "cycle/20250607_fc06", "branch": "develop", "subject": "[Feature]: 增加IGS_DEV宏定义", "status": "ABANDONED", "created": 1749541134, "updated": 1749607981, "mergeable": false, "number": 4260, "owner": "hancheng", "url": "http://********:8081/c/cycle/20250607_fc06/+/4260", "patchset_count": 1, "commit_id": "4ebac0ac6e6470d23e0477b6d8f92d90ef2cb296", "insertions": 13, "deletions": 1}, {"change_id": "I8990c94ace1d5a349ebcdddf763bfd7c637a92c6", "project": "app", "branch": "develop", "subject": "alg_fmk:专项版本宏定义配置", "status": "NEW", "created": 1745223555, "updated": 1749596533, "mergeable": false, "number": 3602, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/app/+/3602", "patchset_count": 6, "commit_id": "425b14c1409bbd17374732d46c8676f3d434b474", "insertions": 13, "deletions": 1}, {"change_id": "I8990c94ace1d5a349ebcdddf763bfd7c637a92c6", "project": "qw_platform", "branch": "develop", "subject": "alg_fmk:专项版本宏定义配置", "status": "NEW", "created": 1745223512, "updated": 1749596526, "mergeable": false, "number": 3601, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/3601", "patchset_count": 4, "commit_id": "b2dba09c5d34f4ca2dbe0e7661da99f8cbc77d20", "insertions": 13, "deletions": 0}, {"change_id": "Id943123cbffd9ed18bc1e5501f82beea9178399a", "project": "qw_platform", "branch": "wr02_factory", "subject": "[A+G]: 取消订阅时，设置采样率为0，关闭sensor", "status": "NEW", "created": 1749180204, "updated": 1749596522, "mergeable": false, "number": 4457, "owner": "lixiaolong", "url": "http://********:8081/c/qw_platform/+/4457", "patchset_count": 2, "commit_id": "fac8db69f615451664253e36c73b77b584cc3379", "insertions": 24, "deletions": 2}, {"change_id": "I6379be8150d3f1308929e73abefbdb3f26776ba9", "project": "sifli", "branch": "develop", "subject": "gpio: fix disable gpio irq bug in A4", "status": "MERGED", "created": 1749558238, "updated": 1749561565, "mergeable": false, "number": 4838, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/4838", "patchset_count": 3, "commit_id": "7f95c7d16a01fe1cd3694ccddaa7e452147c7fea", "insertions": 14, "deletions": 1}, {"change_id": "I734a3e157dda09001de215e8576e6ef099492c56", "project": "sifli", "branch": "develop", "subject": "[wdt]: 屏蔽wdt_reconfig", "status": "MERGED", "created": 1749560514, "updated": 1749561450, "mergeable": false, "number": 4840, "owner": "lixiaolong", "url": "http://********:8081/c/sifli/+/4840", "patchset_count": 1, "commit_id": "87124514c774fa1d6c4569784ff0ce980152052f", "insertions": 11, "deletions": 1}, {"change_id": "I877e63da8126301bd1ce562bece7691d9348c1b0", "project": "cycle/20250607_fc06", "branch": "develop", "subject": "【新增功能】1.蓝牙透传profile编写 2.部分文件系统接入 3.部分通用模块接入", "status": "MERGED", "created": 1749560962, "updated": 1749561075, "mergeable": false, "number": 4842, "owner": "<PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/20250607_fc06/+/4842", "patchset_count": 1, "commit_id": "2e94b653a9f221daea473f5b5196bf957b4a351a", "insertions": 160662, "deletions": 57090}, {"change_id": "Ic46ad43321996f13dd60ef41f47ee51ae49ace43", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 优化隧道补偿实现", "status": "MERGED", "created": 1749560421, "updated": 1749560936, "mergeable": false, "number": 4839, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/4839", "patchset_count": 1, "commit_id": "f9aacfa0a6b61e2763d6973e41e2ac79688d62dc", "insertions": 74, "deletions": 53}, {"change_id": "Ia3573deffd3bd37a83545dfbf47c7c4d0ebda9b8", "project": "app", "branch": "develop", "subject": "[GUI]: 应用卡片添加个人成就", "status": "MERGED", "created": 1749546728, "updated": 1749558150, "mergeable": false, "number": 4835, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4835", "patchset_count": 5, "commit_id": "03d600d2fa7fb06f0a8d538362d9485eed27ffab", "insertions": 880, "deletions": 18}, {"change_id": "Iccc2b86d1dc6780b9950f6e68522dc278da7a113", "project": "app", "branch": "develop", "subject": "[OTA]: 添加切换到OTA页面的接口", "status": "MERGED", "created": 1749552505, "updated": 1749552905, "mergeable": false, "number": 4837, "owner": "lixin", "url": "http://********:8081/c/app/+/4837", "patchset_count": 1, "commit_id": "a8b986202deaf028345eba301ae3db8b614549e2", "insertions": 25, "deletions": 1}, {"change_id": "I6be25dc3ba712b02f432ff5c090ceb9f522789b3", "project": "app", "branch": "develop", "subject": "[GPS]: 解决NMEA语句异常时死机的问题", "status": "MERGED", "created": 1749468884, "updated": 1749551785, "mergeable": false, "number": 4818, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4818", "patchset_count": 2, "commit_id": "5a902325540490b12c6c101ab3cefee2292714a9", "insertions": 16, "deletions": 1}, {"change_id": "Iabcdf9b90bc7d9c0df4f834ac66587efdde3874a", "project": "qw_platform", "branch": "develop", "subject": "[GPS]: 加入发布数据项置信度", "status": "MERGED", "created": 1749460667, "updated": 1749551735, "mergeable": false, "number": 4812, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4812", "patchset_count": 2, "commit_id": "5b18fea66c192eb6d8e8da8066dc031f20ad8b69", "insertions": 11, "deletions": 0}, {"change_id": "I112100c456c07885ccfc7a3bb38d4f83ddf4d552", "project": "app", "branch": "develop", "subject": "[font]: 调整字符初始化", "status": "MERGED", "created": 1749546119, "updated": 1749547935, "mergeable": false, "number": 4834, "owner": "lixin", "url": "http://********:8081/c/app/+/4834", "patchset_count": 1, "commit_id": "f6540ee91a1d73ed86cf16beed3aedef3d0fbd66", "insertions": 1806, "deletions": 1853}, {"change_id": "I112100c456c07885ccfc7a3bb38d4f83ddf4d552", "project": "qw_platform", "branch": "develop", "subject": "[font]: 调整字符初始化过程", "status": "MERGED", "created": 1749546073, "updated": 1749547935, "mergeable": false, "number": 4833, "owner": "lixin", "url": "http://********:8081/c/qw_platform/+/4833", "patchset_count": 2, "commit_id": "7bf8baac7b08f3ec98c9194bf0053a98cc6bf686", "insertions": 1210, "deletions": 1207}, {"change_id": "I7dec489bba3b53a9fb16ba910b860346ece01622", "project": "app", "branch": "develop", "subject": "sports: 17631【功能测试】【运动】速度区间时间与实际不符【100%】 17919【功能测试】【运动-导航-路线管理】运动中删除正在导航的路线成功【100%】【v0.72】 18035【功能测试】【导航--轨迹导航】运动摘要中配速详情页面文字溢出【20%】【v0.7】 18061【功能测试】【运动-室内跑步】跑步的配速和距离数据偏差大【100%】【v0.74】", "status": "MERGED", "created": 1749521544, "updated": 1749545725, "mergeable": false, "number": 4823, "owner": "yukai", "url": "http://********:8081/c/app/+/4823", "patchset_count": 2, "commit_id": "f9655c51c33c190e817ae24b63509b6184e26836", "insertions": 135, "deletions": 123}, {"change_id": "I7dec489bba3b53a9fb16ba910b860346ece01622", "project": "qw_platform", "branch": "develop", "subject": "sports: 解决正在使用的路书可以被删除的问题；增加过长的异常配速显示为--", "status": "MERGED", "created": 1749520934, "updated": 1749545725, "mergeable": false, "number": 4822, "owner": "yukai", "url": "http://********:8081/c/qw_platform/+/4822", "patchset_count": 2, "commit_id": "897d1784dad5eea6340807e02c0ebe7033e01ff1", "insertions": 62, "deletions": 26}, {"change_id": "I44e2bdaba809dd9050966306afa0f7915207b5e2", "project": "app", "branch": "develop", "subject": "[proto]: 接入更新的个人信息字段配置等协议", "status": "MERGED", "created": 1749203633, "updated": 1749545685, "mergeable": false, "number": 4745, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4745", "patchset_count": 3, "commit_id": "606f94ba5f9dd3d72c7d18042689558a5b45c184", "insertions": 140, "deletions": 59}, {"change_id": "I5f6cbcc998cd39f46b61cf7a65a5aff0e24f0b6e", "project": "qw_platform", "branch": "develop", "subject": "sports: 开放活动和线路模拟调试功能", "status": "MERGED", "created": 1749175364, "updated": **********, "mergeable": false, "number": 4723, "owner": "yukai", "url": "http://********:8081/c/qw_platform/+/4723", "patchset_count": 4, "commit_id": "6595bcc9acd6d2ccd22e0f0a3ddf3f0790e1ead4", "insertions": 33, "deletions": 32}, {"change_id": "I5f6cbcc998cd39f46b61cf7a65a5aff0e24f0b6e", "project": "app", "branch": "develop", "subject": "sports: 整合接入活动和线路模拟功能", "status": "MERGED", "created": **********, "updated": **********, "mergeable": false, "number": 4722, "owner": "yukai", "url": "http://********:8081/c/app/+/4722", "patchset_count": 4, "commit_id": "22206e79c28a7276b3902bb3ea04a2fa1ab0cc9f", "insertions": 71, "deletions": 49}, {"change_id": "I5f6482e45da2def1984ef357cb0ce043f6a5967b", "project": "app", "branch": "develop", "subject": "SERVICE[BUG-001]: when an error occurs while modifying the storage of health files, print the log and add the return value print", "status": "MERGED", "created": **********, "updated": **********, "mergeable": false, "number": 4831, "owner": "hongxing", "url": "http://********:8081/c/app/+/4831", "patchset_count": 2, "commit_id": "d3c61d0e2e7dd5d61ceb103b900b25e262eb38a1", "insertions": 26, "deletions": 11}, {"change_id": "I7d58500dc260339e509e6404e706940dbe13933c", "project": "sifli", "branch": "develop", "subject": "[drv]: 恢复小核systick里清WSR的操作", "status": "MERGED", "created": **********, "updated": **********, "mergeable": false, "number": 4829, "owner": "lixiaolong", "url": "http://********:8081/c/sifli/+/4829", "patchset_count": 1, "commit_id": "8c1369d9dfe05acc0808a73b0212a9bd90a036ec", "insertions": 12, "deletions": 0}, {"change_id": "I4d1d2de3396ac2bfb62b0160f4037a8451305fc8", "project": "app", "branch": "develop", "subject": "[pm]: 小核开启降频，取消light sleep", "status": "MERGED", "created": **********, "updated": 1749542184, "mergeable": false, "number": 4830, "owner": "lixiaolong", "url": "http://********:8081/c/app/+/4830", "patchset_count": 1, "commit_id": "7a94749d7563f679493ecf5baa22668b5cb2a43f", "insertions": 14, "deletions": 1}, {"change_id": "I2d674f7a866e6e4d021049ed1cb062849740c015", "project": "qw_platform", "branch": "develop", "subject": "ppg: undef FAKE_ZERO_XYZ,feed normal acc", "status": "MERGED", "created": 1749525421, "updated": 1749539934, "mergeable": false, "number": 4825, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/4825", "patchset_count": 3, "commit_id": "fe3179b20b41400629ad81f998fc9230700412ef", "insertions": 11, "deletions": 1}, {"change_id": "Iddd35a54a7e87e056d91b51914edae4d5b815320", "project": "qw_platform", "branch": "develop", "subject": "factory:工模新增中文", "status": "MERGED", "created": 1749533900, "updated": 1749536700, "mergeable": false, "number": 4828, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4828", "patchset_count": 1, "commit_id": "cc30c233deae3f6d3f2bc7da1aa3bb7662f63c22", "insertions": 237, "deletions": 302}, {"change_id": "Iddd35a54a7e87e056d91b51914edae4d5b815320", "project": "app", "branch": "develop", "subject": "factory:工模新增中文", "status": "MERGED", "created": 1749533774, "updated": 1749536699, "mergeable": false, "number": 4827, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4827", "patchset_count": 1, "commit_id": "32dd7f179ceeed1f9dafe2c0eb9a5d581777545a", "insertions": 76, "deletions": 28}, {"change_id": "I235f9bf0f48801afd6d2b46637872d1120c1517f", "project": "qw_platform", "branch": "develop", "subject": "ui:英文页面ui", "status": "MERGED", "created": 1749456308, "updated": 1749536660, "mergeable": false, "number": 4810, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4810", "patchset_count": 2, "commit_id": "f7ab079a1dff9135c255b03d6aa285d4e37b43c1", "insertions": 18, "deletions": 18}, {"change_id": "I235f9bf0f48801afd6d2b46637872d1120c1517f", "project": "app", "branch": "develop", "subject": "ui:英文页面ui", "status": "MERGED", "created": 1749456245, "updated": 1749536659, "mergeable": false, "number": 4809, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4809", "patchset_count": 1, "commit_id": "9e941550abbea0a8263604be728361d4dcc4f6b5", "insertions": 19, "deletions": 38}, {"change_id": "I1e4e2bd09201fabe49af1847a59d8415e9e69350", "project": "sifli", "branch": "develop", "subject": "[PM]: 统计各行代码pm_request和pm_release次数", "status": "MERGED", "created": 1749526071, "updated": 1749530474, "mergeable": false, "number": 4826, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/sifli/+/4826", "patchset_count": 2, "commit_id": "35133bf6ac98b5f661d4e177a7755dc0b4261e89", "insertions": 129, "deletions": 0}, {"change_id": "Id119a3e5511d0a717c101377cc7508e70e9e31d0", "project": "qw_platform", "branch": "develop", "subject": "ppg: split ppg irq work code", "status": "MERGED", "created": 1749521882, "updated": 1749523548, "mergeable": false, "number": 4824, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/4824", "patchset_count": 2, "commit_id": "64abbd182c25d30b3f37c18cab23cb42854abd7a", "insertions": 48, "deletions": 17}, {"change_id": "I6b2c19ad26ce2a2f012b38fd98ca610fe6679ae0", "project": "sifli", "branch": "develop", "subject": "[pm]: 区分A3和A4板唤醒脚边沿配置", "status": "MERGED", "created": 1749517997, "updated": 1749518388, "mergeable": false, "number": 4821, "owner": "lixiaolong", "url": "http://********:8081/c/sifli/+/4821", "patchset_count": 1, "commit_id": "968a86393d448ec64a01955d3870ce89967b824a", "insertions": 17, "deletions": 3}, {"change_id": "Ifd8929aaac9aaa994eadeaf26967ef9e565b308b", "project": "sifli", "branch": "develop", "subject": "[PPG]: 设置PPG唤醒管脚为上升沿唤醒，和PPG_INT上升沿中断保持一致", "status": "MERGED", "created": 1749469550, "updated": 1749472240, "mergeable": false, "number": 4819, "owner": "lixiaolong", "url": "http://********:8081/c/sifli/+/4819", "patchset_count": 1, "commit_id": "225a4977242d98f799f686c7ffa23e1a7f6a6c91", "insertions": 11, "deletions": 1}, {"change_id": "Id1beabc188c3b3e9e0ecbeaf734a19793725766d", "project": "cycle/c202310_bg1", "branch": "Develop", "subject": "[Fix]: 同步已知问题", "status": "MERGED", "created": 1749462688, "updated": 1749466648, "mergeable": false, "number": 4813, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/c202310_bg1/+/4813", "patchset_count": 1, "commit_id": "bc5ca2fc98c37d7ef1bd3274f3d5de41fea41ca9", "insertions": 25, "deletions": 1}, {"change_id": "I018f65024f73fe5e430646c9ffa7bfc70091411a", "project": "cycle/c202310_bg1", "branch": "Develop", "subject": "[Fix]: 优化字体方案，减少flash和内存占用", "status": "MERGED", "created": 1749269915, "updated": 1749466645, "mergeable": false, "number": 4758, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/c202310_bg1/+/4758", "patchset_count": 2, "commit_id": "ab778640e16b4fa35f53e4613b896755c1797d1e", "insertions": 6935, "deletions": 70}, {"change_id": "Ib9831004eb325837146102aef1286a566cce33f5", "project": "qw_platform", "branch": "develop", "subject": "tools: move ppg_parse to capture_parse_tool", "status": "MERGED", "created": 1749465476, "updated": 1749466080, "mergeable": false, "number": 4815, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/4815", "patchset_count": 1, "commit_id": "8016981a2147bd7e7574172e1e5189ae8214608a", "insertions": 10, "deletions": 32912}, {"change_id": "I2f226755b78e9cc7ad395135808e7d98694a8824", "project": "cycle/20250607_fc06", "branch": "develop", "subject": "【新增功能】1.蓝牙基础适配 包含广播扫描 通用服务功能 2.串口调试功能接入 3.cola_os接入 4.内存池接入", "status": "MERGED", "created": 1749462994, "updated": 1749463149, "mergeable": false, "number": 4814, "owner": "<PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/20250607_fc06/+/4814", "patchset_count": 1, "commit_id": "4d081e5d1350ddd48cce36aef91de8588d6a9f42", "insertions": 194211, "deletions": 61438}, {"change_id": "I0c4e2f6f312c5196fc0a95ee35af2b679ddf7978", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 解决隧道补偿时间补偿异常问题", "status": "MERGED", "created": 1749448856, "updated": 1749462514, "mergeable": false, "number": 4804, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/4804", "patchset_count": 2, "commit_id": "ba2bc245852e8b46a5e7861b1eccf00f0d15e718", "insertions": 24, "deletions": 0}, {"change_id": "I46601b2ed84b8d5b9602fd14381ec446efc9aad5", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 优化freetype配置减少flash和ram占用", "status": "MERGED", "created": 1749275454, "updated": 1749462505, "mergeable": false, "number": 4760, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/4760", "patchset_count": 3, "commit_id": "5ccb7f842465f14aa1d6afcb498ad6561ffe546a", "insertions": 44, "deletions": 18}, {"change_id": "I0bbc7d9d775f8d58d249decbe4dfbcc8121c7311", "project": "sifli", "branch": "develop", "subject": "[wdt]: 修复看门狗不生效问题", "status": "MERGED", "created": 1749460655, "updated": 1749462504, "mergeable": false, "number": 4811, "owner": "lixiaolong", "url": "http://********:8081/c/sifli/+/4811", "patchset_count": 1, "commit_id": "bb0665b44d0c31528820f036b4c826cb8b480b48", "insertions": 54, "deletions": 14}, {"change_id": "I9d84354b09944e6971804cbb4f668fc6fa413fab", "project": "qw_platform", "branch": "develop", "subject": "boot_env: add boot params.", "status": "MERGED", "created": 1749366029, "updated": 1749455671, "mergeable": false, "number": 4773, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/4773", "patchset_count": 15, "commit_id": "c8b9590a78d5d3c15e0f5abf6f8ae4ce4709e4df", "insertions": 84, "deletions": 3}, {"change_id": "I47faa65132afa151c8dc8dd708160d323073ee40", "project": "qw_platform", "branch": "develop", "subject": "[GUI]: 恢复出厂loading页面不息屏", "status": "MERGED", "created": 1749450655, "updated": 1749451869, "mergeable": false, "number": 4805, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4805", "patchset_count": 1, "commit_id": "4a3c9b4b78a993090727f869bb6d097514824d8b", "insertions": 12, "deletions": 2}, {"change_id": "I3543a00830c35e1be194ea619c13726aa3129083", "project": "app", "branch": "develop", "subject": "[GUI]: 17618 删除传感器后再回到列表查看，已删除的传感器没有立刻消失", "status": "MERGED", "created": 1749437200, "updated": 1749438774, "mergeable": false, "number": 4802, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4802", "patchset_count": 1, "commit_id": "9f15dbce2206c3242099d29a23058d92dd8fdc01", "insertions": 328, "deletions": 265}, {"change_id": "I3543a00830c35e1be194ea619c13726aa3129083", "project": "qw_platform", "branch": "develop", "subject": "[GUI]: 17618 删除传感器后再回到列表查看，已删除的传感器没有立刻消失", "status": "MERGED", "created": 1749436485, "updated": 1749438773, "mergeable": false, "number": 4801, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4801", "patchset_count": 1, "commit_id": "2fb60a77aba8546bc20b2bfdccd415d342b5ddb1", "insertions": 22, "deletions": 1}, {"change_id": "I63cc2cd12e50d494b6688e738f7ed0a79ec9d760", "project": "app", "branch": "develop", "subject": "[GUI]: 17618 删除传感器后再回到列表查看，已删除的传感器没有立刻消失 \t增加默认训练文件", "status": "ABANDONED", "created": 1749436477, "updated": 1749437260, "mergeable": false, "number": 4780, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4780", "patchset_count": 1, "commit_id": "fdff7120cdcc18f2a12e01fbefdd906a23f1a1d5", "insertions": 330, "deletions": 265}, {"change_id": "I1c138e2fa342f46e3709a08a239c70dd45c10f40", "project": "cycle/20250607_fc06", "branch": "master", "subject": "【upload】1.FC06工程代码上传", "status": "MERGED", "created": 1749435503, "updated": 1749435763, "mergeable": false, "number": 4779, "owner": "<PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/20250607_fc06/+/4779", "patchset_count": 1, "commit_id": "4ed65996cc068d6fa4984bf8f47a91d8b8de80a3", "insertions": 1000191, "deletions": 0}, {"change_id": "Iac221580848abdb3ba134a753d51770b6826f240", "project": "tools", "branch": "develop", "subject": "[GUI]: 恢复出厂增加资源文件拷贝", "status": "MERGED", "created": 1749432888, "updated": 1749434118, "mergeable": false, "number": 4777, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/tools/+/4777", "patchset_count": 1, "commit_id": "6115c65e00af3b8405dc6a216a56e58e0cc0d945", "insertions": 18, "deletions": 0}, {"change_id": "I99652823177095fa92895ded27f52c42d283bb75", "project": "app", "branch": "develop", "subject": "[GPS]: gps wake引脚高阻态恢复为数字功能", "status": "MERGED", "created": 1749026527, "updated": 1749433684, "mergeable": false, "number": 4688, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4688", "patchset_count": 5, "commit_id": "b30975e9ad58d748d4ae38e293313a92b405599b", "insertions": 12, "deletions": 2}, {"change_id": "I99652823177095fa92895ded27f52c42d283bb75", "project": "sifli", "branch": "develop", "subject": "[GPS]: gps wake引脚高阻态恢复为数字功能", "status": "MERGED", "created": 1749433165, "updated": 1749433683, "mergeable": false, "number": 4778, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/sifli/+/4778", "patchset_count": 1, "commit_id": "e0a5fafeb54d56a7bd61e54d51732e9a4a1c4b59", "insertions": 11, "deletions": 1}, {"change_id": "I99652823177095fa92895ded27f52c42d283bb75", "project": "qw_platform", "branch": "develop", "subject": "[GPS]: gps wake引脚高阻态恢复为数字功能", "status": "MERGED", "created": 1749432495, "updated": 1749433683, "mergeable": false, "number": 4776, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4776", "patchset_count": 2, "commit_id": "817bb3be8c5e945f7d5c3ffd8068999fb9a79e77", "insertions": 14, "deletions": 4}, {"change_id": "I7e99870fa09ba037f770566fd236cf6c253868ff", "project": "tools", "branch": "develop", "subject": "[GUI]: 增加恢复出厂资源文件拷贝", "status": "ABANDONED", "created": 1749431744, "updated": 1749432295, "mergeable": false, "number": 4775, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/tools/+/4775", "patchset_count": 1, "commit_id": "08b6751cf180e190377c6a968d51828c218540a5", "insertions": 10, "deletions": 0}, {"change_id": "Id0778f8ff9930afd4c18d3c2d79ed574f407705c", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: framework\\kvdb 文件阻断处理", "status": "MERGED", "created": 1749007374, "updated": 1749431243, "mergeable": false, "number": 4677, "owner": "wang<PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/4677", "patchset_count": 3, "commit_id": "13f5eaa2892e6ea75fd77a3e19936fc70f224332", "insertions": 47, "deletions": 31}, {"change_id": "I59324df74494e9741e180772007dbddfda4ae4ff", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 解决workout阻断", "status": "MERGED", "created": 1749291674, "updated": 1749431057, "mergeable": false, "number": 4766, "owner": "hancheng", "url": "http://********:8081/c/cycle/app/+/4766", "patchset_count": 1, "commit_id": "83eb6602d38a5b2019ec3e58e0aa991c7cc1c5a4", "insertions": 12, "deletions": 2}, {"change_id": "I9d84354b09944e6971804cbb4f668fc6fa413fab", "project": "app", "branch": "develop", "subject": "global_params: add boot_env.", "status": "MERGED", "created": 1749366145, "updated": 1749375589, "mergeable": false, "number": 4774, "owner": "linjizhao", "url": "http://********:8081/c/app/+/4774", "patchset_count": 3, "commit_id": "20e70ec8c1175bec005ef638dfad792c3afa29e2", "insertions": 27, "deletions": 3}, {"change_id": "I938de510aa89aed3c3ae223ed100ee9745353820", "project": "app", "branch": "develop", "subject": "sonarqube: fix some blocker", "status": "MERGED", "created": 1749363554, "updated": 1749364858, "mergeable": false, "number": 4772, "owner": "linjizhao", "url": "http://********:8081/c/app/+/4772", "patchset_count": 4, "commit_id": "214d2ea4e7430e109228c83ab005d3e568ae1799", "insertions": 98, "deletions": 44}, {"change_id": "I15771c58594c9efbe74aedc24e5ca9fb93d19ebf", "project": "sifli", "branch": "develop", "subject": "ulog_assert: ulog_assert as qw_assert", "status": "MERGED", "created": 1749361349, "updated": 1749363623, "mergeable": false, "number": 4771, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/4771", "patchset_count": 2, "commit_id": "b30be758364f7de8137355fd2795a6c72fe57f57", "insertions": 12, "deletions": 1}, {"change_id": "I813177c711e4becb739c5cedba91e8c597393f68", "project": "sifli", "branch": "develop", "subject": "gpio2_irq: add gpio irq abnormal monitor", "status": "MERGED", "created": 1749357750, "updated": 1749358263, "mergeable": false, "number": 4770, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/4770", "patchset_count": 1, "commit_id": "b7e6c8b6a5d8291fd9abb021c9290e048fac03cc", "insertions": 123, "deletions": 3}, {"change_id": "I5ef7d2b9362db815a419db7d15724e49fa295c73", "project": "qw_platform", "branch": "develop", "subject": "qw_assert: add qw_assert to unified assert interface", "status": "MERGED", "created": 1749348643, "updated": 1749354709, "mergeable": false, "number": 4769, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/4769", "patchset_count": 2, "commit_id": "efd16e659f8c25b14e67ae0e0590774e7dacc6ff", "insertions": 64, "deletions": 3}, {"change_id": "I5ef7d2b9362db815a419db7d15724e49fa295c73", "project": "app", "branch": "develop", "subject": "qw_assert: add qw_assert to unified assert.", "status": "MERGED", "created": 1749348447, "updated": 1749354709, "mergeable": false, "number": 4767, "owner": "linjizhao", "url": "http://********:8081/c/app/+/4767", "patchset_count": 4, "commit_id": "2cb49c7a92c1dd021dfc33d45a9c4bea6f9b453c", "insertions": 29, "deletions": 10}, {"change_id": "I5ef7d2b9362db815a419db7d15724e49fa295c73", "project": "sifli", "branch": "develop", "subject": "qw_assert: add qw_assert to unified assert.", "status": "MERGED", "created": 1749348584, "updated": 1749354708, "mergeable": false, "number": 4768, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/4768", "patchset_count": 2, "commit_id": "a4d07687202d18526a826f0b49ab160a231f37f4", "insertions": 21, "deletions": 2}, {"change_id": "I292c01c298a0b68f782ba5a82de8f70e42e2fc4c", "project": "app", "branch": "develop", "subject": "[GUI]: 18080 将身高设置为超过250cm的任意值后保存的数据应该为默认值", "status": "MERGED", "created": 1749283351, "updated": 1749284008, "mergeable": false, "number": 4764, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4764", "patchset_count": 1, "commit_id": "6d7d5bc17999e47aad677d1ccb57afe5d42f08c1", "insertions": 18, "deletions": 0}, {"change_id": "I90ebd0c83d102cecdace8b0a66ae593bb4f5e896", "project": "app", "branch": "develop", "subject": "[GUI]: 18088 系统设置中已连接速度和踏频的传感器但骑行运动准备页不显示速度和踏频的传感器", "status": "MERGED", "created": 1749280584, "updated": 1749284001, "mergeable": false, "number": 4762, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4762", "patchset_count": 2, "commit_id": "ba108db3a68897b2903972b5309062cd4d1539dd", "insertions": 15, "deletions": 0}, {"change_id": "I777bd0bb35a3fd150afd0bea13b8f0925d2ef8ff", "project": "app", "branch": "develop", "subject": "[GUI]: 增加恢复出厂功能。", "status": "MERGED", "created": 1749267599, "updated": 1749279629, "mergeable": false, "number": 4757, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4757", "patchset_count": 2, "commit_id": "a4d6309175800bb4a4470b8b756751977299b8a2", "insertions": 303, "deletions": 66}, {"change_id": "I777bd0bb35a3fd150afd0bea13b8f0925d2ef8ff", "project": "qw_platform", "branch": "develop", "subject": "[GUI]: 增加恢复出厂功能。", "status": "MERGED", "created": 1749267591, "updated": 1749279629, "mergeable": false, "number": 4756, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4756", "patchset_count": 2, "commit_id": "c06ba140f8c6c52e0b9a7ca25746d80ef803af94", "insertions": 167, "deletions": 73}, {"change_id": "I777bd0bb35a3fd150afd0bea13b8f0925d2ef8ff", "project": "tools", "branch": "develop", "subject": "[GUI]: 增加恢复出厂功能。", "status": "MERGED", "created": 1749267584, "updated": 1749279628, "mergeable": false, "number": 4755, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/tools/+/4755", "patchset_count": 1, "commit_id": "8bf3521ffaea96bc649b0bacea327afc5ebe9b58", "insertions": 17, "deletions": 0}, {"change_id": "Icead362872f7d936a21fcff56d6415a5c351d075", "project": "tools", "branch": "wr02_algosim", "subject": "alg_fmk:数据分析工具打包提交", "status": "MERGED", "created": 1749279119, "updated": 1749279413, "mergeable": false, "number": 4781, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/tools/+/4781", "patchset_count": 1, "commit_id": "738937c93e6c8eab74b17c59d1ac425df5ee2574", "insertions": 25, "deletions": 0}, {"change_id": "I321f2e2d415ecd33b4bb156dd0827afd3593ebc6", "project": "tools", "branch": "wr02_algosim", "subject": "alg_fmk:数据倒灌csv预处理脚本", "status": "MERGED", "created": 1749278961, "updated": 1749279093, "mergeable": false, "number": 4460, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/tools/+/4460", "patchset_count": 1, "commit_id": "53c294048417713fcdea0e7bf8025c7599dd8bd1", "insertions": 316, "deletions": 0}, {"change_id": "If166ca4ca645ef5212beec1a4b0f69495f58073e", "project": "qw_platform", "branch": "develop", "subject": "capture: fix capture pm state bug", "status": "MERGED", "created": 1749276067, "updated": 1749277788, "mergeable": false, "number": 4761, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/4761", "patchset_count": 2, "commit_id": "f8873c415ffa9d1076237136377d748e9e7faed9", "insertions": 11, "deletions": 0}, {"change_id": "I2ab5a1c96b0ef1fc5e294c5047f6bc8229cfcab5", "project": "app", "branch": "develop", "subject": "developer: fix hr capture state sync bug in init", "status": "MERGED", "created": 1749271866, "updated": 1749275788, "mergeable": false, "number": 4759, "owner": "linjizhao", "url": "http://********:8081/c/app/+/4759", "patchset_count": 1, "commit_id": "853abb1b1c1b4db1ba7af6c7af336adcb67e604f", "insertions": 19, "deletions": 6}, {"change_id": "I2ab5a1c96b0ef1fc5e294c5047f6bc8229cfcab5", "project": "qw_platform", "branch": "develop", "subject": "capture: disbale enter sleep if need erpc capture", "status": "MERGED", "created": 1749259293, "updated": 1749275788, "mergeable": false, "number": 4754, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/4754", "patchset_count": 4, "commit_id": "4eab383d42753f5e31eb4caa9ffb4d1cadf32ad4", "insertions": 31, "deletions": 3}, {"change_id": "Ie5b0c384c31aa79836ecca886e914ef0b88880a7", "project": "cycle/c202310_bg1", "branch": "Develop", "subject": "[Fix]: 解决存在路书无法删除的问题", "status": "MERGED", "created": 1749201716, "updated": 1749274531, "mergeable": false, "number": 4744, "owner": "liquan", "url": "http://********:8081/c/cycle/c202310_bg1/+/4744", "patchset_count": 2, "commit_id": "214698d213ef1bda2bf4d54bbfb972e5ab1b0cda", "insertions": 27, "deletions": 1}, {"change_id": "Ie74c21a7d9f6a825f52d9997469403ec982ed9ec", "project": "wearable/hr50", "branch": "develop", "subject": "hr50:增加读fifo鲁棒性，防止溢出", "status": "NEW", "created": 1749212334, "updated": 1749212547, "mergeable": false, "number": 4753, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/wearable/hr50/+/4753", "patchset_count": 1, "commit_id": "b2fa9d268860482d1c43daf02245b96820dcaf4c", "insertions": 11, "deletions": 1}, {"change_id": "I1f57948e0619a78ff596066cc4f4cb7db6a0bf7a", "project": "app", "branch": "develop", "subject": "[GUI]: 解决模拟器编译问题。", "status": "MERGED", "created": 1749210506, "updated": 1749211284, "mergeable": false, "number": 4752, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4752", "patchset_count": 1, "commit_id": "3513367e91d5db0aa0501e3647e9d30b967f793a", "insertions": 13, "deletions": 1}, {"change_id": "Ica632ae5bc57f304ac81daae5c907f8f4d789f8b", "project": "qw_platform", "branch": "develop", "subject": "[GUI]: 调整LV memory pool size", "status": "MERGED", "created": 1749209876, "updated": 1749210894, "mergeable": false, "number": 4751, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4751", "patchset_count": 1, "commit_id": "5a4e89075299093d8f6015a2ece1041eb03a27f6", "insertions": 16, "deletions": 4}, {"change_id": "I46a4f25d91a367d74c4d1f5256cddf6281457ae2", "project": "qw_platform", "branch": "develop", "subject": "Revert \"[GUI]: 调整LV Memory pool size\"", "status": "MERGED", "created": 1749209616, "updated": 1749209825, "mergeable": false, "number": 4458, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4458", "patchset_count": 1, "commit_id": "1f150aed290e6b5de3e5b1a34b7a73c455ac5c4e", "insertions": 16, "deletions": 2}, {"change_id": "I011d6c3c469073372cd24e36df7da5a5a4314b0f", "project": "qw_platform", "branch": "develop", "subject": "[GUI]: 调整LV Memory pool size", "status": "MERGED", "created": 1749207779, "updated": 1749209616, "mergeable": false, "number": 4750, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4750", "patchset_count": 2, "commit_id": "dbb5ab74444e9d7aaa4dba8be15400fb2d52448f", "insertions": 12, "deletions": 2}, {"change_id": "Icff1a5b14b5c7cf60f8f3b37eb401137f1ec9b43", "project": "qw_platform", "branch": "develop", "subject": "qw_platform : 工模添加MAC二维码界面", "status": "MERGED", "created": 1749205974, "updated": 1749207984, "mergeable": false, "number": 4748, "owner": "gaoxing", "url": "http://********:8081/c/qw_platform/+/4748", "patchset_count": 2, "commit_id": "fe8783413edcdb99acd5125b65ddf763657541b5", "insertions": 12, "deletions": 0}, {"change_id": "Icff1a5b14b5c7cf60f8f3b37eb401137f1ec9b43", "project": "app", "branch": "develop", "subject": "app : 工模添加MAC二维码界面，修改GPS界面，PPG陪测模式关闭活体检测", "status": "MERGED", "created": 1749205955, "updated": 1749207984, "mergeable": false, "number": 4747, "owner": "gaoxing", "url": "http://********:8081/c/app/+/4747", "patchset_count": 2, "commit_id": "53fdc9267cd2c8ded9b25ac3e8583309836623e9", "insertions": 1286, "deletions": 666}, {"change_id": "Ice473f37dd2ab5aa989bed7752b317a4a70ce15a", "project": "sifli", "branch": "develop", "subject": "[gpio]: disable GPIO2 irq in HCPU when no irq pin is attached", "status": "MERGED", "created": 1749195931, "updated": 1749207584, "mergeable": false, "number": 4743, "owner": "lixiaolong", "url": "http://********:8081/c/sifli/+/4743", "patchset_count": 1, "commit_id": "d6e05da6ba93994744e7078c84aec52ba5df8bf5", "insertions": 23, "deletions": 0}, {"change_id": "I8a03d6ede67c847c03e4bacf1c258603cf6ea8e1", "project": "app", "branch": "develop", "subject": "[pm]: 关闭light sleep，改用降频", "status": "MERGED", "created": 1749206486, "updated": 1749207543, "mergeable": false, "number": 4749, "owner": "lixiaolong", "url": "http://********:8081/c/app/+/4749", "patchset_count": 2, "commit_id": "15a1486737edb58ca94575ee0381b2e316684579", "insertions": 15, "deletions": 3}, {"change_id": "I5895082e24bad4b5d903b281e848a0313b3062f4", "project": "app", "branch": "develop", "subject": "ui:默认中文", "status": "MERGED", "created": 1749205950, "updated": 1749206518, "mergeable": false, "number": 4746, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4746", "patchset_count": 1, "commit_id": "09b0d89e646e095ab673ffb0f01277c09ec8b6c4", "insertions": 11, "deletions": 1}, {"change_id": "Ie56d323a4e72f5c2b972862a077ccde8dda8159f", "project": "app", "branch": "develop", "subject": "lcpu_eide: set language-c as c11 stand", "status": "MERGED", "created": 1749189762, "updated": 1749204883, "mergeable": false, "number": 4738, "owner": "linjizhao", "url": "http://********:8081/c/app/+/4738", "patchset_count": 2, "commit_id": "02fb3ccfec3f72fef03cbe95ed62768afa91d5f6", "insertions": 11, "deletions": 1}, {"change_id": "Ib7f8bddce397d4f91c930ec2737d0a13f8a792f7", "project": "app", "branch": "develop", "subject": "[img]: 替换图片", "status": "MERGED", "created": 1749190525, "updated": 1749204039, "mergeable": false, "number": 4741, "owner": "lixin", "url": "http://********:8081/c/app/+/4741", "patchset_count": 2, "commit_id": "0eea547eb74a1d744da679df5c92cebb8cd38ce0", "insertions": 2133, "deletions": 2622}, {"change_id": "Ib7f8bddce397d4f91c930ec2737d0a13f8a792f7", "project": "qw_platform", "branch": "develop", "subject": "[grid]: 修正图标使用bug", "status": "MERGED", "created": 1749190422, "updated": 1749204039, "mergeable": false, "number": 4739, "owner": "lixin", "url": "http://********:8081/c/qw_platform/+/4739", "patchset_count": 2, "commit_id": "bff620bae7900fb3e9099a452ded315d37841ad3", "insertions": 16, "deletions": 4}, {"change_id": "I2d5a10544f10d18a629c7b8d236512bb5f76b8dd", "project": "qw_platform", "branch": "develop", "subject": "ui:解决菜单列表控件超长处理bug", "status": "MERGED", "created": 1749193892, "updated": 1749203673, "mergeable": false, "number": 4742, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4742", "patchset_count": 2, "commit_id": "30aecbb5a5b599da50fba08ef21198276b0e7970", "insertions": 27, "deletions": 6}, {"change_id": "Ib377084f8c2e595ad3823260091c9a44f1f9a9f2", "project": "app", "branch": "develop", "subject": "[fix]: 解决手表连接手机时，运动结束大概率无法立即将活动文件同步给app的问题", "status": "MERGED", "created": 1749179962, "updated": 1749203658, "mergeable": false, "number": 4729, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4729", "patchset_count": 3, "commit_id": "461d639534c14426b25e023e09de9cbc00390a46", "insertions": 16, "deletions": 6}, {"change_id": "I38d54a3bb51e0ab11b29a9cd4dc97bd51836fcc2", "project": "cycle/c202310_bg1", "branch": "Develop", "subject": "[Bulid]: Merge remote-tracking branch 'origin/Branch_Navi' into Develop", "status": "MERGED", "created": 1749190473, "updated": 1749201136, "mergeable": false, "number": 4740, "owner": "liquan", "url": "http://********:8081/c/cycle/c202310_bg1/+/4740", "patchset_count": 1, "commit_id": "bd3b364904ba7187afa3b98467cd7ab453a059e6", "insertions": 18610, "deletions": 8688}, {"change_id": "Ic07264a3c9e4b09f28151f7c923c2b9dda3ad193", "project": "app", "branch": "develop", "subject": "[GUI]: 传感器功率校准等待弹窗消失的问题。", "status": "MERGED", "created": 1749188570, "updated": 1749191327, "mergeable": false, "number": 4737, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4737", "patchset_count": 2, "commit_id": "dfaf4afdc3b338eb6d76b9c138682bb4facb5ff6", "insertions": 12, "deletions": 0}, {"change_id": "I2ec0e059d12faa7720cf5e06eccff6b2004526d0", "project": "app", "branch": "develop", "subject": "sonarqube: fix some misra-c blocking issues", "status": "MERGED", "created": 1749186841, "updated": 1749188312, "mergeable": false, "number": 4735, "owner": "linjizhao", "url": "http://********:8081/c/app/+/4735", "patchset_count": 3, "commit_id": "80fa243d23f5984e99bd894064af07a7189aa2d5", "insertions": 249, "deletions": 212}, {"change_id": "I14d8b1a258bbd01690786d988b4e3419a5704265", "project": "qw_platform", "branch": "develop", "subject": "ppg: fix ppg feed accZ bug", "status": "MERGED", "created": 1749187804, "updated": 1749188217, "mergeable": false, "number": 4736, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/4736", "patchset_count": 2, "commit_id": "6487bd9a35fbc7fe138d3781e659e31b7f425752", "insertions": 11, "deletions": 1}, {"change_id": "I10f14889aa34674c543994a665279e52e9ce92ab", "project": "qw_platform", "branch": "develop", "subject": "sonarquce: fix possible null pointer dereference", "status": "MERGED", "created": 1749181962, "updated": 1749183672, "mergeable": false, "number": 4733, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/4733", "patchset_count": 2, "commit_id": "d9ce08e5c88bc0456654d250d392848f0efea961", "insertions": 16, "deletions": 4}, {"change_id": "I0a3fd1044d9f775e18a3ff53edf74211a745a8ef", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 解决三方ANT雷达车灯连接问题", "status": "MERGED", "created": 1749178622, "updated": 1749179652, "mergeable": false, "number": 4728, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/4728", "patchset_count": 2, "commit_id": "e13ecd4b80cec21bdbe76889449308bd2641c3a4", "insertions": 51, "deletions": 33}, {"change_id": "I24333c9d4b78a4ed869d66894ef036e77df9536a", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 优化UI更新最小更新面积计算功能", "status": "MERGED", "created": 1749123404, "updated": 1749178612, "mergeable": false, "number": 4717, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/4717", "patchset_count": 2, "commit_id": "580cf938363b71381349127784c1c678baaadf7b", "insertions": 75, "deletions": 20}, {"change_id": "Id3ee7a899288591a8a5a5e54c1fc3d35bec5c3bc", "project": "app", "branch": "wr02_algosim", "subject": "alg_fmk:gomore数据倒灌仿真", "status": "MERGED", "created": 1749177928, "updated": 1749178403, "mergeable": false, "number": 4725, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/app/+/4725", "patchset_count": 1, "commit_id": "c5c897ad9a33098c372c57360c958deff50793c0", "insertions": 1928, "deletions": 16}, {"change_id": "Id3ee7a899288591a8a5a5e54c1fc3d35bec5c3bc", "project": "sifli", "branch": "wr02_algosim", "subject": "alg_fmk:gomore数据倒灌仿真", "status": "MERGED", "created": 1749177929, "updated": 1749178402, "mergeable": false, "number": 4727, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/sifli/+/4727", "patchset_count": 1, "commit_id": "36bf22635e9e2cd8a42fd748af45cadb8139af8b", "insertions": 13, "deletions": 1}, {"change_id": "Id3ee7a899288591a8a5a5e54c1fc3d35bec5c3bc", "project": "qw_platform", "branch": "wr02_algosim", "subject": "alg_fmk:gomore数据倒灌仿真", "status": "MERGED", "created": 1749177929, "updated": 1749178402, "mergeable": false, "number": 4726, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4726", "patchset_count": 1, "commit_id": "aeb428899395120965a8a09cfc78c86858806c18", "insertions": 52, "deletions": 7}, {"change_id": "I4805ec07506d978c91ecef674977e1b87a1b40e1", "project": "tools", "branch": "develop", "subject": "[QTrace]:屏蔽时间戳回退检查", "status": "MERGED", "created": 1748228117, "updated": 1749178302, "mergeable": false, "number": 4491, "owner": "lixiaolong", "url": "http://********:8081/c/tools/+/4491", "patchset_count": 2, "commit_id": "7f2204a0679b0d183988be4a88382d4b470ee4e5", "insertions": 13, "deletions": 3}, {"change_id": "Id3ee7a899288591a8a5a5e54c1fc3d35bec5c3bc", "project": "sifli", "branch": "develop", "subject": "alg_fmk:gomore数据倒灌仿真", "status": "ABANDONED", "created": 1749020393, "updated": 1749177994, "mergeable": false, "number": 4682, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/sifli/+/4682", "patchset_count": 1, "commit_id": "bd724e2f2478865d2e633a65e2cc9f99f24bff3b", "insertions": 13, "deletions": 1}, {"change_id": "Id3ee7a899288591a8a5a5e54c1fc3d35bec5c3bc", "project": "qw_platform", "branch": "develop", "subject": "alg_fmk:gomore数据倒灌仿真", "status": "ABANDONED", "created": 1748588856, "updated": 1749177989, "mergeable": false, "number": 4633, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4633", "patchset_count": 1, "commit_id": "2c08538ffd3e913cc44a3b13604ae54557d63c25", "insertions": 52, "deletions": 7}, {"change_id": "Id3ee7a899288591a8a5a5e54c1fc3d35bec5c3bc", "project": "app", "branch": "develop", "subject": "alg_fmk:gomore数据倒灌仿真", "status": "ABANDONED", "created": 1746695419, "updated": 1749177984, "mergeable": false, "number": 4047, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/app/+/4047", "patchset_count": 19, "commit_id": "a8d14b52b15f9ed7dc90e3572d17d3cac69671b3", "insertions": 1888, "deletions": 16}, {"change_id": "I05ec471e5e5f2d8693a2b0ba5bab759ca2b00e4b", "project": "app", "branch": "develop", "subject": "alg_fmk:压力算法周期测量ppi输入异常修复", "status": "MERGED", "created": 1749127910, "updated": 1749177507, "mergeable": false, "number": 4719, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/app/+/4719", "patchset_count": 3, "commit_id": "8b00acae522bf169fa26d1f3ab24c182cf76c5d0", "insertions": 38, "deletions": 0}, {"change_id": "Ib8399e0aab3507275d09c8fcff56bea0daf84748", "project": "wearable/hr50", "branch": "develop", "subject": "hr50:ecg启动流程优化", "status": "MERGED", "created": 1749176931, "updated": 1749176946, "mergeable": false, "number": 4724, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/wearable/hr50/+/4724", "patchset_count": 1, "commit_id": "ff422c41d7d0ad6f4f1027e9e90d6848aa4072f5", "insertions": 11366, "deletions": 59}, {"change_id": "I54871da991394141f77eb7f757bb9808431eacc3", "project": "app", "branch": "develop", "subject": "SERVICE[BUG-001]: solve the problem that the resting heart rate is not subscribed after sleep ends", "status": "MERGED", "created": 1749175062, "updated": 1749176617, "mergeable": false, "number": 4721, "owner": "hongxing", "url": "http://********:8081/c/app/+/4721", "patchset_count": 2, "commit_id": "562f4427522c5fd583f2429046f2afc499b49d2f", "insertions": 13, "deletions": 3}, {"change_id": "Ic6f292a84519abbfc4d1d4bd29f5028ec61297e9", "project": "app", "branch": "develop", "subject": "[GUI]: 修改关于页面设备名称显示版本号问题。", "status": "MERGED", "created": 1749174672, "updated": 1749175521, "mergeable": false, "number": 4720, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4720", "patchset_count": 1, "commit_id": "2de26c2d3e5857ccbd5cea5ffbcfe86063bf3c63", "insertions": 22, "deletions": 0}, {"change_id": "Icead362872f7d936a21fcff56d6415a5c351d075", "project": "tools", "branch": "develop", "subject": "alg_fmk:数据分析工具打包提交", "status": "MERGED", "created": 1749124563, "updated": 1749171836, "mergeable": false, "number": 4718, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/tools/+/4718", "patchset_count": 1, "commit_id": "1983b5665438806b32b04741b5974bce6fa16a06", "insertions": 24, "deletions": 0}, {"change_id": "Ic931864bd65ddaa7aaee112551eaf95bfeb9bc0d", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 修复状态页和团期列表滑动事件冲突问题", "status": "MERGED", "created": 1749122411, "updated": 1749122883, "mergeable": false, "number": 4716, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/4716", "patchset_count": 1, "commit_id": "54f406e9aa79cc04a62f3ef8dc60a1c3c2ccf540", "insertions": 11, "deletions": 1}, {"change_id": "I39a65ff94e9281d6f96ae0fcb842fca36e2058ad", "project": "qw_platform", "branch": "develop", "subject": "ppg_test: hr capture test,disable hrv ini for dvtv2.", "status": "ABANDONED", "created": 1749104987, "updated": 1749119294, "mergeable": false, "number": 4703, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/4703", "patchset_count": 5, "commit_id": "99b9aa286c3d05a8692d3e8789018ad77df46818", "insertions": 12194, "deletions": 45}, {"change_id": "I0dfc20e3999baa5f29ffcacd0e06b8a4c3e9ffa1", "project": "qw_platform", "branch": "develop", "subject": "ppg: update ppg parse tools && set XYZ OFFSET TO 0", "status": "MERGED", "created": 1749116829, "updated": 1749118698, "mergeable": false, "number": 4714, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/4714", "patchset_count": 4, "commit_id": "1a991db8f48b3ec0e4ad801459a45923be64d91a", "insertions": 12174, "deletions": 41}, {"change_id": "I2a5e9460f37c7c538d7d67617cbf0063452480da", "project": "qw_platform", "branch": "develop", "subject": "[GUI]:更新字库的字符", "status": "MERGED", "created": 1749114724, "updated": 1749115578, "mergeable": false, "number": 4712, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4712", "patchset_count": 2, "commit_id": "2546013e95063db35b7a4ea68e0e34505e291657", "insertions": 86, "deletions": 26}, {"change_id": "Ife6abbe90333350477f05a3fe394368a21a41186", "project": "app", "branch": "develop", "subject": "[GUI]: 更新固件版本号到0.80. 更新字库版本到122.", "status": "MERGED", "created": 1749114981, "updated": 1749115568, "mergeable": false, "number": 4713, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4713", "patchset_count": 1, "commit_id": "2e0a6b12cec2f2f619faf8347b84a6825437d07d", "insertions": 12, "deletions": 2}, {"change_id": "I7e8e8ae4963a41968a85b17fbddd120e981050b0", "project": "cycle/c202310_bg1", "branch": "Develop", "subject": "[Fix]: 解决三方ANT雷达车灯连接问题", "status": "MERGED", "created": 1749114078, "updated": 1749114352, "mergeable": false, "number": 4711, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/c202310_bg1/+/4711", "patchset_count": 1, "commit_id": "06474dec196c88bc0fa612fbaab32b006d074f55", "insertions": 35, "deletions": 22}, {"change_id": "Iaa3f62e8d5d012020d92c3eec7e70f2aad9a3b12", "project": "app", "branch": "develop", "subject": "[bug]: 修正若干bug", "status": "MERGED", "created": 1749106927, "updated": 1749113473, "mergeable": false, "number": 4710, "owner": "lixin", "url": "http://********:8081/c/app/+/4710", "patchset_count": 4, "commit_id": "80150f58852347888b75f22dbd1e8f0e2204b1b6", "insertions": 1548, "deletions": 1437}, {"change_id": "Iaa3f62e8d5d012020d92c3eec7e70f2aad9a3b12", "project": "qw_platform", "branch": "develop", "subject": "[rlottie]: 修正骑行业务线同步的一个bug", "status": "MERGED", "created": 1749106822, "updated": 1749113473, "mergeable": false, "number": 4709, "owner": "lixin", "url": "http://********:8081/c/qw_platform/+/4709", "patchset_count": 2, "commit_id": "3a83030e3892622fbf74542a22d4bbdf3bd9aaa1", "insertions": 17, "deletions": 4}, {"change_id": "I7a61e42579349bf1769f7583c8eea89d17b43204", "project": "sifli", "branch": "develop", "subject": "gpio2_irq: assert if enable pbio irq in hcpu", "status": "MERGED", "created": 1749000650, "updated": 1749112867, "mergeable": false, "number": 4674, "owner": "linjizhao", "url": "http://********:8081/c/sifli/+/4674", "patchset_count": 4, "commit_id": "0a12890858e7c7c2a85d4e9b82df91cef148593f", "insertions": 12, "deletions": 0}, {"change_id": "Ie251a043437e17a6581cdff85d577810243a8b7d", "project": "app", "branch": "develop", "subject": "ui:页面显示适应多语言", "status": "MERGED", "created": 1749105103, "updated": 1749109514, "mergeable": false, "number": 4705, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4705", "patchset_count": 3, "commit_id": "3fdbf78bcec37be78c2a63159d7308a855461f94", "insertions": 135, "deletions": 65}, {"change_id": "Ie251a043437e17a6581cdff85d577810243a8b7d", "project": "qw_platform", "branch": "develop", "subject": "ui:页面显示适应多语言", "status": "MERGED", "created": 1749105047, "updated": 1749109514, "mergeable": false, "number": 4704, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4704", "patchset_count": 2, "commit_id": "301682d1a3eb6ebe25b399abcfc938e756954b58", "insertions": 31, "deletions": 13}, {"change_id": "I7c78559eae8b150cde2930b5fe0dbca6d9cf1a2c", "project": "app", "branch": "develop", "subject": "[gui]: 增加训练计划中副标题非时间的显示", "status": "MERGED", "created": 1749102965, "updated": 1749109507, "mergeable": false, "number": 4700, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4700", "patchset_count": 2, "commit_id": "240e44897cb4f76192b71543d8929142e319baf0", "insertions": 20, "deletions": 2}, {"change_id": "I7fbd54f7960b9f931a9cb4c97fe545685ff22bdb", "project": "app", "branch": "develop", "subject": "alg_fmk[18038]:调整心率和ppi打开duty以及去掉置信度判断", "status": "MERGED", "created": 1749089764, "updated": 1749108452, "mergeable": false, "number": 4697, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/app/+/4697", "patchset_count": 3, "commit_id": "618b6992251d9f6a6c7d7569bcd4f2ef382f2b5e", "insertions": 25, "deletions": 6}, {"change_id": "Ia98d702d9391e0c14b17e16aa4ca7220bd137298", "project": "app", "branch": "develop", "subject": "[fix]: 解决app端删除活动文件的同时，手表端活动文件也会被移除的问题", "status": "MERGED", "created": 1749105252, "updated": 1749107152, "mergeable": false, "number": 4706, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4706", "patchset_count": 2, "commit_id": "2757e10febe3cb21e0da215a11a4cccfced6117a", "insertions": 11, "deletions": 1}, {"change_id": "I1e5f33d7c2a553586831d62902c314c5c8bed8be", "project": "sifli", "branch": "develop", "subject": "[ble]: 在ble协议栈底层跨核通信过程中增加异常日志、重试与线程释放逻辑，防止因触发SDK原始异常处理代码而导致的线程卡住问题", "status": "MERGED", "created": 1749093748, "updated": 1749107132, "mergeable": false, "number": 4698, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/sifli/+/4698", "patchset_count": 2, "commit_id": "5267eed5c89d0f099a0aa8b486e22efbce55e344", "insertions": 45, "deletions": 1}, {"change_id": "I3efa20cc3f8545845274d5ce0b377fa85aeff3f1", "project": "sifli", "branch": "develop", "subject": "[sensor]: 优化修改传感器模块消息队列模式，防止因队列空间不足导致的异常", "status": "MERGED", "created": 1749040607, "updated": 1749107092, "mergeable": false, "number": 4693, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/sifli/+/4693", "patchset_count": 1, "commit_id": "fef652d6b9da72c6099e570122161a6723179487", "insertions": 13, "deletions": 3}, {"change_id": "I1124a5c946934f2c2b55c6947d019d5d7687fb41", "project": "app", "branch": "develop", "subject": "GUI: 回退运动结算页网格折线图缓存修改", "status": "MERGED", "created": 1749022984, "updated": 1749107082, "mergeable": false, "number": 4686, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4686", "patchset_count": 3, "commit_id": "0d2d308a508257aafc2854892a74246862d1fe0e", "insertions": 12, "deletions": 12}, {"change_id": "If5dbed4dfbdd4b1c6ac66b98c18b5ffa54f9ae8e", "project": "qw_platform", "branch": "develop", "subject": "PLATFORM[BUG-001]: add daily activity data fit packaging function", "status": "MERGED", "created": 1749104006, "updated": 1749105087, "mergeable": false, "number": 4701, "owner": "hongxing", "url": "http://********:8081/c/qw_platform/+/4701", "patchset_count": 2, "commit_id": "37077e766ff4c0d0a468d5ea3cd56bfaa2199b07", "insertions": 173, "deletions": 5}, {"change_id": "If5dbed4dfbdd4b1c6ac66b98c18b5ffa54f9ae8e", "project": "app", "branch": "develop", "subject": "SERVICE[BUG-001]: add logic to sync heartrate data to app", "status": "MERGED", "created": 1749104013, "updated": 1749105086, "mergeable": false, "number": 4702, "owner": "hongxing", "url": "http://********:8081/c/app/+/4702", "patchset_count": 2, "commit_id": "2d070ee2dece6ca2227bcad6cc3ac2d251298c71", "insertions": 106, "deletions": 9}, {"change_id": "Ie0550bdcbc3b9b47bac7e1713d475332d348d553", "project": "qw_platform", "branch": "develop", "subject": "ppg_test: disable hrv && spo2,enable mem check.", "status": "ABANDONED", "created": 1748957530, "updated": 1749104994, "mergeable": false, "number": 4667, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/4667", "patchset_count": 5, "commit_id": "9293f0ab4f6fbc41ce7d5f050f4a52b421df93df", "insertions": 602, "deletions": 13}, {"change_id": "Ie0550bdcbc3b9b47bac7e1713d475332d348d553", "project": "app", "branch": "develop", "subject": "ppg_test: set hr cap default enable if capture_ppg.", "status": "ABANDONED", "created": 1748998203, "updated": 1749104743, "mergeable": false, "number": 4672, "owner": "linjizhao", "url": "http://********:8081/c/app/+/4672", "patchset_count": 5, "commit_id": "a1356ccf812715d15880d17d9f17364607de4beb", "insertions": 27, "deletions": 3}, {"change_id": "I7d75d2016e3a125d68e2701ce3305b06e32ae54c", "project": "platform/external/proto", "branch": "master", "subject": "[proto]: 根据产品需求，移除其它运动相关字段，新增游泳运动相关字段", "status": "MERGED", "created": 1748934711, "updated": 1749103686, "mergeable": false, "number": 4656, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/platform/external/proto/+/4656", "patchset_count": 2, "commit_id": "d17eec18749b67c8f2a409afbc68502f65c798fc", "insertions": 18, "deletions": 7}, {"change_id": "I6798fd926d00b2d0ddd23f6145a91f4cc89d95c9", "project": "app", "branch": "develop", "subject": "[gui]: 修改泳道设置，自定义字段右对齐的问题", "status": "MERGED", "created": 1749086381, "updated": 1749102142, "mergeable": false, "number": 4696, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4696", "patchset_count": 3, "commit_id": "0213f2c24fcfa487b390d553d6e529e23f8b1071", "insertions": 11, "deletions": 0}, {"change_id": "I7b2e5c262ca9ab845a9bf90594f529ffa3df0324", "project": "qw_platform", "branch": "develop", "subject": "[gui]: 将跑步效率的零值过滤掉", "status": "MERGED", "created": 1749085857, "updated": 1749102117, "mergeable": false, "number": 4694, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4694", "patchset_count": 1, "commit_id": "89af8d4c0d5f5dedb52afb434d8d20b9c71e768c", "insertions": 11, "deletions": 1}, {"change_id": "I413bf603783d5e9fbe91126d957252fc3924dc54", "project": "app", "branch": "develop", "subject": "develop: update hr raw data capture state if register callback", "status": "MERGED", "created": **********, "updated": **********, "mergeable": false, "number": 4699, "owner": "linjizhao", "url": "http://********:8081/c/app/+/4699", "patchset_count": 2, "commit_id": "70cd6dc6aafdf40306234b42785b9830110f57c2", "insertions": 26, "deletions": 2}, {"change_id": "I025f5b8f184bc01d98dda1bb46d082eb133a7d28", "project": "app", "branch": "develop", "subject": "SERVICE[BUG-001]: correct the judgment conditions when storing health files", "status": "MERGED", "created": **********, "updated": **********, "mergeable": false, "number": 4695, "owner": "hongxing", "url": "http://********:8081/c/app/+/4695", "patchset_count": 2, "commit_id": "b0660e74c8295f44081f24537402e605ccc00b45", "insertions": 21, "deletions": 8}, {"change_id": "I6a3dd235ab954e0de641eaf0eb8cc91e887e034d", "project": "cycle/c202310_bg1", "branch": "Develop", "subject": "[Fix]: 优化lrucache实现", "status": "MERGED", "created": **********, "updated": **********, "mergeable": false, "number": 4692, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/c202310_bg1/+/4692", "patchset_count": 1, "commit_id": "1666b2c7dd35c9aa7649199e69a96b79269ee826", "insertions": 11, "deletions": 1}, {"change_id": "Id57246bf182b9c4035c54ce44b94315d08a51978", "project": "cycle/c202310_bg1", "branch": "Develop", "subject": "[Fix]: 优化GPS星历应用", "status": "MERGED", "created": **********, "updated": **********, "mergeable": false, "number": 4671, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/c202310_bg1/+/4671", "patchset_count": 1, "commit_id": "b1cc6fd6973029da8b2ed68115a5459989212c95", "insertions": 842, "deletions": 928}, {"change_id": "I5a256db66895b132f03401ec0c5dad9a69842368", "project": "cycle/c202310_bg1", "branch": "Develop", "subject": "[Fix]: 优化隧道补偿实现", "status": "MERGED", "created": **********, "updated": 1749038637, "mergeable": false, "number": 4670, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/c202310_bg1/+/4670", "patchset_count": 1, "commit_id": "f02eb901aeb3e6feedae4a86cafef52d4f06f7a4", "insertions": 68, "deletions": 16}, {"change_id": "I7676423043682ce6c8a0049af3a7c2386b852d1b", "project": "cycle/c202310_bg1", "branch": "Develop", "subject": "[Fix]: 解决训练使用继续骑行导致fit异常问题", "status": "MERGED", "created": **********, "updated": 1749038634, "mergeable": false, "number": 4669, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/c202310_bg1/+/4669", "patchset_count": 1, "commit_id": "a40480e8f1282ab22ac3a28f900774e64e58ad37", "insertions": 52, "deletions": 2}, {"change_id": "Ie4f84025b27f914c2151c667d89b5ec47a1953eb", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 解决UI组件问题", "status": "MERGED", "created": 1749036758, "updated": 1749038248, "mergeable": false, "number": 4691, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/4691", "patchset_count": 2, "commit_id": "13538f64f5eb0b3419309779a99a8fb46c07d0b7", "insertions": 18, "deletions": 3}, {"change_id": "I9e56477cd187068321fbd52f986e208cfb0f8c83", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Optimize]: 优化雷达警示提醒", "status": "MERGED", "created": 1749030894, "updated": 1749036893, "mergeable": false, "number": 4690, "owner": "liquan", "url": "http://********:8081/c/cycle/app/+/4690", "patchset_count": 3, "commit_id": "2122d8e2e32fa40691ae1f29849ffa990c78c88e", "insertions": 12, "deletions": 2}, {"change_id": "Idead835a59002a5ff89fa561ddad689c3048b591", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Feature]: 新增工厂界面检测GM算法", "status": "MERGED", "created": **********, "updated": **********, "mergeable": false, "number": 4687, "owner": "liquan", "url": "http://********:8081/c/cycle/app/+/4687", "patchset_count": 3, "commit_id": "66b8831310c2235b299fd5e2e981a11da7bbfb6d", "insertions": 163, "deletions": 9}, {"change_id": "I78983f14adfa4b3214a67371bf1232b3bacedb83", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 1. 更新GoMore SDK，解决特定批次license无效的问题；", "status": "MERGED", "created": **********, "updated": **********, "mergeable": false, "number": 4685, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/4685", "patchset_count": 1, "commit_id": "a76763c318be15821ba193009d97c348d53ccf90", "insertions": 10, "deletions": 0}, {"change_id": "Ieab59fe6ea485ec63779c528c0b8a6ff403492f6", "project": "app", "branch": "develop", "subject": "SERVICE[BUG-001]: add a restriction that HRV data will only be displayed when there are 7 or more segments of long sleep", "status": "MERGED", "created": **********, "updated": **********, "mergeable": false, "number": 4689, "owner": "hongxing", "url": "http://********:8081/c/app/+/4689", "patchset_count": 2, "commit_id": "904362a5b3f3e85f781c1fe94681621d871961ee", "insertions": 15, "deletions": 2}, {"change_id": "I314a9187c9531e163d2cc8462fd00c1cc35773bb", "project": "qw_platform", "branch": "develop", "subject": "fit: 解决步频fit存储不正确的问题", "status": "MERGED", "created": 1749021436, "updated": 1749022152, "mergeable": false, "number": 4684, "owner": "yukai", "url": "http://********:8081/c/qw_platform/+/4684", "patchset_count": 1, "commit_id": "f5b78506ce47e159b680028777ddea6f09f91f09", "insertions": 66, "deletions": 24}, {"change_id": "I314a9187c9531e163d2cc8462fd00c1cc35773bb", "project": "app", "branch": "develop", "subject": "sports: 解决步频fit存储不正确的问题 解决泳池游泳卡路里数据等未接入的问题 解决泳池游泳运动结算页配速单位不正确的问题", "status": "MERGED", "created": 1749021426, "updated": 1749022151, "mergeable": false, "number": 4683, "owner": "yukai", "url": "http://********:8081/c/app/+/4683", "patchset_count": 2, "commit_id": "d18254cae0f9a731ab2a34f6e3ad39bbdba765c2", "insertions": 118, "deletions": 56}, {"change_id": "I82dcf11d8dc9dbbd4217a6fbe18c2468b55e6336", "project": "wearable/hr50", "branch": "develop", "subject": "hr50:优化代码结构，加入重启测试代码", "status": "MERGED", "created": 1748999675, "updated": 1749020997, "mergeable": false, "number": 4673, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/wearable/hr50/+/4673", "patchset_count": 2, "commit_id": "d969ec9492af5cd347bf91d015b79234b5419ab1", "insertions": 12108, "deletions": 6925}, {"change_id": "Iebd14d786cd147c47ead601c2231a204a65724ff", "project": "app", "branch": "develop", "subject": "SERVICE[BUG-001]: solve the problem that multi-segment HRV data cannot be merged, leading to incorrect calculation of average values", "status": "MERGED", "created": 1749020052, "updated": 1749020516, "mergeable": false, "number": 4681, "owner": "hongxing", "url": "http://********:8081/c/app/+/4681", "patchset_count": 2, "commit_id": "5862453ebdf5755d5a828dd28c92520f2a512387", "insertions": 38, "deletions": 3}, {"change_id": "I23090bb5af8e5074c49851931593c09bbc119d3a", "project": "app", "branch": "develop", "subject": "[fix]: 修复跨核通信时，未传入任何data的问题，防止出现跨核通信数据错位", "status": "MERGED", "created": 1749017714, "updated": 1749019601, "mergeable": false, "number": 4679, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4679", "patchset_count": 2, "commit_id": "868471823350d5fe01bc09f715c8f4387a7077fa", "insertions": 30, "deletions": 4}, {"change_id": "Ibcf3e9ef69898456dbf905d75b064b1250480dae", "project": "app", "branch": "develop", "subject": "[algo]: 修正速度区间显示问题", "status": "MERGED", "created": 1749018377, "updated": 1749018796, "mergeable": false, "number": 4680, "owner": "lixin", "url": "http://********:8081/c/app/+/4680", "patchset_count": 1, "commit_id": "82672c94fd121a2962ed46c24c37a5449278a4ea", "insertions": 15, "deletions": 0}, {"change_id": "Ib065ed5b8ca4801052bfdd59771182890682615a", "project": "app", "branch": "develop", "subject": "[GUI]: 解决GPS配置页面bug", "status": "MERGED", "created": 1749008109, "updated": 1749017926, "mergeable": false, "number": 4678, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4678", "patchset_count": 2, "commit_id": "2a5efd6933209a0317f5136c428bdf3210215cfd", "insertions": 15, "deletions": 2}, {"change_id": "Iec5da867a7373d7fcc82fc6689634b4d5a2e02b8", "project": "app", "branch": "develop", "subject": "[fix]: 解决天气更新无法在应用卡片显示天气的问题", "status": "MERGED", "created": 1749002758, "updated": 1749017891, "mergeable": false, "number": 4675, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4675", "patchset_count": 3, "commit_id": "e8ca27eee9c56a0737729fd0ede0634fa76b7fde", "insertions": 11, "deletions": 1}, {"change_id": "I71ef8ed46699ca9f1271c099cedfc5883541fd0e", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Fix]: 修复状态页显示功耗高的问题以及状态页相关显示问题", "status": "MERGED", "created": 1749003487, "updated": 1749004520, "mergeable": false, "number": 4676, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/4676", "patchset_count": 1, "commit_id": "71f02a473decbd6a06239ae366f50c8cc1649977", "insertions": 48, "deletions": 20}, {"change_id": "Ie95a228ebad1259b6dbb075c2f950b53078e1203", "project": "qw_platform", "branch": "develop", "subject": "[GUI]: 增加传感器测试工具", "status": "MERGED", "created": 1748957019, "updated": 1749004336, "mergeable": false, "number": 4666, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4666", "patchset_count": 1, "commit_id": "9d5dbe8abf01129deb6c827db466902c7483cee6", "insertions": 26, "deletions": 2}, {"change_id": "Ie95a228ebad1259b6dbb075c2f950b53078e1203", "project": "app", "branch": "develop", "subject": "[GUI]:增加传感器测试工具", "status": "MERGED", "created": 1748957010, "updated": 1749004336, "mergeable": false, "number": 4665, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4665", "patchset_count": 5, "commit_id": "63f65a66e23e1f16501d12b99540df4cd34f33d6", "insertions": 2568, "deletions": 20}, {"change_id": "Ieff544284b1b6a302a1fc56ad56b191fcc25bf62", "project": "cycle/app", "branch": "bg2_develop", "subject": "[Build]: 更新版本至v1.15_beta2", "status": "MERGED", "created": 1748526288, "updated": 1749000140, "mergeable": false, "number": 4626, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/cycle/app/+/4626", "patchset_count": 1, "commit_id": "099d72c88d8c122f2af042b43e05d5ab9e37e76c", "insertions": 11, "deletions": 1}, {"change_id": "I5b4ccdfc72b9c410130fc1a32e35169b55ef750c", "project": "qw_platform", "branch": "develop", "subject": "Revert \"[GPS]: 读取数据逻辑修改，由唤醒中断触发\"", "status": "ABANDONED", "created": 1748600289, "updated": 1748997664, "mergeable": false, "number": 4454, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/4454", "patchset_count": 3, "commit_id": "906da590338a51e01bbf12eaef3a90fb1b211ced", "insertions": 42, "deletions": 40}, {"change_id": "If3dd2b0d7fe8de39c0a5fd8c5aa8ac011d153ab7", "project": "app", "branch": "develop", "subject": "dev: 默认写入硬件版本号", "status": "NEW", "created": 1748339415, "updated": 1748991752, "mergeable": false, "number": 4256, "owner": "jinx<PERSON><PERSON>", "url": "http://********:8081/c/app/+/4256", "patchset_count": 1, "commit_id": "7eee7fe25bf6641713a5e90f1edfe994c0061ba0", "insertions": 42, "deletions": 32}, {"change_id": "Ia44da0ec328596a0856f6c7fc8d1e155a33e5ce7", "project": "app", "branch": "develop", "subject": "ota: 添加固件更新设计文档", "status": "NEW", "created": 1748415318, "updated": 1748991751, "mergeable": false, "number": 4257, "owner": "jinx<PERSON><PERSON>", "url": "http://********:8081/c/app/+/4257", "patchset_count": 1, "commit_id": "888127a8d7f6dfe0ccf8bdc74035dcb29784e493", "insertions": 440, "deletions": 0}, {"change_id": "Ief435fcbc40b50cb94a444a007e798e5f1eb4c74", "project": "qw_platform", "branch": "develop", "subject": "[test]: 禁用充电保护", "status": "NEW", "created": 1748572215, "updated": 1748991745, "mergeable": false, "number": 4628, "owner": "lixiaolong", "url": "http://********:8081/c/qw_platform/+/4628", "patchset_count": 1, "commit_id": "4beed46c05875bd6cc430c12d725da46814a0376", "insertions": 11, "deletions": 1}, {"change_id": "I437a6962bc4b8972517d6458e876138e4614b392", "project": "app", "branch": "develop", "subject": "[algo]: 修正一些运动算法问题", "status": "MERGED", "created": 1748958034, "updated": 1748958952, "mergeable": false, "number": 4668, "owner": "lixin", "url": "http://********:8081/c/app/+/4668", "patchset_count": 2, "commit_id": "8b66f61d3f6d87b567c82b4764a77bc3e5ef236f", "insertions": 68, "deletions": 23}, {"change_id": "I3ec47415031cb2280000fb341db669069fb423da", "project": "app", "branch": "develop", "subject": "bug:修改运动准备页间歇训练显示bug", "status": "MERGED", "created": 1748940291, "updated": 1748947856, "mergeable": false, "number": 4661, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4661", "patchset_count": 2, "commit_id": "fa8cc000daede6269beff7c2d4842b10097363d3", "insertions": 21, "deletions": 7}, {"change_id": "I096c9971a600eddf23f6ff96cbbf0971f79899c9", "project": "app", "branch": "develop", "subject": "[fix]: 解决前一次过滤legacy ant传感器时可能会把正常ant传感器也给过滤掉的问题", "status": "MERGED", "created": 1748938450, "updated": 1748947821, "mergeable": false, "number": 4660, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4660", "patchset_count": 2, "commit_id": "83231e92bab9cce4caf2dade0baf7dc11c20c25d", "insertions": 13, "deletions": 1}, {"change_id": "Id4d35001cd7cfe73c605402bde671a40b2d13e6f", "project": "app", "branch": "develop", "subject": "[心率推送]: 新增心率推送模拟出值接口", "status": "MERGED", "created": 1748929198, "updated": 1748947811, "mergeable": false, "number": 4650, "owner": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4650", "patchset_count": 3, "commit_id": "44f889599d853ab937ff8b2d5e8f4fcff558f663", "insertions": 155, "deletions": 18}, {"change_id": "Ia900537ed2e48c1785ee931a98cc048c011243fb", "project": "qw_platform", "branch": "develop", "subject": "algo_capture: add ppg_parge tools", "status": "MERGED", "created": 1748935425, "updated": 1748947751, "mergeable": false, "number": 4658, "owner": "linjizhao", "url": "http://********:8081/c/qw_platform/+/4658", "patchset_count": 3, "commit_id": "56276f2cb3b1d71536c8b6e64af7c2670eaaa4ad", "insertions": 21377, "deletions": 2}, {"change_id": "I3131eea9ce7772785476ae0f28c67310289eb8b5", "project": "app", "branch": "develop", "subject": "[AOD]: 添加运动中AOD模式", "status": "MERGED", "created": 1748946123, "updated": 1748947444, "mergeable": false, "number": 4664, "owner": "lixin", "url": "http://********:8081/c/app/+/4664", "patchset_count": 1, "commit_id": "c8ba0329a3c18d4ad14ad42a667c411dcdd32bda", "insertions": 79, "deletions": 3}, {"change_id": "I3131eea9ce7772785476ae0f28c67310289eb8b5", "project": "qw_platform", "branch": "develop", "subject": "[msg]: 修正快速放弃记录后再开始运动偶现死机问题", "status": "MERGED", "created": 1748946085, "updated": 1748947444, "mergeable": false, "number": 4663, "owner": "lixin", "url": "http://********:8081/c/qw_platform/+/4663", "patchset_count": 1, "commit_id": "3c4a67274466f11fc802ff91bc46194b162651df", "insertions": 35, "deletions": 3}, {"change_id": "I321f2e2d415ecd33b4bb156dd0827afd3593ebc6", "project": "tools", "branch": "develop", "subject": "alg_fmk:数据倒灌csv预处理脚本", "status": "MERGED", "created": 1748598891, "updated": 1748933696, "mergeable": false, "number": 4638, "owner": "chen<PERSON><PERSON>", "url": "http://********:8081/c/tools/+/4638", "patchset_count": 2, "commit_id": "18115453ad6d5a37e228b0b8a0d48feefd5344ee", "insertions": 315, "deletions": 0}, {"change_id": "I0fe3a2f62ca03d52222a635b517d7b9e2ea593eb", "project": "qw_platform", "branch": "develop", "subject": "[GPS]: 1. 时间校准更换API 2. 增加唤醒接口初始化", "status": "MERGED", "created": 1748931667, "updated": 1748933652, "mergeable": false, "number": 4654, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4654", "patchset_count": 1, "commit_id": "46bccbcdd9523e35748af5e32be3ea25eca8c629", "insertions": 25, "deletions": 8}, {"change_id": "I0fe3a2f62ca03d52222a635b517d7b9e2ea593eb", "project": "app", "branch": "develop", "subject": "[GPS]: 1. 时间校准更换API 2. 增加唤醒接口初始化", "status": "MERGED", "created": 1748931655, "updated": 1748933651, "mergeable": false, "number": 4653, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4653", "patchset_count": 1, "commit_id": "18936549ef45e1c33d68e9b69b84e09cc3a0c1c1", "insertions": 16, "deletions": 4}, {"change_id": "I0fe3a2f62ca03d52222a635b517d7b9e2ea593eb", "project": "sifli", "branch": "develop", "subject": "[GPS]: 1. 时间校准更换API 2. 增加唤醒接口初始化", "status": "MERGED", "created": 1748931618, "updated": 1748933651, "mergeable": false, "number": 4652, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/sifli/+/4652", "patchset_count": 1, "commit_id": "dd282af378914b75df11335fd3bc12fb0a2febe5", "insertions": 10, "deletions": 1}, {"change_id": "Iff0f2b18e8de3a841bf1364ab97e052caf107a00", "project": "app", "branch": "develop", "subject": "sports: 解决游泳结束运动后再开始运动记录不正确的问题", "status": "MERGED", "created": 1748863433, "updated": 1748915406, "mergeable": false, "number": 4648, "owner": "yukai", "url": "http://********:8081/c/app/+/4648", "patchset_count": 1, "commit_id": "fd1fb7b445c9a6a8408046bfc9c9fc874be6c80e", "insertions": 61, "deletions": 57}, {"change_id": "I9fea74ab599c10daf0c7e62560a11e688a52e8c9", "project": "app", "branch": "develop", "subject": "sports: 解决泳池游泳距离和时间不正确的问题", "status": "MERGED", "created": 1748610238, "updated": 1748915405, "mergeable": false, "number": 4647, "owner": "yukai", "url": "http://********:8081/c/app/+/4647", "patchset_count": 1, "commit_id": "44db8fa5c87d7e1fc337cdbe41e7937d886b7b45", "insertions": 20, "deletions": 7}, {"change_id": "I5b4ccdfc72b9c410130fc1a32e35169b55ef750c", "project": "sifli", "branch": "develop", "subject": "[GPS]: 中断压测", "status": "ABANDONED", "created": 1748600931, "updated": 1748912756, "mergeable": false, "number": 4644, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/sifli/+/4644", "patchset_count": 1, "commit_id": "2874e3ece83e1959a0103d6bb0e2624eee7abf47", "insertions": 33, "deletions": 58}, {"change_id": "I5b4ccdfc72b9c410130fc1a32e35169b55ef750c", "project": "app", "branch": "develop", "subject": "[GPS]: 中断压测", "status": "ABANDONED", "created": 1748600944, "updated": 1748912752, "mergeable": false, "number": 4645, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4645", "patchset_count": 2, "commit_id": "f0e49b7f9918ced5f5542729182845cf353091ba", "insertions": 21, "deletions": 4}, {"change_id": "I1fa4bfc67f1c662c901a1c71eabe9e5d5878213c", "project": "qw_platform", "branch": "develop", "subject": "[LOG]: 目录调整", "status": "ABANDONED", "created": 1748402260, "updated": 1748912746, "mergeable": false, "number": 4563, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/qw_platform/+/4563", "patchset_count": 3, "commit_id": "198cf72180aaf8ad4c611faed5cd1374e1233ae9", "insertions": 12, "deletions": 1}, {"change_id": "I1fa4bfc67f1c662c901a1c71eabe9e5d5878213c", "project": "sifli", "branch": "develop", "subject": "[LOG]: 目录调整", "status": "ABANDONED", "created": 1748425488, "updated": 1748912740, "mergeable": false, "number": 4579, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/sifli/+/4579", "patchset_count": 1, "commit_id": "140a051e8e81849037033bdf00ca9bc64fdd6070", "insertions": 11, "deletions": 1}, {"change_id": "I1fa4bfc67f1c662c901a1c71eabe9e5d5878213c", "project": "app", "branch": "develop", "subject": "[LOG]: 目录调整", "status": "ABANDONED", "created": 1748421781, "updated": 1748912734, "mergeable": false, "number": 4577, "owner": "wa<PERSON><PERSON><PERSON><PERSON>", "url": "http://********:8081/c/app/+/4577", "patchset_count": 2, "commit_id": "2a40ad9cb87f5122b2b2ee2d6971c1c84cf96da3", "insertions": 11, "deletions": 1}]
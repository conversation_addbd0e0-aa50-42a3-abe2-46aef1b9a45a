#!/bin/bash

# 从参数获取基线文件路径
BASELINE_FILE=$2
OUTPUT_FILE="size_check_result.txt"
RESULT_FILE="jenkins_result.txt"

# 清空输出文件并写入标题
echo "'## Size Check'" > "$OUTPUT_FILE"
echo "'## 检查文件: $1'" >> "$OUTPUT_FILE"
echo "" >> "$OUTPUT_FILE"

# 创建输出函数，同时输出到终端和文件
output() {
    # 终端显示不带引号的内容
    echo -e "$1"
    # 文件中写入带引号的内容
    echo "'$1'" >> "$OUTPUT_FILE"
}

# 只输出到终端的函数
terminal_output() {
    echo -e "$1"
}

# 添加空行
output_blank_line() {
    echo "" >> "$OUTPUT_FILE"
}

output_nonl() {
    # 终端显示不带引号的内容
    echo -n "$1"
    # 文件中写入带引号的内容（不换行）
    echo -n "'$1" >> "$OUTPUT_FILE"
}

# 输出check_exceed结果到文件
output_check_result() {
    local result
    result=$(check_exceed "$1")
    local status=$?
    echo -n "$result"
    echo "$result'" >> "$OUTPUT_FILE"
    return $status
}

# 设置Jenkins环境变量
set_jenkins_result() {
    local result=$1
    local reason=$2
    # 实际设置环境变量，供Jenkins使用
    export SIZE_CHECK_RESULT=$result
    export SIZE_CHECK_REASON="$reason"
    # 同时输出到文件，方便查看
    echo "SIZE_CHECK_RESULT=$result" >> "$RESULT_FILE"
}

# 从基线文件读取数据
read_baseline() {
    if [ -f "$BASELINE_FILE" ]; then
        ER_IROM1_BASELINE=$(sed -n '1p' "$BASELINE_FILE")
        RW_PSRAM1_BASELINE=$(sed -n '2p' "$BASELINE_FILE")
        RW_IRAM1_BASELINE=$(sed -n '3p' "$BASELINE_FILE")
    fi
}

# 保存基线数据到文件
save_baseline() {
    local er_size=$1
    local rw_psram_size=$2
    local rw_iram_size=$3
    echo "$er_size" > "$BASELINE_FILE"
    echo "$rw_psram_size" >> "$BASELINE_FILE"
    echo "$rw_iram_size" >> "$BASELINE_FILE"
    echo "已将当前大小保存为新的基线数据"
}

# 检查参数数量
if [ $# -ne 2 ] && [ $# -ne 5 ]; then
    echo "Usage: $0 <map_file> <baseline_file> [<er_irom1_baseline> <rw_psram1_baseline> <rw_iram1_baseline>]"
    echo "Example 1 (使用基线文件): $0 WR02_App.map size_baseline.txt"
    echo "Example 2 (使用命令行参数): $0 WR02_App.map size_baseline.txt 5000000 7000000 2000000"
    exit 1
fi

MAP_FILE=$1

# 如果没有提供基线参数，尝试从文件读取
if [ $# -eq 2 ]; then
    if [ ! -f "$BASELINE_FILE" ]; then
        echo "错误：未找到基线文件 '$BASELINE_FILE'，首次运行请提供基线参数"
        exit 1
    fi
    read_baseline
else
    ER_IROM1_BASELINE=$3
    RW_PSRAM1_BASELINE=$4
    RW_IRAM1_BASELINE=$5
fi

# 检查文件是否存在
if [ ! -f "$MAP_FILE" ]; then
    echo "Error: Map file '$MAP_FILE' not found!"
    exit 1
fi

# 提取ER_IROM1大小
ER_IROM1_SIZE_HEX=$(grep "Execution Region ER_IROM1 " "$MAP_FILE" | grep -o "Size: 0x[0-9a-fA-F]\+" | cut -d' ' -f2)
if [ -z "$ER_IROM1_SIZE_HEX" ]; then
    echo "Error: Could not find ER_IROM1 region!"
    exit 1
fi

# 提取RW_PSRAM1大小
RW_PSRAM1_SIZE_HEX=$(grep "Execution Region RW_PSRAM1 " "$MAP_FILE" | grep -o "Size: 0x[0-9a-fA-F]\+" | cut -d' ' -f2)
if [ -z "$RW_PSRAM1_SIZE_HEX" ]; then
    echo "Error: Could not find RW_PSRAM1 region!"
    exit 1
fi

# 提取RW_IRAM1大小
RW_IRAM1_SIZE_HEX=$(grep "Image\$\$RW_IRAM1\$\$ZI\$\$Limit " "$MAP_FILE" | awk '{print $2}')
if [ -z "$RW_IRAM1_SIZE_HEX" ]; then
    echo "Error: Could not find RW_IRAM1 region!"
    exit 1
fi

# 转换十六进制为十进制
ER_IROM1_SIZE=$(printf "%d" $ER_IROM1_SIZE_HEX)
RW_PSRAM1_SIZE=$(printf "%d" $RW_PSRAM1_SIZE_HEX)
RW_IRAM1_SIZE=$(printf "%d" $RW_IRAM1_SIZE_HEX)

# 计算增长大小（字节）
calc_increase() {
    local current=$1
    local baseline=$2
    # 计算绝对增长大小（字节）
    echo $((current - baseline))
}

ER_IROM1_INCREASE=$(calc_increase $ER_IROM1_SIZE $ER_IROM1_BASELINE)
RW_PSRAM1_INCREASE=$(calc_increase $RW_PSRAM1_SIZE $RW_PSRAM1_BASELINE)
RW_IRAM1_INCREASE=$(calc_increase $RW_IRAM1_SIZE $RW_IRAM1_BASELINE)

# 检查是否超过20KB
check_exceed() {
    local increase=$1
    if (( increase > 20480 )); then  # 20KB = 20480 bytes
        echo "是"
        return 1  # 超过限制返回1
    else
        echo "否"
        return 0  # 未超过限制返回0
    fi
}

# 添加时间戳到输出文件
output "----------------------------------------"
#output_blank_line

# 输出结果
output "ER_IROM1 区域"
output "当前大小: $ER_IROM1_SIZE bytes ($ER_IROM1_SIZE_HEX)"
output "基准大小: $ER_IROM1_BASELINE bytes"
output "增长大小: ${ER_IROM1_INCREASE} bytes"
output_nonl "是否超过20KB: "
output_check_result $ER_IROM1_INCREASE
ER_EXCEED=$?
output_blank_line

output "RW_PSRAM1 区域"
output "当前大小: $RW_PSRAM1_SIZE bytes ($RW_PSRAM1_SIZE_HEX)"
output "基准大小: $RW_PSRAM1_BASELINE bytes"
output "增长大小: ${RW_PSRAM1_INCREASE} bytes"
output_nonl "是否超过20KB: "
output_check_result $RW_PSRAM1_INCREASE
RW_PSRAM_EXCEED=$?
output_blank_line

output "RW_IRAM1 区域"
output "当前大小: $RW_IRAM1_SIZE bytes ($RW_IRAM1_SIZE_HEX)"
output "基准大小: $RW_IRAM1_BASELINE bytes"
output "增长大小: ${RW_IRAM1_INCREASE} bytes"
output_nonl "是否超过20KB: "
output_check_result $RW_IRAM1_INCREASE
RW_IRAM_EXCEED=$?
output_blank_line

output "----------------------------------------"

# 如果所有区域都没有超过20KB，保存当前大小作为新的基线
if [ $ER_EXCEED -eq 0 ] && [ $RW_PSRAM_EXCEED -eq 0 ] && [ $RW_IRAM_EXCEED -eq 0 ]; then
    save_baseline $ER_IROM1_SIZE $RW_PSRAM1_SIZE $RW_IRAM1_SIZE
    output "所有区域都在限制范围内，已更新基线文件"
    output "最终结果: 通过"
    output "原因: 所有区域增长都未超过20KB限制"
    set_jenkins_result "PASS" "所有区域增长都未超过20KB限制"
    exit 0
else
    output "警告：存在超过20KB限制的区域，未更新基线文件"
    output "最终结果: 不通过"
    output "原因: "
    
    # 构建失败原因
    FAIL_REASON=""
    
    # 输出具体哪些区域超过限制
    if [ $ER_EXCEED -eq 1 ]; then
        output "- ER_IROM1 区域增长超过20KB限制"
        FAIL_REASON="${FAIL_REASON}ER_IROM1,"
    fi
    if [ $RW_PSRAM_EXCEED -eq 1 ]; then
        output "- RW_PSRAM1 区域增长超过20KB限制"
        FAIL_REASON="${FAIL_REASON}RW_PSRAM1,"
    fi
    if [ $RW_IRAM_EXCEED -eq 1 ]; then
        output "- RW_IRAM1 区域增长超过20KB限制"
        FAIL_REASON="${FAIL_REASON}RW_IRAM1,"
    fi
    
    # 移除最后一个逗号
    FAIL_REASON=${FAIL_REASON%,}
    FAIL_REASON="以下区域超过限制: $FAIL_REASON"
    
    set_jenkins_result "FAIL" "$FAIL_REASON"
    exit 1
fi
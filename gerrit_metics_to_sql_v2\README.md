# Gerrit Patch 与 SonarQube 质量数据集成数据库设计说明

## 设计目标
本数据库用于存储 Gerrit Patch（每次 PatchSet 提交）与其对应的 SonarQube 代码质量快照，实现代码变更与质量数据的自动关联与追溯，便于后续统计分析、质量报告和自动化运维。

## 设计原则
1. **一行一提交**：每一行数据唯一对应一次 Gerrit PatchSet（即一次代码提交及其 SonarQube 快照）。
2. **宽表设计**：Gerrit 与 SonarQube 的主要分析指标全部纳入一张表，方便联表分析和报表生成，减少多表关联的复杂度。
3. **原始数据冗余**：保留原始 JSON，便于后续字段扩展和数据溯源。
4. **高效检索**：为常用查询字段（如 commit_id、author、sonar_key 等）建立索引，提升查询效率。
5. **可扩展性**：如后续 SonarQube 指标有新增，可直接在表结构中扩展字段，或通过 JSONB 字段存储。

## 表结构说明（commit_metrics）
每一行数据代表一次 Gerrit PatchSet（即一次提交+其对应的 SonarQube 快照），字段涵盖 Gerrit 和 SonarQube 的主要信息，便于后续分析和追溯。

| 字段名                  | 类型           | 说明                                                         |
|------------------------|---------------|--------------------------------------------------------------|
| commit_id              | VARCHAR(50)   | Gerrit PatchSet 的唯一标识（如 revision 哈希），主键         |
| change_id              | VARCHAR(50)   | Gerrit Change-Id                                             |
| change_id_short        | VARCHAR(10)   | Change-Id 短码，便于检索                                     |
| patch_set              | INT           | PatchSet 编号                                                |
| gerrit_project         | VARCHAR(200)  | Gerrit 项目名                                                |
| branch                 | VARCHAR(100)  | 分支名                                                       |
| subject                | TEXT          | 提交主题/标题                                                |
| status                 | VARCHAR(20)   | Gerrit 变更状态                                              |
| commit_time            | TIMESTAMPTZ   | PatchSet 提交时间                                            |
| created                | TIMESTAMPTZ   | 变更创建时间                                                 |
| updated                | TIMESTAMPTZ   | 变更最后更新时间                                             |
| author                 | VARCHAR(100)  | 作者名称                                                     |
| owner                  | VARCHAR(100)  | 所有者名称                                                   |
| number                 | INT           | Gerrit 变更号                                                |
| url                    | TEXT          | Gerrit 变更详情 URL                                          |
| reviewers              | TEXT[]        | 评审人列表                                                   |
| changed_lines          | INT           | 变更总行数（插入+删除）                                      |
| insertions             | INT           | 插入行数                                                     |
| deletions              | INT           | 删除行数                                                     |
| patchset_count         | INT           | 补丁集总数                                                   |
| repo_path              | VARCHAR(500)  | 仓库路径                                                     |
| sonar_project          | VARCHAR(200)  | SonarQube 项目名称                                           |
| sonar_key              | VARCHAR(200)  | SonarQube 项目 Key                                           |
| sonar_creation_date    | TIMESTAMPTZ   | SonarQube 项目创建时间                                       |
| sq_blocker             | INT           | 当前阻断级别问题数（Blocker）                                |
| sq_critical            | INT           | 当前严重级别问题数（Critical）                               |
| sq_major               | INT           | 当前主要级别问题数（Major）                                  |
| sq_minor               | INT           | 当前次要级别问题数（Minor）                                  |
| sq_info                | INT           | 当前信息级别问题数（Info）                                   |
| sq_resolved_blocker    | INT           | 已解决阻断级别问题数                                         |
| sq_resolved_critical   | INT           | 已解决严重级别问题数                                         |
| sq_resolved_major      | INT           | 已解决主要级别问题数                                         |
| sq_resolved_minor      | INT           | 已解决次要级别问题数                                         |
| sq_resolved_info       | INT           | 已解决信息级别问题数                                         |
| ncloc                  | INTEGER       | 非注释代码行数（有效代码行数）                               |
| statements             | INTEGER       | 语句数                                                       |
| functions              | INTEGER       | 方法/函数数                                                  |
| files                  | INTEGER       | 文件数                                                       |
| comment_lines          | INTEGER       | 注释行数                                                     |
| comment_lines_density  | FLOAT         | 注释率（百分比）                                             |
| complexity             | INTEGER       | 代码圈复杂度总和                                             |
| duplicated_lines_density| FLOAT        | 重复密度（百分比）                                           |
| duplicated_lines       | INTEGER       | 重复行数                                                     |
| duplicated_blocks      | INTEGER       | 重复块数                                                     |
| duplicated_files       | INTEGER       | 包含重复代码的文件数量                                       |
| gerrit_raw             | JSONB         | Gerrit 原始数据 JSON                                         |
| sonar_issues           | JSONB         | SonarQube 原始问题数据 JSON                                  |
| created_at             | TIMESTAMPTZ   | 记录创建时间                                                 |
| updated_at             | TIMESTAMPTZ   | 记录最后更新时间                                             |

> 详细表结构见 `db_schema.sql`。

## 设计原因说明
- **一行一提交**：每一行数据唯一对应一次 Gerrit PatchSet，便于追溯和统计。
- **宽表设计**：将 Gerrit 和 SonarQube 的主要分析指标全部纳入一张表，方便联表分析和报表生成。
- **原始数据冗余**：保留原始 JSON，便于后续字段扩展和数据溯源。
- **高效检索**：为常用查询字段建立索引，提升查询效率。
- **可扩展性**：如后续 SonarQube 指标有新增，可直接扩展字段，或通过 JSONB 字段存储。

## 扩展建议
- 如需细粒度分析（如评审人、变更文件、评论等），可考虑拆分为子表或以 JSONB 形式存储。
- 若 SonarQube 指标有大幅扩展，可采用宽表+JSONB 混合设计，兼顾查询效率与灵活性。

## 典型查询场景
- 查询某作者所有 PatchSet 的代码质量变化趋势。
- 统计某项目某分支的代码复杂度、重复率等质量指标。
- 追溯某次 PatchSet 的所有 Gerrit 审查与 SonarQube 问题详情。

---
如有更多需求或建议，请在本目录下补充说明。 
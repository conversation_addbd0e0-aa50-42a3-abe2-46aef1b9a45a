{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource-sonar"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource-sonar"}, "format": "table", "rawQuery": true, "rawSql": "WITH trend_comparison AS (\n  SELECT \n    -- 最近7天数据\n    COUNT(CASE WHEN analysis_date >= NOW() - INTERVAL '7 days' THEN 1 END) as recent_analyses,\n    AVG(CASE WHEN analysis_date >= NOW() - INTERVAL '7 days' THEN total_issues END) as recent_avg_issues,\n    AVG(CASE WHEN analysis_date >= NOW() - INTERVAL '7 days' THEN coverage END) as recent_avg_coverage,\n    -- 前7天数据\n    AVG(CASE WHEN analysis_date >= NOW() - INTERVAL '14 days' AND analysis_date < NOW() - INTERVAL '7 days' THEN total_issues END) as prev_avg_issues,\n    AVG(CASE WHEN analysis_date >= NOW() - INTERVAL '14 days' AND analysis_date < NOW() - INTERVAL '7 days' THEN coverage END) as prev_avg_coverage\n  FROM sonaree_wr02 \n  WHERE status = 'active'\n    AND analysis_date >= NOW() - INTERVAL '14 days'\n)\nSELECT \n  recent_analyses as \"最近7天分析次数\",\n  ROUND(recent_avg_issues, 1) as \"最近7天平均问题数\",\n  ROUND(recent_avg_coverage, 2) as \"最近7天平均覆盖率%\",\n  ROUND(((recent_avg_issues - prev_avg_issues) / NULLIF(prev_avg_issues, 0) * 100), 2) as \"问题数变化%\",\n  ROUND(((recent_avg_coverage - prev_avg_coverage) / NULLIF(prev_avg_coverage, 0) * 100), 2) as \"覆盖率变化%\",\n  CASE \n    WHEN recent_avg_issues < prev_avg_issues AND recent_avg_coverage > prev_avg_coverage THEN '改善'\n    WHEN recent_avg_issues > prev_avg_issues AND recent_avg_coverage < prev_avg_coverage THEN '恶化'\n    ELSE '稳定'\n  END as \"总体趋势\"\nFROM trend_comparison", "refId": "A"}], "title": "质量趋势概览 (最近7天 vs 前7天)", "type": "stat"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource-sonar"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource-sonar"}, "format": "time_series", "rawQuery": true, "rawSql": "SELECT \n  analysis_date_only as time,\n  COUNT(DISTINCT project_name) as \"活跃项目数\",\n  COUNT(*) as \"总分析次数\",\n  COUNT(CASE WHEN quality_level = 'clean' THEN 1 END) as \"清洁项目数\",\n  COUNT(CASE WHEN quality_level = 'critical' THEN 1 END) as \"严重问题项目数\"\nFROM sonaree_wr02 \nWHERE analysis_date_only >= CURRENT_DATE - INTERVAL '90 days'\n  AND status = 'active'\nGROUP BY analysis_date_only\nORDER BY analysis_date_only", "refId": "A"}], "title": "项目活跃度趋势 (90天)", "type": "timeseries"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource-sonar"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource-sonar"}, "format": "time_series", "rawQuery": true, "rawSql": "SELECT \n  analysis_date_only as time,\n  ROUND(AVG(coverage), 2) as \"平均覆盖率\",\n  ROUND(AVG(line_coverage), 2) as \"平均行覆盖率\",\n  ROUND(AVG(branch_coverage), 2) as \"平均分支覆盖率\",\n  ROUND(AVG(CASE WHEN coverage >= 80 THEN 100.0 ELSE 0 END), 2) as \"高覆盖率项目占比\"\nFROM sonaree_wr02 \nWHERE analysis_date_only >= CURRENT_DATE - INTERVAL '90 days'\n  AND status = 'active'\n  AND coverage > 0\nGROUP BY analysis_date_only\nORDER BY analysis_date_only", "refId": "A"}], "title": "测试覆盖率趋势 (90天)", "type": "timeseries"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource-sonar"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 14}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource-sonar"}, "format": "time_series", "rawQuery": true, "rawSql": "SELECT \n  analysis_date_only as time,\n  ROUND(AVG(issue_density), 2) as \"平均问题密度\",\n  ROUND(AVG(duplicated_lines_density), 2) as \"平均重复代码密度\",\n  ROUND(AVG(comment_lines_density), 2) as \"平均注释密度\",\n  ROUND(AVG(complexity / NULLIF(ncloc, 0) * 1000), 2) as \"平均复杂度密度\"\nFROM sonaree_wr02 \nWHERE analysis_date_only >= CURRENT_DATE - INTERVAL '90 days'\n  AND status = 'active'\n  AND ncloc > 0\nGROUP BY analysis_date_only\nORDER BY analysis_date_only", "refId": "A"}], "title": "代码质量密度指标趋势 (90天)", "type": "timeseries"}], "refresh": "5m", "schemaVersion": 36, "style": "dark", "tags": ["SonarQube", "WR02", "趋势分析"], "templating": {"list": [{"current": {"selected": false, "text": "90", "value": "90"}, "hide": 0, "includeAll": false, "label": "时间范围(天)", "multi": false, "name": "time_range", "options": [{"selected": false, "text": "7", "value": "7"}, {"selected": false, "text": "30", "value": "30"}, {"selected": true, "text": "90", "value": "90"}, {"selected": false, "text": "180", "value": "180"}, {"selected": false, "text": "365", "value": "365"}], "query": "7,30,90,180,365", "queryValue": "", "skipUrlSync": false, "type": "custom"}]}, "time": {"from": "now-90d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "SonarQube WR02 趋势分析仪表板", "uid": "sonaree-wr02-trends", "version": 1, "weekStart": ""}
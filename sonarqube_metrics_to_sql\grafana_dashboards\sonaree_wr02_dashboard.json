{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource-sonar"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource-sonar"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  COUNT(DISTINCT project_name) as \"活跃项目数\",\n  SUM(bugs) as \"总Bug数\",\n  SUM(vulnerabilities) as \"总漏洞数\",\n  SUM(code_smells) as \"代码异味总数\",\n  ROUND(AVG(coverage), 2) as \"平均覆盖率%\",\n  ROUND(AVG(duplicated_lines_density), 2) as \"平均重复率%\",\n  SUM(critical_issues) as \"严重问题总数\"\nFROM sonaree_wr02 \nWHERE analysis_date >= NOW() - INTERVAL '30 days'\n  AND status = 'active'", "refId": "A"}], "title": "SonarQube WR02 关键指标 (最近30天)", "type": "stat"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource-sonar"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "clean"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "critical"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6}, "id": 2, "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "pieType": "pie", "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource-sonar"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  quality_level as metric,\n  COUNT(*) as value\nFROM sonaree_wr02\nWHERE analysis_date >= NOW() - INTERVAL '30 days'\n  AND status = 'active'\nGROUP BY quality_level\nORDER BY \n  CASE quality_level \n    WHEN 'clean' THEN 1 \n    WHEN 'minor' THEN 2 \n    WHEN 'major' THEN 3 \n    WHEN 'critical' THEN 4 \n  END", "refId": "A"}], "title": "代码质量等级分布", "type": "piechart"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource-sonar"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource-sonar"}, "format": "time_series", "rawQuery": true, "rawSql": "SELECT \n  analysis_date as time,\n  SUM(bugs) as \"Bug总数\",\n  SUM(vulnerabilities) as \"漏洞总数\",\n  SUM(code_smells) as \"代码异味总数\"\nFROM sonaree_wr02\nWHERE analysis_date >= CURRENT_DATE - INTERVAL '30 days'\n  AND status = 'active'\nGROUP BY analysis_date\nORDER BY analysis_date", "refId": "A"}], "title": "每日问题趋势", "type": "timeseries"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource-sonar"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 14}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource-sonar"}, "format": "time_series", "rawQuery": true, "rawSql": "SELECT \n  analysis_date as time,\n  ROUND(AVG(coverage), 2) as \"平均覆盖率%\",\n  ROUND(AVG(line_coverage), 2) as \"平均行覆盖率%\",\n  ROUND(AVG(branch_coverage), 2) as \"平均分支覆盖率%\"\nFROM sonaree_wr02\nWHERE analysis_date >= CURRENT_DATE - INTERVAL '30 days'\n  AND status = 'active'\n  AND coverage > 0\nGROUP BY analysis_date\nORDER BY analysis_date", "refId": "A"}], "title": "覆盖率趋势", "type": "timeseries"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource-sonar"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "平均覆盖率%"}, "properties": [{"id": "unit", "value": "percent"}, {"id": "custom.displayMode", "value": "color-background"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 70}, {"color": "green", "value": 90}]}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 14}, "id": 5, "options": {"showHeader": true}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource-sonar"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  project_name as \"项目名称\",\n  branch as \"分支\",\n  MAX(analysis_date) as \"最后分析时间\",\n  SUM(bugs) as \"Bug总数\",\n  SUM(vulnerabilities) as \"漏洞总数\",\n  SUM(code_smells) as \"代码异味总数\",\n  ROUND(AVG(coverage), 2) as \"平均覆盖率%\",\n  ROUND(AVG(duplicated_lines_density), 2) as \"平均重复率%\",\n  quality_level as \"质量等级\"\nFROM sonaree_wr02\nWHERE analysis_date >= NOW() - INTERVAL '30 days'\n  AND status = 'active'\nGROUP BY project_name, branch, quality_level\nORDER BY project_name, branch\nLIMIT 20", "refId": "A"}], "title": "项目质量详情", "type": "table"}], "refresh": "5m", "schemaVersion": 36, "style": "dark", "tags": ["SonarQube", "WR02", "质量监控"], "templating": {"list": []}, "time": {"from": "now-30d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "SonarQube WR02 质量监控仪表板", "uid": "sonarqube-wr02-monitor", "version": 1, "weekStart": ""}
version: '3.8'
services:
  # InfluxDB 2.x
  influxdb:
    image: influxdb:2.7
    container_name: sonar-influxdb
    ports:
      - "8086:8086"
    environment:
      - DOCKER_INFLUXDB_INIT_MODE=setup
      - DOCKER_INFLUXDB_INIT_USERNAME=admin
      - DOCKER_INFLUXDB_INIT_PASSWORD=admin123456
      - DOCKER_INFLUXDB_INIT_ORG=sonarqube
      - DOCKER_INFLUXDB_INIT_BUCKET=sonar_metrics
      - DOCKER_INFLUXDB_INIT_ADMIN_TOKEN=sonar-admin-token-12345678901234567890
      - TZ=Asia/Shanghai
    volumes:
      - influxdb_data:/var/lib/influxdb2
      - influxdb_config:/etc/influxdb2
    restart: unless-stopped
    networks:
      - sonar-monitoring

  # Webhook接收服务
  webhook-service:
    build: ./webhook-service
    container_name: sonar-webhook-influx
    ports:
      - "8080:8080"
    environment:
      - INFLUXDB_URL=http://influxdb:8086
      - INFLUXDB_TOKEN=sonar-admin-token-12345678901234567890
      - INFLUXDB_ORG=sonarqube
      - INFLUXDB_BUCKET=sonar_metrics
      - TZ=Asia/Shanghai
    depends_on:
      - influxdb
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - sonar-monitoring

  # Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: sonar-grafana-influx
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
      - TZ=Asia/Shanghai
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
    restart: unless-stopped
    networks:
      - sonar-monitoring

volumes:
  influxdb_data:
  influxdb_config:
  grafana_data:

networks:
  sonar-monitoring:
    driver: bridge

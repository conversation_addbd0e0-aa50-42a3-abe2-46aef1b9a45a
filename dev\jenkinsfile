pipeline {
    agent { label 'built-in' }
    options {
        // 构建日志中添加时间戳
        timestamps()
        // 禁止并发构建，所有节点使用的是C:\Users\<USER>\P目录，并发构建会出问题。
        disableConcurrentBuilds()
        timeout(time: 1, unit: 'HOURS')
    }
    parameters {
        choice(
            name: 'VersionType',
            choices: ['Beta', 'Release'], 
            description: 'VersionType 必选参数，版本类型，可选择是Beta版本或者Release版本。'
        )
        string(
            name: 'TagVersion',
            trim: true,
            description: '''可选参数，该值默认为空的情况下标签仅以manifest文件的形式归档到制品中，如果需要推送到Gerrit服务器需填写标签名。例如：
            WR02_beta_v0.40
            WR02_beta_v0.40
            WR02_beta_v0.40'''
        )
    }
    environment {
        // 定义全局环境变量,定义顺序不要换
        lowversiontype = sh(script: "echo '${VersionType}' | tr 'A-Z' 'a-z'", returnStdout: true).trim()
        // 这个是用来存nexus的名字，nexus上都是中划线保持统一
        trTagVersion = sh(script: "echo '${TagVersion}' | tr '_' '-'", returnStdout: true).trim()
        currTime = sh(script: 'date +%Y%m%d%H%M', returnStdout: true).trim()
        // 用于归档时，manifest.xml的命名
        tempTagVersion = "WR02_beta_temp"
        trTempTagVersion = sh(script: "echo '${tempTagVersion}' | tr '_' '-'", returnStdout: true).trim()
    }
    stages {
        stage('清理构建缓存') {
            steps {
                node('WinBuild') {
                    // 使用 ws 指令设置自定义工作空间并包含代码块
                    ws('C:\\Users\\<USER>\\Twearwr02') {
                        // 清理工作空间,不能打开，否则每次构建都需要重新加入vscode中。
                        //cleanWs()
                        // 使用 bat 命令执行 Windows 批处理命令下载代码
                        sh '''
                        PATH="/home/<USER>/bin:/usr/local/bin:/usr/bin:/usr/bin:/sbin:/usr/local/bin:/home/<USER>/bin:/cygdrive/c/Program Files/Common Files/Oracle/Java/javapath:/cygdrive/c/App/OpenSSH-Win64:/cygdrive/c/Windows/system32:/cygdrive/c/Windows:/cygdrive/c/Windows/System32/Wbem:/cygdrive/c/Windows/System32/WindowsPowerShell/v1.0:/cygdrive/c/Windows:/cygdrive/c/App/curl/bin:/cygdrive/c/App/GnuWin32/bin:/cygdrive/c/App/7-Zip:/cygdrive/c/Program Files/dotnet:/cygdrive/c/Keil_v5/ARM/ARMCC/bin:/cygdrive/c/Users/<USER>/bin:/cygdrive/c/Users/<USER>/.vscode/extensions/cl.eide-3.20.0/res/tools/win32/unify_builder:/cygdrive/c/Users/<USER>/.vscode/extensions/cl.eide-3.20.1/res/tools/win32/unify_builder:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python37/Scripts:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python37:/cygdrive/c/Program Files (x86)/GNU Arm Embedded Toolchain/10 2021.10/bin:/cygdrive/c/Users/<USER>/AppData/Local/Microsoft/WindowsApps:/cygdrive/c/App/Microsoft VS Code/bin:$PATH";
                        cd ${WORKSPACE}/../Twearwr02
                        echo "清理工作空间: CIBuildScript,CIBuildArtifacts";
                        rm -rf CIBuildArtifacts;
                        rm -rf CIBuildScript;
                        rm -rf CIBuildArtifacts.zip;
                        mkdir CIBuildArtifacts;
                        rm -rf wr02
                        '''
                    }
                }
            }
        }
        stage('下载代码') {
            steps {
                node('WinBuild') {
                    // 使用 ws 指令设置自定义工作空间并包含代码块
                    ws('C:\\Users\\<USER>\\Twearwr02') {
                        // 清理工作空间,不能打开，否则每次构建都需要重新加入vscode中。
                        //cleanWs()
                        // 使用 bat 命令执行 Windows 批处理命令下载代码
                        sh '''
                            PATH="/home/<USER>/bin:/usr/local/bin:/usr/bin:/usr/bin:/sbin:/usr/local/bin:/home/<USER>/bin:/cygdrive/c/Program Files/Common Files/Oracle/Java/javapath:/cygdrive/c/App/OpenSSH-Win64:/cygdrive/c/Windows/system32:/cygdrive/c/Windows:/cygdrive/c/Windows/System32/Wbem:/cygdrive/c/Windows/System32/WindowsPowerShell/v1.0:/cygdrive/c/Windows:/cygdrive/c/App/curl/bin:/cygdrive/c/App/GnuWin32/bin:/cygdrive/c/App/7-Zip:/cygdrive/c/Program Files/dotnet:/cygdrive/c/Keil_v5/ARM/ARMCC/bin:/cygdrive/c/Users/<USER>/bin:/cygdrive/c/Users/<USER>/.vscode/extensions/cl.eide-3.20.0/res/tools/win32/unify_builder:/cygdrive/c/Users/<USER>/.vscode/extensions/cl.eide-3.20.1/res/tools/win32/unify_builder:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python37/Scripts:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python37:/cygdrive/c/Program Files (x86)/GNU Arm Embedded Toolchain/10 2021.10/bin:/cygdrive/c/Users/<USER>/AppData/Local/Microsoft/WindowsApps:/cygdrive/c/App/Microsoft VS Code/bin:$PATH";
                            cd ${WORKSPACE}/../Twearwr02;
                            repo init -u ssh://jenkinsadmin@********:29418/wr02 -m wearable-sifli-wr02.xml -b develop --depth=3;
                            repo start --all develop;
                            echo "repo forall -c git reset --hard HEAD^" ;
                            repo forall -c git reset --hard HEAD^;
                            repo sync -c;
                            unix2dos tools/upgrade_tools/*.bat
                        '''
                    }
                }
            }
        }
        stage('打标签') {
            steps {
                node('WinBuild') {
                    // 使用 ws 指令设置自定义工作空间并包含代码块
                    ws('C:\\Users\\<USER>\\Twearwr02') {
                        // 清理工作空间,不能打开，否则每次构建都需要重新加入vscode中。
                        //cleanWs()
                        // 使用 bat 命令执行 Windows 批处理命令下载代码
                        sh '''
                        PATH="/home/<USER>/bin:/usr/local/bin:/usr/bin:/usr/bin:/sbin:/usr/local/bin:/home/<USER>/bin:/cygdrive/c/Program Files/Common Files/Oracle/Java/javapath:/cygdrive/c/App/OpenSSH-Win64:/cygdrive/c/Windows/system32:/cygdrive/c/Windows:/cygdrive/c/Windows/System32/Wbem:/cygdrive/c/Windows/System32/WindowsPowerShell/v1.0:/cygdrive/c/Windows:/cygdrive/c/App/curl/bin:/cygdrive/c/App/GnuWin32/bin:/cygdrive/c/App/7-Zip:/cygdrive/c/Program Files/dotnet:/cygdrive/c/Keil_v5/ARM/ARMCC/bin:/cygdrive/c/Users/<USER>/bin:/cygdrive/c/Users/<USER>/.vscode/extensions/cl.eide-3.20.0/res/tools/win32/unify_builder:/cygdrive/c/Users/<USER>/.vscode/extensions/cl.eide-3.20.1/res/tools/win32/unify_builder:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python37/Scripts:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python37:/cygdrive/c/Program Files (x86)/GNU Arm Embedded Toolchain/10 2021.10/bin:/cygdrive/c/Users/<USER>/AppData/Local/Microsoft/WindowsApps:/cygdrive/c/App/Microsoft VS Code/bin:$PATH";
                        cd ${WORKSPACE}/../Twearwr02;
                        if [ -n ${TagVersion} ];then
                            maniName="${TagVersion}_${currTime}.xml"
                        else
                            maniName="${tempTagVersion}_${currTime}.xml"
                        fi
                        repo manifest -r -o ${maniName};
                        mv ${maniName} CIBuildArtifacts/;
                        echo "BuildURL: $BUILD_URL" > jenkinsinfo;
                        mv jenkinsinfo CIBuildArtifacts/
                        '''
                    }
                }
            }
        }
        stage('生成编译文件') {
            steps {
                node('WinBuild') {
                    // 使用 ws 指令设置自定义工作空间并包含代码块
                    ws('C:\\Users\\<USER>\\Twearwr02') {
                        echo "开始获取配置文件"
                        sh '''
                        cd ${WORKSPACE}/../Twearwr02
                        cp -r /home/<USER>/CIBuildScript ./;
                        sh CIBuildScript/builder_boot/getbuilder_bootv2.sh version=PIPELINE workdir=Twearwr02;
                        sh CIBuildScript/builder_lcpu/getbuilder_lcpuv2.sh version=PIPELINE workdir=Twearwr02;
                        sh CIBuildScript/builder_app/getbuilder_appv2.sh version=PIPELINE workdir=Twearwr02;
                        '''
                    }
                }
            }
        }
        stage('编译Boot') {
            steps {
                node('WinBuild') {
                    // 使用 ws 指令设置自定义工作空间并包含代码块
                    ws('C:\\Users\\<USER>\\Twearwr02') {
                        echo "开始执行编译"
                        sh '''
                        PATH="/home/<USER>/bin:/usr/local/bin:/usr/bin:/usr/bin:/sbin:/usr/local/bin:/home/<USER>/bin:/cygdrive/c/Program Files/Common Files/Oracle/Java/javapath:/cygdrive/c/App/OpenSSH-Win64:/cygdrive/c/Windows/system32:/cygdrive/c/Windows:/cygdrive/c/Windows/System32/Wbem:/cygdrive/c/Windows/System32/WindowsPowerShell/v1.0:/cygdrive/c/Windows:/cygdrive/c/App/curl/bin:/cygdrive/c/App/GnuWin32/bin:/cygdrive/c/App/7-Zip:/cygdrive/c/Program Files/dotnet:/cygdrive/c/Keil_v5/ARM/ARMCC/bin:/cygdrive/c/Users/<USER>/bin:/cygdrive/c/Users/<USER>/.vscode/extensions/cl.eide-3.20.0/res/tools/win32/unify_builder:/cygdrive/c/Users/<USER>/.vscode/extensions/cl.eide-3.20.1/res/tools/win32/unify_builder:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python37/Scripts:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python37:/cygdrive/c/Program Files (x86)/GNU Arm Embedded Toolchain/10 2021.10/bin:/cygdrive/c/Users/<USER>/AppData/Local/Microsoft/WindowsApps:/cygdrive/c/App/Microsoft VS Code/bin:$PATH";
                        unify_builder -p  ${WORKSPACE}/CIBuildScript/builder_boot/builder_boot.params --rebuild;
                        '''
                    }
                }
            }
        }
        stage('编译LCPU') {
            steps {
                node('WinBuild') {
                    // 使用 ws 指令设置自定义工作空间并包含代码块
                    ws('C:\\Users\\<USER>\\Twearwr02') {
                        echo "开始执行编译"
                        sh '''
                        PATH="/home/<USER>/bin:/usr/local/bin:/usr/bin:/usr/bin:/sbin:/usr/local/bin:/home/<USER>/bin:/cygdrive/c/Program Files/Common Files/Oracle/Java/javapath:/cygdrive/c/App/OpenSSH-Win64:/cygdrive/c/Windows/system32:/cygdrive/c/Windows:/cygdrive/c/Windows/System32/Wbem:/cygdrive/c/Windows/System32/WindowsPowerShell/v1.0:/cygdrive/c/Windows:/cygdrive/c/App/curl/bin:/cygdrive/c/App/GnuWin32/bin:/cygdrive/c/App/7-Zip:/cygdrive/c/Program Files/dotnet:/cygdrive/c/Keil_v5/ARM/ARMCC/bin:/cygdrive/c/Users/<USER>/bin:/cygdrive/c/Users/<USER>/.vscode/extensions/cl.eide-3.20.0/res/tools/win32/unify_builder:/cygdrive/c/Users/<USER>/.vscode/extensions/cl.eide-3.20.1/res/tools/win32/unify_builder:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python37/Scripts:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python37:/cygdrive/c/Program Files (x86)/GNU Arm Embedded Toolchain/10 2021.10/bin:/cygdrive/c/Users/<USER>/AppData/Local/Microsoft/WindowsApps:/cygdrive/c/App/Microsoft VS Code/bin:$PATH";
                        unify_builder -p  ${WORKSPACE}/CIBuildScript/builder_lcpu/builder_lcpu.params --rebuild;
                        '''
                    }
                }
            }
        }
        stage('编译APP') {
            steps {
                node('WinBuild') {
                    // 使用 ws 指令设置自定义工作空间并包含代码块
                    ws('C:\\Users\\<USER>\\Twearwr02') {
                        echo "开始执行编译"
                        sh '''
                        PATH="/home/<USER>/bin:/usr/local/bin:/usr/bin:/usr/bin:/sbin:/usr/local/bin:/home/<USER>/bin:/cygdrive/c/Program Files/Common Files/Oracle/Java/javapath:/cygdrive/c/App/OpenSSH-Win64:/cygdrive/c/Windows/system32:/cygdrive/c/Windows:/cygdrive/c/Windows/System32/Wbem:/cygdrive/c/Windows/System32/WindowsPowerShell/v1.0:/cygdrive/c/Windows:/cygdrive/c/App/curl/bin:/cygdrive/c/App/GnuWin32/bin:/cygdrive/c/App/7-Zip:/cygdrive/c/Program Files/dotnet:/cygdrive/c/Keil_v5/ARM/ARMCC/bin:/cygdrive/c/Users/<USER>/bin:/cygdrive/c/Users/<USER>/.vscode/extensions/cl.eide-3.20.0/res/tools/win32/unify_builder:/cygdrive/c/Users/<USER>/.vscode/extensions/cl.eide-3.20.1/res/tools/win32/unify_builder:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python37/Scripts:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python37:/cygdrive/c/Program Files (x86)/GNU Arm Embedded Toolchain/10 2021.10/bin:/cygdrive/c/Users/<USER>/AppData/Local/Microsoft/WindowsApps:/cygdrive/c/App/Microsoft VS Code/bin:$PATH";
                        unify_builder -p  ${WORKSPACE}/CIBuildScript/builder_app/builder_app.params --rebuild;
                        '''
                    }
                }
            }
        }
        stage('执行打包bat') {
            steps {
                node('WinPackage') {
                    // 使用 ws 指令设置自定义工作空间并包含代码块
                    ws('C:\\Users\\<USER>\\Twearwr02') {
                        echo "开始打包"
                        powershell '''# 切换编码为UTF-8
                        chcp 65001
                        # 调整环境变量
                        $currentPath = $env:PATH
                        $OutputEncoding = [System.Text.Encoding]::UTF8
                        $systemPaths = "C:\\Windows\\System32;C:\\Windows;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
                        $env:PATH = "$systemPaths;$currentPath"
                        # 打印环境变量
                        $env:PATH
                        Write-Host "The current path is: $PWD"
                        # 打印当前路径下的文件清单
                        Get-ChildItem
                        Set-Location -Path ".\\tools\\upgrade_tools"
                        .\\WR02_OTA_Upgrade.bat;
                        '''
                    }
                }
            }
        }
        stage('推送标签') {
            when {
                expression {
                    return env.TagVersion!= null && env.TagVersion.trim()!= ""
                }
            }
            steps {
                node('WinBuild') {
                    // 使用 ws 指令设置自定义工作空间并包含代码块
                    ws('C:\\Users\\<USER>\\Twearwr02') {
                        echo "开始上传tag至Gerrit服务器"
                        sh '''
                        PATH="/home/<USER>/bin:/usr/local/bin:/usr/bin:/usr/bin:/sbin:/usr/local/bin:/home/<USER>/bin:/cygdrive/c/Program Files/Common Files/Oracle/Java/javapath:/cygdrive/c/App/OpenSSH-Win64:/cygdrive/c/Windows/system32:/cygdrive/c/Windows:/cygdrive/c/Windows/System32/Wbem:/cygdrive/c/Windows/System32/WindowsPowerShell/v1.0:/cygdrive/c/Windows:/cygdrive/c/App/curl/bin:/cygdrive/c/App/GnuWin32/bin:/cygdrive/c/App/7-Zip:/cygdrive/c/Program Files/dotnet:/cygdrive/c/Keil_v5/ARM/ARMCC/bin:/cygdrive/c/Users/<USER>/bin:/cygdrive/c/Users/<USER>/.vscode/extensions/cl.eide-3.20.0/res/tools/win32/unify_builder:/cygdrive/c/Users/<USER>/.vscode/extensions/cl.eide-3.20.1/res/tools/win32/unify_builder:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python37/Scripts:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python37:/cygdrive/c/Program Files (x86)/GNU Arm Embedded Toolchain/10 2021.10/bin:/cygdrive/c/Users/<USER>/AppData/Local/Microsoft/WindowsApps:/cygdrive/c/App/Microsoft VS Code/bin:$PATH";
                        cd ${WORKSPACE}/../Twearwr02
                        TagName="${TagVersion}_${currTime}"
                        maniName="${TagName}.xml"
                        cd ${WORKSPACE};
                        repo manifest -r -o ${maniName};
                        VerMess=$(echo $TagVersion | awk -F'_' '{print $3}');
                        repo list  -n > repolist.xml;
                        function getTag(){
                        	reponame=$1;
                            cd ${WORKSPACE}/${reponame};
                            git tag -l | xargs git tag -d;
                            git tag -a ${TagName} -m "Version ${VerMess}";
                            git push qwkj ${TagName};
                        }
                        if [ -f "repolist.xml" ]; then
                            # 使用 cat 命令读取文件内容并使用 while 循环逐行处理
                            cat repolist.xml | while read line; do
                                getTag ${line} ${TagName} ${VerMess}
                            done
                        else
                            echo "repolist.xml 文件不存在，请检查文件路径是否正确。"
                        fi
                        echo "开始同步WR02 repo仓库";
                        git clone "ssh://jenkinsadmin@********:29418/wr02" && (cd "wr02" && mkdir -p `git rev-parse --git-dir`/hooks/ && curl -Lo `git rev-parse --git-dir`/hooks/commit-msg http://********:8081/tools/hooks/commit-msg && chmod +x `git rev-parse --git-dir`/hooks/commit-msg)
                        cd wr02;
                        # 目前写死在develop分支;
                        git checkout develop;
                        mv ${WORKSPACE}/${maniName} qwkj_manifests/version/;
                        git add qwkj_manifests;
                        # 执行git commit命令并使用临时模板;
                        git commit -m "backup: The ${TagName} is automatically generated by Jenkins pipeline."  -m "Signed-off-by: ${BUILD_USER} <${BUILD_USER}@igpsport.com>"
                        git push origin develop;
                        # 给wr02仓库也打一个tag
                        git tag -a ${TagName} -m "Version ${VerMess}";
                        git push origin ${TagName};
                        cd ${WORKSPACE};
                        '''
                    }
                }
            }
        }
        stage('拷贝制品') {
            steps {
                node('WinBuild') {
                    // 使用 ws 指令设置自定义工作空间并包含代码块
                    ws('C:\\Users\\<USER>\\Twearwr02') {
                        echo "开始拷贝制品，如需调整制品内容，联系配置管理员"
                        sh '''
                        PATH="/home/<USER>/bin:/usr/local/bin:/usr/bin:/usr/bin:/sbin:/usr/local/bin:/home/<USER>/bin:/cygdrive/c/Program Files/Common Files/Oracle/Java/javapath:/cygdrive/c/App/OpenSSH-Win64:/cygdrive/c/Windows/system32:/cygdrive/c/Windows:/cygdrive/c/Windows/System32/Wbem:/cygdrive/c/Windows/System32/WindowsPowerShell/v1.0:/cygdrive/c/Windows:/cygdrive/c/App/curl/bin:/cygdrive/c/App/GnuWin32/bin:/cygdrive/c/App/7-Zip:/cygdrive/c/Program Files/dotnet:/cygdrive/c/Keil_v5/ARM/ARMCC/bin:/cygdrive/c/Users/<USER>/bin:/cygdrive/c/Users/<USER>/.vscode/extensions/cl.eide-3.20.0/res/tools/win32/unify_builder:/cygdrive/c/Users/<USER>/.vscode/extensions/cl.eide-3.20.1/res/tools/win32/unify_builder:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python37/Scripts:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python37:/cygdrive/c/Program Files (x86)/GNU Arm Embedded Toolchain/10 2021.10/bin:/cygdrive/c/Users/<USER>/AppData/Local/Microsoft/WindowsApps:/cygdrive/c/App/Microsoft VS Code/bin:$PATH";
                        cd ${WORKSPACE}/../Twearwr02
                        cp -r tools/upgrade_tools CIBuildArtifacts/;
                        rm -rf CIBuildArtifacts/upgrade_tools/Asset;
                        rm -rf CIBuildArtifacts/upgrade_tools/bin_update_src;
                        7z a -tzip CIBuildArtifacts.zip CIBuildArtifacts;
                        '''
                    }
                }
            }
        }
        stage('备份制品至Jenkins') {
            steps {
                node('WinBuild') {
                    // 使用 ws 指令设置自定义工作空间并包含代码块
                    ws('C:\\Users\\<USER>\\Twearwr02') {
                        echo "开始拷贝制品，如需调整制品内容，联系配置管理员"
                        echo 'This is the artifact archiving stage.'
                        archiveArtifacts artifacts: '*.zip', fingerprint: true, followSymlinks: false, onlyIfSuccessful: true
                    }
                }
            }
        }
        stage('上传制品至Nexus') {
            steps {
                node('WinBuild') {
                    // 使用 ws 指令设置自定义工作空间并包含代码块
                    ws('C:\\Users\\<USER>\\Twearwr02') {
                        echo "开始执行编译"
                        script {
                            def nexusArtifactId
                            if (env.TagVersion) {
                                nexusArtifactId = env.trTagVersion
                            } else {
                                nexusArtifactId = env.trTempTagVersion
                            }
                            nexusArtifactUploader artifacts: [[artifactId: "${nexusArtifactId}", 
                                                            classifier: "", 
                                                            file: "CIBuildArtifacts.zip", 
                                                            type: "zip"]], 
                                            credentialsId: "nexus", 
                                            groupId: "wearable-wr02-${lowversiontype}", 
                                            nexusUrl: "*********:8089/", 
                                            nexusVersion: "nexus3", 
                                            protocol: "http", 
                                            repository: "wearable-build", 
                                            version: "${currTime}"
                        }
                    }
                }
            }
        }
    }
    post {
        success {
            echo "构建成功"
        }
        failure {
            echo "构建失败"
        }
        always {
            echo "构建结束"
        }
    }
}
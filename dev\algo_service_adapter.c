/**
 * @file algo_service_adapter.c
 * <AUTHOR> (<EMAIL>)
 * @brief 算法适配层接口
 * @version 0.1
 * @date 2024-11-09
 *
 * @copyright Copyright (c) 2024-2025, <PERSON>han <PERSON>wu Technology Co., Ltd
 *
 */
#include "algo_service_adapter.h"
#define LOG_TAG "algo.adapter"
#include <drv_log.h>
#include "algo_service_component_common.h"

// 算法列表
static algo_compent_ops_t* s_compent[ALGO_TYPE_END] = {0};

/**
 * @brief 注册算法组件到算法列表
 *
 * @param type 算法类型
 * @param compent 组件
 * @return int32_t 0 ： 成功 -1 ： 失败
 */
int32_t algo_compnent_register(ALGO_TYPE type, algo_compent_ops_t* compent)
{
    if (s_compent[type] == NULL)
    {
        s_compent[type] = compent;
        LOG_E("algo_compnent_register type:%d ok\n", type);
        return 0;
    }
    LOG_E("algo_compnent_register compent[%d] already register\n");
    return -1;
}

/**
 * @brief 算法适配层系统上电初始化
 *
 */
static void algo_adapter_init_all(void)
{
    int32_t ret;
    for (uint32_t i = 0; i < ALGO_TYPE_END; i++)
    {
        if (s_compent[i] == NULL || s_compent[i]->init == NULL)
        {
            continue;
        }
        ret = s_compent[i]->init();
        if (ret != 0)
        {
            LOG_E("algo_adapter_init_all i=%d\n", i);
        }
    }
    LOG_E("algo_adapter_init_all\n");
}

/**
 * @brief 算法订阅适配处理
 *
 * @param data 数据
 */
static void algo_adapter_subscribe(void *data)
{
    algo_service_adapter_t *adap_data = (algo_service_adapter_t *)data;
    optional_config_t *config = (optional_config_t *)adap_data->data;
    if (config == NULL || s_compent[adap_data->type] == NULL)
    {
        LOG_E("algo_adapter_subscribe para null type:%d len:%d\n", adap_data->type, adap_data->len);
        return;
    }
    LOG_E("algo_adapter_subscribe type:%d len:%d rate:%f\n", adap_data->type, adap_data->len, config->sampling_rate);
    if (adap_data->type >= ALGO_TYPE_END)
    {
        LOG_E("algo_adapter_subscribe erro type:%d len:%d rate:%f\n", adap_data->type, adap_data->len, config->sampling_rate);
        return;
    }

    int32_t ret = 0;
    algo_config_t algo_config;
    algo_config.type = ALGO_CONFIG_SAMPLING;
    algo_config.args_len = sizeof(optional_config_t);
    optional_config_t *temp = (optional_config_t *)algo_config.args;
    temp = config;
    if (s_compent[adap_data->type]->ioctl != NULL)
    {
        ret = s_compent[adap_data->type]->ioctl(&algo_config);
        if (ret != 0)
        {
            LOG_E("algo_adapter_subscribe ioctl ret:%d\n", ret);
            return;
        }
    }
    if (s_compent[adap_data->type]->open != NULL)
    {
        ret = s_compent[adap_data->type]->open();
        if (ret != 0)
        {
            LOG_E("algo_adapter_subscribe open ret:%d\n", ret);
        }
    }
}

/**
 * @brief 算法取消订阅适配处理
 *
 * @param data 数据
 */
static void algo_adapter_unsubscribe(void *data)
{
    algo_service_adapter_t *adap_data = (algo_service_adapter_t *)data;
    LOG_E("algo_adapter_unsubscribe type:%d len:%d\n", adap_data->type, adap_data->len);
    if (adap_data->type >= ALGO_TYPE_END || s_compent[adap_data->type] == NULL)
    {
        LOG_E("algo_adapter_unsubscribe type:%d len:%d\n", adap_data->type, adap_data->len);
        return;
    }
    int32_t ret = 0;
    if (s_compent[adap_data->type]->close != NULL)
    {
        ret = s_compent[adap_data->type]->close();
        if (ret != 0)
        {
            LOG_E("algo_adapter_unsubscribe close ret:%d\n", ret);
        }
    }
}

/**
 * @brief 算法前置配置适配处理
 *
 * @param data 数据
 */
static void algo_adapter_config(void *data)
{
    algo_service_adapter_t *adap_data = (algo_service_adapter_t *)data;
    LOG_E("algo_adapter_config type:%d len:%d\n", adap_data->type, adap_data->len);

    algo_config_t *config = (algo_config_t *)adap_data->data;
    if (config == NULL || s_compent[adap_data->type] == NULL)
    {
        LOG_E("algo_adapter_config para null type:%d\n", adap_data->type);
        return;
    }
    if (adap_data->type >= ALGO_TYPE_END)
    {
        LOG_E("algo_adapter_config type:%d len:%d\n", adap_data->type, adap_data->len);
        return;
    }
    int32_t ret = 0;
    if (s_compent[adap_data->type]->ioctl != NULL)
    {
        ret = s_compent[adap_data->type]->ioctl(config);
        if (ret != 0)
        {
            LOG_E("algo_adapter_config ioctl ret:%d\n", ret);
        }
    }
}

/**
 * @brief 算法喂数据适配处理
 *
 * @param data 数据
 */
static void algo_adapter_feed(void *data)
{
    algo_service_adapter_t *adap_data = (algo_service_adapter_t *)data;
    if (adap_data->type >= ALGO_TYPE_END  || s_compent[adap_data->type] == NULL)
    {
        LOG_E("algo_adapter_feed type:%d len:%d\n", adap_data->type, adap_data->len);
        return;
    }
    if (s_compent[adap_data->type]->feed == NULL)
    {
        LOG_E("algo_adapter_feed erro\n");
        return;
    }
    uint32_t run_tick = rt_tick_get();
    int32_t ret = s_compent[adap_data->type]->feed(adap_data->input_type, adap_data->data, adap_data->len);
    if (ret != 0)
    {
        LOG_E("algo_adapter_feed eero\n");
    }
    uint32_t end_tick = rt_tick_get();
    uint32_t deta_tick = end_tick - run_tick;
    if (deta_tick > ALGO_EXEC_MAX_TICK)
    {
        LOG_E("algo_adapter_feed type(%d) input_type(%d) tick:%u->%u,diff:%u\n",
            adap_data->type, adap_data->input_type, run_tick, end_tick, deta_tick);
    }
}

// 算法适配ops
static algo_adapter_ops_t s_algo_ada_ops =
{
    .adapter_init_all = algo_adapter_init_all,
    .adapter_subscribe = algo_adapter_subscribe,
    .adapter_unsubscribe = algo_adapter_unsubscribe,
    .adapter_config = algo_adapter_config,
    .adapter_feed = algo_adapter_feed,
};

/**
 * @brief 获取算法适配层操作实体
 *
 * @return algo_adapter_ops_t* 实例
 */
algo_adapter_ops_t* get_algo_adapter_entity(void)
{
    return &s_algo_ada_ops;
}

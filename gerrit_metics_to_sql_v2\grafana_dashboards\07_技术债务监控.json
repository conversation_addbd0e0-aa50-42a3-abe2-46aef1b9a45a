{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  SUM(sq_blocker + sq_critical + sq_major + sq_minor + sq_info) as \"总问题数\",\n  SUM(sq_blocker + sq_critical) as \"严重问题数\",\n  ROUND(AVG(duplicated_lines_density), 2) as \"平均重复度%\",\n  ROUND(AVG(complexity), 0) as \"平均复杂度\",\n  ROUND(AVG(comment_lines_density), 2) as \"平均注释密度%\",\n  COUNT(CASE WHEN complexity > 100 THEN 1 END) as \"高复杂度提交数\"\nFROM commit_metrics \nWHERE commit_time >= NOW() - INTERVAL '30 days'", "refId": "A"}], "title": "技术债务关键指标 (最近30天)", "type": "stat"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Blocker"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Critical"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Major"}, "properties": [{"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6}, "id": 2, "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "pieType": "pie", "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  'Blocker' as metric, SUM(sq_blocker) as value\nFROM commit_metrics\nWHERE commit_time >= NOW() - INTERVAL '30 days'\nUNION ALL\nSELECT 'Critical', SUM(sq_critical)\nFROM commit_metrics\nWHERE commit_time >= NOW() - INTERVAL '30 days'\nUNION ALL\nSELECT 'Major', SUM(sq_major)\nFROM commit_metrics\nWHERE commit_time >= NOW() - INTERVAL '30 days'\nUNION ALL\nSELECT 'Minor', SUM(sq_minor)\nFROM commit_metrics\nWHERE commit_time >= NOW() - INTERVAL '30 days'\nUNION ALL\nSELECT 'Info', SUM(sq_info)\nFROM commit_metrics\nWHERE commit_time >= NOW() - INTERVAL '30 days'\nORDER BY value DESC", "refId": "A"}], "title": "问题严重程度分布", "type": "piechart"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "time_series", "rawQuery": true, "rawSql": "SELECT \n  commit_date as time,\n  SUM(sq_blocker + sq_critical) as \"严重问题数\",\n  SUM(sq_major) as \"主要问题数\",\n  ROUND(AVG(duplicated_lines_density), 2) as \"平均重复度%\",\n  ROUND(AVG(complexity), 0) as \"平均复杂度\"\nFROM commit_metrics \nWHERE commit_date >= CURRENT_DATE - INTERVAL '30 days'\nGROUP BY commit_date\nORDER BY commit_date", "refId": "A"}], "title": "每日技术债务趋势", "type": "timeseries"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "严重问题数"}, "properties": [{"id": "custom.displayMode", "value": "color-background"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 5}, {"color": "red", "value": 10}]}}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 14}, "id": 4, "options": {"showHeader": true}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "table", "rawQuery": true, "rawSql": "SELECT \n  gerrit_project as \"项目名称\",\n  SUM(sq_blocker + sq_critical + sq_major + sq_minor + sq_info) as \"总问题数\",\n  SUM(sq_blocker + sq_critical) as \"严重问题数\",\n  ROUND(AVG(duplicated_lines_density), 2) as \"平均重复度%\",\n  ROUND(AVG(complexity), 0) as \"平均复杂度\",\n  ROUND(AVG(comment_lines_density), 2) as \"平均注释密度%\",\n  COUNT(CASE WHEN complexity > 100 THEN 1 END) as \"高复杂度提交数\",\n  COUNT(CASE WHEN duplicated_lines_density > 10 THEN 1 END) as \"高重复度提交数\"\nFROM commit_metrics \nWHERE commit_time >= NOW() - INTERVAL '30 days'\nGROUP BY gerrit_project\nHAVING COUNT(*) >= 3\nORDER BY \"严重问题数\" DESC, \"总问题数\" DESC\nLIMIT 20", "refId": "A"}], "title": "项目技术债务排行榜", "type": "table"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "points", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 22}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource"}, "format": "time_series", "rawQuery": true, "rawSql": "SELECT \n  commit_time as time,\n  complexity as \"代码复杂度\",\n  duplicated_lines_density as \"重复度%\",\n  (sq_blocker + sq_critical + sq_major + sq_minor + sq_info) as \"问题总数\"\nFROM commit_metrics \nWHERE commit_time >= NOW() - INTERVAL '7 days'\n  AND (complexity > 50 OR duplicated_lines_density > 5 OR (sq_blocker + sq_critical + sq_major + sq_minor + sq_info) > 10)\nORDER BY commit_time", "refId": "A"}], "title": "高风险提交散点图 (最近7天)", "type": "timeseries"}], "refresh": "5m", "schemaVersion": 36, "style": "dark", "tags": ["技术债务", "代码质量"], "templating": {"list": []}, "time": {"from": "now-30d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "技术债务监控仪表板", "uid": "technical-debt-monitor", "version": 1, "weekStart": ""}
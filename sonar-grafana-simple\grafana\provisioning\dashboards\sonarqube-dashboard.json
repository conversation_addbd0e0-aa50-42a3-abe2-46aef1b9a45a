{"dashboard": {"id": null, "title": "SonarQube Quality Dashboard", "tags": ["sonarqube", "quality"], "timezone": "browser", "refresh": "30s", "time": {"from": "now-24h", "to": "now"}, "panels": [{"id": 1, "title": "Quality Gate Status Overview", "type": "stat", "targets": [{"rawSql": "SELECT project_name, CASE WHEN quality_gate_status = 'OK' THEN 1 ELSE 0 END as status FROM sonar_quality_gates WHERE analysed_at >= NOW() - INTERVAL '24 hours' ORDER BY analysed_at DESC", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}, "mappings": [{"options": {"0": {"text": "FAILED"}, "1": {"text": "PASSED"}}, "type": "value"}]}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Analysis Frequency", "type": "timeseries", "targets": [{"rawSql": "SELECT analysed_at as time, COUNT(*) as analyses FROM sonar_quality_gates WHERE analysed_at >= NOW() - INTERVAL '7 days' GROUP BY DATE_TRUNC('hour', analysed_at) ORDER BY time", "format": "time_series", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Project Quality Status", "type": "table", "targets": [{"rawSql": "SELECT project_name, quality_gate_status, status, analysed_at FROM sonar_quality_gates WHERE analysed_at >= NOW() - INTERVAL '24 hours' ORDER BY analysed_at DESC LIMIT 20", "format": "table", "refId": "A"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}}, {"id": 4, "title": "Metrics Trends", "type": "timeseries", "targets": [{"rawSql": "SELECT measured_at as time, metric_key, metric_value FROM sonar_metrics_history WHERE measured_at >= NOW() - INTERVAL '7 days' AND metric_key IN ('coverage', 'bugs', 'vulnerabilities') ORDER BY time", "format": "time_series", "refId": "A"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}}]}}
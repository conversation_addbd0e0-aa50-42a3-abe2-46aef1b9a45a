#!/bin/bash
# SSH方式调用接口

TIMESTAMP_FILE="/home/<USER>/gerrit_site/hooks/inidir/reviewer-added/timestamp_file"
TIME_THRESHOLD=2 # 2秒，限制次钩子2秒内只能被调用一次
current_time=$(date +%s)
shoort_time=$(date +%Y-%m%d-%H%M)
inifiname="/home/<USER>/gerrit_site/hooks/inidir/reviewer-added/reviewinfo_${shoort_time}.txt"

echo "$@" > /home/<USER>/gerrit_site/hooks/testfile

if [ -f "$TIMESTAMP_FILE" ]; then
    file_time=$(stat -c %Y "$TIMESTAMP_FILE")
    time_diff=$((current_time - file_time))
    if [ $time_diff -lt $TIME_THRESHOLD ]; then
        # "钩子在最近已执行，跳过此次执行。"
        exit 0
    fi
fi

# 定义字典arg_dict
declare -A arg_dict

while (( $# > 1 )); do
    #echo $0 $1 $2 $3
    temkey=$1
    value="$2"
    key="${temkey#--}"
    # 使用普通变量赋值
    arg_dict["$key"]="$value"
    shift 2
done

change_value=${arg_dict[change]}
owner_value=${arg_dict[change-owner-username]}
url_value=${arg_dict[change-url]}
project_value=${arg_dict[project]}
branch_value=${arg_dict[branch]}

# 拿到changeid
changeid="$(echo ${change_value} | awk -F '~' '{print $3}')"

# 拿到commitid
commitid=$(ssh -p 29418 gerritadmin@10.0.0.3 gerrit query --format=JSON change:"${changeid}" --current-patch-set  | jq .currentPatchSet.revision | grep -v null | head -1)

# 拿到标题
committittle=$(ssh -p 29418 gerritadmin@10.0.0.3 gerrit query  --current-patch-set --format=JSON ${commitid} | jq .subject | grep -v null | sed 's/^"//;s/"$//')

# 生成已评审名单数组
approvals=($(ssh -p 29418 gerritadmin@10.0.0.3 gerrit query commit:"${commitid}" --all-reviewers  --current-patch-set --format=JSON |jq .currentPatchSet | grep -v null | jq .approvals| jq .[].by.username))

# 生成全量评审名单数组
allReviewers=($(ssh -p 29418 gerritadmin@10.0.0.3 gerrit query commit:"${commitid}" --all-reviewers  --current-patch-set --format=JSON |jq .allReviewers | grep -v null | jq .[].username))

# 白名单，不要带双引号
whitelist=(jenkinsadmin sonarreview lijiajing gerritadmin)
whitelist+=($owner_value)

# 剔除数组中的双引号
approvalslist=()
allReviewerslist=()
for element in "${approvals[@]}"; do
    approvalslist+=(${element//\"/})
done

for element in "${allReviewers[@]}"; do
    allReviewerslist+=(${element//\"/})
done

# 将已评审名单和白名单组成并集
approuser=($(echo ${approvalslist[*]} ${whitelist[*]}|sed 's/ /\n/g'|sort|uniq))

resultlist=()
for elem in "${allReviewerslist[@]}"; do
    found=false
    for item in "${approuser[@]}"; do
        if [[ "$elem" == "$item" ]]; then
            found=true
            break
        fi
    done
    if [[ $found == false ]]; then
        resultlist+=("$elem")
    fi
done

echo "approvalslist:${approvalslist[*]}" >> "${inifiname}"
echo "allReviewerslist:${allReviewerslist[*]}" >> "${inifiname}"
echo "approuser:${approuser[*]}" >> "${inifiname}"
echo "resultlist" "${resultlist[@]}" >> "${inifiname}"
echo "committittle: $committittle" >> "${inifiname}"

# 群机器人通知
function weixinnotice(){
# 填写自己机器人的KEY
api_key="9b4f8886-50c8-410c-88fe-395f174d15ec"

#发送消息
webhook_url="https://qyapi.weixin.qq.com/cgi-bin/webhook/send"

message="**代码检视提醒**\n
👩‍💻亲爱的同事们(${resultlist[@]})，以下代码检视任务等待你的审阅：\n
- **提交人**：${owner_value}
- **项目名称**：${project_value}
- **代码分支**：${branch_value}
- **待审阅地址**：${url_value}
- **patchset标题**：${committittle}\n
⏰请尽快审阅，确保项目进度不受影响。感谢你的配合！"


curl "${webhook_url}?key=${api_key}" \
   -H 'Content-Type: application/json' \
   -d "{
        \"msgtype\": \"text\",
        \"text\": {
            \"content\": \"$message\"
        }
   }"
}

if [[ ${#resultlist[@]} -gt 0 ]];then
  weixinnotice
fi
echo "$current_time" > $TIMESTAMP_FILE

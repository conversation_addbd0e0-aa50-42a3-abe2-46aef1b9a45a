#!/usr/bin/env python3
import re
import sys

def parse_size(line):
    # 使用正则表达式提取Size字段的十六进制值
    size_match = re.search(r'Size: (0x[0-9a-fA-F]+)', line)
    if size_match:
        # 将十六进制转换为十进制
        return int(size_match.group(1), 16)
    return None

def check_region_size(map_file, er_irom1_baseline, rw_psram1_baseline):
    try:
        with open(map_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 查找ER_IROM1区域
        er_irom1_match = re.search(r'Execution Region ER_IROM1 \(.*Size: (0x[0-9a-fA-F]+)', content)
        # 查找RW_PSRAM1区域
        rw_psram1_match = re.search(r'Execution Region RW_PSRAM1 \(.*Size: (0x[0-9a-fA-F]+)', content)
        
        if not er_irom1_match or not rw_psram1_match:
            print("Error: Could not find one or both regions in the map file")
            return
        
        er_irom1_size = int(er_irom1_match.group(1), 16)
        rw_psram1_size = int(rw_psram1_match.group(1), 16)
        
        # 计算增长百分比
        er_irom1_increase = ((er_irom1_size - er_irom1_baseline) / er_irom1_baseline) * 100
        rw_psram1_increase = ((rw_psram1_size - rw_psram1_baseline) / rw_psram1_baseline) * 100
        
        print(f"\nER_IROM1 区域:")
        print(f"当前大小: {er_irom1_size} bytes ({hex(er_irom1_size)})")
        print(f"基准大小: {er_irom1_baseline} bytes")
        print(f"增长百分比: {er_irom1_increase:.2f}%")
        print(f"是否超过20%: {'是' if er_irom1_increase > 20 else '否'}")
        
        print(f"\nRW_PSRAM1 区域:")
        print(f"当前大小: {rw_psram1_size} bytes ({hex(rw_psram1_size)})")
        print(f"基准大小: {rw_psram1_baseline} bytes")
        print(f"增长百分比: {rw_psram1_increase:.2f}%")
        print(f"是否超过20%: {'是' if rw_psram1_increase > 20 else '否'}")
        
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    if len(sys.argv) != 4:
        print("Usage: python check_size.py <map_file> <er_irom1_baseline> <rw_psram1_baseline>")
        print("Example: python check_size.py WR02_App.map 5000000 7000000")
        sys.exit(1)
        
    map_file = sys.argv[1]
    er_irom1_baseline = int(sys.argv[2])
    rw_psram1_baseline = int(sys.argv[3])
    
    check_region_size(map_file, er_irom1_baseline, rw_psram1_baseline) 
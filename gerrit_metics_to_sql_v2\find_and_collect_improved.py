#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
改进版的数据收集脚本 - 解决重复数据问题
主要改进：
1. 基于 (gerrit_project, branch, change_id) 组合判断唯一性
2. 使用 UPSERT 操作确保数据一致性
3. 添加更详细的日志记录
"""

import os
import sys
import json
import logging
import configparser
import requests
import subprocess
from datetime import datetime
import pg8000.native

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def save_commit_metrics_improved(conn, gerrit_data, sonar_data, issues_json, measures_json):
    """
    改进版的数据保存函数
    使用 (gerrit_project, branch, change_id) 作为唯一性判断
    """
    try:
        # 提取关键信息
        change_id = gerrit_data.get('id', '')
        change_id_short = change_id.split('~')[-1] if '~' in change_id else change_id
        gerrit_project = gerrit_data.get('project', '')
        branch = gerrit_data.get('branch', '')
        patch_set = gerrit_data.get('revisions', {})
        
        # 获取最新的patch set信息
        if patch_set:
            latest_revision = list(patch_set.keys())[-1]  # 获取最新的revision
            patch_set_number = patch_set[latest_revision].get('_number', 1)
            commit_id = latest_revision
        else:
            patch_set_number = 1
            commit_id = change_id_short
        
        # 构建唯一标识
        unique_key = f"{gerrit_project}~{branch}~{change_id_short}"
        
        logger.info(f"处理变更: {unique_key}, Patch Set: {patch_set_number}")
        
        # 检查记录是否已存在（基于项目、分支、change_id组合）
        check_result = conn.run("""
            SELECT commit_id, patch_set, updated_at 
            FROM commit_metrics 
            WHERE gerrit_project = :gerrit_project 
              AND branch = :branch 
              AND change_id_short = :change_id_short
        """, 
        gerrit_project=gerrit_project,
        branch=branch,
        change_id_short=change_id_short
        )
        
        record_exists = len(check_result) > 0
        
        if record_exists:
            existing_record = check_result[0]
            existing_patch_set = existing_record[1]
            existing_updated_at = existing_record[2]
            
            logger.info(f"找到已存在记录: Patch Set {existing_patch_set} -> {patch_set_number}")
            
            # 只有当patch set更新时才更新记录
            if patch_set_number >= existing_patch_set:
                logger.info(f"更新记录: {unique_key} (Patch Set: {existing_patch_set} -> {patch_set_number})")
                operation = "UPDATE"
            else:
                logger.info(f"跳过旧版本: {unique_key} (当前: {patch_set_number}, 已存在: {existing_patch_set})")
                return True
        else:
            logger.info(f"插入新记录: {unique_key} (Patch Set: {patch_set_number})")
            operation = "INSERT"
        
        # 提取其他数据...（这里省略数据提取逻辑，与原代码相同）
        # [数据提取代码保持不变]
        
        # 使用 UPSERT 操作
        upsert_sql = """
            INSERT INTO commit_metrics (
                commit_id, change_id, change_id_short, patch_set,
                gerrit_project, branch, subject, status,
                commit_time, created, updated, author, owner, number, url, reviewers,
                changed_lines, insertions, deletions, patchset_count, repo_path,
                sonar_project, sonar_key, sonar_creation_date,
                sq_blocker, sq_critical, sq_major, sq_minor, sq_info,
                sq_resolved_blocker, sq_resolved_critical, sq_resolved_major, 
                sq_resolved_minor, sq_resolved_info,
                gerrit_raw, sonar_issues,
                ncloc, statements, functions, files, comment_lines, comment_lines_density, 
                complexity, duplicated_lines_density, duplicated_lines, duplicated_blocks, duplicated_files,
                commit_date, commit_year, commit_month, commit_week, commit_hour,
                total_issues, total_resolved_issues, critical_issues, issue_density, 
                change_size_category, quality_level,
                created_at, updated_at
            ) VALUES (
                :commit_id, :change_id, :change_id_short, :patch_set,
                :gerrit_project, :branch, :subject, :status,
                :commit_time, :created, :updated, :author, :owner, :number, :url, :reviewers,
                :changed_lines, :insertions, :deletions, :patchset_count, :repo_path,
                :sonar_project, :sonar_key, :sonar_creation_date,
                :sq_blocker, :sq_critical, :sq_major, :sq_minor, :sq_info,
                :sq_resolved_blocker, :sq_resolved_critical, :sq_resolved_major, 
                :sq_resolved_minor, :sq_resolved_info,
                :gerrit_raw, :sonar_issues,
                :ncloc, :statements, :functions, :files, :comment_lines, :comment_lines_density, 
                :complexity, :duplicated_lines_density, :duplicated_lines, :duplicated_blocks, :duplicated_files,
                :commit_date, :commit_year, :commit_month, :commit_week, :commit_hour,
                :total_issues, :total_resolved_issues, :critical_issues, :issue_density, 
                :change_size_category, :quality_level,
                NOW(), NOW()
            )
            ON CONFLICT (gerrit_project, branch, change_id_short) 
            DO UPDATE SET
                commit_id = EXCLUDED.commit_id,
                patch_set = EXCLUDED.patch_set,
                subject = EXCLUDED.subject,
                status = EXCLUDED.status,
                commit_time = EXCLUDED.commit_time,
                updated = EXCLUDED.updated,
                reviewers = EXCLUDED.reviewers,
                changed_lines = EXCLUDED.changed_lines,
                insertions = EXCLUDED.insertions,
                deletions = EXCLUDED.deletions,
                patchset_count = EXCLUDED.patchset_count,
                sonar_project = EXCLUDED.sonar_project,
                sonar_key = EXCLUDED.sonar_key,
                sq_blocker = EXCLUDED.sq_blocker,
                sq_critical = EXCLUDED.sq_critical,
                sq_major = EXCLUDED.sq_major,
                sq_minor = EXCLUDED.sq_minor,
                sq_info = EXCLUDED.sq_info,
                sq_resolved_blocker = EXCLUDED.sq_resolved_blocker,
                sq_resolved_critical = EXCLUDED.sq_resolved_critical,
                sq_resolved_major = EXCLUDED.sq_resolved_major,
                sq_resolved_minor = EXCLUDED.sq_resolved_minor,
                sq_resolved_info = EXCLUDED.sq_resolved_info,
                gerrit_raw = EXCLUDED.gerrit_raw,
                sonar_issues = EXCLUDED.sonar_issues,
                ncloc = EXCLUDED.ncloc,
                statements = EXCLUDED.statements,
                functions = EXCLUDED.functions,
                files = EXCLUDED.files,
                comment_lines = EXCLUDED.comment_lines,
                comment_lines_density = EXCLUDED.comment_lines_density,
                complexity = EXCLUDED.complexity,
                duplicated_lines_density = EXCLUDED.duplicated_lines_density,
                duplicated_lines = EXCLUDED.duplicated_lines,
                duplicated_blocks = EXCLUDED.duplicated_blocks,
                duplicated_files = EXCLUDED.duplicated_files,
                total_issues = EXCLUDED.total_issues,
                total_resolved_issues = EXCLUDED.total_resolved_issues,
                critical_issues = EXCLUDED.critical_issues,
                issue_density = EXCLUDED.issue_density,
                change_size_category = EXCLUDED.change_size_category,
                quality_level = EXCLUDED.quality_level,
                updated_at = NOW()
            WHERE commit_metrics.patch_set <= EXCLUDED.patch_set  -- 只有更新的patch set才更新
        """
        
        # 执行UPSERT操作
        # conn.run(upsert_sql, **parameters)  # 参数传递
        
        logger.info(f"成功{operation}记录: {unique_key}")
        return True
        
    except Exception as e:
        logger.error(f"保存数据时出错: {e}")
        return False

def add_unique_constraint():
    """
    添加数据库唯一约束的SQL脚本
    """
    sql_script = """
    -- 1. 首先清理重复数据（保留最新的patch set）
    WITH ranked_commits AS (
        SELECT 
            *,
            ROW_NUMBER() OVER (
                PARTITION BY gerrit_project, branch, change_id_short 
                ORDER BY patch_set DESC, updated_at DESC
            ) as rn
        FROM commit_metrics
    )
    DELETE FROM commit_metrics 
    WHERE commit_id IN (
        SELECT commit_id 
        FROM ranked_commits 
        WHERE rn > 1
    );
    
    -- 2. 添加唯一约束
    ALTER TABLE commit_metrics 
    ADD CONSTRAINT uk_commit_metrics_unique 
    UNIQUE (gerrit_project, branch, change_id_short);
    
    -- 3. 创建索引以提高查询性能
    CREATE INDEX IF NOT EXISTS idx_commit_metrics_lookup 
    ON commit_metrics (gerrit_project, branch, change_id_short, patch_set);
    """
    
    return sql_script

if __name__ == "__main__":
    print("改进版数据收集脚本")
    print("主要改进:")
    print("1. 基于 (gerrit_project, branch, change_id) 组合判断唯一性")
    print("2. 使用 UPSERT 操作确保数据一致性") 
    print("3. 只保留最新的patch set数据")
    print("\n请先运行数据库清理脚本，然后替换原有的保存函数。")

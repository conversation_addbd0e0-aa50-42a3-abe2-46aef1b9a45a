{"dashboard": {"id": null, "title": "SonarQube Quality Dashboard (InfluxDB)", "tags": ["sonarqube", "quality", "influxdb"], "timezone": "browser", "refresh": "30s", "time": {"from": "now-24h", "to": "now"}, "panels": [{"id": 1, "title": "Quality Gate Status", "type": "stat", "targets": [{"query": "from(bucket: \"sonar_metrics\")\n  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)\n  |> filter(fn: (r) => r[\"_measurement\"] == \"sonar_quality_gate\")\n  |> filter(fn: (r) => r[\"_field\"] == \"status_value\")\n  |> group(columns: [\"project_key\", \"project_name\"])\n  |> last()", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": -1}, {"color": "red", "value": 0}, {"color": "green", "value": 1}]}, "mappings": [{"options": {"-1": {"text": "UNKNOWN"}, "0": {"text": "FAILED"}, "1": {"text": "PASSED"}}, "type": "value"}]}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Analysis Frequency", "type": "timeseries", "targets": [{"query": "from(bucket: \"sonar_metrics\")\n  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)\n  |> filter(fn: (r) => r[\"_measurement\"] == \"sonar_analysis\")\n  |> filter(fn: (r) => r[\"_field\"] == \"count\")\n  |> aggregateWindow(every: 1h, fn: sum, createEmpty: false)\n  |> group(columns: [\"project_key\"])", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Code Coverage Trend", "type": "timeseries", "targets": [{"query": "from(bucket: \"sonar_metrics\")\n  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)\n  |> filter(fn: (r) => r[\"_measurement\"] == \"sonar_metric\")\n  |> filter(fn: (r) => r[\"metric_key\"] == \"coverage\")\n  |> filter(fn: (r) => r[\"_field\"] == \"value\")\n  |> group(columns: [\"project_key\"])", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "Bug Count <PERSON>", "type": "timeseries", "targets": [{"query": "from(bucket: \"sonar_metrics\")\n  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)\n  |> filter(fn: (r) => r[\"_measurement\"] == \"sonar_metric\")\n  |> filter(fn: (r) => r[\"metric_key\"] == \"bugs\")\n  |> filter(fn: (r) => r[\"_field\"] == \"value\")\n  |> group(columns: [\"project_key\"])", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "Quality Gate Conditions", "type": "table", "targets": [{"query": "from(bucket: \"sonar_metrics\")\n  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)\n  |> filter(fn: (r) => r[\"_measurement\"] == \"sonar_condition\")\n  |> filter(fn: (r) => r[\"_field\"] == \"actual_value\" or r[\"_field\"] == \"threshold_value\" or r[\"_field\"] == \"condition_status_value\")\n  |> pivot(rowKey:[\"_time\"], columnKey: [\"_field\"], valueColumn: \"_value\")\n  |> group(columns: [\"project_key\", \"metric_key\"])\n  |> last()", "refId": "A"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}}, {"id": 6, "title": "Project Quality Summary", "type": "table", "targets": [{"query": "from(bucket: \"sonar_metrics\")\n  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)\n  |> filter(fn: (r) => r[\"_measurement\"] == \"sonar_quality_gate\")\n  |> filter(fn: (r) => r[\"_field\"] == \"status_value\")\n  |> group(columns: [\"project_key\", \"project_name\"])\n  |> last()", "refId": "A"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}}]}}
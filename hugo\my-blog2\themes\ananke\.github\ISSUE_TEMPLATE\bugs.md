---
name: Report a bug
about: Use this template to report a bug.
title: "bug: "
labels: status:unconfirmed
assignees: david<PERSON><PERSON><PERSON><PERSON>ur
---

<!---

This issue tracker is **ONLY** used for reporting bugs.

New features should be discussed [in the ideas section of our discussion forum](/discussions/categories/3-ideas).

Build and deploy issues should be discussed [in the Q&A section of our discussion forum](/discussions/categories/2-q-a-support).

Please read through these comments and add your responses and remove the comments when you are done.

Provide a general summary of the issue in the Title above and prefix it with `bug: `

-->

## Expected Behavior

<!---

Could you tell us what you did and what you expected to happen?

-->

## Current Behavior

<!---

Could you tell us what happens instead of the expected behavior?

-->

## Possible Solution

<!---

It's not mandatory, but please let us know if you know the reason for this issue or if you see a fix.

-->

## Steps to Reproduce

<!---

Provide a link to a live example or an unambiguous set of steps to reproduce this bug.
Include code to reproduce, if relevant.
Could you add screenshots if they help?
Could you make us understand what is going on?

-->

1.
2.
3.
4.

## The bigger picture (we need context)

<!---

What is your operating system (Name, Version, is everything up to date?)

-->

<!---

What is the output of the following commands:

- `hugo version`
- `go version`
- `git submodule status` (if you are running Ananke as a submodule)
- `hugo mod graph` (if you are running Ananke as a module)

-->

<!---

Keep the following HTML

-->

<details><summary>Hugo Config</summary>
<p>

<!---

Run `hugo config --format toml` inside of your repository (where you would
run `hugo server`) and copy the _complete_ output right after this closing
comment tag, between <p> and </p>.

-->

</p>
</details>

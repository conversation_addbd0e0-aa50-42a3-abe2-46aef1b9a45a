{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource-sonar"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "覆盖率%"}, "properties": [{"id": "unit", "value": "percent"}, {"id": "custom.displayMode", "value": "color-background"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 60}, {"color": "green", "value": 80}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "问题密度"}, "properties": [{"id": "custom.displayMode", "value": "color-background"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 5}, {"color": "red", "value": 10}]}}]}]}, "gridPos": {"h": 12, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "质量得分"}]}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource-sonar"}, "format": "table", "rawQuery": true, "rawSql": "WITH latest_analysis AS (\n  SELECT \n    project_name,\n    branch,\n    MAX(analysis_date) as latest_date\n  FROM sonaree_wr02 \n  WHERE status = 'active'\n    AND analysis_date >= NOW() - INTERVAL '7 days'\n  GROUP BY project_name, branch\n),\nproject_metrics AS (\n  SELECT \n    s.project_name,\n    s.branch,\n    s.quality_level,\n    s.bugs,\n    s.vulnerabilities,\n    s.code_smells,\n    s.coverage,\n    s.duplicated_lines_density,\n    s.issue_density,\n    s.ncloc,\n    s.complexity,\n    s.cognitive_complexity,\n    s.total_issues,\n    s.critical_issues,\n    -- 计算质量得分 (0-100)\n    CASE \n      WHEN s.quality_level = 'clean' THEN 90 + (s.coverage * 0.1)\n      WHEN s.quality_level = 'minor' THEN 70 + (s.coverage * 0.2)\n      WHEN s.quality_level = 'major' THEN 50 + (s.coverage * 0.2)\n      WHEN s.quality_level = 'critical' THEN 20 + (s.coverage * 0.3)\n      ELSE 50\n    END as quality_score\n  FROM sonaree_wr02 s\n  INNER JOIN latest_analysis la ON s.project_name = la.project_name \n    AND s.branch = la.branch \n    AND s.analysis_date = la.latest_date\n)\nSELECT \n  project_name as \"项目名称\",\n  branch as \"分支\",\n  quality_level as \"质量等级\",\n  ROUND(quality_score, 1) as \"质量得分\",\n  bugs as \"Bug数\",\n  vulnerabilities as \"漏洞数\",\n  code_smells as \"代码异味数\",\n  total_issues as \"总问题数\",\n  critical_issues as \"严重问题数\",\n  ROUND(coverage, 2) as \"覆盖率%\",\n  ROUND(duplicated_lines_density, 2) as \"重复度%\",\n  ROUND(issue_density, 2) as \"问题密度\",\n  ncloc as \"代码行数\",\n  complexity as \"复杂度\",\n  cognitive_complexity as \"认知复杂度\"\nFROM project_metrics\nORDER BY quality_score DESC, coverage DESC\nLIMIT 30", "refId": "A"}], "title": "项目质量对比排行榜 (基于最新分析)", "type": "table"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource-sonar"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "points", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 8, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource-sonar"}, "format": "time_series", "rawQuery": true, "rawSql": "SELECT \n  coverage as time,\n  issue_density as \"问题密度\",\n  CONCAT(project_name, ' (', branch, ')') as metric\nFROM sonaree_wr02 \nWHERE analysis_date >= NOW() - INTERVAL '7 days'\n  AND status = 'active'\n  AND coverage > 0\n  AND issue_density > 0\nORDER BY coverage", "refId": "A"}], "title": "覆盖率 vs 问题密度散点图", "type": "timeseries"}, {"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource-sonar"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "points", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 8, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 12}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource-sonar"}, "format": "time_series", "rawQuery": true, "rawSql": "SELECT \n  ncloc as time,\n  complexity as \"复杂度\",\n  CONCAT(project_name, ' (', branch, ')') as metric\nFROM sonaree_wr02 \nWHERE analysis_date >= NOW() - INTERVAL '7 days'\n  AND status = 'active'\n  AND ncloc > 0\n  AND complexity > 0\nORDER BY ncloc", "refId": "A"}], "title": "代码行数 vs 复杂度散点图", "type": "timeseries"}], "refresh": "5m", "schemaVersion": 36, "style": "dark", "tags": ["SonarQube", "WR02", "项目对比"], "templating": {"list": [{"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "postgres", "uid": "grafana-postgresql-datasource-sonar"}, "definition": "SELECT DISTINCT project_name FROM sonaree_wr02 WHERE status = 'active' ORDER BY project_name", "hide": 0, "includeAll": true, "label": "项目", "multi": true, "name": "project", "options": [], "query": "SELECT DISTINCT project_name FROM sonaree_wr02 WHERE status = 'active' ORDER BY project_name", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-7d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "SonarQube WR02 项目对比分析仪表板", "uid": "sonaree-wr02-comparison", "version": 1, "weekStart": ""}
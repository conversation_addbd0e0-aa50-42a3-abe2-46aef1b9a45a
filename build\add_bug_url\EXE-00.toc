('D:\\vscodedir\\zento_bugaddurl\\dist\\add_bug_url.exe',
 True,
 <PERSON>alse,
 False,
 'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PyInstaller\\bootloader\\images\\icon-console.ico',
 None,
 False,
 False,
 '<?xml version="1.0" encoding="UTF-8" standalone="yes"?><assembly manifestVersion="1.0" xmlns="urn:schemas-microsoft-com:asm.v1"><assemblyIdentity name="add_bug_url" processorArchitecture="amd64" type="win32" version="1.0.0.0"/><trustInfo xmlns="urn:schemas-microsoft-com:asm.v3"><security><requestedPrivileges><requestedExecutionLevel level="asInvoker" uiAccess="false"/></requestedPrivileges></security></trustInfo><dependency><dependentAssembly><assemblyIdentity language="*" name="Microsoft.Windows.Common-Controls" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" type="win32" version="6.0.0.0"/></dependentAssembly></dependency><compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1"><application><supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f0}"/><supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/><supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/><supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/><supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/></application></compatibility><application xmlns="urn:schemas-microsoft-com:asm.v3"><windowsSettings><longPathAware xmlns="http://schemas.microsoft.com/SMI/2016/WindowsSettings">true</longPathAware></windowsSettings></application></assembly>',
 True,
 True,
 False,
 None,
 None,
 None,
 'D:\\vscodedir\\build\\add_bug_url\\add_bug_url.pkg',
 [('PYZ-00.pyz', 'D:\\vscodedir\\build\\add_bug_url\\PYZ-00.pyz', 'PYZ'),
  ('struct',
   'D:\\vscodedir\\build\\add_bug_url\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\vscodedir\\build\\add_bug_url\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\vscodedir\\build\\add_bug_url\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\vscodedir\\build\\add_bug_url\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\vscodedir\\build\\add_bug_url\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_mplconfig',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_mplconfig.py',
   'PYSOURCE'),
  ('add_bug_url', 'D:\\vscodedir\\zento_bugaddurl\\add_bug_url.py', 'PYSOURCE'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('python37.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\python37.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'D:\\Windows Kits\\10\\Windows Performance Toolkit\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('libopenblas.XWYDX2IKJW2NMTWSFYNGFUWKQU3LYTCZ.gfortran-win_amd64.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\.libs\\libopenblas.XWYDX2IKJW2NMTWSFYNGFUWKQU3LYTCZ.gfortran-win_amd64.dll',
   'BINARY'),
  ('_ctypes.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('select.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\DLLs\\select.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\writers.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\window\\indexers.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\testing.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\sparse.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\reshape.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reduction.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\reduction.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\properties.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\parsers.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\ops.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\missing.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\json.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\join.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\interval.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\internals.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\indexing.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\index.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\hashing.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\groupby.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\arrays.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\algos.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\_multiarray_tests.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\lapack_lite.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\linalg\\lapack_lite.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\random\\mtrand.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\random\\_sfc64.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\random\\_philox.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\random\\_pcg64.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\random\\_mt19937.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\random\\bit_generator.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\random\\_generator.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\random\\_common.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_path.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\_path.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_c_internal_utils.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\_c_internal_utils.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\_imagingtk.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\_webp.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\_imagingcms.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\_imaging.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('matplotlib\\backends\\_tkagg.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\backends\\_tkagg.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('matplotlib\\backends\\_backend_agg.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\backends\\_backend_agg.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\markupsafe\\_speedups.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\ft2font.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\ft2font.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_contour.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\_contour.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_tri.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\_tri.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_qhull.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\_qhull.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('kiwisolver\\_cext.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\kiwisolver\\_cext.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_image.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\_image.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\resultproxy.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\cyextension\\resultproxy.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\util.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\cyextension\\util.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\processors.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\cyextension\\processors.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\immutabledict.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\cyextension\\immutabledict.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\collections.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\cyextension\\collections.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('greenlet\\_greenlet.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\greenlet\\_greenlet.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\io\\sas\\_sas.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\sas\\_sas.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\tslib.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\lib.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp37-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\hashtable.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('libcrypto-1_1.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('pandas\\_libs\\window\\MSVCP140.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\window\\MSVCP140.dll',
   'BINARY'),
  ('MSVCP140.dll', 'C:\\Windows\\system32\\MSVCP140.dll', 'BINARY'),
  ('tcl86t.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\DLLs\\tcl86t.dll',
   'BINARY'),
  ('tk86t.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\DLLs\\tk86t.dll',
   'BINARY'),
  ('sqlite3.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll', 'C:\\Windows\\system32\\VCRUNTIME140_1.dll', 'BINARY'),
  ('base_library.zip',
   'D:\\vscodedir\\build\\add_bug_url\\base_library.zip',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Nicosia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('tcl\\encoding\\koi8-r.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.pdf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help.pdf',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulaanbaatar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Kerguelen',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('tcl\\tzdata\\America\\Guatemala',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('tcl\\tzdata\\America\\Belem',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Broken_Hill',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('tcl\\msgs\\lv.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('tk\\palette.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('tk\\msgs\\cs.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Sao_Tome',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.0.tm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\..\\tcl8\\8.6\\http-2.9.0.tm',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('tcl\\tzdata\\America\\Matamoros',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\..\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.pdf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.pdf',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.png',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.png',
   'DATA'),
  ('tcl\\tzdata\\US\\Arizona',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Mariehamn',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('tcl\\tzdata\\America\\Antigua',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('tk\\images\\pwrdLogo150.gif',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('tcl\\encoding\\iso8859-4.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\New_Salem',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Chisinau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('tk\\ttk\\menubutton.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Enderbury',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Melbourne',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('tcl\\msgs\\be.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulan_Bator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('tcl\\tzdata\\America\\Miquelon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-6',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('tcl\\tzdata\\Japan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dakar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Syowa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-8',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Irkutsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6CDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kigali',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('tcl\\tzdata\\Canada\\East-Saskatchewan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('tcl\\msgs\\tr.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.pdf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move.pdf',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novokuznetsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lagos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-14',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('tcl\\encoding\\jis0208.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('tcl\\tzdata\\America\\Jujuy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Brunei',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('tk\\images\\logoMed.gif',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ceuta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'DATA'),
  ('tcl\\tzdata\\ROC',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\..\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Mountain',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'DATA'),
  ('tcl\\msgs\\ar_in.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Merida',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('tcl\\encoding\\gb12345.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('tcl\\msgs\\es_bo.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Famagusta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Catamarca',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Taipei',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yerevan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mbabane',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.png',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home.png',
   'DATA'),
  ('tcl\\tzdata\\PRC',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('tcl\\tzdata\\America\\Guayaquil',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pago_Pago',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Khartoum',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.png',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back.png',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Oral',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Samarkand',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Barthelemy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('tcl\\msgs\\fr_be.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('tcl\\msgs\\ru.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'DATA'),
  ('tk\\msgs\\en.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Uzhgorod',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('tcl\\tzdata\\UTC',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('tcl\\msgs\\es_ec.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('tcl\\tzdata\\Libya',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('tk\\ttk\\xpTheme.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Iqaluit',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Astrakhan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Hobart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('tcl\\msgs\\en_ca.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.svg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back.svg',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Saskatchewan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Samoa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Stanley',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('tcl\\encoding\\cp869.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-10',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Currie',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('tcl\\encoding\\cp862.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move_large.png',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move_large.png',
   'DATA'),
  ('tcl\\msgs\\af_za.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ujung_Pandang',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belfast',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmera',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-darkgrid.mplstyle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-darkgrid.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Dublin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kabul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zurich',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('tcl\\tzdata\\America\\Halifax',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('tcl\\msgs\\mt.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bangkok',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('tcl\\msgs\\zh.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Greenwich',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('tcl\\tzdata\\America\\Inuvik',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('tk\\ttk\\treeview.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fiji',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('tcl\\encoding\\macCyrillic.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.pdf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.pdf',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('tk\\ttk\\panedwindow.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Lima',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('tcl\\msgs\\fa.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('tcl\\tzdata\\America\\Shiprock',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('tcl\\msgs\\fr_ch.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('tcl\\tzdata\\America\\Toronto',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('tcl\\tzdata\\Australia\\ACT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Jersey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Tucuman',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('tk\\images\\pwrdLogo200.gif',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Manila',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Jujuy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tcl\\msgs\\id_id.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'DATA'),
  ('tcl\\msgs\\nl_be.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.pdf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.pdf',
   'DATA'),
  ('tcl\\msgs\\en_gb.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('tcl\\msgs\\af.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('tk\\entry.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('tk\\msgs\\nl.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Majuro',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('tk\\ttk\\button.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kashgar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('tcl\\msgs\\es_hn.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fakaofo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\America\\Rainy_River',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('tcl\\tzdata\\US\\Samoa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kaliningrad',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('tcl\\msgs\\pt_br.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('tcl\\msgs\\el.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'DATA'),
  ('tcl\\msgs\\te_in.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('tcl\\encoding\\dingbats.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimphu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'DATA'),
  ('certifi\\py.typed',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('tcl\\msgs\\ko_kr.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.png',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.png',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Galapagos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Knox',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Chagos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Luanda',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8PDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Ponape',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Phnom_Penh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.svg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home.svg',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Jan_Mayen',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('tcl\\msgs\\en_ph.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ulyanovsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Santiago',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('tcl\\msgs\\es_cr.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('tk\\images\\pwrdLogo75.gif',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Perth',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Comoro',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('tcl\\tzdata\\America\\Guyana',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Efate',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\METADATA',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\METADATA',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'DATA'),
  ('tk\\images\\pwrdLogo175.gif',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lusaka',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('tcl\\msgs\\en_za.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('tcl\\tzdata\\Israel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('tk\\ttk\\notebook.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Campo_Grande',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('tcl\\msgs\\gv_gb.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Istanbul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Banjul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('tk\\obsolete.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chatham',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Oslo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Beulah',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('tcl\\msgs\\es_pr.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('tk\\license.terms',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('tcl\\msgs\\es_pe.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('tcl\\tzdata\\America\\La_Paz',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('tcl\\tzdata\\Australia\\LHI',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-11',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('tcl\\msgs\\es.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home_large.png',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home_large.png',
   'DATA'),
  ('tcl\\tzdata\\America\\Boise',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('matplotlib\\mpl-data\\kpsewhich.lua',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\kpsewhich.lua',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Sydney',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+11',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jakarta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('tk\\ttk\\scale.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Sitka',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('tcl\\tzdata\\America\\Indianapolis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'DATA'),
  ('tcl\\tzdata\\WET',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('tcl\\encoding\\ebcdic.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('tcl\\encoding\\macCentEuro.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('tcl\\msgs\\en_zw.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aden',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('tcl\\tzdata\\America\\Port-au-Prince',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Harare',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-13',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('tk\\icons.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('tcl\\tzdata\\America\\Puerto_Rico',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('tcl\\encoding\\cp1258.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('tcl\\msgs\\kl.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Cocos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\South_Pole',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\ComodRivadavia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Ushuaia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+9',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('tk\\panedwindow.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('tk\\tclIndex',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Bermuda',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Victoria',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('tcl\\opt0.4\\pkgIndex.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sarajevo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('tk\\msgs\\fr.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-7.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'DATA'),
  ('tcl\\encoding\\euc-cn.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qyzylorda',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('tcl\\history.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Brussels',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('tcl\\tzdata\\America\\Creston',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('tk\\ttk\\cursors.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtobe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('tcl\\init.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('tk\\xmfbox.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('tcl\\msgs\\sr.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('tk\\msgs\\eo.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('tcl\\tzdata\\America\\Atka',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('tcl\\tzdata\\America\\Winnipeg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('tcl\\tzdata\\MET',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Djibouti',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Palau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('tk\\clrpick.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('tcl\\msgs\\bn.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Catamarca',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\goog.npz',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\goog.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-pastel.mplstyle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-pastel.mplstyle',
   'DATA'),
  ('tcl\\word.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'DATA'),
  ('tk\\images\\README',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Harbin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('tcl\\tzdata\\America\\Mexico_City',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help_large.png',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help_large.png',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dili',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9YDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('tcl\\tzdata\\America\\Thunder_Bay',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('tk\\scale.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Menominee',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('tcl\\msgs\\nl.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Atikokan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('tcl\\encoding\\cp949.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Calcutta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('tcl\\tzdata\\America\\Costa_Rica',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('tcl\\msgs\\en_bw.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dar_es_Salaam',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('tcl\\msgs\\fa_in.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('tcl\\msgs\\ms_my.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('tcl\\encoding\\cp737.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bratislava',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('tcl\\tzdata\\America\\Santo_Domingo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('tcl\\msgs\\zh_hk.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Maldives',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Vostok',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Accra',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faeroe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Samara',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('tcl\\tzdata\\America\\Dominica',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Honolulu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('tcl\\tzdata\\America\\Juneau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('tcl\\encoding\\cp775.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Whitehorse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('tk\\msgs\\hu.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Sakhalin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tk\\ttk\\spinbox.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Central',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('tk\\scrlbar.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('tcl\\msgs\\hi_in.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vaduz',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuala_Lumpur',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'DATA'),
  ('tcl\\encoding\\cp437.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('tcl\\tzdata\\Arctic\\Longyearbyen',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Malabo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Yukon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-ticks.mplstyle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-ticks.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Monticello',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-7',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+2',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('tk\\msgs\\es.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'DATA'),
  ('tcl\\msgs\\eo.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('tcl\\tzdata\\America\\Swift_Current',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7MDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('tk\\text.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wallis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('tcl\\msgs\\en_sg.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'DATA'),
  ('tcl\\encoding\\iso8859-6.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bangui',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('tcl\\tzdata\\Chile\\Continental',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bishkek',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Rangoon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vladivostok',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('tcl\\tclIndex',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-dark-palette.mplstyle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-dark-palette.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Indianapolis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\America\\Marigot',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('tcl\\tzdata\\US\\Mountain',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Barnaul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('tcl\\tzdata\\America\\Pangnirtung',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('tcl\\tzdata\\America\\Ojinaga',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'DATA'),
  ('tcl\\msgs\\zh_tw.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('tcl\\tzdata\\NZ-CHAT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('tcl\\tzdata\\America\\Chihuahua',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+1',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Tell_City',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tashkent',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macao',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('tk\\ttk\\utils.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Cordoba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('tcl\\opt0.4\\optparse.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Yancowinna',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lome',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('tcl\\msgs\\kl_gl.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('tcl\\encoding\\cp857.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('tk\\ttk\\aquaTheme.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qatar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Reunion',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('tcl\\tzdata\\America\\Regina',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('tcl\\tzdata\\America\\Mendoza',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Choibalsan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Makassar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('tcl\\tzdata\\ROK',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmara',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zagreb',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('tcl\\msgs\\is.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Jamaica',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vincennes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pitcairn',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('tcl\\msgs\\he.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Saigon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('tk\\tearoff.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pyongyang',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Libreville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bujumbura',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guadalcanal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bissau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Queensland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tahiti',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('tcl\\tzdata\\America\\New_York',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('tcl\\msgs\\ru_ua.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kosrae',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vilnius',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('tcl\\tzdata\\America\\Cuiaba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tiraspol',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('tcl\\msgs\\ar_sy.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('tk\\bgerror.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\percent_bachelors_degrees_women_usa.csv',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\percent_bachelors_degrees_women_usa.csv',
   'DATA'),
  ('tk\\dialog.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('tcl\\tzdata\\America\\Yellowknife',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('tk\\images\\logo.eps',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('tcl\\tzdata\\America\\Godthab',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Palmer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuching',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashgabat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Brisbane',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bahrain',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+6',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bucharest',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\WHEEL',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\WHEEL',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Timbuktu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('tcl\\msgs\\sq.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('tcl\\msgs\\eu.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Lower_Princes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('tk\\msgs\\de.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Virgin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'DATA'),
  ('tcl\\tzdata\\America\\Noronha',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('tcl\\msgs\\ca.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yekaterinburg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\GB-Eire',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Mawson',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Isle_of_Man',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'DATA'),
  ('tcl\\encoding\\cp932.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayman',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'DATA'),
  ('tcl\\tzdata\\America\\Santa_Isabel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('tcl\\msgs\\kok.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('tk\\images\\logoLarge.gif',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia_Banderas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kiev',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Urumqi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('tcl\\msgs\\gl.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward_large.png',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('tcl\\tzdata\\America\\Louisville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('tcl\\tzdata\\America\\Blanc-Sablon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('tk\\ttk\\entry.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Douala',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Christmas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\Acre',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Atyrau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('tcl\\msgs\\ar_jo.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Manaus',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Curacao',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'DATA'),
  ('tk\\menu.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('tcl\\tzdata\\Zulu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific-New',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('tcl\\msgs\\ga_ie.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nouakchott',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\LICENSE',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\LICENSE',
   'DATA'),
  ('tcl\\tzdata\\HST',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('tcl\\encoding\\koi8-u.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Velho',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Cape_Verde',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pohnpei',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('tk\\msgs\\ru.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Atlantic',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('tk\\mkpsenc.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('tcl\\tzdata\\America\\Detroit',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UCT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('tcl\\encoding\\iso8859-8.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('tcl\\tzdata\\Australia\\South',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('tcl\\tzdata\\America\\Edmonton',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('tcl\\msgs\\ta_in.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ljubljana',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Beirut',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('tcl\\msgs\\bg.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Kralendijk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.pdf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.pdf',
   'DATA'),
  ('tcl\\tzdata\\PST8PDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Rothera',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\General',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\McMurdo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bamako',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('tcl\\msgs\\pt.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.svg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help.svg',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('tcl\\tzdata\\America\\Adak',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('tk\\images\\tai-ku.gif',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Gaza',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back_large.png',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back_large.png',
   'DATA'),
  ('tcl\\tzdata\\America\\Tortola',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaNorte',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yakutsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('tcl\\msgs\\fr_ca.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('tk\\msgbox.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Wayne',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Damascus',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Porto-Novo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\DeNoronha',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UTC',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kampala',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('tcl\\encoding\\cp861.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('tcl\\msgs\\es_ni.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\St_Helena',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.svg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.svg',
   'DATA'),
  ('tcl\\tzdata\\America\\Boa_Vista',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Petersburg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('tcl\\encoding\\cp936.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('tcl\\tzdata\\NZ',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('tcl\\tzdata\\GB',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('tcl\\safe.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\RECORD',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\RECORD',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('tcl\\tzdata\\GMT0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Zulu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('tcl\\msgs\\nb.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('tcl\\encoding\\gb2312-raw.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('tcl\\msgs\\es_ar.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vienna',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Johns',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('tcl\\msgs\\es_py.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('tcl\\encoding\\iso8859-14.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Omsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('tcl\\encoding\\macRoman.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\msft.csv',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\msft.csv',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('tcl\\encoding\\cp866.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('tcl\\tzdata\\GMT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('tcl\\msgs\\sk.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Seoul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-2',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('tk\\console.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Midway',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jayapura',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+5',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('tcl\\encoding\\euc-kr.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Paris',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('tcl\\msgs\\en_au.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('tcl\\msgs\\en_hk.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Abidjan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('tk\\images\\logo64.gif',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wake',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('tcl\\tzdata\\Portugal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Winamac',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('tcl\\msgs\\mr.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('tcl\\encoding\\cp1250.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dubai',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hebron',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mayotte',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('tcl\\tzdata\\Eire',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Norfolk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Eastern',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.svg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Marquesas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('tcl\\encoding\\cp950.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('tcl\\tzdata\\MST7MDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chuuk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('tcl\\tzdata\\US\\Hawaii',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('tcl\\tzdata\\America\\Yakutat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yangon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'DATA'),
  ('tcl\\encoding\\euc-jp.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ndjamena',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('tcl\\tzdata\\US\\Alaska',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('tcl\\tzdata\\America\\Phoenix',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('tcl\\encoding\\macTurkish.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('tcl\\msgs\\hi.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kwajalein',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Noumea',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('tk\\msgs\\sv.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('tk\\msgs\\en_gb.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hovd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Adelaide',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('tcl\\tzdata\\Poland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('tcl\\tzdata\\Kwajalein',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.png',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.png',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Tegucigalpa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('tcl\\msgs\\sl.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayenne',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Riga',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('tk\\ttk\\winTheme.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tarawa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Antananarivo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('tcl\\msgs\\es_ve.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Monterrey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maseru',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('tcl\\encoding\\ascii.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('tcl\\msgs\\sh.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('tcl\\tzdata\\America\\Managua',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('tk\\listbox.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('tcl\\encoding\\cp864.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-3.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('tk\\msgs\\el.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.svg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib_128.ppm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib_128.ppm',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lord_Howe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('tcl\\tzdata\\America\\Santarem',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('tcl\\msgs\\en_in.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-poster.mplstyle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-poster.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.svg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.svg',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Pacific',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Gaborone',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('tcl\\tzdata\\US\\Indiana-Starke',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('tcl\\msgs\\lt.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('tcl\\tzdata\\Iran',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Nelson',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Acre',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Azores',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Niamey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('tk\\ttk\\clamTheme.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('tcl\\encoding\\iso2022.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tripoli',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Madrid',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'DATA'),
  ('tcl\\encoding\\gb2312.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Center',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('tcl\\tzdata\\America\\Rosario',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Blantyre',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\README.txt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\README.txt',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('tcl\\encoding\\symbol.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('tcl\\tzdata\\America\\Caracas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Conakry',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Reykjavik',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'DATA'),
  ('tcl\\tzdata\\Singapore',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sofia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('tk\\iconlist.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('tcl\\encoding\\macDingbats.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Aruba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Copenhagen',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('tcl\\msgs\\pl.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('tk\\fontchooser.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Nicosia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('tcl\\msgs\\es_uy.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kirov',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('tk\\msgs\\pl.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.pdf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back.pdf',
   'DATA'),
  ('tcl\\encoding\\cp1251.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('tcl\\tzdata\\US\\Eastern',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baghdad',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zaporozhye',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('tcl\\tzdata\\Australia\\NSW',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('tcl\\tzdata\\GMT+0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Brazzaville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('tcl\\tzdata\\EST',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('tcl\\encoding\\iso8859-13.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('tcl\\msgs\\es_mx.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('tcl\\msgs\\hu.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('tcl\\msgs\\ms.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-dark.mplstyle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-dark.mplstyle',
   'DATA'),
  ('tcl\\encoding\\iso8859-9.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('tcl\\tzdata\\Egypt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Johnston',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('tcl\\tzdata\\America\\Rio_Branco',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maputo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('tcl\\encoding\\cp1254.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('tcl\\tzdata\\Hongkong',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Moscow',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tomsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('tcl\\tzdata\\Jamaica',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('tcl\\encoding\\iso8859-2.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Warsaw',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Simferopol',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('tcl\\tzdata\\America\\Maceio',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vevay',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuwait',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave_large.png',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.svg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.svg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Gambier',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('tcl\\encoding\\cp850.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('tcl\\msgs\\en_nz.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('tcl\\msgs\\fo_fo.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Bougainville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('tcl\\tzdata\\America\\Coral_Harbour',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\La_Rioja',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('tcl\\tzdata\\America\\Montserrat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('tcl\\msgs\\et.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Amsterdam',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guam',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pontianak',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('tcl\\tzdata\\America\\Mazatlan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('tcl\\tzdata\\America\\Guadeloupe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('tcl\\tzdata\\America\\Martinique',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimbu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('tcl\\encoding\\cp860.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('tcl\\tzdata\\Navajo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-deep.mplstyle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-deep.mplstyle',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'DATA'),
  ('tcl\\tzdata\\America\\Sao_Paulo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('tcl\\tzdata\\Cuba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tunis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-paper.mplstyle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-paper.mplstyle',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('tk\\ttk\\defaults.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Fortaleza',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Kitts',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('tcl\\msgs\\vi.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Prague',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+10',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('tcl\\tzdata\\America\\Paramaribo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Singapore',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('tk\\msgs\\it.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('tcl\\msgs\\es_cl.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-white.mplstyle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-white.mplstyle',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('tcl\\tzdata\\America\\Scoresbysund',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kathmandu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('tcl\\tzdata\\America\\Recife',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ouagadougou',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('tcl\\tzdata\\CET',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('tcl\\msgs\\it_ch.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('tcl\\tzdata\\America\\Nipigon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Casey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('tk\\comdlg.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots_large.png',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots_large.png',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('tcl\\tzdata\\America\\Los_Angeles',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('tcl\\tzdata\\America\\Danmarkshavn',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('tcl\\tzdata\\America\\Barbados',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faroe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('tcl\\encoding\\shiftjis.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Lucia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nairobi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('tcl\\tzdata\\Australia\\West',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('tcl\\tzdata\\Greenwich',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('tcl\\encoding\\macJapan.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-15.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\East',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('tcl\\msgs\\cs.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ust-Nera',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Madeira',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Berlin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novosibirsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('tk\\tkfbox.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Cambridge_Bay',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Anadyr',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('tcl\\tzdata\\GMT-0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('tcl\\tm.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('tcl\\tzdata\\Europe\\London',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mauritius',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('tcl\\tzdata\\America\\Panama',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('tk\\images\\logo100.gif',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('tcl\\tzdata\\America\\Denver',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Srednekolymsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Araguaina',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-5',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('tcl\\msgs\\en_ie.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\top_level.txt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\top_level.txt',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Riyadh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Stockholm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Freetown',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('tcl\\encoding\\tis-620.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('tk\\ttk\\classicTheme.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Chile\\EasterIsland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Louisville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Monrovia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\HST10',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('tcl\\encoding\\cp1256.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-1.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('tcl\\msgs\\fa_ir.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('tcl\\encoding\\cp1255.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Skopje',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('tcl\\msgs\\de_be.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Saratov',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('tcl\\msgs\\it.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('tk\\button.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('tk\\ttk\\combobox.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('tcl\\encoding\\iso2022-jp.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+8',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('tcl\\tzdata\\America\\Bogota',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('tcl\\tzdata\\America\\Hermosillo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\El_Aaiun',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Marengo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('tcl\\msgs\\es_pa.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('tk\\megawidget.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vientiane',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Luis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'DATA'),
  ('tcl\\msgs\\es_co.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Canberra',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Yap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('tcl\\tzdata\\America\\Havana',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Macquarie',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Canary',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('tcl\\msgs\\gl_es.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Grand_Turk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('tcl\\tzdata\\America\\Montevideo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Muscat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('tcl\\tzdata\\America\\Punta_Arenas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('tcl\\msgs\\fo.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('tk\\ttk\\fonts.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('tk\\ttk\\ttk.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Magadan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('tcl\\encoding\\iso8859-5.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Truk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+12',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('certifi\\cacert.pem',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('tk\\safetk.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('tcl\\tzdata\\Australia\\North',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Monaco',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('tcl\\tzdata\\America\\Vancouver',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('tcl\\encoding\\cp1253.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('tcl\\encoding\\cp1252.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Amman',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('tcl\\msgs\\es_do.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+3',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('tcl\\tzdata\\America\\Moncton',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('tk\\optMenu.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('tcl\\tzdata\\America\\Nassau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-notebook.mplstyle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-notebook.mplstyle',
   'DATA'),
  ('tcl\\encoding\\jis0201.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('tcl\\msgs\\ar.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('tcl\\msgs\\mk.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('tcl\\tzdata\\W-SU',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Juan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dushanbe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tel_Aviv',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Khandyga',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Addis_Ababa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.png',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.png',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('tcl\\tzdata\\America\\Asuncion',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+4',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('tk\\unsupported.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('tcl\\encoding\\iso8859-16.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaSur',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tbilisi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('tcl\\msgs\\hr.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Knox_IN',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Nauru',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('tcl\\encoding\\macThai.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'DATA'),
  ('tcl\\msgs\\kok_in.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Auckland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.svg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.png',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help.png',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Funafuti',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4ADT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Ensenada',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('tcl\\msgs\\id.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('tcl\\encoding\\big5.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('tcl\\tzdata\\Iceland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-3',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'DATA'),
  ('tcl\\msgs\\zh_cn.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dacca',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('tcl\\tzdata\\America\\Chicago',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belgrade',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('tcl\\encoding\\cp852.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tallinn',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('tcl\\tzdata\\America\\Belize',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.pdf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home.pdf',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Istanbul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-talk.mplstyle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-talk.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Shanghai',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('tcl\\tzdata\\America\\Metlakatla',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('tcl\\encoding\\cp863.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Darwin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Budapest',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('tcl\\tzdata\\America\\Buenos_Aires',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('tcl\\msgs\\ga.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kamchatka',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('tcl\\tzdata\\America\\Anchorage',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Almaty',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Salta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Newfoundland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashkhabad',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.14.tm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\..\\tcl8\\8.4\\platform-1.0.14.tm',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ho_Chi_Minh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'DATA'),
  ('tk\\ttk\\altTheme.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('tcl\\msgs\\kw_gb.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('tcl\\msgs\\kw.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lindeman',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('tcl\\msgs\\fr.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-colorblind.mplstyle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-colorblind.mplstyle',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jerusalem',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Windhoek',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('tcl\\msgs\\nn.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('tk\\images\\pwrdLogo100.gif',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('tcl\\tzdata\\UCT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Minsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('tcl\\msgs\\mr_in.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('tcl\\msgs\\te.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('tcl\\msgs\\ja.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Eirunepe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Vincent',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-4',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Rarotonga',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Lisbon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hong_Kong',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.png',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move.png',
   'DATA'),
  ('tcl\\encoding\\macCroatian.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Apia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'DATA'),
  ('tcl\\encoding\\macRomania.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('tk\\choosedir.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Casablanca',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Algiers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'DATA'),
  ('tcl\\msgs\\bn_in.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Buenos_Aires',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tongatapu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lubumbashi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('tcl\\tzdata\\America\\Thule',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vatican',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('tk\\ttk\\scrollbar.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('tcl\\encoding\\cp865.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Grenada',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('tcl\\tzdata\\America\\Glace_Bay',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('tcl\\msgs\\en_be.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('tcl\\msgs\\fi.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Cairo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('tcl\\tzdata\\America\\Nome',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Guernsey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('tcl\\msgs\\de_at.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-9',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\INSTALLER',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\INSTALLER',
   'DATA'),
  ('tk\\focus.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('tcl\\encoding\\cp1257.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('tcl\\msgs\\eu_es.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Eucla',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Mendoza',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('tcl\\encoding\\iso2022-kr.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Troll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chita',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('tcl\\tzdata\\US\\Central',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('tcl\\tzdata\\America\\Cordoba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('tcl\\msgs\\sw.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5EDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.0.tm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\..\\tcl8\\8.5\\tcltest-2.5.0.tm',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('tk\\pkgIndex.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('tcl\\msgs\\ta.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Andorra',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kolkata',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('tcl\\encoding\\gb1988.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('tcl\\encoding\\cp855.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('tcl\\package.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Helsinki',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tirane',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('tcl\\parray.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('tk\\msgs\\da.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('tcl\\msgs\\ro.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('tcl\\http1.0\\pkgIndex.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('tcl\\msgs\\uk.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Katmandu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Tasmania',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Podgorica',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('tcl\\tzdata\\America\\Port_of_Spain',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('tcl\\encoding\\iso8859-10.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Saipan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('tcl\\encoding\\jis0212.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Johannesburg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('tcl\\tzdata\\America\\Goose_Bay',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chongqing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('tk\\msgs\\pt.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('tcl\\msgs\\es_sv.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Luxembourg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('tk\\ttk\\sizegrip.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baku',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('tcl\\msgs\\ar_lb.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('tcl\\msgs\\th.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'DATA'),
  ('tcl\\msgs\\zh_sg.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Thomas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Malta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('tcl\\http1.0\\http.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('tcl\\encoding\\macUkraine.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Rome',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Easter',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('tcl\\tzdata\\US\\East-Indiana',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('tcl\\msgs\\da.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chungking',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kinshasa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('tcl\\encoding\\macIceland.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dhaka',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Colombo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Port_Moresby',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('tcl\\tzdata\\Europe\\San_Marino',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Gibraltar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Krasnoyarsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Volgograd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('tcl\\msgs\\ko.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tk\\spinbox.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Cancun',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-bright.mplstyle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-bright.mplstyle',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('tcl\\tzdata\\America\\El_Salvador',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('tcl\\tzdata\\US\\Michigan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('tcl\\tzdata\\America\\Montreal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-12',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tehran',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\West',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Niue',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('tcl\\tzdata\\EET',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('tk\\ttk\\progress.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mahe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'DATA'),
  ('tcl\\tzdata\\US\\Aleutian',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'DATA'),
  ('tcl\\msgs\\es_gt.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('tcl\\tzdata\\Universal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'DATA'),
  ('tcl\\tzdata\\America\\Resolute',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('tcl\\encoding\\ksc5601.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('tk\\ttk\\vistaTheme.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Busingen',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\DumontDUrville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Universal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson_Creek',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Davis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Juba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('tcl\\tzdata\\MST',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('tcl\\msgs\\gv.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Athens',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Karachi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.png',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.png',
   'DATA'),
  ('matplotlib\\mpl-data\\matplotlibrc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\matplotlibrc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+7',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('tcl\\tzdata\\America\\Rankin_Inlet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('tcl\\clock.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('tcl\\tzdata\\America\\Anguilla',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mogadishu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-muted.mplstyle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-muted.mplstyle',
   'DATA'),
  ('tk\\images\\pwrdLogo.eps',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tokyo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('tcl\\tzdata\\CST6CDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn.mplstyle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-1',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('tcl\\encoding\\macGreek.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('tcl\\msgs\\sv.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('tcl\\encoding\\cp874.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('tcl\\tzdata\\EST5EDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\South_Georgia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('tcl\\tzdata\\Turkey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('tk\\tk.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tk8.6\\tk.tcl',
   'DATA'),
  ('tcl\\msgs\\de.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\logo2.png',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\logo2.png',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kiritimati',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('tcl\\tzdata\\America\\Tijuana',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('tcl\\auto.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-whitegrid.mplstyle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-whitegrid.mplstyle',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA')],
 [],
 False,
 False,
 1747743987,
 [('run.exe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\run.exe',
   'EXECUTABLE')])

-- 数据图表分析示例查询
-- 这些查询可以直接用于生成各种数据图表

-- ========================================
-- 1. 时间趋势分析
-- ========================================

-- 1.1 每日提交数量趋势（用于折线图）
SELECT 
    commit_date,
    total_commits,
    total_issues,
    total_critical_issues
FROM daily_commit_stats
WHERE commit_date >= CURRENT_DATE - INTERVAL '30 days'
ORDER BY commit_date;

-- 1.2 月度提交趋势（用于柱状图）
SELECT 
    month_start,
    total_commits,
    unique_authors,
    total_issues,
    clean_rate_percent
FROM monthly_trend_stats
WHERE commit_year >= EXTRACT(YEAR FROM CURRENT_DATE) - 1
ORDER BY month_start;

-- 1.3 每周提交分布（用于热力图）
SELECT 
    commit_year,
    commit_week,
    COUNT(*) as commit_count,
    ROUND(AVG(changed_lines)::numeric, 2) as avg_changed_lines
FROM commit_metrics
WHERE commit_time >= CURRENT_DATE - INTERVAL '6 months'
GROUP BY commit_year, commit_week
ORDER BY commit_year, commit_week;

-- ========================================
-- 2. 质量分析
-- ========================================

-- 2.1 问题严重程度分布（用于饼图）
SELECT 
    'Blocker' as severity,
    SUM(sq_blocker) as count
FROM commit_metrics
WHERE commit_time >= CURRENT_DATE - INTERVAL '30 days'
UNION ALL
SELECT 
    'Critical' as severity,
    SUM(sq_critical) as count
FROM commit_metrics
WHERE commit_time >= CURRENT_DATE - INTERVAL '30 days'
UNION ALL
SELECT 
    'Major' as severity,
    SUM(sq_major) as count
FROM commit_metrics
WHERE commit_time >= CURRENT_DATE - INTERVAL '30 days'
UNION ALL
SELECT 
    'Minor' as severity,
    SUM(sq_minor) as count
FROM commit_metrics
WHERE commit_time >= CURRENT_DATE - INTERVAL '30 days'
UNION ALL
SELECT 
    'Info' as severity,
    SUM(sq_info) as count
FROM commit_metrics
WHERE commit_time >= CURRENT_DATE - INTERVAL '30 days';

-- 2.2 代码质量等级分布（用于饼图）
SELECT 
    quality_level,
    commit_count,
    percentage
FROM quality_level_distribution;

-- 2.3 变更大小与质量问题关系（用于散点图）
SELECT 
    changed_lines,
    total_issues,
    issue_density,
    quality_level,
    change_size_category
FROM commit_metrics
WHERE commit_time >= CURRENT_DATE - INTERVAL '30 days'
ORDER BY changed_lines;

-- ========================================
-- 3. 开发者分析
-- ========================================

-- 3.1 开发者贡献排名（用于柱状图）
SELECT 
    author,
    total_commits,
    total_changed_lines,
    clean_rate_percent
FROM author_contribution_stats
WHERE total_commits >= 5
ORDER BY total_commits DESC
LIMIT 20;

-- 3.2 开发者质量对比（用于雷达图）
SELECT 
    author,
    total_commits,
    avg_changed_lines,
    avg_issue_density,
    clean_rate_percent
FROM author_contribution_stats
WHERE total_commits >= 10
ORDER BY clean_rate_percent DESC
LIMIT 10;

-- 3.3 评审人活跃度（用于柱状图）
SELECT 
    reviewer,
    review_count,
    unique_authors_reviewed,
    avg_issues_reviewed
FROM reviewer_stats
WHERE review_count >= 5
ORDER BY review_count DESC
LIMIT 15;

-- ========================================
-- 4. 项目分析
-- ========================================

-- 4.1 项目活跃度排名（用于柱状图）
SELECT 
    gerrit_project,
    total_commits,
    unique_authors,
    total_changed_lines
FROM project_quality_stats
ORDER BY total_commits DESC
LIMIT 15;

-- 4.2 项目质量对比（用于雷达图）
SELECT 
    gerrit_project,
    total_commits,
    avg_issue_density,
    avg_comment_density,
    avg_duplication_density,
    clean_rate_percent
FROM project_quality_stats
WHERE total_commits >= 10
ORDER BY clean_rate_percent DESC
LIMIT 10;

-- 4.3 分支质量对比（用于柱状图）
SELECT 
    branch,
    total_commits,
    total_issues,
    clean_rate_percent
FROM branch_stats
WHERE total_commits >= 5
ORDER BY clean_rate_percent DESC;

-- ========================================
-- 5. 高级分析
-- ========================================

-- 5.1 问题密度趋势（用于折线图）
SELECT 
    commit_date,
    ROUND(AVG(issue_density)::numeric, 2) as avg_issue_density,
    ROUND(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY issue_density)::numeric, 2) as median_issue_density,
    ROUND(PERCENTILE_CONT(0.9) WITHIN GROUP (ORDER BY issue_density)::numeric, 2) as p90_issue_density
FROM commit_metrics
WHERE commit_date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY commit_date
ORDER BY commit_date;

-- 5.2 代码复杂度分布（用于直方图）
SELECT 
    CASE 
        WHEN complexity <= 10 THEN '0-10'
        WHEN complexity <= 50 THEN '11-50'
        WHEN complexity <= 100 THEN '51-100'
        WHEN complexity <= 200 THEN '101-200'
        ELSE '200+'
    END as complexity_range,
    COUNT(*) as count
FROM commit_metrics
WHERE complexity IS NOT NULL
GROUP BY complexity_range
ORDER BY 
    CASE complexity_range
        WHEN '0-10' THEN 1
        WHEN '11-50' THEN 2
        WHEN '51-100' THEN 3
        WHEN '101-200' THEN 4
        WHEN '200+' THEN 5
    END;

-- 5.3 提交时间分布（用于热力图）
SELECT 
    commit_hour,
    COUNT(*) as commit_count,
    ROUND(AVG(changed_lines)::numeric, 2) as avg_changed_lines
FROM commit_metrics
WHERE commit_time >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY commit_hour
ORDER BY commit_hour;

-- 5.4 代码重复度分析（用于散点图）
SELECT 
    duplicated_lines_density,
    total_issues,
    changed_lines,
    gerrit_project
FROM commit_metrics
WHERE duplicated_lines_density IS NOT NULL 
  AND duplicated_lines_density > 0
  AND commit_time >= CURRENT_DATE - INTERVAL '30 days'
ORDER BY duplicated_lines_density DESC;

-- ========================================
-- 6. 实时监控查询
-- ========================================

-- 6.1 今日提交统计
SELECT 
    COUNT(*) as today_commits,
    COUNT(DISTINCT author) as today_authors,
    SUM(changed_lines) as today_changed_lines,
    SUM(total_issues) as today_issues,
    SUM(critical_issues) as today_critical_issues
FROM commit_metrics
WHERE commit_date = CURRENT_DATE;

-- 6.2 本周质量趋势
SELECT 
    commit_date,
    COUNT(*) as commits,
    ROUND(AVG(issue_density)::numeric, 2) as avg_issue_density,
    COUNT(CASE WHEN quality_level = 'clean' THEN 1 END) as clean_commits
FROM commit_metrics
WHERE commit_date >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY commit_date
ORDER BY commit_date;

-- 6.3 高风险提交（用于告警）
SELECT 
    commit_id,
    author,
    gerrit_project,
    changed_lines,
    total_issues,
    critical_issues,
    quality_level,
    commit_time
FROM commit_metrics
WHERE (sq_blocker > 0 OR sq_critical > 0)
  AND commit_time >= CURRENT_DATE - INTERVAL '7 days'
ORDER BY critical_issues DESC, commit_time DESC;

-- ========================================
-- 7. 对比分析
-- ========================================

-- 7.1 本月与上月对比
WITH current_month AS (
    SELECT 
        COUNT(*) as commits,
        SUM(changed_lines) as changed_lines,
        SUM(total_issues) as issues,
        AVG(issue_density) as avg_issue_density
    FROM commit_metrics
    WHERE commit_year = EXTRACT(YEAR FROM CURRENT_DATE)
      AND commit_month = EXTRACT(MONTH FROM CURRENT_DATE)
),
previous_month AS (
    SELECT 
        COUNT(*) as commits,
        SUM(changed_lines) as changed_lines,
        SUM(total_issues) as issues,
        AVG(issue_density) as avg_issue_density
    FROM commit_metrics
    WHERE commit_year = EXTRACT(YEAR FROM CURRENT_DATE - INTERVAL '1 month')
      AND commit_month = EXTRACT(MONTH FROM CURRENT_DATE - INTERVAL '1 month')
)
SELECT 
    'Current Month' as period,
    commits,
    changed_lines,
    issues,
    avg_issue_density
FROM current_month
UNION ALL
SELECT 
    'Previous Month' as period,
    commits,
    changed_lines,
    issues,
    avg_issue_density
FROM previous_month;

-- 7.2 项目间质量对比
SELECT 
    gerrit_project,
    total_commits,
    ROUND(AVG(issue_density)::numeric, 2) as avg_issue_density,
    ROUND(AVG(comment_lines_density)::numeric, 2) as avg_comment_density,
    ROUND(AVG(duplicated_lines_density)::numeric, 2) as avg_duplication_density,
    clean_rate_percent
FROM project_quality_stats
WHERE total_commits >= 20
ORDER BY avg_issue_density ASC; 
#!/bin/bash
# 读取commit提交信息，校验格式
# 函数用于校验commit格式
# 标题的命名格式:
# 1).标题必须包含模块名，模块名可以为英文数字-_混合.
# 2).[BUG-*]为可选，但是模块名后一旦有[BUG-字段，那么就需要匹配格式module[BUG-(1-8)位数字]的格式。
# 3).不管有没有[BUG-*],英文冒号后面需要有空格.

# 定义变量
projectName="$2"
commitID="$(echo "$@" | awk -F '--newrev ' '{print $2}' | awk '{print $1}')"
uploaderUsername="$(echo "$@" | awk -F '--uploader-username ' '{print $2}' | awk '{print $1}')"
gerritPath="/home/<USER>/gerrit_site/git"
projectPath="${gerritPath}/${projectName}"
iniCommit=${commitID:0:7}
iniPath="/home/<USER>/gerrit_site/hooks/inidir/commit-received"
iniTittle="${iniPath}/tittle_${iniCommit}"
iniContent="${iniPath}/content_${iniCommit}"

if [ ! -d ${iniPath} ];then
    mkdir -p ${iniPath}
fi

# 获取标题和正文的commit提交信息
commitTittle="$(git --git-dir="${projectPath}.git" log --pretty=format:"%s" -n 1 ${commitID})"
commitMsg="$(git --git-dir="${projectPath}.git" log --pretty=format:"%b" -n 1 ${commitID})"

# 调试打印信息
echo "$@" > /home/<USER>/gerrit_site/hooks/commit-received.ini
echo "${commitTittle}"  > ${iniTittle}
echo "${commitMsg}"  >  ${iniContent}

# 函数用于校验标题格式
check_tittle_commit_format() {
  local commit_msg=$(cat $1)
  if [[ $commit_msg =~ ":" ]];then
      echo "Commit 标题信息是有效的."
  else
      echo "错误: Commit标题信息不符合格式要求."
      echo "示例: 'APP: xxx'"
  fi
#  local pattern='^[a-zA-Z0-9_-]+(\[BUG\-[0-9]{1,8}\])?:\s.*$'
#
#  if [[ $commit_msg =~ $pattern ]]; then
#    # 如果有BUG字段，确保它后面有ID
#    if [[ $commit_msg == *"[BUG-"* ]]; then
#      if ! [[ $commit_msg =~ \[BUG\-[0-9]{1,8}\]: ]]; then
#        echo "错误: Commit标题信息当有BUG字段时应该带上BUG id,示例：[BUG-001] ."
#        exit 1
#      fi
#    fi
#    echo "成功: Commit 标题信息是有效的."
#  else
#    echo "错误: Commit标题信息不符合格式要求."
#    echo "示例: 'APP: xxx'或者'GUI[BUG-001]: XXX.其中[BUG-001]为可写项,英文冒号':'为必写项。"
#    exit 1
#  fi
}

# 测试用例
commit_messages=(
  "module[BUG-1234]: fix some issue"
  "module[BUG-1]: quick fix"
  "module: some change"
  "module[BUG-123456789]: wrong id length"
  "module[BUG-]: missing id"
  "module[BUG]: missing id"
  "123[BUG-1234]: numeric module name"
  "module[BUG-1234] fix without colon"
  "module[BUG-1234]:fix without space"
)

# 遍历测试用例并校验
#for msg in "${commit_messages[@]}"; do
#  echo "commit:$msg"
#  validate_commit_format "$msg"
#done

# 检查正文提交信息格式
check_content_commit_format() {
    content_message="$1"
    uploaderUsername="$2"
    # 判断uploaderUsername是否为gerritadmin,如果是则跳过
    if [[ "$uploaderUsername" == "gerritadmin" ]]; then
        echo "跳过: 提交者为gerritadmin，跳过正文提交信息格式校验."
        exit 0
    fi
    
    # 检查ChangeId格式
    if [[ $(cat $content_message  | grep '^Change-Id: ' |wc -l) < 1 ]];then
       echo "错误: 'Change-Id: ' 必须顶格写，且冒号后有空格."
       exit 1
    fi

    # 检查Signed-off-by格式
    if [[ $(cat $content_message  | grep '^Signed-off-by: ' |wc -l) -lt 1 ]];then
       echo "错误: 'Singed-off-by ' 必须顶格写，且冒号后有空格."
       exit 1
    fi

    # 检查 '[details]: ' 的格式
    if [[ $(cat $content_message | grep -c '\[detail') -gt 0 ]]
    then
      depend_content="$(cat $content_message | grep '\[detail')"
      if [[ $(echo $depend_content| grep -c '^\[detail]: ') -ge 1 ]]
      then
         echo ""
      else
         echo "错误: '[detail]: ' 必须顶格写，且冒号后有空格."
         exit 1
      fi
    fi

    # 检查 '[depended on]: ' 的格式
    if [[ $(cat $content_message | grep -c '\[depend') -gt 0 ]]
    then
      depend_content="$(cat $content_message | grep '\[depen')"
      if [[ $(echo $depend_content| grep -c '^\[depended on]: ') -ge 1 ]]
      then
         echo ""
      else
         echo "错误: '[depended on]: ' 必须顶格写，且冒号后有空格."
         exit 1
      fi
    fi
}

# 检查标题
check_tittle_commit_format "${iniTittle}" "${uploaderUsername}"

# 检查正文
check_content_commit_format "${iniContent}" "${uploaderUsername}"
exit 
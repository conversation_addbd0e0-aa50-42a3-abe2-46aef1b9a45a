apiVersion: 1

datasources:
  - name: PostgreSQL-SonarQube
    type: postgres
    access: proxy
    url: postgres:5432
    database: sonar_grafana
    user: grafana_user
    secureJsonData:
      password: grafana_pass
    jsonData:
      sslmode: disable
      maxOpenConns: 100
      maxIdleConns: 100
      maxIdleConnsAuto: true
      connMaxLifetime: 14400
      postgresVersion: 1500
      timescaledb: false
    isDefault: true
    editable: true

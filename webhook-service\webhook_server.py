#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import logging
import requests
from datetime import datetime
from flask import Flask, request, jsonify
from dateutil import parser

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/app/logs/webhook.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# VictoriaMetrics配置
VICTORIA_METRICS_URL = os.getenv('VICTORIA_METRICS_URL', 'http://localhost:8428')

def send_to_victoria_metrics(metrics_data):
    """发送指标数据到VictoriaMetrics"""
    try:
        url = f"{VICTORIA_METRICS_URL}/api/v1/import/prometheus"
        headers = {'Content-Type': 'text/plain'}
        
        response = requests.post(url, data=metrics_data, headers=headers)
        if response.status_code == 204:
            logger.info("成功发送指标到VictoriaMetrics")
            return True
        else:
            logger.error(f"发送指标失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        logger.error(f"发送指标到VictoriaMetrics时出错: {e}")
        return False

def convert_sonar_webhook_to_metrics(webhook_data):
    """将SonarQube webhook数据转换为Prometheus格式的指标"""
    try:
        # 解析webhook数据
        project_key = webhook_data.get('project', {}).get('key', 'unknown')
        project_name = webhook_data.get('project', {}).get('name', 'unknown')
        task_id = webhook_data.get('taskId', 'unknown')
        status = webhook_data.get('status', 'unknown')
        analysed_at = webhook_data.get('analysedAt', datetime.now().isoformat())
        
        # 解析时间戳
        try:
            timestamp = int(parser.parse(analysed_at).timestamp() * 1000)
        except:
            timestamp = int(datetime.now().timestamp() * 1000)
        
        # 质量门状态指标
        quality_gate_status = 1 if status == 'SUCCESS' else 0
        
        # 基础指标
        metrics_lines = []
        
        # 质量门状态
        metrics_lines.append(
            f'sonarqube_quality_gate_status{{project_key="{project_key}",project_name="{project_name}",task_id="{task_id}"}} {quality_gate_status} {timestamp}'
        )
        
        # 分析完成指标
        metrics_lines.append(
            f'sonarqube_analysis_completed{{project_key="{project_key}",project_name="{project_name}",status="{status}"}} 1 {timestamp}'
        )
        
        # 如果有质量门详细信息
        if 'qualityGate' in webhook_data:
            qg_data = webhook_data['qualityGate']
            qg_status = qg_data.get('status', 'unknown')
            
            metrics_lines.append(
                f'sonarqube_quality_gate_detailed{{project_key="{project_key}",project_name="{project_name}",qg_status="{qg_status}"}} 1 {timestamp}'
            )
            
            # 条件详情
            if 'conditions' in qg_data:
                for condition in qg_data['conditions']:
                    metric_key = condition.get('metricKey', 'unknown')
                    operator = condition.get('operator', 'unknown')
                    status = condition.get('status', 'unknown')
                    actual_value = condition.get('actualValue', '0')
                    
                    try:
                        actual_value_num = float(actual_value)
                    except:
                        actual_value_num = 0
                    
                    metrics_lines.append(
                        f'sonarqube_condition_value{{project_key="{project_key}",metric_key="{metric_key}",operator="{operator}",status="{status}"}} {actual_value_num} {timestamp}'
                    )
        
        return '\n'.join(metrics_lines)
        
    except Exception as e:
        logger.error(f"转换webhook数据时出错: {e}")
        return None

@app.route('/webhook/sonarqube', methods=['POST'])
def sonarqube_webhook():
    """接收SonarQube webhook"""
    try:
        # 获取webhook数据
        webhook_data = request.get_json()
        
        if not webhook_data:
            logger.warning("收到空的webhook数据")
            return jsonify({'error': 'No data received'}), 400
        
        logger.info(f"收到SonarQube webhook: {json.dumps(webhook_data, indent=2)}")
        
        # 转换为指标格式
        metrics_data = convert_sonar_webhook_to_metrics(webhook_data)
        
        if metrics_data:
            # 发送到VictoriaMetrics
            if send_to_victoria_metrics(metrics_data):
                return jsonify({'status': 'success', 'message': 'Webhook processed successfully'}), 200
            else:
                return jsonify({'status': 'error', 'message': 'Failed to send metrics'}), 500
        else:
            return jsonify({'status': 'error', 'message': 'Failed to convert webhook data'}), 500
            
    except Exception as e:
        logger.error(f"处理webhook时出错: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    return jsonify({'status': 'healthy', 'timestamp': datetime.now().isoformat()}), 200

@app.route('/metrics', methods=['GET'])
def metrics():
    """提供服务自身的指标"""
    return "webhook_service_up 1\n", 200

if __name__ == '__main__':
    # 确保日志目录存在
    os.makedirs('/app/logs', exist_ok=True)
    
    logger.info("启动SonarQube Webhook服务...")
    app.run(host='0.0.0.0', port=8080, debug=False)

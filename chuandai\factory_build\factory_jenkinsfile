pipeline {
    agent { label 'WinBuild' }
    options {
        timestamps()
        disableConcurrentBuilds()
        timeout(time: 1, unit: 'HOURS')
        buildDiscarder(logRotator(numToKeepStr: '30'))
    }
    parameters {
        choice(
            name: 'VersionType',
            choices: ['Factory'], 
            description: 'VersionType 必选参数，版本类型，当前为Factory版本。'
        )
    }
    environment {
        // 统一项目配置
        PROJECT_NAME = "wr02"
        PROJECT_TYPE = "${VersionType}"
        PROJECT_TYPE_LOWER = sh(script: "echo '${VersionType}' | tr 'A-Z' 'a-z'", returnStdout: true).trim()
        
        // 时间戳，用于版本标识
        BUILD_TIMESTAMP = sh(script: 'date +%Y%m%d%H%M', returnStdout: true).trim()
        
        // 版本号处理 - TagVersion可能通过外部传入，如不存在则使用默认值
        DEFAULT_TAG_VERSION = "wr02_factory"
        // 将下划线转换为中划线(nexus要求)
        FORMATTED_TAG_VERSION = sh(script: "echo '${TagVersion ?: DEFAULT_TAG_VERSION}' | tr '_' '-'", returnStdout: true).trim()
        
        // 版本命名规则统一
        VERSION_NAME = "${PROJECT_TYPE_LOWER}-${BUILD_NUMBER}-${BUILD_TIMESTAMP}"
        ARTIFACT_NAME = "${PROJECT_NAME}-${VERSION_NAME}"
        
        // 统一定义PATH，避免在多个阶段重复定义
        //PATH = "/home/<USER>/bin:/usr/local/bin:/usr/bin:/usr/bin:/sbin:/usr/local/bin:/home/<USER>/bin:/cygdrive/c/Program Files/Common Files/Oracle/Java/javapath:/cygdrive/c/App/OpenSSH-Win64:/cygdrive/c/Windows/system32:/cygdrive/c/Windows:/cygdrive/c/Windows/System32/Wbem:/cygdrive/c/Windows/System32/WindowsPowerShell/v1.0:/cygdrive/c/Windows:/cygdrive/c/App/curl/bin:/cygdrive/c/App/GnuWin32/bin:/cygdrive/c/App/7-Zip:/cygdrive/c/Program Files/dotnet:/cygdrive/c/Keil_v5/ARM/ARMCC/bin:/cygdrive/c/Users/<USER>/bin:/cygdrive/c/Users/<USER>/.vscode/extensions/cl.eide-3.20.0/res/tools/win32/unify_builder:/cygdrive/c/Users/<USER>/.vscode/extensions/cl.eide-3.20.1/res/tools/win32/unify_builder:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python37/Scripts:/cygdrive/c/Users/<USER>/AppData/Local/Programs/Python/Python37:/cygdrive/c/Program Files (x86)/GNU Arm Embedded Toolchain/10 2021.10/bin:/cygdrive/c/Users/<USER>/AppData/Local/Microsoft/WindowsApps:/cygdrive/c/App/Microsoft VS Code/bin"
        
        // 服务器配置
        NEXUS_URL = "*********:8089"
        REPO_URL = "ssh://jenkinsadmin@********:29418/wr02"
        MANIFEST_FILE = "wearable-sifli-wr02-factory.xml"
        BRANCH_NAME = "wr02_factory"
    }
    stages {
        stage('清理构建缓存') {
            steps {
                sh '''
                echo "清理工作空间: CIBuildScript,CIBuildArtifacts";
                rm -rf CIBuild*;
                mkdir CIBuildArtifacts;
                '''
            }
        }
        stage('下载代码') {
            options {
                timeout(time: 20, unit: 'MINUTES')
            }
            steps {
                // 使用环境变量而不是参数
                sh """
                echo "下载代码"
                mkdir -p .repo/repo_cache
                export REPO_CACHE=\$(pwd)/.repo/repo_cache
                repo init -u ${env.REPO_URL} -m ${env.MANIFEST_FILE} -b ${env.BRANCH_NAME};
                repo start --all ${env.BRANCH_NAME};
                echo "repo forall -c git reset --hard HEAD^" ;
                repo forall -c git gc --prune=now;
                repo forall -c git reset --hard HEAD^;
                repo sync -c --no-clone-bundle;
                unix2dos tools/upgrade_tools/*.bat
                cmd.exe /c 'icacls  tools/upgrade_tools/bin_update_src/qw_file_packerbg1.exe  /grant Everyone:F'
                """
                
                // 错误处理
                script {
                    def exitCode = sh(script: "if [ -d '.repo' ]; then echo 'success'; else echo 'fail'; fi", returnStdout: true).trim()
                    if (exitCode == 'fail') {
                        error "代码同步失败，请检查网络或仓库配置"
                    }
                }
            }
        }
        stage('生成编译配置文件') {
            steps {
                sh '''
                cp -r /home/<USER>/CIBuildScript ./;
                sh CIBuildScript/builder_boot/getbuilder_bootv32.sh version=PIPELINE;
                sh CIBuildScript/builder_lcpu/getbuilder_lcpuv32.sh version=PIPELINE;
                sh CIBuildScript/builder_app/getbuilder_appv32.sh version=PIPELINE;
                '''
                
                // 错误处理
                script {
                    def configFiles = sh(script: "find CIBuildScript -name '*.params' | wc -l", returnStdout: true).trim()
                    if (configFiles == '0') {
                        error "编译配置文件生成失败，请检查CIBuildScript路径"
                    }
                }
            }
        }
        stage('编译BOOT') {
            options {
                timeout(time: 15, unit: 'MINUTES')
            }
            steps {
                sh '''
                echo "编译BOOT"
                unify_builder -p CIBuildScript/builder_boot/builder_boot.params --rebuild
                '''
                
                // 错误处理
                script {
                    def bootBuildSuccess = sh(script: "find . -name '*boot*.bin' | wc -l", returnStdout: true).trim()
                    if (bootBuildSuccess == '0') {
                        error "BOOT编译失败，请检查编译日志"
                    }
                }
            }
        }
        stage('编译LCPU') {
            options {
                timeout(time: 15, unit: 'MINUTES')
            }
            steps {
                sh '''
                echo "编译LCPU"
                unify_builder -p CIBuildScript/builder_lcpu/builder_lcpu.params --rebuild
                '''
                
                // 错误处理
                script {
                    def lcpuBuildSuccess = sh(script: "find . -name '*lcpu*.bin' | wc -l", returnStdout: true).trim()
                    if (lcpuBuildSuccess == '0') {
                        error "LCPU编译失败，请检查编译日志"
                    }
                }
            }
        }
        stage('编译APP') {
            options {
                timeout(time: 15, unit: 'MINUTES')
            }
            steps {
                sh '''
                echo "编译APP"
                unify_builder -p CIBuildScript/builder_app/builder_app.params --rebuild
                '''
                
                // 错误处理
                script {
                    def appBuildSuccess = sh(script: "find . -name '*app*.bin' | wc -l", returnStdout: true).trim()
                    if (appBuildSuccess == '0') {
                        error "APP编译失败，请检查编译日志"
                    }
                }
            }
        }
        // stage('执行打包BAT') {
        //     steps {
        //         sh '''
        //         cd tools/upgrade_tools/;
        //         /cygdrive/c/Windows/System32/cmd.exe /c 'WR02_OTA_Upgrade.bat'
        //         '''
        //     }
        // }
        stage('执行打包BAT') {
            steps {
                script {
                    bat """
                        cd "tools\\upgrade_tools"
                        call WR02_OTA_Upgrade.bat
                            
                    """
                }
            }
        }
        stage('拷贝制品') {
            steps {
                sh '''
                echo "拷贝制品"
                cp -r tools/upgrade_tools CIBuildArtifacts/
                rm -rf CIBuildArtifacts/upgrade_tools/Asset
                rm -rf CIBuildArtifacts/upgrade_tools/bin_update_src
                7z a -tzip CIBuildArtifacts.zip CIBuildArtifacts
                '''
            }
        }
        stage('制品备份归档至jenkins') {
            steps {
                archiveArtifacts artifacts: '*.zip', fingerprint: true, onlyIfSuccessful: true
            }
        }
        stage('制品推送至nexus') {
            steps {
                script {
                    def nexusArtifactId
                    if (env.TagVersion) {
                        nexusArtifactId = env.FORMATTED_TAG_VERSION
                    } else {
                        nexusArtifactId = "${env.ARTIFACT_NAME}"
                    }
                    nexusArtifactUploader artifacts: [[artifactId: "${nexusArtifactId}", 
                                                    classifier: "", 
                                                    file: "CIBuildArtifacts.zip", 
                                                    type: "zip"]], 
                                    credentialsId: "nexus", 
                                    groupId: "wearable-wr02-${env.PROJECT_TYPE_LOWER}", 
                                    nexusUrl: "${env.NEXUS_URL}", 
                                    nexusVersion: "nexus3", 
                                    protocol: "http", 
                                    repository: "wearable-build", 
                                    version: "${env.VERSION_NAME}"
                }
            }
        }
    }
    post {
        success {
            echo "构建成功"
        }
        failure {
            echo "构建失败"
        }
        always {
            // 无论成功失败都需清理的临时文件
            sh '''
            echo "清理临时文件"
            rm -rf .repo/repo_cache
            '''
        }
    }
}

import os
import json
import time
import sys

if len(sys.argv) < 2:
    print(f"用法: python {os.path.basename(sys.argv[0])} <base_dir>")
    sys.exit(1)

BASE_DIR = sys.argv[1]
OUTPUT_FILE = os.path.join(BASE_DIR, "merged-build-wrapper-dump.json")

def clean_json_file(path):
    # 去除注释行
    with open(path, "r", encoding="utf-8") as f:
        lines = [line for line in f if not line.lstrip().startswith("#")]
    return json.loads("".join(lines))

def main():
    start_time = time.time()
    captures = []
    version = None
    for subdir in os.listdir(BASE_DIR):
        json_path = os.path.join(BASE_DIR, subdir, "build-wrapper-dump.json")
        if os.path.isfile(json_path):
            data = clean_json_file(json_path)
            if version is None:
                version = data.get("version", "")
            captures.extend(data.get("captures", []))
    merged = {"version": version, "captures": captures}
    with open(OUTPUT_FILE, "w", encoding="utf-8") as f:
        json.dump(merged, f, ensure_ascii=False, indent=2)
    end_time = time.time()
    print(f"合并完成，输出文件：{OUTPUT_FILE}")
    print(f"总耗时：{end_time - start_time:.2f} 秒")

if __name__ == "__main__":
    main() 
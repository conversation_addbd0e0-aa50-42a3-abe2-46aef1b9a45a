#!/bin/bash
# eideincDirsTemp.json incList libList defineList
# builder_v2.params incDirs libDirs defines

#获取eide.json文件
echo ${bashWorkSpace}
codeDir="$(pwd)"

#codeDir="/workspace/wearable-wr02-build"
#scriptDir=${codeDir}/CIBuildScript


#lcpuEideDir="${codeDir}/app/project/WR02_App/.eide"
#\cp ${lcpuEideDir}/eide.json ${scriptDir}/builder_app/
#cd ${scriptDir}/builder_app/

# 生成 incDirs linDirs defines临时文件
cat eide.json | jq .targets | jq .[] | jq .custom_dep > eideincDirsTemp.json


# 获取virtualFolder下所有文件（包括各级子文件夹中的文件）的path字段值
jq '.virtualFolder | recurse(.folders[]) |.files[].path' eide.json > all_paths.xml

# 生产sourceList临时文件
# 获取excludeList中的所有排除项
#exclude_list=$(jq -r '.targets.WR02_APP.excludeList[]' eide.json)
# 拿到virtual_root排除目录
jq -r '.targets.WR02_APP.excludeList[]' eide.json | sed 's/<virtual_root>\///' > exclude_list.xml

# 调试时使用
#echo ${exclude_list[@]} > exclude_list.xml

# 用于存储所有要排除的完整path，先初始化为空
excluded_paths=()

# 定义日志文件
> execute.log
# 定义文件排除清单
> execute_file.xml
# 定义目录排除清单
> execute_dir.xml
# 定义模块排除清单
> execute_module.xml



# 下面的方法要全部重写。
# 根据是否文件路径还是路径，
# 如果是文件，截取文件名前的路径，通过方法拿到json，在json中过滤带文件名的路径。
# 如果是路径，，通过方法拿到json，在json中拿到所有的路径。

#jq '.virtualFolder| .folders[] | select (.name == "Applications") | .folders[] | select (.name == "Application")| .folders[] | select (.name == "App")| .folders[] | select (.name == "basic_app_module")| .folders[] | select (.name == "activity_record") |.files[].path' eide.json 

# 将路径拆分成数组
#input_path="Applications/Application/App/basic_app_module/activity_record"

function getaimjson (){
	#输入路径，追加输出路径至文件中
    input_path=$1
	IFS='/' read -ra path_parts <<< "$input_path"
	jq_query=".virtualFolder"
	for part in "${path_parts[@]}"; do
	jq_query+=" |.folders[] | select(.name == \"$part\") "
	done
	#echo $jq_query
	# 执行jq拿到路径
	jq "$jq_query" eide.json | grep \"path\" | awk '{print $NF}'
}
#getaimjson $input_path


# 处理excludeList中的每一项，按规则提取并收集要排除的路径。
while read -r exclude_item; do
    # 判断输入的是模块还是文件。
    if [[ "${exclude_item}" == *"."* ]]; then
        echo "${exclude_item} 是文件"
        # 当如输入的排除行为文件，则获取路径值。
        exclude_item_dir_name=$(dirname "${exclude_item}")
        exclude_item_base_name=$(basename "${exclude_item}")
        echo "获取到的路径为: ${exclude_item_dir_name}"
        # 调用getjson函数拿到对于路径下的json文件。过滤出排除的文件名重定向至execute_file.xml中。
        getaimjson "${exclude_item_dir_name}" >  tmp_file.list
        sub_paths=$(cat tmp_file.list  | grep "${exclude_item_base_name}")
        if [ -n "${sub_paths}" ];
        then
            echo "${sub_paths}"
            echo "${sub_paths}" >> execute_file.xml
        else
            echo "无此文件$exclude_item_base_name"
        fi
    else
		# 如果没有文件后缀，则按目录处理。将路径传给getjson函数解析
        echo "${exclude_item} 是json模块"
        getaimjson "${exclude_item}" >> execute_module.xml
    fi
#上面这个while循环拿到了 execute_file.xml execute_module.xml这两个个排除的文件清单。
# 从所有的文件清单中排除掉三个排除清单的文件。
done <<< "$(cat exclude_list.xml)"

# 将两份排除文件合并成一份，然后排序，从all_paths.xml中排除掉。
cat execute_file.xml execute_module.xml > execute_combined.xml

sort all_paths.xml  > fileall_sorted.xml
sort execute_combined.xml > filecom_sorted.xml

comm -23 fileall_sorted.xml filecom_sorted.xml > fileend

# 循环处理每个path值，判断是否在排除列表中，如果不在则输出
filtered_paths=""
while read -r path; do
    should_include=1
    for excluded_path in "${excluded_paths[@]}"; do
        if [[ "$path" == "$excluded_path" ]]; then
            should_include=0
            break
        fi
    done
    if [ $should_include -eq 1 ]; then
        filtered_paths="$filtered_paths$path\n"
    fi
done <<< "$(cat execute_file.xml)"

# 将最终排除后保留的path值保存到file1.txt文件中
echo -e "$filtered_paths" > eideFolderPathTemp.xml

#cat eide.json | jq .virtualFolder | grep path > eideFolderPathTemp.xml

# 通过读取eideincDirsTemp.json文件,将里面的incList,libList,defineList内容,写入builder_v2.params文件中的incDirs,libDirs,defines文件
jq -r '.incList' eideincDirsTemp.json > incList.json
jq -r '.libList' eideincDirsTemp.json > libList.json
jq -r '.defineList' eideincDirsTemp.json > defineList.json

# 将incList.json文件内容开头空6，文件内容不变
cp incList.json incListTemp.json
perl -pi -e 's/^/      /' incListTemp.json
# 去掉incListTemp.json文件中'[',']'行前面的空格
perl -pi -e 's/^\s+(\[|\])/$1/' incListTemp.json

# libList.json文件内容开头空6格，文件内容不变
cp libList.json libListTemp.json
perl -pi -e 's/^/      /' libListTemp.json
# libListTemp.json文件中'[',']'行前面的空格
perl -pi -e 's/^\s+(\[|\])/$1/' libListTemp.json

# defineList.json文件内容开头空6格
cp defineList.json defineListTemp.json
perl -pi -e 's/^/      /' defineListTemp.json
# defineListTemp.json文件中'[',']'行前面的空格
perl -pi -e 's/^\s+(\[|\])/$1/' defineListTemp.json


sed -n '1h;1!H;${;g;s/\n/,\n/g;s/,\n$//;p;}' eideFolderPathTemp.xml > filePathTemp.xml

# 将所有行对齐
#sed '$!s/$/,/' eideFolderPathTemp.xml > eideFolderPath.xml
# 空8格并且去掉path关键字
#awk '{gsub(/^ *"path": /, ""); printf "        %s\n", $0}' eideFolderPath.xml > filePathTemp.xml

aimFileName="builder_app.params"
cp builder_app_module.params ${aimFileName}
# 读取incList.json文件,将里面的内容替换入builder_v2.params文件中的 "replace_incList",并且前面空5格子
perl -pi -e "s|replace_incList|$(cat incListTemp.json)|g" ${aimFileName}
perl -pi -e "s|replace_libList|$(cat libListTemp.json)|g" ${aimFileName}
perl -pi -e "s|replace_defineList|$(cat defineListTemp.json)|g" ${aimFileName}
perl -pi -e "s|replace_sourceList|$(cat filePathTemp.xml)|g" ${aimFileName}

# 特殊处理，"SIFLI_BUILD="000000"" 替换为"SIFLI_BUILD=\"000000\"",
sed -i 's/"SIFLI_BUILD="000000""/"SIFLI_BUILD=\\"000000\\""/g' ${aimFileName}

# 清理工作目录
#rm -rf incListTemp.json
#rm -rf libListTemp.json
#rm -rf defineListTemp.json
#rm -rf eideFolderPathTemp.xml
#rm -rf eideFolderPath.xml
#rm -rf filePathTemp.xml
#rm -rf incList.json
#rm -rf libList.json
#rm -rf defineList.json
#rm -rf eideincDirsTemp.json

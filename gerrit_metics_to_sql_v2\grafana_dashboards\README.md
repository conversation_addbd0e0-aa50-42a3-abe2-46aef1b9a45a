# Grafana 研发效能看板使用指南

## 概述

本目录包含了基于 Gerrit + SonarQube 数据的研发效能分析看板，用于监控和分析团队的开发效率、代码质量和协作情况。

## 看板列表

### 1. 研发效能总览 (01_研发效能总览.json)
- **用途**: 团队整体研发效能的宏观视图
- **关键指标**:
  - 提交趋势图
  - 代码质量分布
  - 活跃开发者排行
  - 问题密度趋势

### 2. 代码质量分析 (02_代码质量分析.json)
- **用途**: 深度分析代码质量问题
- **关键指标**:
  - 问题严重程度分布
  - 代码复杂度趋势
  - 重复代码密度
  - 注释密度分析
  - 质量等级趋势

### 3. 团队协作分析 (03_团队协作分析.json)
- **用途**: 分析团队协作和代码审查情况
- **关键指标**:
  - 评审人活跃度
  - 变更大小分布
  - 平均变更行数趋势
  - 项目活跃度
  - 分支活跃度对比

### 4. 开发者个人效能 (04_开发者个人效能.json)
- **用途**: 个人开发者的效能分析
- **关键指标**:
  - 个人提交趋势
  - 代码质量分布
  - 平均变更行数
  - 问题密度
  - 清洁代码率
  - 项目贡献分布

## 安装步骤

### 1. 配置数据源
在 Grafana 中添加 PostgreSQL 数据源：
```
名称: your-postgres-datasource
类型: PostgreSQL
主机: your-database-host
端口: 5432
数据库: your-database-name
用户: your-username
密码: your-password
```

### 2. 导入看板
1. 在 Grafana 中点击 "+" -> "Import"
2. 选择对应的 JSON 文件
3. 修改数据源为你的 PostgreSQL 数据源名称
4. 点击 "Import" 完成导入

### 3. 配置变量 (个人效能看板)
对于开发者个人效能看板，需要配置以下变量：
- **开发者**: 自动从数据库获取开发者列表
- **时间范围**: 可选择 7d、30d、90d

## 数据表结构要求

确保你的 `commit_metrics` 表包含以下字段：

```sql
CREATE TABLE commit_metrics (
    commit_id VARCHAR(50) PRIMARY KEY,
    change_id VARCHAR(50),
    change_id_short VARCHAR(20),
    branch VARCHAR(50),
    gerrit_project VARCHAR(100),
    author VARCHAR(100),
    reviewers TEXT[], -- 数组类型
    commit_date DATE,
    changed_lines INTEGER,
    change_size_category VARCHAR(20),
    
    -- SonarQube 指标
    ncloc INTEGER,
    complexity INTEGER,
    comment_lines INTEGER,
    comment_lines_density DECIMAL(5,2),
    duplicated_lines INTEGER,
    duplicated_lines_density DECIMAL(5,2),
    duplicated_files INTEGER,
    duplicated_blocks INTEGER,
    
    -- 问题统计
    sq_blocker INTEGER,
    sq_critical INTEGER,
    sq_major INTEGER,
    sq_minor INTEGER,
    sq_info INTEGER,
    
    -- 计算字段
    issue_density DECIMAL(10,2),
    quality_level VARCHAR(20),
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 自定义配置

### 修改数据源
在每个 JSON 文件中，将 `"uid": "your-postgres-datasource"` 替换为你的实际数据源 UID。

### 调整时间范围
修改 `"time"` 部分的 `"from"` 和 `"to"` 值来调整默认时间范围。

### 添加阈值告警
在面板配置中添加 `"thresholds"` 来设置告警阈值。

## 常见问题

### 1. 数据不显示
- 检查数据源连接是否正常
- 确认 SQL 查询语法是否正确
- 验证数据表中是否有数据

### 2. 变量不工作
- 确认数据源配置正确
- 检查变量查询语法
- 验证数据库权限

### 3. 性能问题
- 为常用查询字段添加索引
- 考虑使用物化视图
- 优化 SQL 查询

## 扩展建议

### 1. 添加更多指标
- 代码审查时间
- 构建成功率
- 测试覆盖率
- 部署频率

### 2. 集成其他工具
- Jenkins 构建数据
- JIRA 问题跟踪
- GitLab/GitHub 数据

### 3. 告警配置
- 代码质量下降告警
- 提交频率异常告警
- 问题密度超标告警

## 技术支持

如有问题，请检查：
1. Grafana 版本兼容性
2. PostgreSQL 版本支持
3. 数据源连接配置
4. 数据库权限设置 
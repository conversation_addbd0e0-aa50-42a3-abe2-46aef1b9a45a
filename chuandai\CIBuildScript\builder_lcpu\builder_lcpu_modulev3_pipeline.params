{
    "name": "project",
    "target": "iGSBG1_LCPU",
    "toolchain": "AC6",
    "toolchainLocation": "C:\\Keil_v5\\ARM\\ARMCLANG",
    "toolchainCfgFile": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.20.1\\res\\data\\models\\win32/arm.v6.model.json",
    "buildMode": "fast|multhread",
    "showRepathOnLog": true,
    "threadNum": 32,
    "rootDir": "c:\\Users\\<USER>\\Pwearwr02\\app\\project\\WR02_LCPU\\ec-lb555\\lcpu",
    "dumpPath": "build\\iGSBG1_LCPU",
    "outDir": "build\\iGSBG1_LCPU",
    "incDirs": replace_incList,
    "libDirs": replace_libList,
    "defines": replace_defineList,
    "sourceList": [
        replace_sourceList
        ],
    "sourceParams": {},
    "sourceParamsMtime": 1734396499020.3442,
    "options": {
        "version": 3,
        "beforeBuildTasks": [
            {
                "name": "prebuild.bat",
                "command": "cd .\\. && prebuild.bat",
                "disable": false,
                "abortAfterFailed": true,
                "stopBuildAfterFailed": true
            }
        ],
        "afterBuildTasks": [
            {
                "name": "postbuild.bat !L",
                "command": "cd .\\. && postbuild.bat .\\${OutDirBase}\\${ProjectName}.axf",
                "disable": false,
                "abortAfterFailed": true
            }
        ],
        "global": {
            "use-microLIB": true,
            "output-debug-info": "enable",
            "microcontroller-cpu": "cortex-m33-sp",
            "microcontroller-fpu": "cortex-m33-sp",
            "microcontroller-float": "cortex-m33-sp",
            "target": "cortex-m33-sp"
        },
        "c/cpp-compiler": replace_cxxcompiler,
        "asm-compiler": {
            "$use": "asm-auto",
            "misc-controls": "--fpu=FPv5-SP --cpreproc_opts=-mfpu=fpv5-sp-d16 --cpreproc_opts=-mfloat-abi=hard --cpreproc_opts=-DARMCM33_DSP_FP --cpreproc --cpreproc_opts=--target=arm-arm-none-eabi --cpreproc_opts=-mfloat-abi=hard --cpu=Cortex-M33 --cpreproc_opts=-mcpu=Cortex-M33 --li -g --cpreproc_opts=-D__UVISION_VERSION=\"532\" --diag_suppress=A1609 --cpreproc"
        },
        "linker": {
            "output-format": "elf",
            "misc-controls": "--cpu=Cortex-M33 --strict --scatter ./linker_scripts/link_lcpu.sct --summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols --info sizes --info totals --info unused --info veneers --any_contingency --list build/lcpu.map --symdefs=build/lcpu.symdefs ../../../../../sifli/drivers/hal/sifli_rom_55x_a3.sym --library_type=microlib --any_contingency --predefine=-DLB55X_CHIP_ID=3 --predefine=\"-DSF32LB55X\" --predefine=\"-DUSE_HAL_DRIVER\" --predefine=\"-DLB55X_CHIP_ID=3\" --predefine=\"-DSOC_BF0_LCPU\" --predefine=\"-DROM_ENABLED\" --predefine=\"-DARM_MATH_LOOPUNROLL\" --predefine=\"-DSIFLI_VERSION=33554439\" --predefine=\"-DSIFLI_BUILD=\"000000\"\" --predefine=\"-DRT_USING_ARM_LIBC\"",
            "ro-base": "0x00000000",
            "rw-base": "0x20000000",
            "link-scatter": [
                "\"c:/Users/<USER>/Pwearwr02/app/project/WR02_LCPU/ec-lb555/lcpu/linker_scripts/link_lcpu.sct\""
            ]
        }
    },
    "env": {
        "workspaceFolder": "c:\\Users\\<USER>\\Pwearwr02\\app\\project\\WR02_LCPU\\ec-lb555\\lcpu",
        "workspaceFolderBasename": "lcpu",
        "OutDir": "c:\\Users\\<USER>\\Pwearwr02\\app\\project\\WR02_LCPU\\ec-lb555\\lcpu\\build\\iGSBG1_LCPU",
        "OutDirRoot": "build",
        "OutDirBase": "build\\iGSBG1_LCPU",
        "ProjectName": "project",
        "ConfigName": "iGSBG1_LCPU",
        "ProjectRoot": "c:\\Users\\<USER>\\Pwearwr02\\app\\project\\WR02_LCPU\\ec-lb555\\lcpu",
        "ExecutableName": "c:\\Users\\<USER>\\Pwearwr02\\app\\project\\WR02_LCPU\\ec-lb555\\lcpu\\build\\iGSBG1_LCPU\\project",
        "ChipPackDir": "",
        "ChipName": "",
        "SYS_Platform": "win32",
        "SYS_DirSep": "\\",
        "SYS_DirSeparator": "\\",
        "SYS_PathSep": ";",
        "SYS_PathSeparator": ";",
        "SYS_EOL": "\r\n",
        "EIDE_BUILDER_DIR": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.20.1\\res\\tools\\win32\\unify_builder",
        "EIDE_BINARIES_VER": "12.0.1",
        "EIDE_MSYS": "C:\\Users\\<USER>\\.eide\\bin\\builder\\msys\\bin",
        "EIDE_TOOL_GCC_ARM": "C:\\Program Files (x86)\\GNU Arm Embedded Toolchain\\10 2021.10\\bin",
        "EIDE_TOOL_JLINK": "C:\\Users\\<USER>\\.eide\\tools\\jlink",
        "EIDE_TOOL_OPENOCD": ".",
        "ToolchainRoot": "C:\\Keil_v5\\ARM\\ARMCLANG"
    },
    "sysPaths": [],
    "sha": {
        "c/cpp-defines": "4dab87f3991393de27ae4448672691d0",
        "beforeBuildTasks": "3e0ba0a657f95a92eb7b9dc9cf0a3e84",
        "afterBuildTasks": "ffbd74766229b38bfc37a4901f939c96",
        "global": "3ff48b1dc77de63bee514437f2e3c87c",
        "c/cpp-compiler": "81741a678c5575da6ab4e353fde850c9",
        "asm-compiler": "944004e4f6977437440cd91a64f40ca7",
        "linker": "f0a1924cc52798fb683bb64c55486222"
    }
}
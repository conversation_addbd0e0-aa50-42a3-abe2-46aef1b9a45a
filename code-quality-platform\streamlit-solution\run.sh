#!/bin/bash

echo "🚀 启动代码质量效能分析平台 (Streamlit版)"
echo "=" * 50

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi

# 检查pip
if ! command -v pip &> /dev/null; then
    echo "❌ pip 未安装，请先安装pip"
    exit 1
fi

# 安装依赖
echo "📦 安装Python依赖包..."
pip install -r requirements.txt

# 检查数据库连接
echo "🔍 检查数据库连接..."
python3 -c "
import psycopg2
try:
    conn = psycopg2.connect(
        host='**********',
        port='5434', 
        database='mydatabase',
        user='admin',
        password='admin123'
    )
    print('✅ 数据库连接成功')
    conn.close()
except Exception as e:
    print(f'❌ 数据库连接失败: {e}')
    print('请检查数据库配置和网络连接')
"

echo ""
echo "🌐 启动Streamlit应用..."
echo "访问地址: http://localhost:8501"
echo ""
echo "按 Ctrl+C 停止服务"
echo ""

# 启动Streamlit
streamlit run app.py --server.port 8501 --server.address 0.0.0.0

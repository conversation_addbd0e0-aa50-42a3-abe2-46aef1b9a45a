('D:\\vscodedir\\build\\跑步实时数据提取工具\\PYZ-00.pyz',
 [('PIL',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsStubImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\FitsStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\__future__.py',
   'PYMODULE'),
  ('_compat_pickle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\_compression.py',
   'PYMODULE'),
  ('_dummy_thread',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('_py_abc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('_strptime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\ast.py',
   'PYMODULE'),
  ('asyncio',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.streams',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.transports',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\base64.py',
   'PYMODULE'),
  ('bdb',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\bdb.py',
   'PYMODULE'),
  ('bisect',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\bisect.py',
   'PYMODULE'),
  ('bz2',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\calendar.py',
   'PYMODULE'),
  ('cmd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\cmd.py',
   'PYMODULE'),
  ('code',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\codeop.py',
   'PYMODULE'),
  ('colorsys',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\colorsys.py',
   'PYMODULE'),
  ('concurrent',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\copy.py',
   'PYMODULE'),
  ('csv',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\datetime.py',
   'PYMODULE'),
  ('dateutil',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\decimal.py',
   'PYMODULE'),
  ('difflib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\difflib.py',
   'PYMODULE'),
  ('dis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\dis.py',
   'PYMODULE'),
  ('doctest',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\doctest.py',
   'PYMODULE'),
  ('email',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\email\\utils.py',
   'PYMODULE'),
  ('et_xmlfile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('fractions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\fractions.py',
   'PYMODULE'),
  ('ftplib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\glob.py',
   'PYMODULE'),
  ('greenlet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\greenlet\\__init__.py',
   'PYMODULE'),
  ('gzip',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\hmac.py',
   'PYMODULE'),
  ('html',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\html\\entities.py',
   'PYMODULE'),
  ('http',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\http\\server.py',
   'PYMODULE'),
  ('imp',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\imp.py',
   'PYMODULE'),
  ('importlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.resources',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib_metadata',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._py39compat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\importlib_metadata\\_py39compat.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('inspect',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\inspect.py',
   'PYMODULE'),
  ('jinja2',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\lzma.py',
   'PYMODULE'),
  ('markupsafe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mimetypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.semaphore_tracker',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\multiprocessing\\semaphore_tracker.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._version',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('numpy.compat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.machar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\machar.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.core.records',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.distutils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\distutils\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.cpuinfo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\distutils\\cpuinfo.py',
   'PYMODULE'),
  ('numpy.fft',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.linalg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.testing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.version',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\opcode.py',
   'PYMODULE'),
  ('openpyxl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('optparse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\optparse.py',
   'PYMODULE'),
  ('packaging',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.markers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._random',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_testing\\_random.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas.api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.chainmap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\compat\\chainmap.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.aggregation',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\aggregation.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\arrays\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.dtype',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\dtype.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.describe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\describe.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\indexers.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.numeric',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\indexes\\numeric.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.methods',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\ops\\methods.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.series',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.indexers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\window\\indexers.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io.api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.date_converters',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\date_converters.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.excel._xlwt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\excel\\_xlwt.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.latex',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\formats\\latex.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.testing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\util\\testing.py',
   'PYMODULE'),
  ('pandas.util.version',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pdb',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\pdb.py',
   'PYMODULE'),
  ('pickle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\pickle.py',
   'PYMODULE'),
  ('pkg_resources',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._elffile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._parser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._tokenizer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.metadata',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\metadata.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.__main__',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\__main__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.android',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.macos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.unix',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.version',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.windows',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('pkg_resources._vendor.typing_extensions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('pkgutil',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\platform.py',
   'PYMODULE'),
  ('plistlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\plistlib.py',
   'PYMODULE'),
  ('pprint',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\pprint.py',
   'PYMODULE'),
  ('py_compile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\py_compile.py',
   'PYMODULE'),
  ('pydoc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pytz',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\random.py',
   'PYMODULE'),
  ('runpy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\runpy.py',
   'PYMODULE'),
  ('selectors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\selectors.py',
   'PYMODULE'),
  ('shlex',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\signal.py',
   'PYMODULE'),
  ('six',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\six.py',
   'PYMODULE'),
  ('socket',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\socket.py',
   'PYMODULE'),
  ('socketserver',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\socketserver.py',
   'PYMODULE'),
  ('sqlalchemy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.connectors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\connectors\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.aioodbc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\connectors\\aioodbc.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.asyncio',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\connectors\\asyncio.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.pyodbc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\connectors\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.cyextension',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\cyextension\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects._typing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.aioodbc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\aioodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.information_schema',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\information_schema.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.json',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pymssql',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pymssql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pyodbc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.aiomysql',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\aiomysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.asyncmy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\asyncmy.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.cymysql',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\cymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.dml',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.enumerated',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\enumerated.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.expression',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.json',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mariadb',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mariadb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mariadbconnector',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mariadbconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqlconnector',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqlconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqldb',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqldb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pymysql',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pyodbc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reflection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reserved_words',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reserved_words.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.types',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.cx_oracle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\cx_oracle.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.dictionary',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\dictionary.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.oracledb',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\oracledb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.types',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql._psycopg_common',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\_psycopg_common.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.array',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\array.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.asyncpg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\asyncpg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.dml',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ext',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ext.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.hstore',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\hstore.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.json',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.named_types',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\named_types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.operators',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\operators.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg8000',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg8000.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg_catalog',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg_catalog.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2cffi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2cffi.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ranges',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ranges.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.types',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.aiosqlite',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\aiosqlite.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.dml',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.json',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlcipher',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlcipher.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlite',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlite.py',
   'PYMODULE'),
  ('sqlalchemy.engine',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\engine\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_processors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\engine\\_py_processors.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_row',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\engine\\_py_row.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\engine\\_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.engine.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\engine\\base.py',
   'PYMODULE'),
  ('sqlalchemy.engine.characteristics',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\engine\\characteristics.py',
   'PYMODULE'),
  ('sqlalchemy.engine.create',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\engine\\create.py',
   'PYMODULE'),
  ('sqlalchemy.engine.cursor',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\engine\\cursor.py',
   'PYMODULE'),
  ('sqlalchemy.engine.default',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\engine\\default.py',
   'PYMODULE'),
  ('sqlalchemy.engine.events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\engine\\events.py',
   'PYMODULE'),
  ('sqlalchemy.engine.interfaces',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\engine\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.engine.mock',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\engine\\mock.py',
   'PYMODULE'),
  ('sqlalchemy.engine.processors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\engine\\processors.py',
   'PYMODULE'),
  ('sqlalchemy.engine.reflection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\engine\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.engine.result',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\engine\\result.py',
   'PYMODULE'),
  ('sqlalchemy.engine.row',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\engine\\row.py',
   'PYMODULE'),
  ('sqlalchemy.engine.url',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\engine\\url.py',
   'PYMODULE'),
  ('sqlalchemy.engine.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\engine\\util.py',
   'PYMODULE'),
  ('sqlalchemy.event',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\event\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.event.api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\event\\api.py',
   'PYMODULE'),
  ('sqlalchemy.event.attr',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\event\\attr.py',
   'PYMODULE'),
  ('sqlalchemy.event.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\event\\base.py',
   'PYMODULE'),
  ('sqlalchemy.event.legacy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\event\\legacy.py',
   'PYMODULE'),
  ('sqlalchemy.event.registry',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\event\\registry.py',
   'PYMODULE'),
  ('sqlalchemy.exc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.ext',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\ext\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\ext\\asyncio\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\ext\\asyncio\\base.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.engine',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\ext\\asyncio\\engine.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.exc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\ext\\asyncio\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.result',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\ext\\asyncio\\result.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.scoping',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\ext\\asyncio\\scoping.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.session',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\ext\\asyncio\\session.py',
   'PYMODULE'),
  ('sqlalchemy.ext.baked',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\ext\\baked.py',
   'PYMODULE'),
  ('sqlalchemy.ext.compiler',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\ext\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.future',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\future\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.future.engine',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\future\\engine.py',
   'PYMODULE'),
  ('sqlalchemy.inspection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\inspection.py',
   'PYMODULE'),
  ('sqlalchemy.log',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\log.py',
   'PYMODULE'),
  ('sqlalchemy.orm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.orm._orm_constructors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\_orm_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.orm._typing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.orm.attributes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\attributes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.bulk_persistence',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\bulk_persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.clsregistry',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\clsregistry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.collections',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\collections.py',
   'PYMODULE'),
  ('sqlalchemy.orm.context',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\context.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\decl_api.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\decl_base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dependency',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\dependency.py',
   'PYMODULE'),
  ('sqlalchemy.orm.descriptor_props',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\descriptor_props.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dynamic',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\dynamic.py',
   'PYMODULE'),
  ('sqlalchemy.orm.evaluator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\evaluator.py',
   'PYMODULE'),
  ('sqlalchemy.orm.events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\events.py',
   'PYMODULE'),
  ('sqlalchemy.orm.exc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.orm.identity',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\identity.py',
   'PYMODULE'),
  ('sqlalchemy.orm.instrumentation',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\instrumentation.py',
   'PYMODULE'),
  ('sqlalchemy.orm.interfaces',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.orm.loading',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\loading.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapped_collection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\mapped_collection.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapper',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\mapper.py',
   'PYMODULE'),
  ('sqlalchemy.orm.path_registry',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\path_registry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.persistence',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.properties',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\properties.py',
   'PYMODULE'),
  ('sqlalchemy.orm.query',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\query.py',
   'PYMODULE'),
  ('sqlalchemy.orm.relationships',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\relationships.py',
   'PYMODULE'),
  ('sqlalchemy.orm.scoping',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\scoping.py',
   'PYMODULE'),
  ('sqlalchemy.orm.session',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\session.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\state.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state_changes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\state_changes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategies',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\strategies.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategy_options',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\strategy_options.py',
   'PYMODULE'),
  ('sqlalchemy.orm.sync',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\sync.py',
   'PYMODULE'),
  ('sqlalchemy.orm.unitofwork',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\unitofwork.py',
   'PYMODULE'),
  ('sqlalchemy.orm.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\util.py',
   'PYMODULE'),
  ('sqlalchemy.orm.writeonly',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\orm\\writeonly.py',
   'PYMODULE'),
  ('sqlalchemy.pool',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\pool\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.pool.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\pool\\base.py',
   'PYMODULE'),
  ('sqlalchemy.pool.events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\pool\\events.py',
   'PYMODULE'),
  ('sqlalchemy.pool.impl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\pool\\impl.py',
   'PYMODULE'),
  ('sqlalchemy.schema',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.sql._dml_constructors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\_dml_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._elements_constructors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\_elements_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._orm_types',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\_orm_types.py',
   'PYMODULE'),
  ('sqlalchemy.sql._py_util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.sql._selectable_constructors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\_selectable_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._typing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.sql.annotation',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\annotation.py',
   'PYMODULE'),
  ('sqlalchemy.sql.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.sql.cache_key',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\cache_key.py',
   'PYMODULE'),
  ('sqlalchemy.sql.coercions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\coercions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.compiler',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.sql.crud',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\crud.py',
   'PYMODULE'),
  ('sqlalchemy.sql.ddl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\ddl.py',
   'PYMODULE'),
  ('sqlalchemy.sql.default_comparator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\default_comparator.py',
   'PYMODULE'),
  ('sqlalchemy.sql.dml',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.sql.elements',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\elements.py',
   'PYMODULE'),
  ('sqlalchemy.sql.events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\events.py',
   'PYMODULE'),
  ('sqlalchemy.sql.expression',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy.sql.functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\functions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.lambdas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\lambdas.py',
   'PYMODULE'),
  ('sqlalchemy.sql.naming',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\naming.py',
   'PYMODULE'),
  ('sqlalchemy.sql.operators',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\operators.py',
   'PYMODULE'),
  ('sqlalchemy.sql.roles',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\roles.py',
   'PYMODULE'),
  ('sqlalchemy.sql.schema',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql.selectable',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\selectable.py',
   'PYMODULE'),
  ('sqlalchemy.sql.sqltypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\sqltypes.py',
   'PYMODULE'),
  ('sqlalchemy.sql.traversals',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\traversals.py',
   'PYMODULE'),
  ('sqlalchemy.sql.type_api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\type_api.py',
   'PYMODULE'),
  ('sqlalchemy.sql.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\util.py',
   'PYMODULE'),
  ('sqlalchemy.sql.visitors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\sql\\visitors.py',
   'PYMODULE'),
  ('sqlalchemy.types',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\types.py',
   'PYMODULE'),
  ('sqlalchemy.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\util\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.util._collections',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\util\\_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util._concurrency_py3k',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\util\\_concurrency_py3k.py',
   'PYMODULE'),
  ('sqlalchemy.util._has_cy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\util\\_has_cy.py',
   'PYMODULE'),
  ('sqlalchemy.util._py_collections',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\util\\_py_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util.compat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\util\\compat.py',
   'PYMODULE'),
  ('sqlalchemy.util.concurrency',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\util\\concurrency.py',
   'PYMODULE'),
  ('sqlalchemy.util.deprecations',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\util\\deprecations.py',
   'PYMODULE'),
  ('sqlalchemy.util.langhelpers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\util\\langhelpers.py',
   'PYMODULE'),
  ('sqlalchemy.util.preloaded',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\util\\preloaded.py',
   'PYMODULE'),
  ('sqlalchemy.util.queue',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\util\\queue.py',
   'PYMODULE'),
  ('sqlalchemy.util.topological',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\util\\topological.py',
   'PYMODULE'),
  ('sqlalchemy.util.typing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\sqlalchemy\\util\\typing.py',
   'PYMODULE'),
  ('sqlite3',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\ssl.py',
   'PYMODULE'),
  ('string',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\threading.py',
   'PYMODULE'),
  ('tkinter',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('tracemalloc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\tty.py',
   'PYMODULE'),
  ('typing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.case',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.result',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('uu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\uu.py',
   'PYMODULE'),
  ('uuid',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\uuid.py',
   'PYMODULE'),
  ('webbrowser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\webbrowser.py',
   'PYMODULE'),
  ('xml',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.dom',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\zipfile.py',
   'PYMODULE'),
  ('zipp',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp.py310compat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python37\\lib\\site-packages\\zipp\\py310compat.py',
   'PYMODULE')])

{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "postgres", "uid": "${DS_POSTGRESQL-CODEQUALITY}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "${DS_POSTGRESQL-CODEQUALITY}"}, "format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT \n  month_start as time,\n  total_commits as \"月度提交数\",\n  unique_authors as \"活跃开发者数\",\n  total_issues as \"月度问题数\"\nFROM monthly_trend_stats \nWHERE month_start >= CURRENT_DATE - INTERVAL '12 months'\nORDER BY month_start", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "月度活动趋势 (最近12个月)", "type": "timeseries"}, {"datasource": {"type": "postgres", "uid": "${DS_POSTGRESQL-CODEQUALITY}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 70}, {"color": "green", "value": 90}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "${DS_POSTGRESQL-CODEQUALITY}"}, "format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT \n  month_start as time,\n  clean_rate_percent as \"清洁代码率\"\nFROM monthly_trend_stats \nWHERE month_start >= CURRENT_DATE - INTERVAL '12 months'\nORDER BY month_start", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "月度清洁代码率趋势", "type": "timeseries"}, {"datasource": {"type": "postgres", "uid": "${DS_POSTGRESQL-CODEQUALITY}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "${DS_POSTGRESQL-CODEQUALITY}"}, "format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT \n  month_start as time,\n  avg_issue_density as \"平均问题密度\"\nFROM monthly_trend_stats \nWHERE month_start >= CURRENT_DATE - INTERVAL '12 months'\nORDER BY month_start", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "月度问题密度趋势", "type": "timeseries"}], "refresh": "5m", "schemaVersion": 36, "style": "dark", "tags": ["code-quality", "trends"], "templating": {"list": []}, "time": {"from": "now-1y", "to": "now"}, "timepicker": {}, "timezone": "", "title": "代码质量效能分析 - 趋势分析", "uid": "code-quality-trends", "version": 1, "weekStart": ""}
# 构建文件合并工具

## 项目简介
这是一个用于合并多个SonarQube build-wrapper-dump.json文件的Python脚本，主要用于聚合多模块项目的构建信息。

## 功能特点
- 遍历指定目录下的子目录，查找build-wrapper-dump.json文件
- 去除JSON文件中的注释行
- 合并所有文件的captures数据
- 生成统一的merged-build-wrapper-dump.json输出文件

## 依赖环境
- Python 3.6+
- 标准库：os, json, time, sys（无需额外安装依赖）

## 使用方法
```
python merge_build.py <base_directory>
```

## 参数说明
- `<base_directory>`: 必需，包含子目录的基础目录路径，脚本会在该目录下的所有子目录中查找build-wrapper-dump.json文件

## 输出说明
合并后的JSON文件将保存为：`<base_directory>/merged-build-wrapper-dump.json`

## 使用示例
```
python merge_build.py D:\projects\my_project\builds
```
此命令会处理D:\projects\my_project\builds目录下所有子目录中的build-wrapper-dump.json文件，并在该目录下生成merged-build-wrapper-dump.json

## 注意事项
1. 确保基础目录下包含多个子目录，每个子目录中存在有效的build-wrapper-dump.json文件
2. JSON文件中的注释行（以#开头的行）会被自动忽略
3. 输出文件会覆盖同名文件，请确保目标路径不存在重要文件
4. 脚本会保留第一个JSON文件中的version信息

## 版本历史
- v1.0: 初始版本，实现基本合并功能
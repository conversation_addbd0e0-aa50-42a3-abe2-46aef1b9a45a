#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import sys
from urllib.parse import urljoin

class SonarQubeWebhookManager:
    def __init__(self, sonar_url, token):
        self.sonar_url = sonar_url.rstrip('/')
        self.token = token
        self.session = requests.Session()
        self.session.auth = (token, '')
    
    def create_webhook(self, project_key, webhook_url, webhook_name="Grafana Webhook"):
        """为指定项目创建webhook"""
        try:
            # 检查项目是否存在
            if not self.check_project_exists(project_key):
                print(f"项目 {project_key} 不存在")
                return False
            
            # 创建webhook
            url = f"{self.sonar_url}/api/webhooks/create"
            data = {
                'project': project_key,
                'name': webhook_name,
                'url': webhook_url
            }
            
            response = self.session.post(url, data=data)
            
            if response.status_code == 200:
                webhook_data = response.json()
                print(f"成功为项目 {project_key} 创建webhook:")
                print(f"  - Webhook ID: {webhook_data['webhook']['key']}")
                print(f"  - Webhook URL: {webhook_data['webhook']['url']}")
                return True
            else:
                print(f"创建webhook失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"创建webhook时出错: {e}")
            return False
    
    def list_webhooks(self, project_key=None):
        """列出webhooks"""
        try:
            url = f"{self.sonar_url}/api/webhooks/list"
            params = {}
            if project_key:
                params['project'] = project_key
            
            response = self.session.get(url, params=params)
            
            if response.status_code == 200:
                webhooks_data = response.json()
                webhooks = webhooks_data.get('webhooks', [])
                
                if webhooks:
                    print(f"找到 {len(webhooks)} 个webhook:")
                    for webhook in webhooks:
                        print(f"  - {webhook['name']}: {webhook['url']}")
                        if 'project' in webhook:
                            print(f"    项目: {webhook['project']['name']} ({webhook['project']['key']})")
                else:
                    print("未找到任何webhook")
                
                return webhooks
            else:
                print(f"获取webhook列表失败: {response.status_code} - {response.text}")
                return []
                
        except Exception as e:
            print(f"获取webhook列表时出错: {e}")
            return []
    
    def check_project_exists(self, project_key):
        """检查项目是否存在"""
        try:
            url = f"{self.sonar_url}/api/components/show"
            params = {'component': project_key}
            
            response = self.session.get(url, params=params)
            return response.status_code == 200
            
        except Exception as e:
            print(f"检查项目存在性时出错: {e}")
            return False
    
    def list_projects(self):
        """列出所有项目"""
        try:
            url = f"{self.sonar_url}/api/components/search"
            params = {
                'qualifiers': 'TRK',
                'ps': 100  # 每页100个项目
            }
            
            response = self.session.get(url, params=params)
            
            if response.status_code == 200:
                projects_data = response.json()
                projects = projects_data.get('components', [])
                
                print(f"找到 {len(projects)} 个项目:")
                for project in projects:
                    print(f"  - {project['name']} ({project['key']})")
                
                return projects
            else:
                print(f"获取项目列表失败: {response.status_code} - {response.text}")
                return []
                
        except Exception as e:
            print(f"获取项目列表时出错: {e}")
            return []

def main():
    # SonarQube服务器配置
    sonar_configs = [
        {
            'name': 'SonarQube Community (9090)',
            'url': 'http://10.0.0.2:9000',
            'token': '0da72b170a333b2aa5d1a8e48cc8ba14ce3cb1fd'
        },
        {
            'name': 'SonarQube Enterprise (9001)',
            'url': 'http://10.0.0.59:9001',
            'token': 'squ_a60fc304111367163786cc06084df9b8d96e5ff1'
        }
    ]
    
    # Webhook服务URL（需要根据实际部署调整）
    webhook_url = "http://10.0.0.39:8080/webhook/sonarqube"
    
    for config in sonar_configs:
        print(f"\n=== 配置 {config['name']} ===")
        
        manager = SonarQubeWebhookManager(config['url'], config['token'])
        
        # 列出项目
        print("\n1. 获取项目列表:")
        projects = manager.list_projects()
        
        # 列出现有webhooks
        print("\n2. 现有webhooks:")
        manager.list_webhooks()
        
        # 为每个项目创建webhook
        print("\n3. 创建webhooks:")
        for project in projects:
            project_key = project['key']
            webhook_name = f"Grafana Webhook - {project['name']}"
            
            print(f"\n为项目 {project_key} 创建webhook...")
            manager.create_webhook(project_key, webhook_url, webhook_name)

if __name__ == "__main__":
    main()

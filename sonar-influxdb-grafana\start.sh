#!/bin/bash

echo "🚀 SonarQube InfluxDB Grafana 集成方案启动脚本"
echo "=" * 60

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ 错误: Docker未运行，请先启动Docker"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p logs
mkdir -p grafana/provisioning/datasources
mkdir -p grafana/provisioning/dashboards

# 启动服务
echo "🚀 启动服务栈..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
echo "   InfluxDB 需要一些时间来初始化..."
sleep 45

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

echo ""
echo "🏥 执行健康检查..."

# 检查InfluxDB
echo "   检查InfluxDB..."
if curl -f http://localhost:8086/health > /dev/null 2>&1; then
    echo "   ✅ InfluxDB运行正常"
else
    echo "   ❌ InfluxDB异常，可能还在启动中..."
fi

# 检查Webhook服务
echo "   检查Webhook服务..."
if curl -f http://localhost:8080/health > /dev/null 2>&1; then
    echo "   ✅ Webhook服务运行正常"
else
    echo "   ❌ Webhook服务异常"
fi

# 检查Grafana
echo "   检查Grafana..."
if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
    echo "   ✅ Grafana运行正常"
else
    echo "   ❌ Grafana异常"
fi

echo ""
echo "📊 服务访问地址:"
echo "   🎯 Grafana:    http://localhost:3000 (admin/admin123)"
echo "   📡 Webhook:    http://localhost:8080"
echo "   💾 InfluxDB:   http://localhost:8086 (admin/admin123456)"

echo ""
echo "🔧 管理命令:"
echo "   查看日志:      docker-compose logs -f"
echo "   停止服务:      docker-compose down"
echo "   重启服务:      docker-compose restart"

echo ""
echo "📋 下一步操作:"
echo "   1. 配置webhooks:   python setup_webhooks.py"
echo "   2. 发送测试数据:   python test_webhook.py"
echo "   3. 访问Grafana:    http://localhost:3000"
echo "   4. 访问InfluxDB:   http://localhost:8086"

echo ""
echo "🧪 测试命令:"
echo "   健康检查:      curl http://localhost:8080/health"
echo "   查看统计:      curl http://localhost:8080/stats"
echo "   Prometheus指标: curl http://localhost:8080/metrics"

echo ""
echo "✅ 启动完成! 请等待所有服务完全就绪后再进行配置。"

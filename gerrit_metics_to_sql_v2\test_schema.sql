-- 测试数据库表结构
-- 这个脚本用于验证表结构是否正确创建

-- 1. 测试表是否存在
SELECT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'commit_metrics'
) as table_exists;

-- 2. 测试触发器是否存在
SELECT trigger_name, event_manipulation, action_statement
FROM information_schema.triggers 
WHERE event_object_table = 'commit_metrics';

-- 3. 测试视图是否存在
SELECT table_name 
FROM information_schema.views 
WHERE table_schema = 'public' 
AND table_name LIKE '%stats%' OR table_name LIKE '%distribution%';

-- 4. 测试索引是否存在
SELECT indexname, indexdef
FROM pg_indexes 
WHERE tablename = 'commit_metrics';

-- 5. 测试插入一条测试数据（如果表为空）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM commit_metrics LIMIT 1) THEN
        INSERT INTO commit_metrics (
            commit_id,
            change_id,
            change_id_short,
            patch_set,
            gerrit_project,
            branch,
            subject,
            status,
            commit_time,
            created,
            updated,
            author,
            owner,
            number,
            url,
            reviewers,
            changed_lines,
            insertions,
            deletions,
            patchset_count,
            repo_path,
            sonar_project,
            sonar_key,
            sonar_creation_date,
            sq_blocker,
            sq_critical,
            sq_major,
            sq_minor,
            sq_info,
            sq_resolved_blocker,
            sq_resolved_critical,
            sq_resolved_major,
            sq_resolved_minor,
            sq_resolved_info,
            gerrit_raw,
            sonar_issues,
            ncloc,
            statements,
            functions,
            files,
            comment_lines,
            comment_lines_density,
            complexity,
            duplicated_lines_density,
            duplicated_lines,
            duplicated_blocks,
            duplicated_files,
            commit_date,
            commit_year,
            commit_month,
            commit_week,
            commit_hour,
            total_issues,
            total_resolved_issues,
            critical_issues,
            issue_density,
            change_size_category,
            quality_level
        ) VALUES (
            'test_commit_001',
            'test_change_001',
            'test_cha',
            1,
            'test-project',
            'master',
            'Test commit for schema validation',
            'MERGED',
            NOW(),
            NOW(),
            NOW(),
            'test_author',
            'test_owner',
            1001,
            'http://test.com',
            ARRAY['reviewer1', 'reviewer2'],
            50,
            30,
            20,
            1,
            'test-project',
            'test-sonar-project',
            'test-sonar-key',
            NOW(),
            0,
            1,
            2,
            3,
            0,
            0,
            0,
            0,
            0,
            0,
            '{}',
            '{}',
            1000,
            500,
            20,
            10,
            100,
            5.0,
            50,
            2.0,
            20,
            2,
            1,
            CURRENT_DATE,
            EXTRACT(YEAR FROM NOW()),
            EXTRACT(MONTH FROM NOW()),
            EXTRACT(WEEK FROM NOW()),
            EXTRACT(HOUR FROM NOW()),
            6,
            0,
            1,
            6.0,
            'medium',
            'major'
        );
        
        RAISE NOTICE 'Test data inserted successfully';
    ELSE
        RAISE NOTICE 'Table already contains data, skipping test insert';
    END IF;
END $$;

-- 6. 测试视图查询
SELECT 'Testing daily_commit_stats view' as test_name;
SELECT COUNT(*) as record_count FROM daily_commit_stats;

SELECT 'Testing author_contribution_stats view' as test_name;
SELECT COUNT(*) as record_count FROM author_contribution_stats;

SELECT 'Testing project_quality_stats view' as test_name;
SELECT COUNT(*) as record_count FROM project_quality_stats;

-- 7. 测试派生字段是否正确计算
SELECT 
    commit_id,
    total_issues,
    critical_issues,
    issue_density,
    change_size_category,
    quality_level,
    commit_date,
    commit_year,
    commit_month
FROM commit_metrics 
LIMIT 5;

-- 8. 清理测试数据（可选）
-- DELETE FROM commit_metrics WHERE commit_id = 'test_commit_001'; 
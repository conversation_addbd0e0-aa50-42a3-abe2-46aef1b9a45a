#!/bin/bash

echo "🛑 SonarQube InfluxDB Grafana 集成方案停止脚本"
echo "=" * 50

# 显示当前运行的服务
echo "📊 当前运行的服务:"
docker-compose ps

echo ""
read -p "🤔 确定要停止所有服务吗? (y/N): " confirm

if [[ $confirm =~ ^[Yy]$ ]]; then
    echo "🛑 停止所有服务..."
    docker-compose down
    
    echo ""
    read -p "🗑️  是否删除所有数据卷? (这将删除所有数据!) (y/N): " delete_volumes
    
    if [[ $delete_volumes =~ ^[Yy]$ ]]; then
        echo "🗑️  删除数据卷..."
        docker-compose down -v
        echo "✅ 所有数据已删除"
    else
        echo "💾 数据卷已保留"
    fi
    
    echo ""
    echo "✅ 服务已停止"
    echo ""
    echo "🔄 重新启动命令:"
    echo "   ./start.sh"
    
else
    echo "❌ 操作已取消"
fi

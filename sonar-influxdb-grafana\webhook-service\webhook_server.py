#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import logging
from datetime import datetime
from flask import Flask, request, jsonify
from dateutil import parser
from influxdb_client import InfluxDBClient, Point, WritePrecision
from influxdb_client.client.write_api import SYNCHRONOUS

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/app/logs/webhook.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# InfluxDB配置
INFLUXDB_URL = os.getenv('INFLUXDB_URL', 'http://localhost:8086')
INFLUXDB_TOKEN = os.getenv('INFLUXDB_TOKEN', 'sonar-admin-token-12345678901234567890')
INFLUXDB_ORG = os.getenv('INFLUXDB_ORG', 'sonarqube')
INFLUXDB_BUCKET = os.getenv('INFLUXDB_BUCKET', 'sonar_metrics')

# 创建InfluxDB客户端
influx_client = InfluxDBClient(url=INFLUXDB_URL, token=INFLUXDB_TOKEN, org=INFLUXDB_ORG)
write_api = influx_client.write_api(write_options=SYNCHRONOUS)
query_api = influx_client.query_api()

def write_to_influxdb(points):
    """写入数据到InfluxDB"""
    try:
        write_api.write(bucket=INFLUXDB_BUCKET, org=INFLUXDB_ORG, record=points)
        logger.info(f"成功写入 {len(points)} 个数据点到InfluxDB")
        return True
    except Exception as e:
        logger.error(f"写入InfluxDB失败: {e}")
        return False

def convert_webhook_to_influx_points(webhook_data):
    """将SonarQube webhook数据转换为InfluxDB数据点"""
    try:
        points = []
        
        # 解析基本信息
        project_key = webhook_data.get('project', {}).get('key', 'unknown')
        project_name = webhook_data.get('project', {}).get('name', 'unknown')
        task_id = webhook_data.get('taskId', 'unknown')
        status = webhook_data.get('status', 'unknown')
        analysed_at = webhook_data.get('analysedAt', datetime.now().isoformat())
        
        # 解析时间戳
        try:
            timestamp = parser.parse(analysed_at)
        except:
            timestamp = datetime.now()
        
        # 1. 质量门状态点
        quality_gate_status = 'unknown'
        if 'qualityGate' in webhook_data:
            quality_gate_status = webhook_data['qualityGate'].get('status', 'unknown')
        
        # 质量门状态数值化 (OK=1, ERROR=0, 其他=-1)
        status_value = 1 if quality_gate_status == 'OK' else (0 if quality_gate_status == 'ERROR' else -1)
        
        quality_gate_point = Point("sonar_quality_gate") \
            .tag("project_key", project_key) \
            .tag("project_name", project_name) \
            .tag("task_id", task_id) \
            .tag("analysis_status", status) \
            .tag("quality_gate_status", quality_gate_status) \
            .field("status_value", status_value) \
            .field("analysis_count", 1) \
            .time(timestamp, WritePrecision.NS)
        
        points.append(quality_gate_point)
        
        # 2. 质量门条件点
        if 'qualityGate' in webhook_data and 'conditions' in webhook_data['qualityGate']:
            for condition in webhook_data['qualityGate']['conditions']:
                metric_key = condition.get('metricKey', 'unknown')
                operator = condition.get('operator', 'unknown')
                cond_status = condition.get('status', 'unknown')
                actual_value = condition.get('actualValue', '0')
                threshold_value = condition.get('threshold', '0')
                
                # 转换数值
                try:
                    actual_value_num = float(actual_value) if actual_value else 0
                except:
                    actual_value_num = 0
                
                try:
                    threshold_value_num = float(threshold_value) if threshold_value else 0
                except:
                    threshold_value_num = 0
                
                # 条件状态数值化
                condition_status_value = 1 if cond_status == 'OK' else (0 if cond_status == 'ERROR' else -1)
                
                condition_point = Point("sonar_condition") \
                    .tag("project_key", project_key) \
                    .tag("project_name", project_name) \
                    .tag("metric_key", metric_key) \
                    .tag("operator", operator) \
                    .tag("condition_status", cond_status) \
                    .field("actual_value", actual_value_num) \
                    .field("threshold_value", threshold_value_num) \
                    .field("condition_status_value", condition_status_value) \
                    .time(timestamp, WritePrecision.NS)
                
                points.append(condition_point)
                
                # 3. 指标历史点 (用于趋势分析)
                metric_point = Point("sonar_metric") \
                    .tag("project_key", project_key) \
                    .tag("project_name", project_name) \
                    .tag("metric_key", metric_key) \
                    .field("value", actual_value_num) \
                    .time(timestamp, WritePrecision.NS)
                
                points.append(metric_point)
        
        # 4. 分析事件点
        analysis_point = Point("sonar_analysis") \
            .tag("project_key", project_key) \
            .tag("project_name", project_name) \
            .tag("status", status) \
            .field("duration", 1) \
            .field("count", 1) \
            .time(timestamp, WritePrecision.NS)
        
        points.append(analysis_point)
        
        return points
        
    except Exception as e:
        logger.error(f"转换webhook数据时出错: {e}")
        return []

@app.route('/webhook/sonarqube', methods=['POST'])
def sonarqube_webhook():
    """接收SonarQube webhook"""
    try:
        # 获取webhook数据
        webhook_data = request.get_json()
        
        if not webhook_data:
            logger.warning("收到空的webhook数据")
            return jsonify({'error': 'No data received'}), 400
        
        project_key = webhook_data.get('project', {}).get('key', 'unknown')
        logger.info(f"收到SonarQube webhook: 项目={project_key}")
        
        # 转换为InfluxDB数据点
        points = convert_webhook_to_influx_points(webhook_data)
        
        if points:
            # 写入InfluxDB
            if write_to_influxdb(points):
                return jsonify({
                    'status': 'success', 
                    'message': f'Webhook processed successfully, {len(points)} points written',
                    'project_key': project_key
                }), 200
            else:
                return jsonify({'status': 'error', 'message': 'Failed to write to InfluxDB'}), 500
        else:
            return jsonify({'status': 'error', 'message': 'No data points generated'}), 500
            
    except Exception as e:
        logger.error(f"处理webhook时出错: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    try:
        # 测试InfluxDB连接
        health = influx_client.health()
        if health.status == "pass":
            return jsonify({
                'status': 'healthy', 
                'influxdb': 'connected',
                'influxdb_version': health.version
            }), 200
        else:
            return jsonify({
                'status': 'unhealthy', 
                'influxdb': 'disconnected',
                'error': health.message
            }), 500
    except Exception as e:
        return jsonify({'status': 'unhealthy', 'error': str(e)}), 500

@app.route('/stats', methods=['GET'])
def get_stats():
    """获取统计信息"""
    try:
        # 查询最近24小时的数据统计
        query = f'''
        from(bucket: "{INFLUXDB_BUCKET}")
          |> range(start: -24h)
          |> filter(fn: (r) => r["_measurement"] == "sonar_quality_gate")
          |> group(columns: ["project_key"])
          |> count()
        '''
        
        result = query_api.query(org=INFLUXDB_ORG, query=query)
        
        stats = {
            'projects': [],
            'total_analyses': 0
        }
        
        for table in result:
            for record in table.records:
                project_key = record.values.get('project_key', 'unknown')
                count = record.get_value()
                stats['projects'].append({
                    'project_key': project_key,
                    'analyses_24h': count
                })
                stats['total_analyses'] += count
        
        return jsonify(stats), 200
        
    except Exception as e:
        logger.error(f"获取统计信息时出错: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/metrics', methods=['GET'])
def prometheus_metrics():
    """提供Prometheus格式的指标"""
    try:
        # 查询最新的质量门状态
        query = f'''
        from(bucket: "{INFLUXDB_BUCKET}")
          |> range(start: -1h)
          |> filter(fn: (r) => r["_measurement"] == "sonar_quality_gate")
          |> filter(fn: (r) => r["_field"] == "status_value")
          |> group(columns: ["project_key"])
          |> last()
        '''
        
        result = query_api.query(org=INFLUXDB_ORG, query=query)
        
        metrics_lines = []
        metrics_lines.append("# HELP sonar_quality_gate_status SonarQube quality gate status")
        metrics_lines.append("# TYPE sonar_quality_gate_status gauge")
        
        for table in result:
            for record in table.records:
                project_key = record.values.get('project_key', 'unknown')
                status_value = record.get_value()
                metrics_lines.append(f'sonar_quality_gate_status{{project_key="{project_key}"}} {status_value}')
        
        return '\n'.join(metrics_lines), 200, {'Content-Type': 'text/plain'}
        
    except Exception as e:
        logger.error(f"生成Prometheus指标时出错: {e}")
        return f"# Error: {str(e)}", 500, {'Content-Type': 'text/plain'}

if __name__ == '__main__':
    # 确保日志目录存在
    os.makedirs('/app/logs', exist_ok=True)
    
    logger.info("启动SonarQube InfluxDB Webhook服务...")
    app.run(host='0.0.0.0', port=8080, debug=False)

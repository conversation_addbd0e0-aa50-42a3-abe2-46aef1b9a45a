# 📊 SonarQube WR02 Grafana 仪表盘集合

基于 `sonaree_wr02` 表数据创建的专业级 Grafana 仪表盘集合，提供全方位的代码质量监控和分析功能。

## 🎯 仪表盘概览

### 1. SonarQube WR02 增强版质量监控仪表板
**文件**: `sonaree_wr02_enhanced_dashboard.json`  
**UID**: `sonaree-wr02-enhanced`

#### 功能特性
- **质量概览**: 活跃项目数、总问题数、严重问题数、平均覆盖率等关键指标
- **质量分布**: 代码质量等级分布饼图，问题类型分布环形图
- **趋势分析**: 每日问题趋势、测试覆盖率趋势、代码质量指标趋势
- **详细表格**: 项目质量详情表，支持按质量得分排序
- **问题分析**: 问题严重程度分布趋势、代码复杂度趋势

#### 适用场景
- 日常质量监控
- 团队质量汇报
- 质量改进跟踪
- 管理层质量概览

---

### 2. SonarQube WR02 项目对比分析仪表板
**文件**: `sonaree_wr02_project_comparison.json`  
**UID**: `sonaree-wr02-comparison`

#### 功能特性
- **智能排行榜**: 基于质量得分的项目排行，综合考虑质量等级和覆盖率
- **散点图分析**: 
  - 覆盖率 vs 问题密度散点图
  - 代码行数 vs 复杂度散点图
- **项目筛选**: 支持多项目选择过滤
- **质量得分算法**: 自定义质量评分系统

#### 质量得分计算规则
```sql
CASE 
  WHEN quality_level = 'clean' THEN 90 + (coverage * 0.1)
  WHEN quality_level = 'minor' THEN 70 + (coverage * 0.2)
  WHEN quality_level = 'major' THEN 50 + (coverage * 0.2)
  WHEN quality_level = 'critical' THEN 20 + (coverage * 0.3)
  ELSE 50
END
```

#### 适用场景
- 项目间质量对比
- 识别优秀项目和问题项目
- 质量基准设定
- 项目质量评估

---

### 3. SonarQube WR02 趋势分析仪表板
**文件**: `sonaree_wr02_trend_analysis.json`  
**UID**: `sonaree-wr02-trends`

#### 功能特性
- **趋势对比**: 最近7天 vs 前7天的质量变化对比
- **长期趋势**: 90天项目活跃度趋势、测试覆盖率趋势
- **质量密度**: 问题密度、重复代码密度、注释密度、复杂度密度趋势
- **时间范围选择**: 支持7天、30天、90天、180天、365天时间范围

#### 关键指标
- **项目活跃度**: 活跃项目数、总分析次数、清洁项目数、严重问题项目数
- **覆盖率指标**: 平均覆盖率、行覆盖率、分支覆盖率、高覆盖率项目占比
- **质量密度**: 问题密度、重复代码密度、注释密度、复杂度密度

#### 适用场景
- 长期质量趋势分析
- 质量改进效果评估
- 团队质量成熟度评估
- 质量目标设定和跟踪

---

## 🔧 技术特性

### 数据源配置
- **数据源UID**: `grafana-postgresql-datasource-sonar`
- **数据库表**: `sonaree_wr02`
- **查询优化**: 使用索引友好的查询，支持大数据量

### 查询优化特性
1. **时间范围过滤**: 所有查询都包含时间范围限制，提高性能
2. **状态过滤**: 只查询 `status = 'active'` 的记录
3. **空值处理**: 使用 `NULLIF` 和 `COALESCE` 处理空值
4. **聚合优化**: 合理使用 `GROUP BY` 和聚合函数

### 视觉设计特性
1. **颜色编码**: 
   - 绿色: 良好状态 (clean, 高覆盖率)
   - 黄色: 警告状态 (minor, 中等覆盖率)
   - 橙色: 注意状态 (major)
   - 红色: 严重状态 (critical, 低覆盖率)

2. **阈值设置**:
   - 覆盖率: <60%(红) 60-80%(黄) >80%(绿)
   - 问题密度: <5(绿) 5-10(黄) >10(红)

## 📋 安装和使用

### 1. 导入仪表盘
```bash
# 方法1: 通过Grafana UI导入
# 1. 登录Grafana
# 2. 点击 "+" -> "Import"
# 3. 上传JSON文件或粘贴JSON内容

# 方法2: 通过API导入
curl -X POST \
  http://your-grafana-url/api/dashboards/import \
  -H 'Authorization: Bearer YOUR_API_KEY' \
  -H 'Content-Type: application/json' \
  -d @sonaree_wr02_enhanced_dashboard.json
```

### 2. 配置数据源
确保Grafana中已配置名为 `grafana-postgresql-datasource-sonar` 的PostgreSQL数据源。

### 3. 验证数据
```sql
-- 验证数据是否存在
SELECT COUNT(*) FROM sonaree_wr02 WHERE status = 'active';

-- 检查最新数据
SELECT MAX(analysis_date) FROM sonaree_wr02;
```

## 🎨 自定义配置

### 修改时间范围
在仪表盘设置中可以修改默认时间范围：
- 质量监控: 默认30天
- 项目对比: 默认7天  
- 趋势分析: 默认90天

### 添加告警
可以基于以下指标设置告警：
- 严重问题数量超过阈值
- 覆盖率低于目标值
- 问题密度超过限制
- 质量等级为critical的项目数量

### 自定义查询
所有查询都可以根据实际需求进行调整：
- 修改时间范围
- 添加项目过滤
- 调整聚合方式
- 增加新的指标

## 📊 使用建议

### 日常监控流程
1. **每日检查**: 使用增强版质量监控仪表板
2. **每周对比**: 使用项目对比分析仪表板
3. **每月趋势**: 使用趋势分析仪表板

### 质量改进流程
1. **识别问题**: 通过项目对比找出问题项目
2. **分析趋势**: 通过趋势分析了解质量变化
3. **制定计划**: 基于数据制定改进计划
4. **跟踪效果**: 通过日常监控跟踪改进效果

---

## 🔍 故障排除

### 常见问题
1. **数据不显示**: 检查数据源配置和表名
2. **查询超时**: 优化查询条件，添加索引
3. **图表异常**: 检查数据类型和空值处理

### 性能优化
1. **添加索引**:
```sql
CREATE INDEX idx_sonaree_wr02_analysis_date ON sonaree_wr02(analysis_date_only);
CREATE INDEX idx_sonaree_wr02_project_status ON sonaree_wr02(project_name, status);
```

2. **查询优化**: 根据实际数据量调整LIMIT和时间范围

---

🎉 **这些仪表盘为您提供了全方位的SonarQube WR02数据分析能力，助力提升代码质量管理效率！**

#!/bin/bash

# Variables
REPO="qwkjtestrepo"
USER="LuckyDoue"
TOKEN="*********************************************************************************************"
FILE_PATH="algo_service_adapter.c"
COMMIT_MESSAGE="test"

# Upload the file to the repository using GitHub API
UPLOAD_URL="https://api.github.com/repos/$USER/$REPO/contents/$FILE_PATH"

DATA="{\"message\":\"$COMMIT_MESSAGE\",\"content\":\"$COMMIT_MESSAGE\"}"

curl -X PUT -H "Authorization: token $TOKEN" -d "$DATA" $UPLOAD_URL

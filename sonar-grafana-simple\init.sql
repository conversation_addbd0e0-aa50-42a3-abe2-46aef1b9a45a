-- 创建SonarQube数据表
CREATE TABLE IF NOT EXISTS sonar_quality_gates (
    id SERIAL PRIMARY KEY,
    project_key VARCHAR(255) NOT NULL,
    project_name VARCHAR(255) NOT NULL,
    task_id VARCHAR(255),
    status VARCHAR(50) NOT NULL,
    quality_gate_status VARCHAR(50),
    analysed_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    webhook_data JSONB
);

-- 创建质量门条件表
CREATE TABLE IF NOT EXISTS sonar_conditions (
    id SERIAL PRIMARY KEY,
    quality_gate_id INTEGER REFERENCES sonar_quality_gates(id),
    metric_key VARCHAR(255) NOT NULL,
    operator VARCHAR(10),
    status VARCHAR(50),
    actual_value NUMERIC,
    threshold_value NUMERIC,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建项目指标历史表
CREATE TABLE IF NOT EXISTS sonar_metrics_history (
    id SERIAL PRIMARY KEY,
    project_key VARCHAR(255) NOT NULL,
    metric_key VARCHAR(255) NOT NULL,
    metric_value NUMERIC,
    measured_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_quality_gates_project_key ON sonar_quality_gates(project_key);
CREATE INDEX IF NOT EXISTS idx_quality_gates_analysed_at ON sonar_quality_gates(analysed_at);
CREATE INDEX IF NOT EXISTS idx_conditions_quality_gate_id ON sonar_conditions(quality_gate_id);
CREATE INDEX IF NOT EXISTS idx_conditions_metric_key ON sonar_conditions(metric_key);
CREATE INDEX IF NOT EXISTS idx_metrics_history_project_key ON sonar_metrics_history(project_key);
CREATE INDEX IF NOT EXISTS idx_metrics_history_metric_key ON sonar_metrics_history(metric_key);
CREATE INDEX IF NOT EXISTS idx_metrics_history_measured_at ON sonar_metrics_history(measured_at);

-- 插入示例数据
INSERT INTO sonar_quality_gates (project_key, project_name, task_id, status, quality_gate_status, analysed_at, webhook_data) 
VALUES 
('test-project', 'Test Project', 'task-123', 'SUCCESS', 'OK', NOW(), '{"test": true}'),
('demo-project', 'Demo Project', 'task-456', 'FAILED', 'ERROR', NOW() - INTERVAL '1 hour', '{"test": true}')
ON CONFLICT DO NOTHING;

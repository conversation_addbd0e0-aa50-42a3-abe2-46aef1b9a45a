# 代码质量效能分析度量平台

基于您现有的 Gerrit + SonarQube PostgreSQL 数据，提供多种代码质量效能分析方案。

## 🎯 方案概览

| 方案 | 技术栈 | 优势 | 适用场景 |
|------|--------|------|----------|
| **方案1: Grafana方案** | Grafana + PostgreSQL | 快速部署，专业图表 | 运维监控，团队仪表板 |
| **方案2: Streamlit方案** | Python + Streamlit | 交互性强，自定义度高 | 数据分析，深度洞察 |

## 📊 数据源

基于您的 `gerrit_metics_to_sql_v2` 项目中的 PostgreSQL 数据：
- **数据库**: **********:5434/mydatabase
- **主表**: commit_metrics
- **视图**: 8个预定义分析视图
- **数据**: Gerrit代码审查 + SonarQube质量分析

## 🚀 方案1: Grafana解决方案

### 特点
- ✅ 专业的监控仪表板
- ✅ 丰富的图表类型
- ✅ 实时数据刷新
- ✅ 告警和通知功能
- ✅ 多用户权限管理

### 快速启动

```bash
cd code-quality-platform/grafana-solution

# 修改数据源配置（连接到您的现有数据库）
# 编辑 grafana/provisioning/datasources/datasources.yml

# 启动服务
docker-compose up -d

# 访问Grafana
# http://localhost:3001 (admin/admin123)
```

### 仪表板功能
- 📈 **总览仪表板**: 关键指标概览
- 📊 **趋势分析**: 时间序列分析
- 👨‍💻 **开发者分析**: 个人贡献统计
- 📁 **项目分析**: 项目质量对比
- 🛡️ **质量分析**: 问题分布分析
- ⚠️ **风险监控**: 高风险提交告警

## 🎨 方案2: Streamlit解决方案

### 特点
- ✅ 高度交互式界面
- ✅ 实时数据筛选
- ✅ 自定义分析功能
- ✅ 导出分析报告
- ✅ 易于二次开发

### 快速启动

```bash
cd code-quality-platform/streamlit-solution

# 安装依赖
pip install -r requirements.txt

# 启动应用
chmod +x run.sh
./run.sh

# 或直接运行
streamlit run app.py

# 访问应用
# http://localhost:8501
```

### 功能模块
- 🏠 **总览**: 关键指标和趋势概览
- 📈 **趋势分析**: 月度/日度趋势分析
- 👨‍💻 **开发者分析**: 开发者能力雷达图
- 📁 **项目分析**: 项目质量矩阵
- 🛡️ **质量分析**: 问题分布和复杂度分析
- ⚠️ **风险监控**: 高风险提交监控

## 📋 核心分析指标

### 代码质量指标
- **问题密度**: 每千行代码的问题数
- **质量等级**: clean/minor/major/critical
- **问题分布**: Blocker/Critical/Major/Minor/Info
- **清洁代码率**: 无问题提交占比

### 效能指标
- **提交频率**: 每日/月度提交统计
- **代码变更量**: 插入/删除行数统计
- **开发者活跃度**: 贡献者数量和活跃程度
- **项目活跃度**: 项目提交数和参与人数

### 风险指标
- **高风险提交**: 包含严重问题的提交
- **复杂度分布**: 代码复杂度统计
- **重复度分析**: 代码重复率统计
- **趋势告警**: 质量下降趋势预警

## 🔧 配置说明

### 数据库连接配置

**Grafana方案**:
```yaml
# grafana/provisioning/datasources/datasources.yml
url: **********:5434  # 您的数据库地址
database: mydatabase
user: admin
password: admin123
```

**Streamlit方案**:
```python
# config.py
DB_CONFIG = {
    'host': '**********',
    'port': '5434',
    'database': 'mydatabase',
    'user': 'admin',
    'password': 'admin123'
}
```

### 环境变量配置

```bash
# 可选：通过环境变量配置数据库连接
export DB_HOST=**********
export DB_PORT=5434
export DB_NAME=mydatabase
export DB_USER=admin
export DB_PASSWORD=admin123
```

## 📊 使用场景

### 团队管理者
- 查看团队整体代码质量趋势
- 识别高风险项目和开发者
- 制定质量改进计划

### 项目经理
- 监控项目代码质量状况
- 对比不同项目的质量水平
- 评估项目交付风险

### 开发者
- 查看个人代码质量表现
- 对比团队平均水平
- 改进代码质量

### 质量工程师
- 深度分析代码质量问题
- 识别质量改进机会
- 制定质量标准和流程

## 🛠️ 扩展开发

### 添加新的分析维度
1. 在数据库中添加新的视图或查询
2. 在对应方案中添加新的图表组件
3. 配置数据刷新和缓存策略

### 集成其他数据源
- Jenkins构建数据
- JIRA缺陷数据  
- Git提交数据
- 测试覆盖率数据

### 自定义告警规则
- 质量下降告警
- 高风险提交通知
- 异常活动检测

## 📞 技术支持

### 常见问题
1. **数据库连接失败**: 检查网络连接和数据库配置
2. **图表显示异常**: 检查数据格式和时间范围
3. **性能问题**: 优化查询语句和添加索引

### 日志查看
```bash
# Grafana日志
docker logs code-quality-grafana

# Streamlit日志
# 在终端中直接显示
```

---

🎉 **选择适合您团队的方案，开始您的代码质量效能分析之旅！**

<!DOCTYPE html>
<html lang='zh-cn'>
<head>
  <meta charset='utf-8'>
  <meta http-equiv='X-UA-Compatible' content='IE=edge'>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="renderer" content="webkit">
  <title>地盘 - 禅道</title>
<script>window.config={"webRoot":"\/biz812\/","debug":false,"appName":"","cookieLife":30,"requestType":"PATH_INFO","requestFix":"-","moduleVar":"m","methodVar":"f","viewVar":"t","defaultView":"html","themeRoot":"\/biz812\/theme\/","currentModule":"my","currentMethod":"index","clientLang":"zh-cn","requiredFields":"","router":"\/biz812\/index.php","save":"\u4fdd\u5b58","runMode":"","timeout":30000,"pingInterval":"","onlybody":"no","version":"biz8.12","tabSession":false};
window.lang={"submitting":"\u7a0d\u5019...","save":"\u4fdd\u5b58","expand":"\u5c55\u5f00\u5168\u90e8","timeout":"\u8fde\u63a5\u8d85\u65f6\uff0c\u8bf7\u68c0\u67e5\u7f51\u7edc\u73af\u5883\uff0c\u6216\u91cd\u8bd5\uff01","confirmDraft":"\u6709\u672a\u4fdd\u5b58\u7684%name%\u8868\u5355\uff0c\u662f\u5426\u6062\u590d\uff1f","resume":"\u6062\u590d","program":"\u9879\u76ee\u96c6","project":"\u9879\u76ee","product":"\u4ea7\u54c1","task":"\u4efb\u52a1","story":"\u7814\u53d1\u9700\u6c42","bug":"Bug","testcase":"\u7528\u4f8b","zahost":"\u5bbf\u4e3b\u673a","zanode":"\u6267\u884c\u8282\u70b9","gitlab":"","gogs":"","gitea":"","jenkins":"","sonarqube":"","repo":"\u4ee3\u7801\u5e93"};

</script>
<link rel='stylesheet' href='/biz812/theme/default/zh-cn.default.css?v=biz8.12' type='text/css' media='screen' />
<script src='/biz812/js/all.js?v=biz8.12'></script>
<style>.dropdown-menu.with-search {padding: 0; min-width: 150px; overflow: hidden; max-height: 302px;}
.dropdown-menu > .menu-search .input-group {width: 100%;}
.dropdown-menu > .menu-search .input-group-addon {position: absolute; right: 10px; top: 0; z-index: 10; background: none; border: none; color: #666;}
.dropdown-menu > .menu-search .form-control {border: none !important; box-shadow: none !important; border-top: 1px solid #ddd !important;}
.dropdown-list {display: block; padding: 0; max-height: 270px; overflow-y: auto;}
.dropdown-list > li > a {display: block; padding: 3px 20px; clear: both; font-weight: normal; line-height: 1.53846154; color: #141414; white-space: nowrap;}
.dropdown-list > li > a:hover,
.dropdown-list > li > a:focus {color: #1a4f85; text-decoration: none; background-color: #ddd;}

.btn-toolbar .nav-title {float: left; display: inline-block; padding: 6px 12px; margin-bottom: 0; font-size: 13px; font-weight: bolder; line-height: 18px; text-align: center; white-space: nowrap; vertical-align: middle;}
#myStoryForm table tbody tr td.c-actions .dividing-line {width: 1px; height: 16px; display: inline-block; vertical-align: middle; background: #F4F5F7; margin: 0 4px 0 0;}
</style><link rel='icon' href='/biz812/favicon.ico' type='image/x-icon' />
<link rel='shortcut icon' href='/biz812/favicon.ico' type='image/x-icon' />
<!--[if lt IE 10]>
<script src='/biz812/js/jquery/placeholder/min.js?v=biz8.12'></script>
<![endif]-->
<style>
#editorSteps .nav-primary>li.active>a,#editorSteps .nav-primary>li.active>a:focus,#editorSteps .nav-primary>li.active>a:hover {color: #fff;}
.menu.leftmenu .nav-primary >li{width:100%}
.menu.leftmenu .nav-primary >li > a {border:none;}
</style>
<script>workflowNavGroup = {"cs":"system"};</script>
<script>
if(typeof(parent.window.navGroup) == 'object') $.extend(parent.window.navGroup, workflowNavGroup);
</script>
<style>
.outer .with-side .side, .outer .with-side .side + .main{visibility: visible;}
#featurebar {margin: -8px 0 10px;}
#featurebar .nav:after {clear:left;}
#featurebar .nav li {float:left; margin-right:10px;padding:6px;}
#featurebar .nav li a {padding:0px;}
#featurebar .nav li.active a{top: -1px; display: inline-block; font-weight: 700; color: #006af1;}
#featurebar .nav li.active a:after {position: absolute;bottom: -5px;display: block;width: 100%;content: ' ';border-bottom: 2px solid #006af1;}
#main #menuActions {margin-top: -40px !important;}
.with-side .side{left:auto;}
.icon-remove:before{content:"\e90d";}
.with-side .side .panel .panel-body{padding:10px;}

.tablesorter thead > tr > th > a {position: relative; display: inline-block; padding-right: 16px; color: #3c4353;}
.tablesorter thead > tr > th > a:after, .tablesorter thead > tr > th > a:before {position: absolute; top: 0; right: 0; font-family: ZentaoIcon; font-size: 14px; font-style: normal; font-weight: 400; font-variant: normal; line-height: 1.1; color: #3c4353; text-transform: none; content: "\f0de"; opacity: .5;}
.tablesorter thead > tr > th > a:after{content: "\f0dd";}
.tablesorter thead > tr > th > a.sort-down, .tablesorter thead > tr > th > a.sort-up, .tablesorter thead > tr > th > a:hover { color: #006af1; text-decoration: none;}
.tablesorter thead > tr > th > a.sort-down:after, .tablesorter thead > tr > th > a.sort-up:before, .tablesorter thead > tr > th > a:hover:after, .tablesorter thead > tr > th > a:hover:before { color: #006af1; opacity: 1;}
</style>
<style>
#navbar ul.nav{padding-left:10px;}
#navbar ul.nav li.divider{margin:15px 10px;}
</style>
<script>
$(function()
{
    $('#globalBarLogo a').eq(1).attr('title', '企业版 8.12');
    $('#globalBarLogo a .version').html(' 企业版 8.12');
    $('#globalBarLogo a .upgrade').html('旗舰版');
})
</script>
</head>
<body class=''>
<header id='header'>
  <div id='mainHeader'>
    <div class='container'>
      <div id='heading'>
        <div class='btn-group header-btn'><a href='/biz812/my.html' class='btn' style='padding-top: 2px' ><i class='icon icon-menu-my'></i> 地盘</a>
</div>              </div>
      <nav id='navbar'><ul class='nav nav-default'>
<li class=' active' data-id='index'><a href='/biz812/my.html'  >仪表盘</a>
</li>
<li class=' ' data-id='calendar'><a href='/biz812/my-calendar.html'  >日程</a>
</li>
<li class=' ' data-id='effort'><a href='/biz812/effort-calendar.html'  >日志</a>
</li>
<li class='divider'></li><li class=' ' data-id='work'><a href='/biz812/my-work-task.html'  >待处理</a>
</li>
<li class=' ' data-id='audit'><a href='/biz812/my-audit-all--time_desc.html'  >审批</a>
</li>
<li class=' ' data-id='project'><a href='/biz812/my-project.html'  >项目</a>
</li>
<li class=' ' data-id='execution'><a href='/biz812/my-execution-undone.html'  >执行</a>
</li>
<li class=' ' data-id='contribute'><a href='/biz812/my-contribute-task.html'  >贡献</a>
</li>
<li class='divider'></li><li class=' ' data-id='dynamic'><a href='/biz812/my-dynamic.html'  >动态</a>
</li>
<li class=' ' data-id='contacts'><a href='/biz812/my-managecontacts.html'  >联系人</a>
</li>
<li><a href="http://10.0.105.8/spa/workflow/static/index.html#/main/workflow/add?_key=1hezwb" target="_blank">OA</a></li>
</ul>
</nav>
      <div id='headerActions'></div>
      <div id='toolbar'>
        <div id='userMenu'>
          <ul id="userNav" class="nav nav-default">
            <li class='dropdown dropdown-hover' id='globalCreate'><ul class='dropdown-menu pull-right create-list'><li><a href='/biz812/todo-create.html'  data-app=''><i class='icon icon-todo'></i> 待办</a>
</li><li><a href='/biz812/effort-batchCreate.html'  data-app=''><i class='icon icon-time'></i> 日志</a>
</li><li class="divider"></li><li><a href='/biz812/bug-create-51--from=global.html'  data-app=''><i class='icon icon-bug'></i> Bug</a>
</li><li><a href='/biz812/story-create-51-0-0-0-0-0-0-0-from=global.html'  data-app=''><i class='icon icon-lightbulb'></i> 研发需求</a>
</li><li><a href='/biz812/task-create-0-0-0-0-0-from=global.html'  data-app=''><i class='icon icon-check-sign'></i> 任务</a>
</li><li><a href='/biz812/testcase-create-51--0--0-0-from=global.html'  data-app=''><i class='icon icon-sitemap'></i> 用例</a>
</li><li><a href='/biz812/doc-selectLibType--0-0.html?onlybody=yes' class='iframe' data-width='750px' data-app=''><i class='icon icon-doc'></i> 文档</a>
</li><li class="divider"></li><li><a href='/biz812/execution-create-0-0-0-0-no-0-from=global.html'  data-app=''><i class='icon icon-run'></i> 执行</a>
</li><li><a href='/biz812/project-createGuide-0-global.html' data-toggle="modal" data-app=''><i class='icon icon-project'></i> 项目</a>
</li><li><a href='/biz812/product-create--from=global.html'  data-app=''><i class='icon icon-product'></i> 产品</a>
</li><li><a href='/biz812/program-create-0-from=global.html'  data-app=''><i class='icon icon-program'></i> 项目集</a>
</li><li class="divider"></li><li><a href='/biz812/kanban-createSpace.html?onlybody=yes' class='iframe' data-width='75%' data-app=''><i class='icon icon-cube'></i> 空间</a>
</li><li><a href='/biz812/kanban-create.html?onlybody=yes' class='iframe' data-width='75%' data-app=''><i class='icon icon-kanban'></i> 看板</a>
</li></ul><a class='dropdown-toggle' data-toggle='dropdown'><i class='icon icon-plus-solid-circle text-secondary'></i></a></li>
            <li class='dropdown dropdown-hover has-avatar' id='userDropDownMenu'><ul class='dropdown-menu pull-right'><li class="user-profile-item"><a href='/biz812/my-profile.html?onlybody=yes' data-width='700' class='iframe  no-role'><div class='avatar has-text avatar-circle' id="menu-avatar" style='background: hsl(142, 40%, 60%);'><span class='text text-len-1'>A</span></div><div class="user-profile-name">admin</div><div class="user-profile-role"></div></a></li><li class="divider"></li><li><a href='/biz812/my-profile.html?onlybody=yes' class='iframe' data-width='700' ><i class='icon icon-account'></i> 个人档案</a>
</li><li class="user-tutorial"><a href='/biz812/tutorial-start.html?onlybody=yes' class='iframe' data-class-name='modal-inverse' data-width='800' data-headerless='true' data-backdrop='true' data-keyboard='true' ><i class='icon icon-guide'></i> 新手引导</a>
</li><li class="preference-setting"><a href='/biz812/my-preference-false.html?onlybody=yes' class='iframe' data-width='700' ><i class='icon icon-controls'></i> 个性化设置</a>
</li><li class="change-password"><a href='/biz812/my-changepassword.html?onlybody=yes' class='iframe' data-width='600' ><i class='icon icon-cog-outline'></i> 修改密码</a>
</li><li class='divider'></li><li class='dropdown-submenu top'><a href='javascript:;'><i class='icon icon-theme'></i> 主题</a><ul class='dropdown-menu pull-left'><li class='selected'><a href='javascript:selectTheme("default");' data-value='default'>禅道蓝（默认）</a></li><li ><a href='javascript:selectTheme("blue");' data-value='blue'>青春蓝</a></li><li ><a href='javascript:selectTheme("green");' data-value='green'>叶兰绿</a></li><li ><a href='javascript:selectTheme("red");' data-value='red'>赤诚红</a></li><li ><a href='javascript:selectTheme("purple");' data-value='purple'>玉烟紫</a></li><li ><a href='javascript:selectTheme("pink");' data-value='pink'>芙蕖粉</a></li><li ><a href='javascript:selectTheme("blackberry");' data-value='blackberry'>露莓黑</a></li><li ><a href='javascript:selectTheme("classic");' data-value='classic'>经典蓝</a></li></ul></li><li class='dropdown-submenu top switch-language'><a href='javascript:;'><i class='icon icon-lang'></i> Language</a><ul class='dropdown-menu pull-left'><li class='selected'><a href='javascript:selectLang("zh-cn");'>简体</a></li><li ><a href='javascript:selectLang("zh-tw");'>繁體</a></li><li ><a href='javascript:selectLang("en");'>English</a></li></ul></li><li class="divider"></li><li><a href='/biz812/user-logout.html'  target='_top' ><i class='icon icon-exit'></i> 签退</a>
</li></ul><a class='dropdown-toggle' data-toggle='dropdown'><div class='avatar has-text avatar-circle'  style='background: hsl(142, 40%, 60%);'><span class='text text-len-1'>A</span></div></a><script>$("#userDropDownMenu").on("click", function(){$(this).removeClass("dropdown-hover");});$("#userDropDownMenu").on("hover", function(){$(this).next().removeClass("open");$(this).addClass("dropdown-hover");});</script></li>
          </ul>
        </div>
      </div>
    </div>
  </div>
    </header>

<script>
adjustMenuWidth();
if(window.navigator.userAgent.indexOf('xuanxuan') > 0)
{
    $('li.user-tutorial').addClass('hide');

    /* Fix double header covering #main. */
    $('document').ready(function()
    {
        $('#subHeader').parent().parent().children('#main').css('top', '100px');
    });
}
</script>
<main id='main'  >
  <div class='container'>
<style>body {padding-bottom: 20px;}
.nav > li > .btn {padding: 0 5px; line-height: 24px; margin: 3px; color: #3C4353;}
.nav > li > .btn.btn-primary {color: #fff;}
.nav .icon-more {font-size: 14px;}
.block-statistic .nav-stacked {overflow: auto; max-height: 220px;}
#dashboard .col-side {width: 450px; max-width: 450px;}
#dashboard .panel-body {max-height: 400px; overflow: auto;}
#dashboard .panel-body.has-table {padding-top: 0;}
#dashboard .panel-move-handler {position: absolute; top: 0; left: 0; right: 50px; height: 40px; z-index: 10; cursor: move;}
#dashboard .panel-heading {cursor: move; padding-right: 75px;}
@media (min-width: 992px)
{
    #dashboard > .row {display: table; width: 100%; margin: 0;}
    #dashboard > .row > .col-main,
    #dashboard > .row > .col-side {display: table-cell; float: none; padding: 0; vertical-align: top;}
    #dashboard > .row > .col-main {padding-right: 10px;}
    #dashboard > .row > .col-side {padding-left: 10px;}
}
#dashboard.sortable-sorting .panel {margin-bottom: 10px; box-shadow: 0 1px 1px rgba(0,0,0,.075), 0 2px 6px 0 rgba(0,0,0,.075); height: 46px;}
#dashboard.sortable-sorting .panel > * {display: none;}
#dashboard.sortable-sorting .panel:before {display: block; content: attr(data-name); padding: 12px 20px; font-weight: bold; font-size: 14px; color: #3c4353; opacity: 1 !important; text-align: left; visibility: visible;}
#dashboard.sortable-sorting .panel.dragging {visibility: visible; background: transparent; background: rgba(0,0,0,.075); box-shadow: inset 0 2px 6px rgba(0,0,0,.075);}
#dashboard.sortable-sorting .panel.dragging:before {color: #bbb;}
#dashboard.sortable-sorting .panel.drag-shadow {box-shadow: 0 1px 3px rgba(0,0,0,.175), 0 3px 8px 0 rgba(0,0,0,.175);}

#dashboard .block-sm .hide-in-sm {display: none;}
#dashboard .block-dynamic .panel-body {overflow-x: hidden;}
.modal-body {padding: 10px 20px 20px 20px;}

#dashboard .block-scrumoverview .table {margin-bottom: 12px;}
#dashboard .block-scrumoverview .table thead tr th {padding-left: 0; text-align: right; border-bottom: unset;}
#dashboard .block-scrumoverview .table tbody tr td {padding-left: 0;}
#dashboard .block-scrumoverview .table tbody tr th {text-align: right;}
</style><script src='/biz812/js/jquery/tablesorter/min.js?v=biz8.12'></script>
<script src='/biz812/js/jquery/tablesorter/metadata.js?v=biz8.12'></script>
<style>
.tablesorter-header-inner {cursor: pointer;}
.tablesorter-header-inner:hover {color: #000;font-weight:bold;}
table.tablesorter tr.tablesorter-headerRow .header .tablesorter-header-inner:after {font-family: ZentaoIcon; font-weight: normal; content: "\f0dc"; font-size: 14px; color: #838a9c}
table.tablesorter tr.tablesorter-headerRow .header.tablesorter-headerAsc .tablesorter-header-inner{color: #000;font-weight:bold;}
table.tablesorter tr.tablesorter-headerRow .header.tablesorter-headerAsc .tablesorter-header-inner:after{content: "\f0d8"; color: #000;}
table.tablesorter tr.tablesorter-headerRow .header.tablesorter-headerDesc .tablesorter-header-inner{color: #000;font-weight:bold;}
table.tablesorter tr.tablesorter-headerRow .header.tablesorter-headerDesc .tablesorter-header-inner:after{content: "\f0d7"; color: #000;}
table.tablesorter tr.tablesorter-headerRow .header.sorter-false .tablesorter-header-inner:after{content:"";}
table.tablesorter.table-borderless > thead > tr > th {border-bottom: 1px solid #e5e5e5;}
</style>
<script>
function sortTable(selector, options)
{
    var $table = $(selector);
    $table.tablesorter($.extend(
    {
        saveSort: true,
        widgets: ['zebra', 'saveSort'],
        widgetZebra: {css: ['odd', 'even'] },
        ignoreChildRow: true,
        cssHeader: 'header'
    }, $table.data(), options)).on('mouseenter', 'tbody tr', function()
    {
        $(this).addClass('hoover');
    }).on('mouseleave', 'tbody tr', function()
    {
        $(this).removeClass('hoover');
    }).on('click', 'tbody tr', function()
    {
        $(this).toggleClass('clicked');
    });
}
$.fn.sortTable = function(options)
{
    return this.each(function()
    {
        sortTable(this, options);
    });
};
/* sort table after page load. */
$(function(){$('.tablesorter').sortTable();});
</script>
<div class='dashboard auto-fade-in fade' id='dashboard' data-confirm-remove-block='确定隐藏区块吗？'>
    <div class="row">
    <div class='col-main'>
                  <div class='panel block-statistic ' id='block4' data-id='4' data-name='项目统计' data-order='1' data-url='/biz812/block-printBlock-4-my.html'>
                        <div class='panel-heading'>
          <div class='panel-title'>项目统计</div>
                  <nav class='panel-actions nav nav-default'>
                        <li><a href='/biz812/project-browse.html' title='更多' >更多</a>
</li>            <li class='dropdown'>
              <a href='javascript:;' data-toggle='dropdown' class='panel-action'><i class='icon icon-ellipsis-v'></i></a>
              <ul class='dropdown-menu pull-right'>
                <li><a href='javascript:;' class='refresh-panel'><i class='icon-repeat'></i> 刷新</a></li>
                                <li><a data-toggle='modal' href="/biz812/block-admin-4-my.html" class='edit-block' data-title='项目统计' >编辑</a></li>
                <li><a href='javascript:deleteBlock(4);' class='hidden-panel'>隐藏</a></li>
                <li>                <a href='/biz812/block-close-4.html' class='close-block' onclick="return confirm('确定永久关闭该区块吗？关闭后所有人都将无法使用该区块，可以在后台自定义中打开。')" target='hiddenwin' >永久关闭</a>
                </li>
                                <li class="divider"></li>
                <li><a href='/biz812/block-admin-0-my.html' data-toggle='modal' data-type='ajax' data-width='700' data-title='添加区块' >添加区块</a>
</li>
                <li><a href='/biz812/block-ajaxReset-my.html'  target='hiddenwin' >恢复默认</a>
</li>
              </ul>
            </li>
          </nav>
                </div>
                <div class='panel-body scrollbar-hover'></div>
      </div>
                  <div class='panel block-recentproject ' id='block6' data-id='6' data-name='我近期参与的项目' data-order='2' data-url='/biz812/block-printBlock-6-my.html'>
                        <div class='panel-heading'>
          <div class='panel-title'>我近期参与的项目</div>
                  <nav class='panel-actions nav nav-default'>
                        <li><a href='/biz812/project-browse.html' title='更多' >更多</a>
</li>            <li class='dropdown'>
              <a href='javascript:;' data-toggle='dropdown' class='panel-action'><i class='icon icon-ellipsis-v'></i></a>
              <ul class='dropdown-menu pull-right'>
                <li><a href='javascript:;' class='refresh-panel'><i class='icon-repeat'></i> 刷新</a></li>
                                <li><a data-toggle='modal' href="/biz812/block-admin-6-my.html" class='edit-block' data-title='我近期参与的项目' >编辑</a></li>
                <li><a href='javascript:deleteBlock(6);' class='hidden-panel'>隐藏</a></li>
                <li>                <a href='/biz812/block-close-6.html' class='close-block' onclick="return confirm('确定永久关闭该区块吗？关闭后所有人都将无法使用该区块，可以在后台自定义中打开。')" target='hiddenwin' >永久关闭</a>
                </li>
                                <li class="divider"></li>
                <li><a href='/biz812/block-admin-0-my.html' data-toggle='modal' data-type='ajax' data-width='700' data-title='添加区块' >添加区块</a>
</li>
                <li><a href='/biz812/block-ajaxReset-my.html'  target='hiddenwin' >恢复默认</a>
</li>
              </ul>
            </li>
          </nav>
                </div>
                <div class='panel-body scrollbar-hover'></div>
      </div>
                  <div class='panel block-assigntome ' id='block7' data-id='7' data-name='待处理' data-order='3' data-url='/biz812/block-printBlock-7-my.html'>
                        <div class='panel-heading'>
          <div class='panel-title'>待处理</div>
                  <nav class='panel-actions nav nav-default'>
                                    <li class='dropdown'>
              <a href='javascript:;' data-toggle='dropdown' class='panel-action'><i class='icon icon-ellipsis-v'></i></a>
              <ul class='dropdown-menu pull-right'>
                <li><a href='javascript:;' class='refresh-panel'><i class='icon-repeat'></i> 刷新</a></li>
                                <li><a data-toggle='modal' href="/biz812/block-admin-7-my.html" class='edit-block' data-title='待处理' >编辑</a></li>
                <li><a href='javascript:deleteBlock(7);' class='hidden-panel'>隐藏</a></li>
                <li>                <a href='/biz812/block-close-7.html' class='close-block' onclick="return confirm('确定永久关闭该区块吗？关闭后所有人都将无法使用该区块，可以在后台自定义中打开。')" target='hiddenwin' >永久关闭</a>
                </li>
                                <li class="divider"></li>
                <li><a href='/biz812/block-admin-0-my.html' data-toggle='modal' data-type='ajax' data-width='700' data-title='添加区块' >添加区块</a>
</li>
                <li><a href='/biz812/block-ajaxReset-my.html'  target='hiddenwin' >恢复默认</a>
</li>
              </ul>
            </li>
          </nav>
                </div>
                <div class='panel-body scrollbar-hover'></div>
      </div>
                  <div class='panel block-projectteam ' id='block8' data-id='8' data-name='项目人力投入' data-order='4' data-url='/biz812/block-printBlock-8-my.html'>
                        <div class='panel-heading'>
          <div class='panel-title'>项目人力投入</div>
                  <nav class='panel-actions nav nav-default'>
                                    <li class='dropdown'>
              <a href='javascript:;' data-toggle='dropdown' class='panel-action'><i class='icon icon-ellipsis-v'></i></a>
              <ul class='dropdown-menu pull-right'>
                <li><a href='javascript:;' class='refresh-panel'><i class='icon-repeat'></i> 刷新</a></li>
                                <li><a data-toggle='modal' href="/biz812/block-admin-8-my.html" class='edit-block' data-title='项目人力投入' >编辑</a></li>
                <li><a href='javascript:deleteBlock(8);' class='hidden-panel'>隐藏</a></li>
                <li>                <a href='/biz812/block-close-8.html' class='close-block' onclick="return confirm('确定永久关闭该区块吗？关闭后所有人都将无法使用该区块，可以在后台自定义中打开。')" target='hiddenwin' >永久关闭</a>
                </li>
                                <li class="divider"></li>
                <li><a href='/biz812/block-admin-0-my.html' data-toggle='modal' data-type='ajax' data-width='700' data-title='添加区块' >添加区块</a>
</li>
                <li><a href='/biz812/block-ajaxReset-my.html'  target='hiddenwin' >恢复默认</a>
</li>
              </ul>
            </li>
          </nav>
                </div>
                <div class='panel-body scrollbar-hover'></div>
      </div>
                  <div class='panel block-project ' id='block9' data-id='9' data-name='项目列表' data-order='5' data-url='/biz812/block-printBlock-9-my.html'>
                        <div class='panel-heading'>
          <div class='panel-title'>项目列表</div>
                  <nav class='panel-actions nav nav-default'>
                        <li><a href='/biz812/project-browse.html' title='更多' >更多</a>
</li>            <li class='dropdown'>
              <a href='javascript:;' data-toggle='dropdown' class='panel-action'><i class='icon icon-ellipsis-v'></i></a>
              <ul class='dropdown-menu pull-right'>
                <li><a href='javascript:;' class='refresh-panel'><i class='icon-repeat'></i> 刷新</a></li>
                                <li><a data-toggle='modal' href="/biz812/block-admin-9-my.html" class='edit-block' data-title='项目列表' >编辑</a></li>
                <li><a href='javascript:deleteBlock(9);' class='hidden-panel'>隐藏</a></li>
                <li>                <a href='/biz812/block-close-9.html' class='close-block' onclick="return confirm('确定永久关闭该区块吗？关闭后所有人都将无法使用该区块，可以在后台自定义中打开。')" target='hiddenwin' >永久关闭</a>
                </li>
                                <li class="divider"></li>
                <li><a href='/biz812/block-admin-0-my.html' data-toggle='modal' data-type='ajax' data-width='700' data-title='添加区块' >添加区块</a>
</li>
                <li><a href='/biz812/block-ajaxReset-my.html'  target='hiddenwin' >恢复默认</a>
</li>
              </ul>
            </li>
          </nav>
                </div>
                <div class='panel-body scrollbar-hover'></div>
      </div>
          </div>
    <div class='col-side'>
                  <div class='panel block-sm block-dynamic ' id='block2' data-id='2' data-name='最新动态' data-order='6' data-url='/biz812/block-printBlock-2-my.html'>
                        <div class='panel-heading'>
          <div class='panel-title'>最新动态</div>
                  <nav class='panel-actions nav nav-default'>
            <li><a href='/biz812/company-dynamic.html' title='更多' >更多</a>
</li>            <li class='dropdown'>
              <a href='javascript:;' data-toggle='dropdown' class='panel-action'><i class='icon icon-ellipsis-v'></i></a>
              <ul class='dropdown-menu pull-right'>
                <li><a href='javascript:;' class='refresh-panel'><i class='icon-repeat'></i> 刷新</a></li>
                                <li><a data-toggle='modal' href="/biz812/block-admin-2-my.html" class='edit-block' data-title='最新动态' >编辑</a></li>
                <li><a href='javascript:deleteBlock(2);' class='hidden-panel'>隐藏</a></li>
                                <li><a href='/biz812/block-close-2.html' class='close-block' onclick="return confirm('确定永久关闭该区块吗？关闭后所有人都将无法使用该区块，可以在后台自定义中打开。')" target='hiddenwin' >永久关闭</a>
                                                <li class="divider"></li>
                <li><a href='/biz812/block-admin-0-my.html' data-toggle='modal' data-type='ajax' data-width='700' data-title='添加区块' >添加区块</a>
</li>
                <li><a href='/biz812/block-ajaxReset-my.html'  target='hiddenwin' >恢复默认</a>
</li>
              </ul>
            </li>
          </nav>
                </div>
                <div class='panel-body scrollbar-hover'></div>
      </div>
                  <div class='panel block-sm block-list ' id='block3' data-id='3' data-name='我的待办' data-order='7' data-url='/biz812/block-printBlock-3-my.html'>
                        <div class='panel-heading'>
          <div class='panel-title'>我的待办</div>
                  <nav class='panel-actions nav nav-default'>
            <li><a href='/biz812/my-todo-all.html' title='更多' >更多</a>
</li>            <li class='dropdown'>
              <a href='javascript:;' data-toggle='dropdown' class='panel-action'><i class='icon icon-ellipsis-v'></i></a>
              <ul class='dropdown-menu pull-right'>
                <li><a href='javascript:;' class='refresh-panel'><i class='icon-repeat'></i> 刷新</a></li>
                                <li><a data-toggle='modal' href="/biz812/block-admin-3-my.html" class='edit-block' data-title='我的待办' >编辑</a></li>
                <li><a href='javascript:deleteBlock(3);' class='hidden-panel'>隐藏</a></li>
                                <li><a href='/biz812/block-close-3.html' class='close-block' onclick="return confirm('确定永久关闭该区块吗？关闭后所有人都将无法使用该区块，可以在后台自定义中打开。')" target='hiddenwin' >永久关闭</a>
                                                <li class="divider"></li>
                <li><a href='/biz812/block-admin-0-my.html' data-toggle='modal' data-type='ajax' data-width='700' data-title='添加区块' >添加区块</a>
</li>
                <li><a href='/biz812/block-ajaxReset-my.html'  target='hiddenwin' >恢复默认</a>
</li>
              </ul>
            </li>
          </nav>
                </div>
                <div class='panel-body scrollbar-hover'></div>
      </div>
                  <div class='panel block-sm block-contribute ' id='block5' data-id='5' data-name='我的贡献' data-order='8' data-url='/biz812/block-printBlock-5-my.html'>
                        <div class='panel-heading'>
          <div class='panel-title'>我的贡献</div>
                  <nav class='panel-actions nav nav-default'>
                        <li class='dropdown'>
              <a href='javascript:;' data-toggle='dropdown' class='panel-action'><i class='icon icon-ellipsis-v'></i></a>
              <ul class='dropdown-menu pull-right'>
                <li><a href='javascript:;' class='refresh-panel'><i class='icon-repeat'></i> 刷新</a></li>
                                <li><a data-toggle='modal' href="/biz812/block-admin-5-my.html" class='edit-block' data-title='我的贡献' >编辑</a></li>
                <li><a href='javascript:deleteBlock(5);' class='hidden-panel'>隐藏</a></li>
                                <li><a href='/biz812/block-close-5.html' class='close-block' onclick="return confirm('确定永久关闭该区块吗？关闭后所有人都将无法使用该区块，可以在后台自定义中打开。')" target='hiddenwin' >永久关闭</a>
                                                <li class="divider"></li>
                <li><a href='/biz812/block-admin-0-my.html' data-toggle='modal' data-type='ajax' data-width='700' data-title='添加区块' >添加区块</a>
</li>
                <li><a href='/biz812/block-ajaxReset-my.html'  target='hiddenwin' >恢复默认</a>
</li>
              </ul>
            </li>
          </nav>
                </div>
                <div class='panel-body scrollbar-hover'></div>
      </div>
          </div>
  </div>
</div>
<script>
config.ordersSaved        = '排序已保存';
config.confirmRemoveBlock = '确定隐藏区块吗？';
config.cannotPlaceInLeft  = '此区块无法放置在左侧。';
config.cannotPlaceInRight = '此区块无法放置在右侧。';

var module   = 'my';
var useGuest = false;


$(function()
{
    function checkRemind()
    {
        $.getJSON(createLink('misc', 'getRemind'), function(response)
        {
            if(!response || !response.data || !response.data.content) return;

            var myModalTrigger = new $.zui.ModalTrigger(
            {
                title: response.data.title,
                custom: response.data.content,
                width: 600
            });
            $('#showAnnual').click(function(){myModalTrigger.close()});
        });
    }
    setTimeout(checkRemind, 1000);

    $('#dashboard .row .panel').each(function()
    {
        refreshBlock($(this));
    })
});
</script>
<script>/**
 * Delete block.
 *
 * @param  int    $index
 * @access public
 * @return void
 */
function deleteBlock(index)
{
    if(confirm(config.confirmRemoveBlock))
    {
        $.getJSON(createLink('block', 'delete', 'index=' + index + '&module=' + module), function(data)
        {
            if(data.result != 'success')
            {
                alert(data.message);
                return false;
            }
            else
            {
                window.location.reload(true);
            }
        });
    }
}

/**
 * Sort blocks.
 *
 * @param  array $orders  format is {'blockid' : 1, 'block1' : 2}
 * @param  function $callback
 * @access public
 * @return void
 */
function sortBlocks(newOrders, callback)
{
    $.getJSON(createLink('block', 'sort', 'orders=' + newOrders.join(',') + '&module=' + module), callback);
}

/**
 * Resize block
 * @param  string $blockId
 * @param  function $callback
 * @access public
 * @return void
 */
function resizeBlock(blockID, width, callback)
{
    $.getJSON(createLink('block', 'resize', 'id=' + blockID + '&type=horizontal&data=' + width), function(data)
    {
        callback && callback();
        refreshBlock($('#block' + blockID));
    });
}

/**
 * refreshBlock
 *
 * @param  object   $panel
 * @param  function afterRefresh
 * @param  number   forceRefresh
 * @access public
 * @return void
 */
function refreshBlock($panel, afterRefresh, forceRefresh = 0)
{
    var url = $panel.data('url');
    var headers = {};
    if(forceRefresh) headers['X-Zt-Refresh'] = forceRefresh;
    $panel.addClass('load-indicator loading');
    $.ajax({url: url, dataType: 'html', headers: headers}).done(function(data)
    {
        var $data = $(data);
        if($data.hasClass('panel')) $panel.empty().append($data.children());
        else if($panel.find('#assigntomeBlock').length) $panel.find('#assigntomeBlock').empty().append($data.children());
        else
        {
            $panel.children('.panel-move-handler,style,script').remove();
            $panel.find('.panel-body,.empty-tip').first().replaceWith($data);
            $panel.find('.iframe').initIframeModal();
        }
        $panel.find('.progress-pie').progressPie();
        if($.isFunction(afterRefresh))
        {
            afterRefresh.call(this,
            {
                result: true,
                data: data,
                $panel: $panel
            });
        }
        $panel.find('.tablesorter').sortTable();
        $panel.find('.chosen').chosen();
        $panel.children('.table-header-fixed').remove();
        initTableHeader($panel);
        $(".sparkline").sparkline();
    }).fail(function()
    {
        $panel.addClass('panel-error');
        if($.isFunction(afterRefresh))
        {
            afterRefresh.call(this,
            {
                result: false,
                $panel: $panel
            });
        }
    }).always(function()
    {
        $panel.removeClass('load-indicator loading');
    });
}

/**
 * Init table header
 * @access public
 * @return void
 */
function initTableHeader($wrapper)
{
    ($wrapper || $('#dashboard')).find('.panel-body > table.table-fixed-head').each(function()
    {
        var $table = $(this);
        var $tabPane = $table.closest('.tab-pane');
        if ($tabPane.length && !$tabPane.hasClass('active'))
        {
            $('[data-tab][href="#' + $tabPane.attr('id') + '"]').one('shown.zui.tab', function()
            {
                initTableHeader($tabPane);
            });
            return;
        }

        var $panel = $tabPane.length ? $tabPane : $table.closest('.panel');

        if(!$table.length || !$table.children('thead').length || ($panel.find('#assigntomeBlock').length && $panel.find('#assigntomeBlock > div').length > 1)) return;
        var isFixed = $panel.find('.panel-body').height() < $table.outerHeight();

        $panel.toggleClass('with-fixed-header', isFixed);
        var $header = $panel.children('.table-header-fixed').toggle(isFixed);
        if(!isFixed)
        {
            $table.find('thead').css('visibility', 'visible');
            return;
        }
        var tableWidth = $table.width();
        var $oldTableHead = $table.find('thead');
        var updateTh = function()
        {
            $header.find('thead').empty().append($oldTableHead.find('tr').clone());
        };
        if(!$header.length)
        {
            $header = $('<div class="table-header-fixed" style="position: absolute; left: 10px; top: 0; right: 0; padding: 0 10px 0 0; background: #fff;"><table class="table table-fixed no-margin"></table></div>').css('right', $panel.width() - tableWidth - 20);
            $oldTableHead.find('th').each(function(idx)
            {
                $(this).attr('data-idx', idx);
            });
            $header.find('.table').addClass($table.attr('class')).append($oldTableHead.css('visibility', 'hidden').clone().css('visibility', 'visible'));
            $panel.addClass('with-fixed-header').append($header);
            var $heading = $panel.children('.panel-heading');
            if($heading.length) $header.css('top', $heading.outerHeight());
            if($table.hasClass('tablesorter'))
            {
                $header.on('mousedown mouseup', 'th[data-idx]', function(e)
                {
                    var $th = $(this);
                    $oldTableHead.find('th[data-idx="' + $th.data('idx') + '"]').trigger(e);
                    if(e.type === 'mouseup')
                    {
                        setTimeout(updateTh, 10);
                        setTimeout(updateTh, 200);
                    }
                });
            }
        }
        else
        {
            updateTh();
        }

        var timeoutCall = null;
        $table.parent().off('scroll.initTableHeader').on('scroll.initTableHeader', function()
        {
            clearTimeout(timeoutCall);
            var $tableContainer = $(this);
            timeoutCall = setTimeout(function() {
                $panel.toggleClass('table-scrolled', $tableContainer.scrollTop() > 0);
            }, 200);
        });
    });
}

/**
 * Check refresh progress
 * @param  object $dashboard
 * @access public
 * @return void
 */
function checkRefreshProgress($dashboard, doneCallback)
{
    if($dashboard.find('.panel-loading').length) setTimeout(function() {checkRefreshProgress($dashboard, doneCallback);}, 500);
    else doneCallback();
}
/**
 * Hidden block.
 *
 * @param  index $index
 * @access public
 * @return void
 */
function hiddenBlock(index)
{
    $.getJSON(createLink('block', 'delete', 'index=' + index + '&module=' + module + '&type=hidden'), function(data)
    {
        if(data.result != 'success')
        {
            alert(data.message);
            return false;
        }

        $('#dashboard #block' + index).addClass('hidden');
    })
}

/**
 * Block initialization.
 *
 * @access public
 * @return void
 */
$(function()
{
    initTableHeader();
    $(window).on('resize', function()
    {
        initTableHeader();
    });

    // Init dashboard
    var sortMessager = new $.zui.Messager({close: false});
    $('#dashboard').sortable(
    {
        selector: '.panel',
        trigger: '.panel-heading:not(.not-move-handler),.panel-move-handler',
        containerSelector: '.col-main,.col-side',
        canMoveHere: function($ele, $target)
        {
            var fixedCol   = $ele.data('fixed');
            var $targetCol = $target.closest('.col-main,.col-side');
            var targetCol  = $targetCol.hasClass('col-main') ? 'main' : 'side';
            if($ele.hasClass('block-guide') && targetCol != 'main') return false;
            if(fixedCol)
            {
                if(targetCol !== fixedCol)
                {
                    !sortMessager.isShow && sortMessager.show(fixedCol === 'main' ? config.cannotPlaceInRight : config.cannotPlaceInRight);
                    return false;
                }
                else
                {
                    sortMessager.isShow && sortMessager.hide();
                }
                return true;
            }
        },
        start: function()
        {
            $('body').css('overflow', 'hidden');
        },
        finish: function(e)
        {
            $('body').css('overflow', 'auto');
            var newOrders = [];
            var isSideCol = e.element.parent().is('.col-side');
            e.list.each(function(index, data)
            {
                newOrders.push(data.item.data('id'));
            });
            sortBlocks(newOrders, function()
            {
                resizeBlock(e.element.data('id'), isSideCol ? 4 : 8);
            });

            e.element.toggleClass('block-sm', isSideCol);
        },
        always: function(){sortMessager.isShow && sortMessager.hide();}
    }).on('click', '.refresh-panel', function()
    {
        refreshBlock($(this).closest('.panel'), null, 1);
    });
});

/**
 * Reload roadmap.
 *
 * @param  int    productID
 * @param  int    roadMapID
 * @access public
 * @return void
 */
function reloadRoadmap(productID, roadMapID)
{
    $.ajax(
    {
        url: createLink('block', 'printScrumroadmapBlock', 'productID=' + productID + '&roadMapID=' + roadMapID),
        dataType: "html",
        async: false,
        data: {productID: productID, roadMapID: roadMapID},
        type: 'post',
        success: function(data)
        {
            $("#roadMap" + roadMapID).html(data);
            $("#" + roadMapID).chosen();
        }
    })
}

</script>
</div><script>
$.initSidebar();
</script>

<iframe frameborder='0' name='hiddenwin' id='hiddenwin' scrolling='no' class='debugwin hidden'></iframe>
</main><div id="noticeBox"></div>
<script>
/* Alert got messages. */
needPing = false;
$(function()
{
    var windowBlur = false;
    if(window.Notification && Notification.permission == 'granted')
    {
        window.onblur  = function(){windowBlur = true;}
        window.onfocus = function(){windowBlur = false;}
    }

    setInterval(function()
    {
        $.get(createLink('message', 'ajaxGetMessage', "windowBlur=" + (windowBlur ? '1' : '0')), function(data)
        {
            if(!windowBlur)
            {
                $('#noticeBox').append(data);
                adjustNoticePosition();
            }
            else
            {
                if(data)
                {
                    if(typeof data == 'string') data = $.parseJSON(data);
                    if(typeof data.message == 'string') notifyMessage(data);
                }
            }
        });
    }, 300000);
})

</script>

<script>startCron()
</script>
<script>$(function()
{
    if(typeof mode === 'string')
    {
        $('#subNavbar li[data-id=' + mode + ']').addClass('active');
        if(typeof rawMethod === 'string' && rawMethod == 'work')
        {
            $("#subNavbar li[data-id='task'] a").append('<span class="label label-light label-badge">' + taskCount + '</span>');
            $("#subNavbar li[data-id='story'] a").append('<span class="label label-light label-badge">' + storyCount + '</span>');
            $("#subNavbar li[data-id='bug'] a").append('<span class="label label-light label-badge">' + bugCount + '</span>');
            $("#subNavbar li[data-id='testcase'] a").append('<span class="label label-light label-badge">' + caseCount + '</span>');
            $("#subNavbar li[data-id='testtask'] a").append('<span class="label label-light label-badge">' + testTaskCount + '</span>');

            if(isOpenedURAndSR !== 0) $("#subNavbar li[data-id='requirement'] a").append('<span class="label label-light label-badge">' + requirementCount + '</span>');

            if(isBiz !== 0 || isMax !== 0) 
            {
                $("#subNavbar li[data-id='feedback'] a").append('<span class="label label-light label-badge">' + feedbackCount + '</span>');
                $("#subNavbar li[data-id='ticket'] a").append('<span class="label label-light label-badge">' + ticketCount + '</span>');
            }
            if(isMax !== 0)
            {
                $("#subNavbar li[data-id='issue'] a").append('<span class="label label-light label-badge">' + issueCount + '</span>');
                $("#subNavbar li[data-id='risk'] a").append('<span class="label label-light label-badge">' + riskCount + '</span>');
                $("#subNavbar li[data-id='nc'] a").append('<span class="label label-light label-badge">' + qaCount + '</span>');
                $("#subNavbar li[data-id='myMeeting'] a").append('<span class="label label-light label-badge">' + meetingCount + '</span>');
            }
        }
    }

    var $scp = $('[data-id="changePassword"] a');
    if($scp.length > 0)
    {
        var sign = config.requestType == 'GET' ? '&' : '?';
        $scp.attr('href', $scp.attr('href') + sign + 'onlybody=yes').modalTrigger({width:500, type:'iframe'});
    }
});
$(function()
{
    /* Set the heights of every block to keep them same height. */
    projectBoxHeight = $('#projectbox').height();
    productBoxHeight = $('#productbox').height();
    if(projectBoxHeight < 180) $('#projectbox').css('height', 180);
    if(productBoxHeight < 180) $('#productbox').css('height', 180);

    $('.panel-block').scroll(function()
    {
        var hasFixed  = $(this).find('.fixedHead').size() > 0;
        if(!hasFixed)
        {
            $(this).css('position', 'relative');
            var hasHeading = $(this).find('.panel-heading').size() > 0;
            var fixed = hasHeading ? $(this).find('.panel-heading').clone() : "<table class='fixedHead' style='position:absolute;top:0px;z-index:10'><thead>" + $(this).find('table thead').html() + '</thead></table>';
            $(this).prepend(fixed);
            if(hasHeading)
            {
                var firstHeading = $(this).find('.panel-heading:first');
                var lastHeading  = $(this).find('.panel-heading:last');
                firstHeading.addClass('fixedHead');
                firstHeading.css({'position':'absolute','top':'0px'});
                firstHeading.width(lastHeading.width());
                firstHeading.height(lastHeading.height());
            }
            else
            {
                var $fixTable = $(this).find('table.fixedHead');
                $fixTable.addClass($(this).find('table:last').attr('class'));
                var $dataTable = $(this).find('table:last thead th');
                $fixTable.find('thead th').each(function(i){$fixTable.find('thead th').eq(i).width($dataTable.eq(i).width());})
            }
        }
        $(this).find('.fixedHead').css('top',$(this).scrollTop());
    });
});

</script>
<script>
$('#poweredBy').append("<a href='\/biz812\/user-logout.html' class='btn btn-sm btn-danger' id='signOut' style='color:#fff;' >\u7b7e\u9000<\/a>\n")
if(new Date().getTime() < '1733220000000')
{
    $('#poweredBy #signOut').click(function()
    {
        if(!confirm("\u8fd8\u672a\u5230\u6700\u665a\u7b7e\u9000\u65f6\u95f4\uff0c\u662f\u5426\u7b7e\u9000\uff1f")) return false;
    })
}

$.extend(
{
    setAjaxForm: function(formID, callback, beforeSubmit)
    {
        if($(document).data('setAjaxForm:' + formID)) return;

        form = $(formID);

        var options =
        {
            target  : null,
            timeout : config.timeout,
            dataType:'json',

            success: function(response)
            {
                var submitButton = $(formID).find(':input[type=submit], .submit');

                /* The response is not an object, some error occers, bootbox.alert it. */
                if($.type(response) != 'object')
                {
                    $.enableForm(formID);
                    if(response) return bootbox.alert(response);
                    return bootbox.alert('No response.');
                }

                /* The response.result is success. */
                if(response.result == 'success')
                {
                    if(response.message && response.message.length)
                    {
                        submitButton.popover($.extend(
                        {
                            trigger: 'manual',
                            content: response.message,
                            tipClass: 'popover-success popover-form-result',
                            placement: response.placement ? response.placement : 'right'
                        }, submitButton.data())).popover('show');
                        setTimeout(function(){submitButton.popover('destroy')}, 2000);
                    }

                    if($.isFunction(callback)) return callback(response);

                    if($('#responser').length && response.message && response.message.length)
                    {
                        $('#responser').html(response.message).addClass('red f-12px').show().delay(3000).fadeOut(100);
                    }

                    if(response.closeModal)
                    {
                        setTimeout($.zui.closeModal, 1200);
                    }

                    if(response.callback)
                    {
                        var rcall = window[response.callback];
                        if($.isFunction(rcall))
                        {
                            if(rcall() === false)
                            {
                                return;
                            }
                        }
                    }

                    if(response.locate)
                    {
                        if(response.locate == 'loadInModal')
                        {
                            var modal = $('.modal');
                            setTimeout(function()
                            {
                                modal.load(modal.attr('ref'), function(){$(this).find('.modal-dialog').css('width', $(this).data('width'));
                                $.zui.ajustModalPosition()})
                            }, 1000);
                        }
                        else if(response.locate == 'parent')
                        {
                            setTimeout(function(){window.parent.location.reload();}, 1200);
                        }
                        else
                        {
                            var reloadUrl = response.locate == 'reload' ? location.href : response.locate;
                            setTimeout(function(){location.href = reloadUrl;}, 1200);
                        }
                    }

                    if(response.ajaxReload)
                    {
                        var $target = $(response.ajaxReload);
                        if($target.length === 1)
                        {
                            $target.load(document.location.href + ' ' + response.ajaxReload, function()
                            {
                                // $target.dataTable();
                                $target.find('[data-toggle="modal"]').modalTrigger();
                            });
                        }
                    }

                    return true;
                }

                /**
                 * The response.result is fail.
                 */

                $.enableForm(formID);
                /* The result.message is just a string. */
                if($.type(response.message) == 'string')
                {
                    if($('#responser').length == 0)
                    {
                        submitButton.popover($.extend(
                        {
                            trigger: 'manual',
                            content: response.message,
                            tipClass: 'popover-danger popover-form-result',
                            placement: response.placement ? response.placement : 'right'
                        }, submitButton.data())).popover('show');
                        setTimeout(function(){submitButton.popover('destroy')}, 2000);
                    }
                    $('#responser').html(response.message).addClass('red small text-danger').show().delay(5000).fadeOut(100);
                }

                /* The result.message is just a object. */
                if($.type(response.message) == 'object')
                {
                    $.each(response.message, function(key, value)
                    {
                        /* Define the id of the error objecjt and it's label. */
                        var errorOBJ   = '#' + key;
                        var errorLabel = key + 'Label';

                        /* Create the error message. */
                        var errorMSG = '<span id="' + errorLabel + '" for="' + key  + '"  class="text-error red">';
                        if($.type(value) == 'string')
                        {
                            errorMSG += value;
                        }
                        else
                        {
                            $.each(value, function(subKey, subValue)
                            {
                                errorMSG += subKey != value.length - 1 ? subValue.replace(/[\。|\.]/, ';') : subValue;
                            })
                        }
                        errorMSG += '</span>';

                        /* Append error message, set style and set the focus events. */
                        $('#' + errorLabel).remove();
                        var $errorOBJ = $(formID).find(errorOBJ);
                        if($errorOBJ.closest('.input-group').length > 0)
                        {
                            $errorOBJ.closest('.input-group').after(errorMSG)
                        }
                        else
                        {
                            $errorOBJ.parent().append(errorMSG);
                        }
                        $errorOBJ.css('margin-bottom', 0);
                        $errorOBJ.css('border-color','#953B39')
                        $errorOBJ.change(function()
                        {
                            $errorOBJ.css('margin-bottom', 0);
                            $errorOBJ.css('border-color','')
                            $('#' + errorLabel).remove();
                        });
                    })

                    /* Focus the first error field thus to nitify the user. */
                    var firstErrorField = $('#' +$('span.red').first().attr('for'));
                    var topOffset;
                    if(firstErrorField.length) topOffset = parseInt(firstErrorField.offset().top) - 20;   // 20px offset more for margin.

                    /* If there's the navbar-fixed-top element, minus it's height. */
                    if($('.navbar-fixed-top').size())
                    {
                        topOffset = topOffset - parseInt($('.navbar-fixed-top').height());
                    }

                    /* Scroll to the error field and foucus it. */
                    $(document).scrollTop(topOffset);
                    firstErrorField.focus();
                }

                if($.isFunction(callback)) return callback(response);
            },

            /* When error occers, alert the response text, status and error. */
            error: function(jqXHR, textStatus, errorThrown)
            {
                $.enableForm(formID);
                if(textStatus == 'timeout')
                {
                    bootbox.alert(v.lang.timeout);
                    return false;
                }
                bootbox.alert(jqXHR.responseText + textStatus + errorThrown);
            }
        };

        /* Call ajaxSubmit to sumit the form. */
        $(document).on('submit', formID, function()
        {
            $.disableForm(formID);
            if(!beforeSubmit || beforeSubmit() !== false)
            {
                $(this).ajaxSubmit(options);
            }
            else
            {
                $.enableForm(formID);
            }
            return false;    // Prevent the submitting event of the browser.
        }).data('setAjaxForm:' + formID, true);
    },

    /* Disable a form. */
    disableForm:function(formID)
    {
        $(formID).find(':submit').attr('disabled', true);
    },

    /**
     * Set ajax loader.
     *
     * Bind click event for some elements thus when click them,
     * use $.load to load page into target.
     *
     * @param string selector
     * @param string target
     */
    setAjaxLoader: function(selector, target)
    {
        /* Avoid duplication of binding */
        var data = $('body').data('ajaxLoader');
        if(data && data[selector]) return;
        if(!data) data = {};
        data[selector] = true;
        $('body').data('ajaxLoader', data);

        $(document).on('click', selector, function()
        {
            var url = $(this).attr('href');
            if(!url) url = $(this).data('rel');
            if(!url) return false;

            var $target = $(target);
            if(!$target.size()) return false;

            var width = $(this).data('width');
            $target.load(url, function()
            {
                if(width) $target.find('.modal-dialog').css('width', width);
                if($target.hasClass('modal') && $.zui.ajustModalPosition)
                {
                    $.zui.ajustModalPosition();
                    $target.find('.modal-dialog .modal-body').resize(function(){$.zui.ajustModalPosition();});
                }
            });

            return false;
        });
    },

    /**
     * Set ajax jsoner.
     *
     * @param string   selector
     * @param object   callback
     */
    setAjaxJSONER: function(selector, callback)
    {
        $(document).on('click', selector, function()
        {
            /* Try to get the href of current element, then try it's data-rel attribute. */
            url = $(this).attr('href');
            if(!url) url = $(this).data('rel');
            if(!url) return false;

            $.getJSON(url, function(response)
            {
                /* If set callback, call it. */
                if($.isFunction(callback)) return callback(response);

                /* If the response has message attribute, show it in #responser or alert it. */
                if(response.message)
                {
                    if($('#responser').length)
                    {
                        $('#responser').html(response.message);
                        $('#responser').addClass('text-info f-12px');
                        $('#responser').show().delay(3000).fadeOut(100);
                    }
                    else
                    {
                        bootbox.alert(response.message);
                    }
                }

                /* If the response has locate param, locate the browse. */
                if(response.locate) return location.href = response.locate;

                /* If target and source returned in reponse, update target with the source. */
                if(response.target && response.source)
                {
                    $(response.target).load(response.source);
                }
            });

            return false;
        });
    },

    /**
     * Set ajax deleter.
     *
     * @param  string $selector
     * @access public
     * @return void
     */
    setAjaxDeleter: function (selector, callback)
    {
        $(selector).each(function()
        {
            href = $(this).attr('href');
            $(this).attr('href', '###').attr('data-href', href);
        })

        $(document).on('click', selector, function()
        {
            if(confirm('您确定要执行删除操作吗？'))
            {
                var deleter = $(this);
                deleter.text('删除中');

                $.getJSON(deleter.attr('data-href'), function(data)
                {
                    callback && callback(data);
                    if(data.result == 'success')
                    {
                        if(deleter.parents('#ajaxModal').size()) return $.reloadAjaxModal(1200);
                        if(data.locate) return location.href = data.locate;
                        return location.reload();
                    }
                    else
                    {
                        alert(data.message);
                        return location.reload();
                    }
                });
            }
            return false;
        });
        return false;
    },

    /**
     * Set reload deleter.
     *
     * @param  string $selector
     * @access public
     * @return void
     */
    setReloadDeleter: function (selector)
    {
        href = $(selector).attr('href');
        $(selector).attr('href', '###').attr('data-href', href);

        $(document).on('click', selector, function()
        {
            if(confirm('您确定要执行删除操作吗？'))
            {
                var deleter = $(this);
                deleter.text('删除中');

                $.getJSON(deleter.attr('data-href'), function(data)
                {
                    var afterDelete = deleter.data('afterDelete');
                    if($.isFunction(afterDelete))
                    {
                        $.proxy(afterDelete, deleter)(data);
                    }

                    if(data.result == 'success')
                    {
                        var table     = $(deleter).closest('table');
                        var replaceID = table.attr('id');

                        table.wrap("<div id='tmpDiv'></div>");
                        var $tmpDiv = $('#tmpDiv');
                        $tmpDiv.load(document.location.href + ' #' + replaceID, function()
                        {
                            $tmpDiv.replaceWith($tmpDiv.html());
                            var $target = $('#' + replaceID);
                            $target.find('.reloadDeleter').data('afterDelete', afterDelete);
                            $target.find('[data-toggle="modal"]').modalTrigger();
                            if($target.hasClass('table-data'))
                            {
                                $target.dataTable();
                            }
                            if(typeof sortTable == 'function')
                            {
                                sortTable();
                            }
                            else
                            {
                                $('tfoot td').css('background', 'white').unbind('click').unbind('hover');
                            }
                        });
                    }
                    else
                    {
                        alert(data.message);
                    }
                });
            }
            return false;
        });
    },

    /**
     * Set reload.
     *
     * @param  string $selector
     * @access public
     * @return void
     */
    setReload: function (selector)
    {
        $(document).on('click', selector, function()
        {
            var reload = $(this);
            $.getJSON(reload.attr('href'), function(data)
            {
                if(data.result == 'success')
                {
                    var table     = $(reload).closest('table');
                    var replaceID = table.attr('id');

                    table.wrap("<div id='tmpDiv'></div>");
                    $('#tmpDiv').load(document.location.href + ' #' + replaceID, function()
                    {
                        $('#tmpDiv').replaceWith($('#tmpDiv').html());
                        if(typeof sortTable == 'function')
                        {
                            sortTable();
                        }
                        else
                        {
                            $('tfoot td').css('background', 'white').unbind('click').unbind('hover');
                        }
                    });
                }
                else
                {
                    alert(data.message);
                }
            });
            return false;
        });
    },

    /**
     * Reload ajax modal.
     *
     * @param int duration
     * @access public
     * @return void
     */
    reloadAjaxModal: function(duration)
    {
        if(typeof(duration) == 'undefined') duration = 1000;
        setTimeout(function()
        {
            var modal = $('#ajaxModal');
            modal.load(modal.attr('ref'), function(){$(this).find('.modal-dialog').css('width', $(this).data('width')); $.zui.ajustModalPosition()})
        }, duration);
    }
});

$(function()
{
    $.setAjaxDeleter('.deleter');
    $.setAjaxLoader('.loadInModal', '#triggerModal');
    $('[data-toggle="tooltip"]').tooltip();
    $.ajaxForm('#ajaxForm');
})

function setRequiredFields()
{
    if(!config.requiredFields) return false;
    requiredFields = config.requiredFields.split(',');
    for(i = 0; i < requiredFields.length; i++)
    {
        $('#' + requiredFields[i]).closest('td,th').prepend("<div class='required required-wrapper'></div>");
        var colEle = $('#' + requiredFields[i]).closest('[class*="col-"]');
        if(colEle.parent().hasClass('form-group')) colEle.addClass('required');
    }
}

/**
 * The most recent month is highlighted
 */
function addRecentlyMonthActive()
{
    var isActive = false;
    $('.side .panel-body .tree ul').each(function()
    {
        $(this).find('li').each(function()
        {
            if($(this).hasClass('active') && !isActive) isActive = true;
        })
	});

    if(!isActive) $('.side .panel-body .tree ul:last li:last').addClass('active')
}
</script>
<script>
$(function()
{
    $('#poweredBy a').eq(1).html('<i class="icon-zentao"></i> <span class="version">禅道企业版 8.12</span>');
})
</script>
</body>
</html>

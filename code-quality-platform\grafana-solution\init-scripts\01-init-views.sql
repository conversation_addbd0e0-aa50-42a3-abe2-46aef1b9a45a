-- 导入现有的视图和函数
-- 注意：这个脚本会在PostgreSQL容器启动时执行
-- 实际使用时，您应该直接连接到现有的数据库

-- 创建commit_metrics表（简化版，实际使用时应该直接连接到现有表）
CREATE TABLE IF NOT EXISTS commit_metrics (
    -- 提交标识
    commit_id VARCHAR(50) PRIMARY KEY,
    change_id VARCHAR(50) NOT NULL,
    change_id_short VARCHAR(10) NOT NULL,
    patch_set INT NOT NULL,
    
    -- Gerrit 基础信息
    gerrit_project VARCHAR(200) NOT NULL,
    branch VARCHAR(100) NOT NULL,
    subject TEXT NOT NULL,
    status VARCHAR(20) NOT NULL,
    commit_time TIMESTAMPTZ NOT NULL,
    created TIMESTAMPTZ NOT NULL,
    updated TIMESTAMPTZ NOT NULL,
    author VARCHAR(100) NOT NULL,
    owner VARCHAR(100) NOT NULL,
    number INT NOT NULL,
    url TEXT NOT NULL,
    reviewers TEXT[] NOT NULL,
    changed_lines INT NOT NULL,
    insertions INT NOT NULL,
    deletions INT NOT NULL,
    patchset_count INT NOT NULL,
    repo_path VARCHAR(500) NOT NULL,
    
    -- SonarQube 项目信息
    sonar_project VARCHAR(200),
    sonar_key VARCHAR(200),
    sonar_creation_date TIMESTAMPTZ,
    
    -- SonarQube 聚合指标 - 当前问题
    sq_blocker INT DEFAULT 0,
    sq_critical INT DEFAULT 0,
    sq_major INT DEFAULT 0,
    sq_minor INT DEFAULT 0,
    sq_info INT DEFAULT 0,
    
    -- SonarQube 聚合指标 - 已解决问题
    sq_resolved_blocker INT DEFAULT 0,
    sq_resolved_critical INT DEFAULT 0,
    sq_resolved_major INT DEFAULT 0,
    sq_resolved_minor INT DEFAULT 0,
    sq_resolved_info INT DEFAULT 0,
    
    -- SonarQube 代码质量与规模指标
    ncloc INTEGER,
    statements INTEGER,
    functions INTEGER,
    files INTEGER,
    comment_lines INTEGER,
    comment_lines_density FLOAT,
    complexity INTEGER,
    duplicated_lines_density FLOAT,
    duplicated_lines INTEGER,
    duplicated_blocks INTEGER,
    duplicated_files INTEGER,
    
    -- 数据分析增强字段
    commit_date DATE,
    commit_year INT,
    commit_month INT,
    commit_week INT,
    commit_hour INT,
    
    -- 统计计算字段
    total_issues INT,
    total_resolved_issues INT,
    critical_issues INT,
    issue_density FLOAT,
    
    -- 分类字段
    change_size_category VARCHAR(20),
    quality_level VARCHAR(20),
    
    -- 原始数据备份
    gerrit_raw JSONB,
    sonar_issues JSONB,
    
    -- 创建和更新时间
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_commit_metrics_commit_time ON commit_metrics(commit_time);
CREATE INDEX IF NOT EXISTS idx_commit_metrics_author ON commit_metrics(author);
CREATE INDEX IF NOT EXISTS idx_commit_metrics_gerrit_project ON commit_metrics(gerrit_project);
CREATE INDEX IF NOT EXISTS idx_commit_metrics_commit_date ON commit_metrics(commit_date);
CREATE INDEX IF NOT EXISTS idx_commit_metrics_quality_level ON commit_metrics(quality_level);

-- 创建视图
-- 1. 每日提交统计视图
CREATE OR REPLACE VIEW daily_commit_stats AS
SELECT 
    commit_date,
    COUNT(*) as total_commits,
    COUNT(DISTINCT author) as unique_authors,
    COUNT(DISTINCT gerrit_project) as unique_projects,
    SUM(changed_lines) as total_changed_lines,
    SUM(insertions) as total_insertions,
    SUM(deletions) as total_deletions,
    ROUND(AVG(changed_lines)::numeric, 2) as avg_changed_lines,
    SUM(total_issues) as total_issues,
    SUM(critical_issues) as total_critical_issues,
    ROUND(AVG(issue_density)::numeric, 2) as avg_issue_density
FROM commit_metrics
GROUP BY commit_date
ORDER BY commit_date DESC;

-- 2. 作者贡献统计视图
CREATE OR REPLACE VIEW author_contribution_stats AS
SELECT 
    author,
    COUNT(*) as total_commits,
    SUM(changed_lines) as total_changed_lines,
    ROUND(AVG(changed_lines)::numeric, 2) as avg_changed_lines,
    SUM(total_issues) as total_issues,
    SUM(critical_issues) as total_critical_issues,
    ROUND(AVG(issue_density)::numeric, 2) as avg_issue_density,
    COUNT(CASE WHEN quality_level = 'clean' THEN 1 END) as clean_commits,
    COUNT(CASE WHEN quality_level = 'critical' THEN 1 END) as critical_commits,
    ROUND((COUNT(CASE WHEN quality_level = 'clean' THEN 1 END)::float / NULLIF(COUNT(*), 0) * 100)::numeric, 2) as clean_rate_percent
FROM commit_metrics
GROUP BY author
ORDER BY total_commits DESC;

-- 3. 项目质量统计视图
CREATE OR REPLACE VIEW project_quality_stats AS
SELECT 
    gerrit_project,
    COUNT(*) as total_commits,
    COUNT(DISTINCT author) as unique_authors,
    SUM(changed_lines) as total_changed_lines,
    SUM(total_issues) as total_issues,
    SUM(critical_issues) as total_critical_issues,
    ROUND(AVG(issue_density)::numeric, 2) as avg_issue_density,
    ROUND(AVG(comment_lines_density)::numeric, 2) as avg_comment_density,
    ROUND(AVG(duplicated_lines_density)::numeric, 2) as avg_duplication_density,
    ROUND(AVG(complexity)::numeric, 2) as avg_complexity,
    COUNT(CASE WHEN quality_level = 'clean' THEN 1 END) as clean_commits,
    COUNT(CASE WHEN quality_level = 'critical' THEN 1 END) as critical_commits,
    ROUND((COUNT(CASE WHEN quality_level = 'clean' THEN 1 END)::float / NULLIF(COUNT(*), 0) * 100)::numeric, 2) as clean_rate_percent
FROM commit_metrics
GROUP BY gerrit_project
ORDER BY total_commits DESC;

-- 4. 月度趋势统计视图
CREATE OR REPLACE VIEW monthly_trend_stats AS
SELECT 
    commit_year,
    commit_month,
    TO_DATE(commit_year::text || '-' || LPAD(commit_month::text, 2, '0') || '-01', 'YYYY-MM-DD') as month_start,
    COUNT(*) as total_commits,
    COUNT(DISTINCT author) as unique_authors,
    COUNT(DISTINCT gerrit_project) as unique_projects,
    SUM(changed_lines) as total_changed_lines,
    SUM(total_issues) as total_issues,
    SUM(critical_issues) as total_critical_issues,
    ROUND(AVG(issue_density)::numeric, 2) as avg_issue_density,
    COUNT(CASE WHEN quality_level = 'clean' THEN 1 END) as clean_commits,
    ROUND((COUNT(CASE WHEN quality_level = 'clean' THEN 1 END)::float / NULLIF(COUNT(*), 0) * 100)::numeric, 2) as clean_rate_percent
FROM commit_metrics
GROUP BY commit_year, commit_month
ORDER BY commit_year DESC, commit_month DESC;

-- 5. 质量等级分布视图
CREATE OR REPLACE VIEW quality_level_distribution AS
SELECT 
    quality_level,
    COUNT(*) as commit_count,
    ROUND((COUNT(*)::float / NULLIF((SELECT COUNT(*) FROM commit_metrics), 0) * 100)::numeric, 2) as percentage,
    ROUND(AVG(changed_lines)::numeric, 2) as avg_changed_lines,
    ROUND(AVG(total_issues)::numeric, 2) as avg_issues
FROM commit_metrics
GROUP BY quality_level
ORDER BY 
    CASE quality_level 
        WHEN 'clean' THEN 1 
        WHEN 'minor' THEN 2 
        WHEN 'major' THEN 3 
        WHEN 'critical' THEN 4 
    END;

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
企业微信集成模块
用于发送质量报告通知到企业微信机器人
适用于Jenkins环境
"""

import os
import requests
import base64
import hashlib
import json
import configparser
import sys
import logging
import re

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('WeChat_Sender')

class WeChatWorkSender:
    """企业微信消息发送类"""
    
    def __init__(self, webhook_key=None, config_file=None):
        """
        初始化企业微信发送器
        
        参数:
            webhook_key (str): 企业微信机器人WebhookKey
            config_file (str): 配置文件路径
        """
        self.webhook_key = webhook_key
        
        # 如果没有直接提供webhook_key，则从配置文件加载
        if webhook_key is None and config_file is not None:
            self._load_config(config_file)
    
    def _load_config(self, config_file):
        """
        从配置文件加载企业微信配置
        
        参数:
            config_file (str): 配置文件路径
        """
        if not os.path.exists(config_file):
            logger.info(f"配置文件 {config_file} 不存在，创建默认配置")
            self._create_default_config(config_file)
        
        try:
            config = configparser.ConfigParser()
            config.read(config_file)
            
            if 'WeChat' in config:
                self.webhook_key = config['WeChat'].get('WebhookKey', '')
                logger.info(f"从配置文件加载WebhookKey: {self.webhook_key[:4]}..." if len(self.webhook_key) > 8 else "未配置WebhookKey")
        except Exception as e:
            logger.error(f"加载配置文件出错: {str(e)}")
    
    def _create_default_config(self, config_file):
        """
        创建默认的配置文件
        
        参数:
            config_file (str): 配置文件路径
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(os.path.abspath(config_file)), exist_ok=True)
            
            config = configparser.ConfigParser()
            config['WeChat'] = {
                'WebhookKey': 'YOUR_WEBHOOK_KEY_HERE'
            }
            
            with open(config_file, 'w') as f:
                config.write(f)
            
            logger.info(f"已创建默认配置文件 {config_file}")
        except Exception as e:
            logger.error(f"创建配置文件时出错: {str(e)}")
    
    def send_image(self, image_path):
        """
        发送图片到企业微信机器人
        
        参数:
            image_path (str): 图片文件路径
            
        返回:
            bool: 发送是否成功
        """
        # 检查webhook_key是否已配置
        if not self.webhook_key or self.webhook_key == 'YOUR_WEBHOOK_KEY_HERE':
            logger.warning("企业微信WebhookKey未配置，无法发送消息")
            return False
        
        # 企业微信API地址
        url = f"https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key={self.webhook_key}"
        
        # 读取图片文件
        try:
            # 确保路径是绝对路径
            if not os.path.isabs(image_path):
                image_path = os.path.abspath(image_path)
            
            logger.info(f"读取图片文件: {image_path}")
            with open(image_path, 'rb') as f:
                image_data = f.read()
        except IOError as e:
            logger.error(f"读取图片文件出错: {str(e)}")
            return False
        
        # 将图片编码为base64
        try:
            base64_image = base64.b64encode(image_data).decode('utf-8')
            
            # 计算MD5哈希值
            md5_hash = hashlib.md5(image_data).hexdigest()
            
            # 准备请求负载
            payload = {
                "msgtype": "image",
                "image": {
                    "base64": base64_image,
                    "md5": md5_hash
                }
            }
            
            logger.info(f"准备发送图片，图片大小: {len(image_data)/1024:.2f} KB")
            
            # 发送请求
            response = requests.post(url, json=payload)
            response_data = response.json()
            
            if response_data.get('errcode') == 0:
                logger.info("图片发送成功!")
                return True
            else:
                logger.error(f"发送到企业微信时出错: {response_data.get('errmsg')}")
                return False
                
        except Exception as e:
            logger.error(f"发送图片时发生异常: {str(e)}")
            return False
    
    def send_markdown(self, content, mentioned_list=None):
        """
        发送markdown格式消息到企业微信机器人
        
        参数:
            content (str): markdown格式的消息内容
            mentioned_list (list): 需要@的用户ID列表
            
        返回:
            bool: 发送是否成功
        """
        # 检查webhook_key是否已配置
        if not self.webhook_key or self.webhook_key == 'YOUR_WEBHOOK_KEY_HERE':
            logger.warning("企业微信WebhookKey未配置，无法发送消息")
            return False
        
        # 企业微信API地址
        url = f"https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key={self.webhook_key}"
        
        try:
            # 准备请求负载
            payload = {
                "msgtype": "markdown",
                "markdown": {
                    "content": content
                }
            }
            
            # 添加需要@的用户ID列表（如果提供了）
            if mentioned_list:
                payload["markdown"]["mentioned_list"] = mentioned_list
            
            logger.info("准备发送markdown消息")
            
            # 发送请求
            response = requests.post(url, json=payload)
            response_data = response.json()
            
            if response_data.get('errcode') == 0:
                logger.info("markdown消息发送成功!")
                return True
            else:
                logger.error(f"发送到企业微信时出错: {response_data.get('errmsg')}")
                return False
                
        except Exception as e:
            logger.error(f"发送markdown消息时发生异常: {str(e)}")
            return False

    def upload_image(self, image_path):
        """
        上传图片到企业微信临时素材
        
        参数:
            image_path (str): 图片路径
            
        返回:
            str: media_id 或 None
        """
        if not self.webhook_key:
            logger.warning("企业微信WebhookKey未配置，无法上传图片")
            return None
            
        try:
            url = f"https://qyapi.weixin.qq.com/cgi-bin/webhook/upload_media?key={self.webhook_key}&type=file"
            
            with open(image_path, 'rb') as f:
                files = {
                    'media': f
                }
                response = requests.post(url, files=files)
                result = response.json()
                
                if result.get('errcode') == 0:
                    logger.info("图片上传成功")
                    return result.get('media_id')
                else:
                    logger.error(f"图片上传失败: {result.get('errmsg')}")
                    return None
                    
        except Exception as e:
            logger.error(f"上传图片时发生错误: {str(e)}")
            return None

    def send_news(self, message):
        """
        发送图文消息
        
        参数:
            message (dict): 消息内容，包含图文信息
            
        返回:
            bool: 发送是否成功
        """
        # 检查webhook_key是否已配置
        if not self.webhook_key or self.webhook_key == 'YOUR_WEBHOOK_KEY_HERE':
            logger.warning("企业微信WebhookKey未配置，无法发送消息")
            return False
            
        # 企业微信webhook API地址
        url = f"https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key={self.webhook_key}"
        
        try:
            # 准备请求数据
            data = {
                "msgtype": "news",
                "news": {
                    "articles": [
                        {
                            "title": message["news"]["articles"][0]["title"],
                            "description": message["news"]["articles"][0]["description"],
                            "url": message["news"]["articles"][0]["url"],
                            "picurl": f"https://example.com/path/to/image.jpg"  # 图片URL需要是公网可访问的
                        }
                    ]
                }
            }
            
            logger.info("准备发送图文消息...")
            
            # 发送请求
            response = requests.post(url, json=data)
            result = response.json()
            
            # 检查响应
            if result.get('errcode') == 0:
                logger.info("图文消息发送成功")
                return True
            else:
                logger.error(f"图文消息发送失败: {result.get('errmsg')}")
                return False
                
        except Exception as e:
            logger.error(f"发送图文消息时发生错误: {str(e)}")
            return False

# 用于测试的示例代码
if __name__ == "__main__":
    logger.info("==== 企业微信发送测试 ====")
    # 获取配置文件路径
    config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "wechat_config.ini")
    logger.info(f"配置文件路径: {config_path}")
    
    # 创建发送器
    sender = WeChatWorkSender(config_file=config_path)
    
    # 测试发送markdown消息
    logger.info("测试发送markdown消息...")
    markdown_result = sender.send_markdown("### 质量日简报测试\n> 项目：wearable-wr02-daily-sonarqube\n> 日期：2023-05-24\n\n**指标**\n- Bugs: 10\n- 阻断问题: 5")
    logger.info(f"markdown消息发送结果: {'成功' if markdown_result else '失败'}")
    
    # 测试发送图片
    test_image = os.path.join(os.path.dirname(os.path.abspath(__file__)), "reports", "test_image.png")
    if os.path.exists(test_image):
        logger.info(f"测试发送图片: {test_image}...")
        image_result = sender.send_image(test_image)
        logger.info(f"图片发送结果: {'成功' if image_result else '失败'}")
    else:
        logger.warning(f"测试图片不存在: {test_image}")
        
    logger.info("==== 测试完成 ====") 
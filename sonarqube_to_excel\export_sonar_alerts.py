import requests
import pandas as pd
import argparse
from datetime import datetime
import base64

def get_sonar_projects(url, token):
    """获取SonarQube中的所有项目"""
    # 对token进行base64编码以用于Basic认证
    auth = base64.b64encode(f'{token}:'.encode()).decode()
    headers = {
        'Authorization': f'Basic {auth}'
    }
    projects_url = f'{url}/api/projects/search'
    response = requests.get(projects_url, headers=headers)
    if response.status_code == 200:
        return response.json().get('components', [])
    else:
        print(f'获取项目失败: {response.status_code}')
        return []

def get_project_issues(url, token, project_key, severities):
    """获取指定项目的特定严重级别的问题"""
    # 对token进行base64编码以用于Basic认证
    auth = base64.b64encode(f'{token}:'.encode()).decode()
    headers = {
        'Authorization': f'Basic {auth}'
    }
    issues_url = f'{url}/api/issues/search'
    issues = []
    page = 1
    max_results = 10000  # SonarQube API限制最多返回10000个结果
    max_pages = max_results // 500  # 计算最大页数
    
    while page <= max_pages:
        # 添加调试信息
        print(f'正在获取第 {page} 页数据，当前已获取 {len(issues)} 个问题')
        
        params = {
            'projectKeys': project_key,
            'severities': severities,
            'p': page,
            'ps': 500  # 每页获取500条
        }
        response = requests.get(issues_url, headers=headers, params=params)
        if response.status_code == 200:
            data = response.json()
            
            # 检查是否超过API限制
            total_available = data.get('paging', {}).get('total', 0)
            print(f'API返回的总问题数: {total_available}')
            if total_available > max_results:
                print(f'警告: 项目 {project_key} 的问题总数 ({total_available}) 超过了SonarQube API限制 ({max_results})')
                print(f'将只获取前 {max_results} 个结果')
            
            # 只添加属于当前项目的问题
            project_issues = [issue for issue in data.get('issues', []) if issue.get('project') == project_key]
            
            # 检查是否会超过API限制
            remaining_slots = max_results - len(issues)
            if len(project_issues) > remaining_slots:
                project_issues = project_issues[:remaining_slots]
                issues.extend(project_issues)
                print(f'已达到API限制，共获取 {len(issues)} 个问题')
                break
            
            issues.extend(project_issues)
            
            # 检查是否已获取所有问题或达到API限制
            if data.get('paging', {}).get('total') <= len(issues) or len(issues) >= max_results:
                break
                break
            page += 1
        else:
            try:
                # 尝试获取错误详情
                error_details = response.json()
                error_msg = f'获取项目 {project_key} 问题失败: {response.status_code}, 详情: {error_details}'
                print(f'请求参数: {params}')
            except ValueError:
                # 如果响应不是JSON格式
                error_msg = f'获取项目 {project_key} 问题失败: {response.status_code}, 响应内容: {response.text}'
                print(f'请求参数: {params}')
            if issues:
                print(f'警告: {error_msg}, 但已获取部分问题数据')
            else:
                print(f'错误: {error_msg}, 未获取任何问题数据')
            break
    return issues

def export_to_excel(issues, output_file, url):
    """将问题导出到Excel，并添加数据透视表"""
    if not issues:
        print('没有找到问题数据')
        return

    # 准备导出数据
    export_data = []
    for issue in issues:
        # 映射严重级别到中文
        severity_map = {
            'BLOCKER': '阻断问题',
            'CRITICAL': '严重问题',
            'MAJOR': '主要问题',
            'MINOR': '次要问题',
            'INFO': '信息问题'
        }

        # 获取规则详情
        rule = issue.get('rule', '')
        rule_name = issue.get('rule', '')
        if '|' in rule:
            rule_name = rule.split('|')[-1]

        # 处理问题路径，移除键名:部分
        component = issue.get('component', '')
        if ':' in component:
            # 只保留冒号后面的部分
            component = component.split(':', 1)[1].strip()

        export_data.append({
            '项目键': issue.get('project', ''),
            '告警名称': issue.get('message', ''),
            '违反规则': rule_name,
            '规则键': rule,
            '严重级别': severity_map.get(issue.get('severity', ''), issue.get('severity', '')),
            '问题路径': component,
            '行号': issue.get('line', ''),
            '创建时间': issue.get('creationDate', ''),
            '更新时间': issue.get('updateDate', ''),
            '问题状态': issue.get('status', ''),
            '问题类型': issue.get('type', ''),
            # 构建问题URL
            '问题URL': f'{url}/project/issues?id={issue.get("project")}&issues={issue.get("key")}&open={issue.get("key")}'
        })

    # 创建DataFrame
    df = pd.DataFrame(export_data)

    # 创建ExcelWriter对象
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 将原始数据写入sheet1
        df.to_excel(writer, sheet_name='问题明细', index=False)
        print(f'问题明细已写入sheet1')

        # 筛选状态为OPEN的问题
        open_issues = df[df['问题状态'] == 'OPEN']
        print(f'找到 {len(open_issues)} 个状态为OPEN的问题')

        # 如果有OPEN状态的问题，创建数据透视表
        if not open_issues.empty:
            # 创建数据透视表，按问题路径统计问题数量
            pivot_table = pd.pivot_table(
                open_issues,
                index=['问题路径'],
                values=['告警名称'],
                aggfunc='count',
                margins=True,
                margins_name='总计'
            )
            pivot_table.rename(columns={'告警名称': '问题数量'}, inplace=True)

            # 将数据透视表写入sheet2
            pivot_table.to_excel(writer, sheet_name='OPEN问题路径统计')
            print(f'OPEN问题路径统计数据透视表已写入sheet2')

            # 获取sheet2并设置格式
            sheet = writer.sheets['OPEN问题路径统计']
            
            # 导入所需样式类
            from openpyxl.styles import Alignment, Font, Border, Side
            
            # 在C列第一行添加'责任人'并设置加粗
            sheet['C1'] = '责任人'
            sheet['C1'].font = Font(bold=True)
            
            # 设置A列左对齐
            left_alignment = Alignment(horizontal='left')
            
            # 定义网格线样式
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            
            # 获取最大行号
            max_row = sheet.max_row
            
            # 遍历A列所有单元格，设置左对齐和网格线
            for row in sheet.iter_rows(min_row=1, max_row=max_row, min_col=1, max_col=1):
                for cell in row:
                    cell.alignment = left_alignment
                    cell.border = thin_border
                    # 首行保持加粗，其他行取消加粗
                    if cell.row != 1:
                        cell.font = Font(bold=False)
            
            # 遍历B列和C列所有单元格，设置网格线
            for row in sheet.iter_rows(min_row=1, max_row=max_row, min_col=2, max_col=3):
                for cell in row:
                    cell.border = thin_border
        else:
            print('没有找到状态为OPEN的问题，无法创建数据透视表')

    print(f'数据已成功导出到 {output_file}')

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='导出SonarQube项目告警数据到Excel')
    parser.add_argument('--url', default='http://**********:9002', help='SonarQube URL')
    parser.add_argument('--output', default=f'sonar_alerts_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx', help='输出Excel文件名')
    parser.add_argument('project_key', help='要导出的项目键')
    parser.add_argument('token', help='SonarQube 令牌')
    args = parser.parse_args()

    # 设置要导出的严重级别
    severities = 'BLOCKER,CRITICAL,MAJOR'

    # 获取项目
    projects = get_sonar_projects(args.url, args.token)
    if not projects:
        print('没有找到任何项目')
        return

    # 过滤指定项目
    filtered_projects = [p for p in projects if p.get('key') == args.project_key]
    if not filtered_projects:
        print(f'未找到项目键为 {args.project_key} 的项目')
        return
    projects = filtered_projects
    print(f'将导出项目: {projects[0].get("name")} ({args.project_key})')

    # 获取所有问题
    all_issues = []
    for project in projects:
        project_key = project.get('key')
        project_name = project.get('name')
        print(f'正在获取项目 {project_name} ({project_key}) 的问题...')
        issues = get_project_issues(args.url, args.token, project_key, severities)
        print(f'项目 {project_name} ({project_key}) 找到 {len(issues)} 个问题')
        all_issues.extend(issues)
    print(f'总共找到 {len(all_issues)} 个问题')

    # 验证所有问题都属于指定项目
    for issue in all_issues:
        if issue.get('project') != args.project_key:
            print(f'警告: 发现不属于指定项目的问题: {issue.get("project")}')

    # 导出到Excel
    export_to_excel(all_issues, args.output, args.url)

if __name__ == '__main__':
    main()
# SonarQube到Grafana集成方案

## 方案概述

本方案通过webhook将SonarQube的质量门数据实时传输到Grafana进行可视化展示。

### 架构流程
```
SonarQube → Webhook → 中间件服务 → VictoriaMetrics → Grafana
```

## 部署步骤

### 1. 启动服务栈

```bash
# 进入项目目录
cd d:/vscodedir

# 启动监控服务栈
docker-compose -f docker-compose/sonar-webhook-service.yml up -d
```

### 2. 验证服务状态

```bash
# 检查服务状态
docker-compose -f docker-compose/sonar-webhook-service.yml ps

# 查看webhook服务日志
docker logs sonar-webhook-service

# 检查VictoriaMetrics
curl http://localhost:8428/api/v1/label/__name__/values

# 检查Grafana
# 访问 http://localhost:3000 (admin/admin123)
```

### 3. 配置SonarQube Webhooks

#### 方法1: 使用脚本自动配置

```bash
# 安装依赖
pip install requests

# 运行webhook配置脚本
python scripts/setup_sonar_webhooks.py
```

#### 方法2: 手动配置

1. 登录SonarQube管理界面
2. 进入 **Administration** → **Configuration** → **Webhooks**
3. 点击 **Create**
4. 填写配置：
   - **Name**: Grafana Webhook
   - **URL**: `http://*********:8080/webhook/sonarqube`
   - **Secret**: (可选)

### 4. 配置Grafana

1. 访问 http://localhost:3000
2. 使用 admin/admin123 登录
3. 数据源会自动配置为VictoriaMetrics
4. 仪表板会自动导入

## 监控指标

### 主要指标

- `sonarqube_quality_gate_status`: 质量门状态 (0=失败, 1=通过)
- `sonarqube_analysis_completed`: 分析完成事件
- `sonarqube_condition_value`: 质量门条件的具体数值

### 标签维度

- `project_key`: 项目键
- `project_name`: 项目名称
- `metric_key`: 指标键 (如 coverage, bugs, vulnerabilities)
- `status`: 状态 (SUCCESS, FAILED)

## 测试验证

### 1. 测试webhook接收

```bash
# 发送测试webhook
curl -X POST http://localhost:8080/webhook/sonarqube \
  -H "Content-Type: application/json" \
  -d '{
    "taskId": "test-task-123",
    "status": "SUCCESS",
    "analysedAt": "2025-01-04T10:00:00+0000",
    "project": {
      "key": "test-project",
      "name": "Test Project"
    },
    "qualityGate": {
      "status": "OK",
      "conditions": [
        {
          "metricKey": "coverage",
          "operator": "LT",
          "status": "OK",
          "actualValue": "85.5"
        }
      ]
    }
  }'
```

### 2. 验证数据存储

```bash
# 查询VictoriaMetrics中的数据
curl "http://localhost:8428/api/v1/query?query=sonarqube_quality_gate_status"
```

### 3. 触发真实分析

在您的项目中运行SonarQube分析，观察Grafana仪表板的更新。

## 故障排除

### 常见问题

1. **Webhook接收失败**
   - 检查网络连通性
   - 验证webhook URL配置
   - 查看webhook服务日志

2. **数据未显示在Grafana**
   - 检查VictoriaMetrics连接
   - 验证时间范围设置
   - 查看Grafana数据源配置

3. **指标格式错误**
   - 检查webhook数据格式
   - 查看转换逻辑日志

### 日志查看

```bash
# Webhook服务日志
docker logs sonar-webhook-service

# VictoriaMetrics日志
docker logs victoriametrics

# Grafana日志
docker logs grafana
```

## 扩展配置

### 添加更多SonarQube实例

修改 `scripts/setup_sonar_webhooks.py` 中的 `sonar_configs` 配置，添加新的SonarQube实例。

### 自定义仪表板

1. 在Grafana中创建新的仪表板
2. 使用以下查询示例：

```promql
# 项目质量门通过率
avg_over_time(sonarqube_quality_gate_status[1h])

# 分析频率
rate(sonarqube_analysis_completed[5m])

# 代码覆盖率趋势
sonarqube_condition_value{metric_key="coverage"}
```

### 告警配置

在Grafana中配置告警规则，当质量门失败时发送通知。

## 安全考虑

1. 使用HTTPS传输webhook数据
2. 配置webhook密钥验证
3. 限制网络访问权限
4. 定期更新服务镜像

## 性能优化

1. 调整VictoriaMetrics保留期
2. 配置Grafana查询缓存
3. 优化webhook处理逻辑
4. 监控服务资源使用

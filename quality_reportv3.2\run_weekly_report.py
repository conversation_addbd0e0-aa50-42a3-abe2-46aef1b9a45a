#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
生成代码质量周报
包含Excel数据表格和可视化图表
"""

import os
import sqlite3
from datetime import datetime, timedelta
import pandas as pd
import logging
import configparser
from report_visualizer import generate_excel_report

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('Weekly_Report')

def load_project_config():
    """
    加载项目配置
    从config.ini的[SonarQube]部分读取ProjectID
    返回一个字典，其中ProjectID作为键和显示名称
    """
    script_dir = os.path.dirname(os.path.abspath(__file__))
    config_path = os.path.join(script_dir, "config.ini")
    
    config = configparser.ConfigParser()
    # 使用 utf-8 编码读取配置文件
    with open(config_path, 'r', encoding='utf-8') as f:
        config.read_file(f)
    
    if 'SonarQube' not in config:
        raise ValueError("配置文件中缺少 [SonarQube] 部分")
    
    if 'ProjectID' not in config['SonarQube']:
        raise ValueError("配置文件中缺少 ProjectID 配置项")
    
    project_id = config['SonarQube']['ProjectID'].strip()
    # 使用项目ID作为显示名称
    return {project_id: project_id}

def get_weekly_data(project_id):
    """获取过去一周的工作日数据（排除周末和全零数据）"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    db_path = os.path.join(script_dir, "quality_data.db")
    
    # 连接数据库
    conn = sqlite3.connect(db_path)
    
    # 获取今天的日期
    today = datetime.now().date()
    # 获取20天前的日期（确保能获取足够的工作日数据）
    days_ago = today - timedelta(days=14)
    
    # 构建查询 - 获取较长时间范围的数据
    query = """
    SELECT date, bugs, vulnerabilities, code_smells,
           blocker_issues, major_issues, info_issues,
           duplications_percentage, duplicated_lines,
           duplicated_blocks, duplicated_files,
           code_lines, comment_lines,
           comment_percentage, complexity
    FROM metrics
    WHERE date >= ? AND date <= ?
    AND project_id = ?
    ORDER BY date DESC
    """
    
    # 使用pandas读取数据
    df = pd.read_sql_query(
        query, conn,
        params=(days_ago.strftime('%Y-%m-%d'),
                today.strftime('%Y-%m-%d'),
                project_id)
    )
    
    # 重命名列
    df.columns = [
        '日期', 'Bugs数量', '漏洞数量', '代码异味',
        '阻断问题', '主要问题', '提示问题', '代码重复率', '重复行数',
        '重复块数', '重复文件数', '代码行数', '注释行数',
        '注释率', '圈复杂度'
    ]
    
    # 转换日期字符串为日期对象
    df['日期'] = pd.to_datetime(df['日期'])
    
    # 筛选工作日（周一到周五）
    df['工作日'] = df['日期'].dt.dayofweek < 5  # 0-4 对应周一到周五
    df = df[df['工作日'] == True].drop('工作日', axis=1)
    
    # 过滤掉所有指标都为0的行
    numeric_columns = df.select_dtypes(include=['int64', 'float64']).columns
    df = df[~(df[numeric_columns] == 0).all(axis=1)]
    
    # 只保留最近的7个工作日
    df = df.sort_values('日期', ascending=False).head(7).sort_values('日期')
    
    # 日期重新转回字符串格式
    df['日期'] = df['日期'].dt.strftime('%Y-%m-%d')
    
    conn.close()
    return df

def generate_weekly_report(project_id):
    """生成周报Excel文件和可视化图表（只包含工作日）"""
    if not project_id:
        logger.error("未指定项目ID")
        return None
        
    # 获取数据
    df = get_weekly_data(project_id)
    
    if df.empty:
        logger.error(f"没有找到项目 {project_id} 过去工作日的数据！")
        return None
    
    # 如果数据少于2行，无法计算变化
    if len(df) < 2:
        logger.error(f"项目 {project_id} 的工作日数据不足，至少需要两天的数据！")
        return None
        
    # 计算一些统计信息
    stats = {
        '指标': [
            'Bugs数量',
            '漏洞数量',
            '代码异味',
            '阻断问题',
            '主要问题',
            '提示问题',
            '代码重复率',
            '重复行数',
            '重复块数',
            '重复文件数',
            '注释率'
        ]
    }
    
    # 获取第一行和最后一行数据
    first_row = df.iloc[0]
    last_row = df.iloc[-1]
    
    # 为每个指标获取初始值和最终值
    stats['周初值'] = [
        first_row['Bugs数量'],
        first_row['漏洞数量'],
        first_row['代码异味'],
        first_row['阻断问题'],
        first_row['主要问题'],
        first_row['提示问题'],
        first_row['代码重复率'],
        first_row['重复行数'],
        first_row['重复块数'],
        first_row['重复文件数'],
        first_row['注释率']
    ]
    
    stats['周末值'] = [
        last_row['Bugs数量'],
        last_row['漏洞数量'],
        last_row['代码异味'],
        last_row['阻断问题'],
        last_row['主要问题'],
        last_row['提示问题'],
        last_row['代码重复率'],
        last_row['重复行数'],
        last_row['重复块数'],
        last_row['重复文件数'],
        last_row['注释率']
    ]
    
    # 计算变化量和变化率
    stats['变化量'] = []
    stats['变化率'] = []
    
    for start, end in zip(stats['周初值'], stats['周末值']):
        # 处理变化量
        if start is None or end is None:
            stats['变化量'].append(0)
            stats['变化率'].append('N/A')
        else:
            try:
                change = end - start
                stats['变化量'].append(change)
                # 处理变化率
                if start == 0 and end == 0:
                    stats['变化率'].append('0.0%')
                elif start != 0:
                    stats['变化率'].append(f"{(change / start * 100):.1f}%")
                else:
                    stats['变化率'].append('N/A')
            except (TypeError, ValueError):
                stats['变化量'].append(0)
                stats['变化率'].append('N/A')
    
    # 创建统计DataFrame
    stats_df = pd.DataFrame(stats)
    
    # 生成报告路径
    script_dir = os.path.dirname(os.path.abspath(__file__))
    date_str = datetime.now().strftime("%Y%m%d")
    
    # 使用新的目录命名格式：项目-week
    report_dir = os.path.join(script_dir, "reports", f"{project_id}-week")
    os.makedirs(report_dir, exist_ok=True)
    
    # 修改Excel报告文件名，包含projectid和工作日标记
    excel_path = os.path.join(
        report_dir,
        f"weekly_report_{project_id}_workday_{date_str}.xlsx"
    )
    
    # 创建Excel写入器
    with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
        # 添加工作日说明 - 保持"每日数据"作为工作表名以兼容visualizer
        days_covered = len(df)
        
        # 写入每日数据 - 使用"每日数据"作为工作表名
        df.to_excel(writer, sheet_name='每日数据', index=False)
        
        # 写入统计数据
        stats_df.to_excel(writer, sheet_name='周统计', index=False)
        
        # 生成可视化图表和分析文案
        generate_excel_report(df, writer, project_id)  # 传递project_id参数给generate_excel_report
        
        # 设置列宽
        for sheet_name in writer.sheets:
            worksheet = writer.sheets[sheet_name]
            for idx, col in enumerate(worksheet.columns, 1):
                worksheet.column_dimensions[chr(64 + idx)].width = 15
        
        # 添加说明：这是工作日数据
        daily_sheet = writer.sheets['每日数据']
        daily_sheet.cell(row=1, column=len(df.columns) + 2).value = f"注：此报告仅包含最近{days_covered}个工作日数据（不含周末）"

    logger.info(f"工作日周报已生成: {excel_path}")
    
    return {
        'excel_report': excel_path
    }

def generate_all_projects_reports():
    """生成项目周报"""
    try:
        projects = load_project_config()
        project_id = list(projects.keys())[0]  # 获取唯一的项目ID
        
        logger.info(f"正在生成项目 {project_id} 的周报...")
        report_paths = generate_weekly_report(project_id)
        
        if report_paths:
            logger.info(f"项目 {project_id} 的周报已生成")
        else:
            logger.warning(f"项目 {project_id} 的周报生成失败")
            
    except Exception as e:
        logger.error(f"生成周报时发生错误: {str(e)}")
        raise

def ensure_metrics_table(db_path):
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    try:
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='metrics'")
        if not cursor.fetchone():
            cursor.execute('''
            CREATE TABLE metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date TEXT,
                project_id TEXT,
                bugs INTEGER,
                vulnerabilities INTEGER,
                code_smells INTEGER,
                blocker_issues INTEGER,
                major_issues INTEGER,
                info_issues INTEGER DEFAULT 0,
                duplications_percentage REAL,
                comment_percentage REAL,
                code_lines INTEGER,
                comment_lines INTEGER,
                duplicated_lines INTEGER,
                duplicated_blocks INTEGER,
                duplicated_files INTEGER,
                complexity INTEGER,
                total_lines INTEGER
            )
            ''')
            print("metrics表已创建")
        conn.commit()
    finally:
        conn.close()

# 在主逻辑前调用
script_dir = os.path.dirname(os.path.abspath(__file__))
db_path = os.path.join(script_dir, "quality_data.db")
ensure_metrics_table(db_path)

if __name__ == "__main__":
    try:
        generate_all_projects_reports()
    except Exception as e:
        logger.error(f"生成周报时发生错误: {str(e)}")
        import traceback
        logger.error(traceback.format_exc()) 
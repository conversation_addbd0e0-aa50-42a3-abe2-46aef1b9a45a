# Gerrit + SonarQube 数据图表分析指南

## 概述

本项目将Gerrit代码审查数据和SonarQube代码质量数据整合到一个宽表中，并提供丰富的分析视图和查询示例，支持各种数据图表生成。

## 数据表结构

### 主表：commit_metrics

包含以下主要字段组：

#### 1. 基础标识字段
- `commit_id`: 提交唯一标识
- `change_id`: Gerrit变更ID
- `change_id_short`: 变更ID短码
- `patch_set`: 补丁集编号

#### 2. Gerrit信息字段
- `gerrit_project`: 项目名
- `branch`: 分支名
- `subject`: 提交主题
- `status`: 变更状态
- `author`: 作者
- `owner`: 所有者
- `reviewers`: 评审人列表
- `changed_lines`: 变更行数
- `insertions`: 插入行数
- `deletions`: 删除行数

#### 3. SonarQube质量指标
- `sq_blocker/critical/major/minor/info`: 各级别问题数
- `sq_resolved_*`: 已解决问题数
- `ncloc`: 非注释代码行数
- `complexity`: 圈复杂度
- `duplicated_lines_density`: 重复密度
- `comment_lines_density`: 注释密度

#### 4. 计算字段（自动生成）
- `total_issues`: 总问题数
- `critical_issues`: 严重问题数
- `issue_density`: 问题密度
- `change_size_category`: 变更大小分类
- `quality_level`: 质量等级
- 时间维度字段：`commit_date`, `commit_year`, `commit_month`, `commit_week`, `commit_hour`

## 分析视图

### 1. daily_commit_stats
每日提交统计，包含提交数量、作者数、项目数、代码变更量、问题数等。

### 2. author_contribution_stats
作者贡献统计，包含提交数量、代码变更量、质量问题、清洁代码率等。

### 3. project_quality_stats
项目质量统计，包含项目活跃度、质量问题、代码质量指标等。

### 4. change_size_distribution
变更大小分布，按small/medium/large/huge分类统计。

### 5. quality_level_distribution
质量等级分布，按clean/minor/major/critical分类统计。

### 6. monthly_trend_stats
月度趋势统计，支持长期趋势分析。

### 7. reviewer_stats
评审人统计，包含评审数量、评审质量等。

### 8. branch_stats
分支统计，各分支的活跃度和质量情况。

## 图表类型与查询示例

### 1. 时间趋势图

#### 折线图：每日提交趋势
```sql
SELECT 
    commit_date,
    total_commits,
    total_issues,
    total_critical_issues
FROM daily_commit_stats
WHERE commit_date >= CURRENT_DATE - INTERVAL '30 days'
ORDER BY commit_date;
```

#### 柱状图：月度趋势
```sql
SELECT 
    month_start,
    total_commits,
    unique_authors,
    clean_rate_percent
FROM monthly_trend_stats
WHERE commit_year >= EXTRACT(YEAR FROM CURRENT_DATE) - 1
ORDER BY month_start;
```

### 2. 分布图

#### 饼图：问题严重程度分布
```sql
SELECT 
    'Blocker' as severity,
    SUM(sq_blocker) as count
FROM commit_metrics
WHERE commit_time >= CURRENT_DATE - INTERVAL '30 days'
UNION ALL
SELECT 'Critical', SUM(sq_critical)
UNION ALL
SELECT 'Major', SUM(sq_major)
UNION ALL
SELECT 'Minor', SUM(sq_minor)
UNION ALL
SELECT 'Info', SUM(sq_info);
```

#### 饼图：代码质量等级分布
```sql
SELECT 
    quality_level,
    commit_count,
    percentage
FROM quality_level_distribution;
```

### 3. 对比图

#### 柱状图：开发者贡献排名
```sql
SELECT 
    author,
    total_commits,
    total_changed_lines,
    clean_rate_percent
FROM author_contribution_stats
WHERE total_commits >= 5
ORDER BY total_commits DESC
LIMIT 20;
```

#### 雷达图：项目质量对比
```sql
SELECT 
    gerrit_project,
    total_commits,
    avg_issue_density,
    avg_comment_density,
    avg_duplication_density,
    clean_rate_percent
FROM project_quality_stats
WHERE total_commits >= 10
ORDER BY clean_rate_percent DESC
LIMIT 10;
```

### 4. 散点图

#### 变更大小与质量问题关系
```sql
SELECT 
    changed_lines,
    total_issues,
    issue_density,
    quality_level,
    change_size_category
FROM commit_metrics
WHERE commit_time >= CURRENT_DATE - INTERVAL '30 days'
ORDER BY changed_lines;
```

### 5. 热力图

#### 提交时间分布
```sql
SELECT 
    commit_hour,
    COUNT(*) as commit_count,
    AVG(changed_lines) as avg_changed_lines
FROM commit_metrics
WHERE commit_time >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY commit_hour
ORDER BY commit_hour;
```

## 实时监控查询

### 今日统计
```sql
SELECT 
    COUNT(*) as today_commits,
    COUNT(DISTINCT author) as today_authors,
    SUM(changed_lines) as today_changed_lines,
    SUM(total_issues) as today_issues,
    SUM(critical_issues) as today_critical_issues
FROM commit_metrics
WHERE commit_date = CURRENT_DATE;
```

### 高风险提交告警
```sql
SELECT 
    commit_id,
    author,
    gerrit_project,
    changed_lines,
    total_issues,
    critical_issues,
    quality_level,
    commit_time
FROM commit_metrics
WHERE (sq_blocker > 0 OR sq_critical > 0)
  AND commit_time >= CURRENT_DATE - INTERVAL '7 days'
ORDER BY critical_issues DESC, commit_time DESC;
```

## 使用建议

### 1. 性能优化
- 使用提供的索引字段进行查询
- 对于大数据量查询，建议添加时间范围限制
- 使用视图进行预聚合查询

### 2. 数据更新
- 数据通过 `find_and_collect.py` 脚本定期更新
- 支持增量更新，避免重复数据
- 建议每日或每周运行数据收集

### 3. 图表工具集成
- 支持与Grafana、Tableau、PowerBI等工具集成
- 可直接使用提供的SQL查询作为数据源
- 建议设置自动刷新以保持数据实时性

### 4. 告警设置
- 基于 `critical_issues` 设置质量告警
- 基于 `issue_density` 设置密度告警
- 基于提交频率设置活跃度告警

## 扩展分析

### 1. 自定义指标
可以通过修改视图或创建新视图来添加自定义指标：
- 代码质量评分
- 团队效率指标
- 项目健康度评分

### 2. 预测分析
基于历史数据可以进行：
- 问题趋势预测
- 代码质量预测
- 项目风险预测

### 3. 关联分析
可以结合其他数据源进行：
- 缺陷关联分析
- 性能影响分析
- 成本效益分析

## 注意事项

1. **数据完整性**：确保Gerrit和SonarQube数据同步
2. **性能考虑**：大数据量时注意查询优化
3. **权限控制**：根据用户角色限制数据访问
4. **数据备份**：定期备份重要分析数据
5. **版本管理**：记录数据表结构变更历史 
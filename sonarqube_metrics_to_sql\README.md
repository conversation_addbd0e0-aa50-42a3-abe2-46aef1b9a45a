# SonarQube 指标同步脚本使用说明

## 功能介绍

`sync_sonarqube_metrics.py` 脚本用于从 SonarQube 平台获取项目代码质量指标，并将数据同步到 PostgreSQL 数据库中。该脚本支持增量更新，确保每天只保留最新的一条记录。
目前在sonarqube社区版服务器:10.0.0.2服务器上，路径:/home/<USER>/sync_sonaree
仓库地址：git clone "http://10.0.0.3:8081/ci/cidata_sync_sql"
## 使用方法

### 基本用法

```bash
python sync_sonarqube_metrics.py [sonar平台标识] [sonarqube项目ID] [表名]
```

### 参数说明

- `[sonar平台标识]`: 配置文件中定义的 SonarQube 平台标识符（对应 config.ini 中的节名称）
- `[sonarqube项目ID]`: SonarQube 中的项目唯一标识符（通常在项目设置中可以找到）
- `[表名]`: 必填参数，指定要同步数据的数据库表名

### 示例

```bash
python sync_sonarqube_metrics.py sonar_prod my-project-key sonaree_wr02
```

## 配置文件要求

脚本需要读取 `config.ini` 文件来获取数据库和 SonarQube 的配置信息。配置文件格式如下：

```ini
[Database]
user = 数据库用户名
password = 数据库密码
host = 数据库主机地址
port = 数据库端口
database = 数据库名称

[sonar平台标识]
url = SonarQube 平台URL
token = SonarQube 访问令牌
```

### 配置说明

- `[Database]` 部分：数据库连接信息
- `[sonar平台标识]` 部分：SonarQube 平台配置，`sonar平台标识` 应与命令行参数中的第一个参数一致
- `token`：SonarQube 的访问令牌，需要有项目的读取权限

## 数据同步流程

1. 脚本连接到 PostgreSQL 数据库
2. 根据提供的项目 ID 从 SonarQube 获取项目信息和指标数据
3. 检查数据库中是否已存在当天的指标数据
4. 如果存在，则更新最新的一条记录；如果不存在，则插入新记录
5. 同步完成后输出结果信息

## 同步的指标

脚本同步以下 SonarQube 指标：

- `bugs`: Bug 数量
- `vulnerabilities`: 安全漏洞数量
- `code_smells`: 代码异味数量
- `coverage`: 代码覆盖率 (%)
- `duplicated_lines_density`: 代码重复率 (%)
- `ncloc`: 非注释代码行数
- `sq_blocker`: 阻断级别问题数量
- `sq_critical`: 严重级别问题数量
- `sq_major`: 主要级别问题数量
- `sq_minor`: 次要级别问题数量
- `sq_info`: 信息级别问题数量
- `comment_lines_density`: 注释行密度 (%)
- `complexity`: 代码复杂度
- `files`: 文件数量
- `functions`: 函数/方法数量
- `statements`: 语句数量
- `duplicated_lines`: 重复行数
- `duplicated_blocks`: 重复块数量
- `duplicated_files`: 重复文件数量
- `raw_json`: 原始指标数据的 JSON 备份

## 注意事项

1. 确保 Python 环境已安装必要的依赖包：`pg8000`, `requests`
2. 确保 PostgreSQL 数据库中已创建对应的表结构
3. 确保 SonarQube 访问令牌具有足够的权限
4. 表名是必填参数，不会自动生成，请确保指定的表名在数据库中已存在
5. 数据同步以本地日期为准，每天只保留一条最新记录

## 依赖安装

```bash
pip install pg8000 requests
```
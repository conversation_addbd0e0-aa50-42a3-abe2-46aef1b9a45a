#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
代码质量报告可视化模块
在Excel中生成图表
"""

import pandas as pd
from datetime import datetime
import os
import logging
import re
from openpyxl.chart import LineChart, BarChart, Reference
from openpyxl.chart.axis import DateAxis
from openpyxl.chart.label import DataLabelList
from openpyxl.drawing.colors import ColorChoice
from openpyxl.drawing.line import LineProperties
import openpyxl.styles

# 配置日志
logger = logging.getLogger(__name__)

class QualityVisualizer:
    def __init__(self, df, writer, project_id=None, report_type='weekly'):
        """
        初始化可视化器
        
        参数:
            df: pandas DataFrame，包含质量数据
            writer: ExcelWriter对象
            project_id: 项目ID，用于提取项目名
            report_type: 报告类型，'weekly'为周报，'monthly'为月报
        """
        self.df = df
        self.writer = writer
        self.workbook = writer.book
        self.project_id = project_id
        self.project_name = self.extract_project_name(project_id) if project_id else ""
        self.product_line = self.get_product_line(project_id) if project_id else ""
        self.report_type = report_type
        
    def extract_project_name(self, project_id):
        """
        从projectid中提取项目名
        例如：从cycle-sonarqube-wr02中只提取cycle
        """
        if not project_id:
            return ""
            
        # 将project_id按'-'分割，只取第一个部分
        parts = project_id.split('-')
        if parts:
            return parts[0].upper()
        return ""
        
    def get_product_line(self, project_id):
        """根据project_id获取产品线名称"""
        if not project_id:
            return ""
        
        if project_id.lower().startswith('cycle'):
            return "骑行线"
        elif project_id.lower().startswith('wearable'):
            return "穿戴线"
        return ""
        
    def create_trend_chart(self, metric_name, worksheet):
        """为指定指标创建趋势图"""
        if self.report_type == 'monthly':
            # 月报使用折线图
            chart = LineChart()
            chart.style = 2  # 使用内置的样式
            
            # 在图表标题中添加产品线
            title_prefix = f"{self.product_line}" if self.product_line else ""
            chart.title = f"{title_prefix} {metric_name}趋势图"
                
            chart.y_axis.title = "数量"
            chart.x_axis.title = "日期"
            
            # 设置网格线
            chart.y_axis.majorGridlines = None  # 清除Y轴网格线
            chart.x_axis.majorGridlines = None  # 清除X轴网格线
            
            # 设置图表样式
            chart.y_axis.number_format = '#,##0'  # 数字格式化
            chart.y_axis.tickLblPos = "low"  # Y轴标签位置
            
            # 获取数据范围
            max_row = len(self.df) + 1  # 加1是因为有表头
            
            # 获取指标所在的列号
            col_idx = list(self.df.columns).index(metric_name) + 1  # Excel列号从1开始
            
            # 添加数据
            data = Reference(worksheet, 
                            min_col=col_idx,
                            max_col=col_idx,
                            min_row=1,
                            max_row=max_row)
            
            chart.add_data(data, titles_from_data=True)
            
            # 添加日期作为分类轴
            dates = Reference(worksheet, 
                             min_col=1,
                             min_row=2,
                             max_row=max_row)
            chart.set_categories(dates)
            
            # 设置折线图样式
            line_series = chart.series[0]
            line_series.smooth = False  # 使用直线，不使用平滑曲线 为True时为平滑曲线
            line_series.marker.symbol = "circle"  # 数据点样式
            line_series.marker.size = 5  # 数据点大小
            
            # 设置折线样式
            line_series.graphicalProperties.line = LineProperties(w=30000, solidFill=ColorChoice(srgbClr='FF6B4A'))  # 设置线条粗细和颜色为橘红色，1B4F72深蓝色
            
            # 添加数据标签
            line_series.dLbls = DataLabelList()
            line_series.dLbls.showVal = True
            line_series.dLbls.position = "t"  # 标签位置在上方
            
            # 设置图表大小和位置
            chart.height = 12  # 图表高度
            chart.width = 25   # 图表宽度
            
            # 设置图例位置
            chart.legend.position = 'b'  # 图例位置在底部
            
            # 在指定位置插入图表
            worksheet.add_chart(chart, "A10")
            
            return chart
        else:
            # 周报使用柱状图
            chart = BarChart()
            chart.type = "col"  # 设置为垂直柱状图
            chart.style = 2  # 使用内置的样式
            
            # 在图表标题中添加产品线
            title_prefix = f"{self.product_line}" if self.product_line else ""
            chart.title = f"{title_prefix} {metric_name}趋势图"
                
            chart.y_axis.title = "数量"
            chart.x_axis.title = "日期"
            
            # 设置网格线 - 只保留Y轴网格线
            chart.y_axis.majorGridlines = None  # 先清除默认网格线
            chart.x_axis.majorGridlines = None  # 不显示X轴网格线
            
            # 设置图表样式
            chart.y_axis.number_format = '#,##0'  # 数字格式化
            chart.y_axis.tickLblPos = "low"  # Y轴标签位置
            
            # 获取数据范围
            max_row = len(self.df) + 1  # 加1是因为有表头
            
            # 获取指标所在的列号
            col_idx = list(self.df.columns).index(metric_name) + 1  # Excel列号从1开始
            
            # 添加数据
            data = Reference(worksheet, 
                            min_col=col_idx,
                            max_col=col_idx,
                            min_row=1,
                            max_row=max_row)
            
            chart.add_data(data, titles_from_data=True)
            
            # 添加日期作为分类轴
            dates = Reference(worksheet, 
                             min_col=1,
                             min_row=2,
                             max_row=max_row)
            chart.set_categories(dates)
            
            # 设置柱状图样式
            bar_series = chart.series[0]
            bar_series.graphicalProperties.line.noFill = True  # 移除边框
            # 设置柱状图颜色为橘红色
            bar_series.graphicalProperties.solidFill = ColorChoice(srgbClr='FF6B4A')  # 橘红色
            
            # 添加柱状图数据标签
            bar_series.dLbls = DataLabelList()
            bar_series.dLbls.showVal = True
            bar_series.dLbls.position = "t"  # 标签位置在上方
            
            # 创建折线图并添加到柱状图
            line = LineChart()
            line.add_data(data, titles_from_data=True)
            line.set_categories(dates)
            
            # 设置折线图样式
            line_series = line.series[0]
            line_series.smooth = False  # 直线，不使用平滑曲线
            line_series.marker.symbol = "circle"  # 数据点样式
            line_series.marker.size = 7  # 增大数据点大小
            
            # 设置折线样式
            line_series.graphicalProperties.line = LineProperties(w=40000, solidFill=ColorChoice(srgbClr='1B4F72'))  # 设置线条粗细和颜色
            
            # 合并图表
            chart += line
            
            # 设置图表大小和位置
            chart.height = 12  # 适当减小高度
            chart.width = 25   # 适当减小宽度
            
            # 设置图例位置
            chart.legend.position = 'b'  # 图例位置在底部
            
            # 在指定位置插入图表
            worksheet.add_chart(chart, "A10")
            
            return chart

    def create_all_metric_charts(self, worksheet):
        """为所有指标创建单独的趋势图"""
        metrics = [
            ('Bugs数量', 2),
            ('漏洞数量', 3),
            ('代码异味', 4),
            ('阻断问题', 5),
            ('主要问题', 6),
            ('提示问题', 7),
            ('代码重复率', 8),
            ('重复行数', 9),
            ('重复块数', 10),
            ('重复文件数', 11),
            ('代码行数', 12),
            ('注释行数', 13),
            ('注释率', 14),
            ('圈复杂度', 15)
        ]
        
        for metric_name, col_index in metrics:
            sheet_name = f"{metric_name}趋势"
            self.create_trend_chart(metric_name, worksheet)
    
    def generate_analysis_text(self):
        """生成分析文案"""
        start_data = self.df.iloc[0]
        end_data = self.df.iloc[-1]
        
        # 计算变化
        bug_change = end_data['Bugs数量'] - start_data['Bugs数量']
        vuln_change = end_data['漏洞数量'] - start_data['漏洞数量']
        blocker_change = end_data['阻断问题'] - start_data['阻断问题']
        info_change = end_data['提示问题'] - start_data['提示问题']
        dup_change = end_data['代码重复率'] - start_data['代码重复率']
        
        # 生成文案
        title_prefix = f"{self.product_line} " if self.product_line else ""
        text = [
            [f'{title_prefix}本周代码质量分析报告'],
            [],
            ['关键指标变化'],
            ['1. Bugs情况'],
            ['当前数量', f'{end_data["Bugs数量"]}个'],
            ['周环比', f'{"增加" if bug_change > 0 else "减少"}{abs(bug_change)}个'],
            ['分析', self._get_trend_analysis('Bugs', bug_change)],
            [],
            ['2. 安全漏洞'],
            ['当前数量', f'{end_data["漏洞数量"]}个'],
            ['周环比', f'{"增加" if vuln_change > 0 else "减少"}{abs(vuln_change)}个'],
            ['分析', self._get_trend_analysis('漏洞', vuln_change)],
            [],
            ['3. 阻断问题'],
            ['当前数量', f'{end_data["阻断问题"]}个'],
            ['周环比', f'{"增加" if blocker_change > 0 else "减少"}{abs(blocker_change)}个'],
            ['分析', self._get_trend_analysis('阻断', blocker_change)],
            [],
            ['4. 提示问题'],
            ['当前数量', f'{end_data["提示问题"]}个'],
            ['周环比', f'{"增加" if info_change > 0 else "减少"}{abs(info_change)}个'],
            ['分析', self._get_trend_analysis('提示', info_change)],
            [],
            ['5. 代码重复情况'],
            ['当前重复率', f'{end_data["代码重复率"]}%'],
            ['重复行数', f'{end_data["重复行数"]}行'],
            ['重复文件数', f'{end_data["重复文件数"]}个'],
            ['周环比', f'{"上升" if dup_change > 0 else "下降"}{abs(dup_change):.1f}%'],
            ['分析', self._get_trend_analysis('重复', dup_change, end_data)],
            [],
            ['代码质量建议'],
            *[[suggestion] for suggestion in self._generate_suggestions(end_data).split('\n')],
            [],
            ['下周改进重点'],
            *[[focus] for focus in self._generate_improvement_focus(end_data).split('\n')]
        ]
        
        return text
    
    def _get_trend_analysis(self, metric_type, change, latest_data=None):
        """生成趋势分析文案"""
        if metric_type == "重复":
            if change == 0:
                return f"代码重复率保持在{latest_data['代码重复率']}%，涉及{latest_data['重复行数']}行代码，分布在{latest_data['重复文件数']}个文件中。"
            elif change < 0:
                return f"代码重复率下降{abs(change)}%，当前重复行数为{latest_data['重复行数']}行，说明代码复用得到了改善。"
            else:
                return f"代码重复率上升{change}%，当前重复行数为{latest_data['重复行数']}行，建议关注新增重复代码。"
        elif change == 0:
            return f"{metric_type}数量保持稳定，说明相关问题得到了良好控制。"
        elif change < 0:
            return f"{metric_type}数量呈下降趋势，显示团队在质量管控方面取得了积极成效。"
        else:
            return f"{metric_type}数量有所上升，建议关注新增问题的原因并及时处理。"
    
    def _generate_suggestions(self, latest_data):
        """生成改进建议"""
        suggestions = []
        
        if latest_data['Bugs数量'] > 50:
            suggestions.append("- 建议加强代码审查力度，关注高频错误模式")
        if latest_data['漏洞数量'] > 0:
            suggestions.append("- 安全漏洞需要优先处理，建议进行安全专项复查")
        if latest_data['代码重复率'] > 5:
            suggestions.append(f"- 代码重复率偏高（{latest_data['代码重复率']}%，涉及{latest_data['重复行数']}行），建议进行代码重构，提取公共组件")
        if latest_data['注释率'] < 20:
            suggestions.append("- 代码注释率偏低，建议补充必要的注释说明")
        
        return "\n".join(suggestions) if suggestions else "- 各项指标表现良好，建议保持现有的质量管理措施"
    
    def _generate_improvement_focus(self, latest_data):
        """生成下周改进重点"""
        focus = []
        
        # 根据当前数据确定改进重点
        metrics = {
            '阻断问题': latest_data['阻断问题'],
            'Bugs': latest_data['Bugs数量'],
            '漏洞': latest_data['漏洞数量'],
            '代码异味': latest_data['代码异味']
        }
        
        # 按数量排序，取top 2作为重点
        sorted_metrics = sorted(metrics.items(), key=lambda x: x[1], reverse=True)
        for metric, value in sorted_metrics[:2]:
            if value > 0:
                focus.append(f"- 重点解决{metric}问题，目标是在下周实现{value//2}个的削减")
        
        return "\n".join(focus) if focus else "- 建议持续保持代码质量，巩固已有成果"

def generate_excel_report(df, writer, project_id=None, report_type='weekly'):
    """在Excel中生成所有报告内容"""
    visualizer = QualityVisualizer(df, writer, project_id, report_type)
    
    # 获取"每日数据"工作表
    daily_data_sheet = writer.sheets['每日数据']
    
    # 需要生成趋势图的指标列表
    metrics = [
        'Bugs数量', '漏洞数量', '代码异味', '阻断问题', 
        '主要问题', '提示问题', '代码重复率', '重复行数', '重复块数',
        '重复文件数', '代码行数', '注释行数', '注释率', 
        '圈复杂度'
    ]
    
    try:
        # 为每个指标创建单独的趋势图
        for metric in metrics:
            # 创建新的工作表
            sheet_name = f"{metric}趋势"
            df.to_excel(writer, sheet_name=sheet_name, index=False)
            metric_sheet = writer.sheets[sheet_name]
            
            # 在新工作表中创建趋势图
            visualizer.create_trend_chart(metric, metric_sheet)
            
            # 设置列宽
            for idx, col in enumerate(metric_sheet.columns, 1):
                metric_sheet.column_dimensions[chr(64 + idx)].width = 15
                
        # 设置统计表格格式
        stats_sheet_name = '周统计' if report_type == 'weekly' else '月统计'
        if stats_sheet_name in writer.sheets:
            stats_sheet = writer.sheets[stats_sheet_name]
            
            # 设置所有单元格居中对齐和网格线
            for row in stats_sheet.iter_rows():
                for cell in row:
                    cell.alignment = openpyxl.styles.Alignment(horizontal='center', vertical='center')
                    cell.border = openpyxl.styles.Border(
                        left=openpyxl.styles.Side(style='thin'),
                        right=openpyxl.styles.Side(style='thin'),
                        top=openpyxl.styles.Side(style='thin'),
                        bottom=openpyxl.styles.Side(style='thin')
                    )
                
    except Exception as e:
        logger.error(f"生成图表时发生错误: {str(e)}")
        raise 
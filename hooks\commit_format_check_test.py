#!/usr/bin/env python3
# Commit格式校验脚本自测用例

import unittest
import re
from commit_format_check import (
    check_title_format,
    check_body_format,
    check_zentao_reference,
    check_change_id,
    check_author_email,
    is_whitelist_user
)


class TestCommitFormatCheck(unittest.TestCase):
    # 标题格式校验测试
    def test_title_format(self):
        # 正常情况
        self.assertTrue(check_title_format("模块名: 简洁描述"))
        self.assertTrue(check_title_format("Fix: 修复了一个bug"))
        self.assertTrue(check_title_format("文档: 更新了项目说明"))
        
        # 包含中文的标题
        self.assertTrue(check_title_format("功能: 添加了中文显示支持"))
        
        # 边界情况
        self.assertTrue(check_title_format("a" * 40))  # 纯英文刚好40字符
        self.assertTrue(check_title_format("测试" * 20))  # 纯中文刚好20个(显示宽度40)
        
        # 错误情况
        self.assertFalse(check_title_format(""))  # 空标题
        self.assertFalse(check_title_format("模块名:无空格"))  # 冒号后无空格
        self.assertFalse(check_title_format("a" * 41))  # 纯英文超过40字符
        self.assertFalse(check_title_format("测试" * 21))  # 纯中文超过20个(显示宽度42)
        self.assertFalse(check_title_format("模块名：有中文冒号"))  # 中文冒号(会警告但返回True)
        
        # 混合中英文标题
        self.assertTrue(check_title_format("模块: 这是一个mixed title"))  # 显示宽度计算
        self.assertFalse(check_title_format("模块: 这是一个非常长的mixed title，超过了显示宽度限制"))

    # 正文格式校验测试
    def test_body_format(self):
        # 正常情况
        self.assertTrue(check_body_format(""))  # 空正文
        self.assertTrue(check_body_format("这是正文内容"))
        self.assertTrue(check_body_format("1. 第一项改动\n2. 第二项改动"))
        
        # 错误情况
        self.assertFalse(check_body_format("12345"))  # 只有纯数字
        self.assertFalse(check_body_format("67890"))

    # 禅道关联单号校验测试
    def test_zentao_reference(self):
        # 正常情况
        self.assertTrue(check_zentao_reference(""))  # 无禅道号
        self.assertTrue(check_zentao_reference("bug-view-12345"))  # 正确格式
        self.assertTrue(check_zentao_reference("bug-view-123456"))  # 正确格式(6位)
        self.assertTrue(check_zentao_reference("这是正文\nbug-view-12345\nChange-Id: ..."))  # 在中间
        
        # 错误情况
        self.assertFalse(check_zentao_reference("bug-view-1234"))  # 少于5位
        self.assertFalse(check_zentao_reference("bug-view-1234567"))  # 多于6位
        self.assertFalse(check_zentao_reference("bug-view-abcde"))  # 非数字
        self.assertFalse(check_zentao_reference("bugview-12345"))  # 缺少连字符
        self.assertFalse(check_zentao_reference("bug-view-12345a"))  # 数字后有字符

    # ChangeID校验测试
    def test_change_id(self):
        # 正常情况
        self.assertTrue(check_change_id("Change-Id: Ia39aac81dfacacd6c3fdec8922c5edf0b72e6413"))
        self.assertTrue(check_change_id("其他内容\nChange-Id: Ia39aac81dfacacd6c3fdec8922c5edf0b72e6413\n更多内容"))
        
        # 错误情况
        self.assertFalse(check_change_id(""))  # 无Change-Id
        self.assertFalse(check_change_id("Change-Id: "))  # 空Change-Id
        self.assertFalse(check_change_id("Change-Id: 1234567890abcdef1234567890abcdef12345678"))  # 缺少I开头
        self.assertFalse(check_change_id("Change-Id: Ia39aac81dfacacd6c3fdec8922c5edf0b72e641"))  # 少于40位
        self.assertFalse(check_change_id("Change-Id: Ia39aac81dfacacd6c3fdec8922c5edf0b72e64134"))  # 多于40位
        self.assertFalse(check_change_id("Change-Id: IA39AAC81DFACACD6C3FDEC8922C5EDF0B72E6413"))  # 大写字母

    # 作者邮箱校验测试
    def test_author_email(self):
        # 正常情况
        self.assertTrue(check_author_email("Signed-off-by: Test User <<EMAIL>>"))
        self.assertTrue(check_author_email("其他内容\nSigned-off-by: Test User <<EMAIL>>\n更多内容"))
        self.assertTrue(check_author_email("Signed-off-by: User <<EMAIL>>"))  # 复杂邮箱
        
        # 错误情况
        self.assertFalse(check_author_email(""))  # 无Signed-off-by
        self.assertFalse(check_author_email("Signed-off-by: "))  # 空Signed-off-by
        self.assertFalse(check_author_email("Signed-off-by: Test User"))  # 无邮箱
        self.assertFalse(check_author_email("Signed-off-by: <NAME_EMAIL>"))  # 邮箱格式错误
        self.assertFalse(check_author_email("Signed-off-by: Test User <invalid-email>"))  # 无效邮箱

    # 白名单用户检查测试
    def test_whitelist_user(self):
        self.assertTrue(is_whitelist_user("gerritadmin"))
        self.assertTrue(is_whitelist_user("jenkinsadmin"))
        self.assertTrue(is_whitelist_user("sonarreview"))
        self.assertFalse(is_whitelist_user("testuser"))
        self.assertFalse(is_whitelist_user("admin"))


if __name__ == '__main__':
    unittest.main()
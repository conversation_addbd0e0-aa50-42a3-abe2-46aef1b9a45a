#!/bin/bash
source /etc/profile
echo "PUSHINFO:${PUSHINFO}"

cd "${WORKSPACE}" || exit
# 工程自己下载代码和区分差异代码
CURRCODE_DIR="${WORKSPACE}/CURRCODE"
DIFFCODE_DIR="${WORKSPACE}/DIFFCODE"
rm -rf DIFFCODE 
mkdir DIFFCODE 
rm -rf *.txt

cd CURRCODE

# 替换trigger工程的repo命令
REPOINFO="${REPOINFO/jenkinsadmin/sonarde}"

# 同步基线代码
$REPOINFO
# 同步基线代码之前先撤销前一次的同步。
#echo "repo forall -c git reset --hard HEAD^" 
#repo forall -c git reset --hard HEAD^
repo forall -c "git gc --prune=now"
repo forall -c "git reset --hard HEAD^"
repo forall -c "git clean -dfx -e .clangd  .eide.usr.ctx.json  eide.json"
echo "repo sync -c --no-clone-bundle"
repo sync -c --no-clone-bundle

repo list >  ${WORKSPACE}/repolist.txt

# 解析trigger工程传递过来的参数
echo ${PUSHINFO} | jq -r '.[] | "\(.name) \(.ref) \(.change_id_trans) \(.commit_id_trans)"' > ${WORKSPACE}/pushinfo.txt

# 获取路径
function getProjectDir() {
    local gerrit_project="$1"
    project_dir=$(cat  ${WORKSPACE}/repolist.txt | grep ":\ ${gerrit_project}$" | awk -F ':' '{print $1}')
    echo "${project_dir}"
}

# 获取增量文件
function getDiff(){
    shortcommit=${projectcommit:0:7}
    difftxt=${projectname}_filelist_${shortcommit}.txt
    git diff-tree --no-commit-id --name-status -r "${projectcommit}"   | grep -v ^D |awk '{print $2}' > "${difftxt}"
    # 在文本中加上项目的路径
    sed -i "s|^|${project_dir}/|g" "${difftxt}"
    mv "${difftxt}" "${CURRCODE_DIR}"
    # 开始拷贝文件
    cd "${CURRCODE_DIR}"
    # 提取目录部分并创建目录结构
    cat "${difftxt}" | xargs -n1 dirname | sort -u | xargs -I {} -n1 -P8 mkdir -p ${DIFFCODE_DIR}/{}
	# 拷贝文件
	cat "${difftxt}" | xargs -n1 -P8 cp --parent -t "${DIFFCODE_DIR}"
    mv "${CURRCODE_DIR}/${difftxt}" "${WORKSPACE}"
}

# 循环读取多仓的提交记录
countcherrynum=1
while IFS= read -r line; do
    echo "##################start cherry-pick $countcherrynum patchset##################"
    echo "patchset inof: $line"
	projectname=$(echo $line |awk '{print $1}')
    projectcommit=$(echo $line |awk '{print $NF}')
    GERRIT_REFSPECEX=$(echo $line |awk '{print $2}')
	project_dir=$(getProjectDir "${projectname}") #获取仓库的本地路径
    project_dir=$(echo "$project_dir" | sed 's/ //g') #去除路径中的空格
    #echo "project_dir:###${project_dir}###测试有没有空格###"
	cd ${CURRCODE_DIR}/${project_dir}
    git fetch "ssh://sonarde@10.0.0.3:29418/${projectname}" ${GERRIT_REFSPECEX} && git cherry-pick FETCH_HEAD
    #fetch_return_code=$?
    # 这里不能加判断，如果恰好在sonar同步代码的过程中，trigger工程被多仓上库的第二笔提交终止，那么此时就会报错退出。
    #if [ $fetch_return_code -ne 0 ]; then
    #    ssh -p 29418 sonarde@10.0.0.3 gerrit review --verified=-1 ${projectcommit}  --message "'Sync Code Failed! ${BUILD_URL}: FAILED'"
    #    exit 1
    #fi
    #获取增量文件
    getDiff
    let countcherrynum++
done < ${WORKSPACE}/pushinfo.txt

# sonar扫描
cd ${WORKSPACE}
SONAR_URL="http://10.0.0.59:9001"
SHOAR_ID=${GERRIT_CHANGE_IDX:0:8}
PROJECT_KEY="wearable-sonar-${SHOAR_ID}"
#AUTH_TOKEN="sqa_7e7b48283c1571ce51f934e92e4937137a483db3"
AUTH_TOKEN="squ_a60fc304111367163786cc06084df9b8d96e5ff1"
SCAN_DIR="DIFFCODE"

echo "开始清理冗余代码"
rm -rf ${SCAN_DIR}/sifli  # 34万行
rm -rf ${SCAN_DIR}/app/Application/App/UI/Image # 7万行
rm -rf ${SCAN_DIR}/app/Application/Lib_3rd/GoMore/SDK
rm -rf ${SCAN_DIR}/app/Application/Lib_New/arm_math # 7万行
rm -rf ${SCAN_DIR}/app/Application/App/touchx_gui/touchx_win32
rm -rf ${SCAN_DIR}/qw_platform/qwos_app/GUI/Font/DINPro_Font # 2.3万行
rm -rf ${SCAN_DIR}/qw_platform/qwos/driver/sensor/ppg/gh3220  # 1万行
rm -rf ${SCAN_DIR}/qw_platform/qwos/module/touchx/lv_engine/src/font/*[0-9]*.c # 14万行
rm -rf ${SCAN_DIR}/qw_platform/qwos/module/touchx_external/media/libjpeg 
rm -rf ${SCAN_DIR}/qw_platform/qwos/module/fit/fit_sdk/fit_example.c
rm -rf ${SCAN_DIR}/qw_platform/qwos/module/fit/inc_fit_codec/fit_example.h
find ${SCAN_DIR}/app/Application/App/radio/nanopb -type f -name '*\.pb\.*' -exec rm -v {} \;

echo "##################start cppcheck scanner##################"
# 执行cppcheck扫描
cppcheck ${SCAN_DIR}/ -i ${SCAN_DIR}/app/Application/App/UI/Image/ \
-i ${SCAN_DIR}/app/Application/Lib_3rd/GoMore/SDK/ \
-i ${SCAN_DIR}/app/Application/App/radio/nanopb/*/*.pb.* \
--enable=all --addon=/opt/cppcheck-2.17.1/misra/misra.json --output-file=cppcheck-result.xml --xml

echo "##################start sonarqube scanner##################"
# 执行sonar扫描
sonar-scanner \
-Dsonar.host.url="${SONAR_URL}"  \
-Dsonar.c.file.suffixes=.c,.h  \
-Dsonar.cpp.file.suffixes=.cc,.cpp,.cxx,.c++,.hh,.hpp,.hxx,.h++,.ipp  \
-Dsonar.cxx.file.suffixes=- \
-Dsonar.cxx.cppcheck.reportPaths=cppcheck-result.xml \
-Dsonar.projectKey="${PROJECT_KEY}" \
-Dsonar.projectName="${PROJECT_KEY}" \
-Dsonar.language="c,cpp" \
-Dsonar.login="${AUTH_TOKEN}" \
-Dsonar.projectVersion="${BUILD_TAG}" \
-Dsonar.sources="${SCAN_DIR}"/ \
-Dsonar.exclusions="${SCAN_DIR}/app/Application/App/UI/Image/**, \
${SCAN_DIR}/app/Application/Lib_3rd/GoMore/SDK/**,\
${SCAN_DIR}/app/Application/App/radio/nanopb/*/*.pb.* "

sleep 3s

# 拿到任务ID
ceTaskId=$(< "${WORKSPACE}"/.scannerwork/report-task.txt grep ceTaskId | awk -F '=' '{print $2}') 

# 拿到dashboardUrl
dashboardUrl="${SONAR_URL}/dashboard?id=${PROJECT_KEY}"
echo "${dashboardUrl}"

# 等待SonarQube扫描完成
wait_for_sonar() {
    local ceTaskId=$1
    local max_retries=30
    local retry_delay=6
    local status=""
    for ((i = 0; i < max_retries; i++)); do
        response=$(curl -s -u "$AUTH_TOKEN:" "$SONAR_URL/api/ce/task?id=$ceTaskId")
        status=$(echo "$response" | jq -r '.task.status')
        if [ "$status" == "PENDING" ] || [ "$status" == "IN_PROGRESS" ]; then
            echo "[Waiting]: SonarQube scan in progress. Waiting...$i"
            sleep $retry_delay
        else
            break
        fi
    done
    if [ "$status" == "SUCCESS" ]; then
        echo "[Success]: SonarQube scan completed."
    else
        echo "[Error]: SonarQube scan did failed."
        exit 1
    fi
}

# 获取阻断文件
get_blocking_files() {
while true; do
    response=$(curl -s -u "$AUTH_TOKEN:" "$SONAR_URL/api/issues/search?componentKeys=$PROJECT_KEY&severities=BLOCKER&p=$CURRENT_PAGE&ps=$PAGE_SIZE")
    issues=$(echo "$response" | jq -r '.issues[].component')
 
    if [ -z "$issues" ]; then
        break
    fi
 
    for issue in $issues; do
        file_name=$(echo "$issue" | cut -d':' -f2)
        echo "$file_name"
    done
 
    ((CURRENT_PAGE++))
done
}
 
# 获取问题个数
get_sonar_issue(){
    issue_severities=$1 # INFO --[提示]; MINOR --[次要]; MAJOR --[主要]; CRITICAL --[严重]; BLOCKER --阻断]
    local issue_result
    issue_result=$(curl -s -u "$AUTH_TOKEN:" "$SONAR_URL/api/issues/search?componentKeys=${PROJECT_KEY}&statuses=OPEN&severities=${issue_severities}" | jq -r '.total')
    echo "${issue_result}"
}

# 获取代码覆盖率
get_code_coverage() {
    local coverage_result
    coverage_result=$(curl -s -u "$AUTH_TOKEN:" "$SONAR_URL/api/measures/component?component=${PROJECT_KEY}&metricKeys=coverage" | jq -r '.component.measures[0].value')
    echo "${coverage_result:-0}"
}

# 获取代码重复率
get_code_duplications() {
    local duplications_result
    duplications_result=$(curl -s -u "$AUTH_TOKEN:" "$SONAR_URL/api/measures/component?component=${PROJECT_KEY}&metricKeys=duplicated_lines_density" | jq -r '.component.measures[0].value')
    echo "${duplications_result:-0}"
}

# 获取代码行数统计
get_code_lines() {
    local metric=$1  # ncloc, lines, statements, functions, classes
    local response
    response=$(curl -s -u "$AUTH_TOKEN:" "$SONAR_URL/api/measures/component?component=${PROJECT_KEY}&metricKeys=${metric}")
    
    # Check for error response
    if echo "$response" | jq -e '.errors' >/dev/null 2>&1; then
        echo "0"
        return
    fi
    
    local lines_result
    lines_result=$(echo "$response" | jq -r '.component.measures[] | select(.metric=="'${metric}'") | .value // "0"')
    if [ -z "$lines_result" ]; then
        echo "0"
    else
        echo "${lines_result}"
    fi
}

# 获取代码复杂度
get_complexity() {
    local complexity_type=$1  # complexity, cognitive_complexity
    local response
    response=$(curl -s -u "$AUTH_TOKEN:" "$SONAR_URL/api/measures/component?component=${PROJECT_KEY}&metricKeys=${complexity_type}")
    
    # Check for error response
    if echo "$response" | jq -e '.errors' >/dev/null 2>&1; then
        echo "0"
        return
    fi
    
    local complexity_result
    complexity_result=$(echo "$response" | jq -r '.component.measures[] | select(.metric=="'${complexity_type}'") | .value // "0"')
    if [ -z "$complexity_result" ]; then
        echo "0"
    else
        echo "${complexity_result}"
    fi
}

# 获取代码质量评分
get_quality_gate_status() {
    local response
    response=$(curl -s -u "$AUTH_TOKEN:" "$SONAR_URL/api/qualitygates/project_status?projectKey=${PROJECT_KEY}")
    
    # Check for error response
    if echo "$response" | jq -e '.errors' >/dev/null 2>&1; then
        echo "UNKNOWN"
        return
    fi
    
    local quality_gate_result
    quality_gate_result=$(echo "$response" | jq -r '.projectStatus.status // "UNKNOWN"')
    if [ -z "$quality_gate_result" ]; then
        echo "UNKNOWN"
    else
        echo "${quality_gate_result}"
    fi
}

# 获取代码质量门禁详情
get_quality_gate_details() {
    local response
    response=$(curl -s -u "$AUTH_TOKEN:" "$SONAR_URL/api/qualitygates/project_status?projectKey=${PROJECT_KEY}")
    
    # Check for error response
    if echo "$response" | jq -e '.errors' >/dev/null 2>&1; then
        echo "No quality gate details available (Insufficient privileges)"
        return
    fi
    
    local quality_gate_details
    quality_gate_details=$(echo "$response" | jq -r '.projectStatus.conditions[] | "\(.metricKey): \(.status) - \(.actualValue) \(.comparator) \(.errorThreshold)" // "No conditions available"')
    if [ -z "$quality_gate_details" ]; then
        echo "No quality gate details available"
    else
        echo "${quality_gate_details}"
    fi
}

# 获取代码可靠性评分
get_reliability_rating() {
    local response
    response=$(curl -s -u "$AUTH_TOKEN:" "$SONAR_URL/api/measures/component?component=${PROJECT_KEY}&metricKeys=reliability_rating")
    
    # Check for error response
    if echo "$response" | jq -e '.errors' >/dev/null 2>&1; then
        echo "UNKNOWN"
        return
    fi
    
    local reliability_result
    reliability_result=$(echo "$response" | jq -r '.component.measures[] | select(.metric=="reliability_rating") | .value // "UNKNOWN"')
    if [ -z "$reliability_result" ]; then
        echo "UNKNOWN"
    else
        echo "${reliability_result}"
    fi
}

# 获取代码安全性评分
get_security_rating() {
    local response
    response=$(curl -s -u "$AUTH_TOKEN:" "$SONAR_URL/api/measures/component?component=${PROJECT_KEY}&metricKeys=security_rating")
    
    # Check for error response
    if echo "$response" | jq -e '.errors' >/dev/null 2>&1; then
        echo "UNKNOWN"
        return
    fi
    
    local security_result
    security_result=$(echo "$response" | jq -r '.component.measures[] | select(.metric=="security_rating") | .value // "UNKNOWN"')
    if [ -z "$security_result" ]; then
        echo "UNKNOWN"
    else
        echo "${security_result}"
    fi
}

# 获取代码可维护性评分
get_maintainability_rating() {
    local response
    response=$(curl -s -u "$AUTH_TOKEN:" "$SONAR_URL/api/measures/component?component=${PROJECT_KEY}&metricKeys=sqale_rating")
    
    # Check for error response
    if echo "$response" | jq -e '.errors' >/dev/null 2>&1; then
        echo "UNKNOWN"
        return
    fi
    
    local maintainability_result
    maintainability_result=$(echo "$response" | jq -r '.component.measures[] | select(.metric=="sqale_rating") | .value // "UNKNOWN"')
    if [ -z "$maintainability_result" ]; then
        echo "UNKNOWN"
    else
        echo "${maintainability_result}"
    fi
}

# 获取代码技术债务
get_technical_debt() {
    local response
    response=$(curl -s -u "$AUTH_TOKEN:" "$SONAR_URL/api/measures/component?component=${PROJECT_KEY}&metricKeys=sqale_index")
    
    # Check for error response
    if echo "$response" | jq -e '.errors' >/dev/null 2>&1; then
        echo "0"
        return
    fi
    
    local debt_result
    debt_result=$(echo "$response" | jq -r '.component.measures[] | select(.metric=="sqale_index") | .value // "0"')
    if [ -z "$debt_result" ]; then
        echo "0"
    else
        echo "${debt_result}"
    fi
}

# 获取代码注释行数
get_comment_lines() {
    local response
    response=$(curl -s -u "$AUTH_TOKEN:" "$SONAR_URL/api/measures/component?component=${PROJECT_KEY}&metricKeys=comment_lines")
    
    # Check for error response
    if echo "$response" | jq -e '.errors' >/dev/null 2>&1; then
        echo "0"
        return
    fi
    
    local comment_lines_result
    comment_lines_result=$(echo "$response" | jq -r '.component.measures[] | select(.metric=="comment_lines") | .value // "0"')
    if [ -z "$comment_lines_result" ]; then
        echo "0"
    else
        echo "${comment_lines_result}"
    fi
}

# 获取代码注释率
get_comment_rate() {
    local response
    response=$(curl -s -u "$AUTH_TOKEN:" "$SONAR_URL/api/measures/component?component=${PROJECT_KEY}&metricKeys=comment_lines_density")
    
    # Check for error response
    if echo "$response" | jq -e '.errors' >/dev/null 2>&1; then
        echo "0"
        return
    fi
    
    local comment_rate_result
    comment_rate_result=$(echo "$response" | jq -r '.component.measures[] | select(.metric=="comment_lines_density") | .value // "0"')
    if [ -z "$comment_rate_result" ]; then
        echo "0"
    else
        echo "${comment_rate_result}"
    fi
}

# 将数字评分转换为字母等级
convert_rating_to_letter() {
    local rating=$1
    case $rating in
        "1.0") echo "A" ;;
        "2.0") echo "B" ;;
        "3.0") echo "C" ;;
        "4.0") echo "D" ;;
        "5.0") echo "E" ;;
        *) echo "未知" ;;
    esac
}

# 在文件开头添加输出文件定义
OUTPUT_FILE="sonar_check_result.txt"
RESULT_FILE="jenkins_result.txt"

## 清空输出文件并写入标题
#echo "'## SonarQube Check'" > "$OUTPUT_FILE"
#echo "'## 检查项目: ${PROJECT_KEY}'" >> "$OUTPUT_FILE"
#echo "" >> "$OUTPUT_FILE"

# 创建输出函数，同时输出到终端和文件
output() {
    # 终端显示不带引号的内容
    echo -e "$1"
    # 文件中写入带引号的内容
    echo "'$1'" >> "$OUTPUT_FILE"
}

# 只输出到终端的函数
terminal_output() {
    echo -e "$1"
}

# 添加空行
output_blank_line() {
    echo "" >> "$OUTPUT_FILE"
}

output_nonl() {
    # 终端显示不带引号的内容
    echo -n "$1"
    # 文件中写入带引号的内容（不换行）
    echo -n "'$1" >> "$OUTPUT_FILE"
}

# 设置Jenkins环境变量
set_jenkins_result() {
    local result=$1
    local reason=$2
    # 实际设置环境变量，供Jenkins使用
    export SONAR_CHECK_RESULT=$result
    export SONAR_CHECK_REASON="$reason"
    # 同时输出到文件，方便查看
    echo "SONAR_CHECK_RESULT=$result" >> "$RESULT_FILE"
}

# 修改 get_all_metrics 函数
get_all_metrics() {
    local response
    response=$(curl -s -u "$AUTH_TOKEN:" "$SONAR_URL/api/measures/component?component=${PROJECT_KEY}&metricKeys=ncloc,lines,statements,functions,classes,complexity,cognitive_complexity,reliability_rating,security_rating,sqale_rating,sqale_index,comment_lines,comment_lines_density")
    
    # 检查响应是否包含错误
    if echo "$response" | jq -e '.errors' >/dev/null 2>&1; then
        output "Error: Unable to fetch metrics. Please check permissions and project key."
        return 1
    fi
    
    # 解析所有指标
    local metrics
    metrics=$(echo "$response" | jq -r '.component.measures[] | "\(.metric)=\(.value)"')
    
    # 创建关联数组存储指标
    declare -A metric_values
    while IFS='=' read -r metric value; do
        metric_values[$metric]=$value
    done <<< "$metrics"
    
    # 输出标题
    output "## SonarDE Check"
    output "## 检查项目: ${PROJECT_KEY}"
    output "报告链接: View the SonarDE info [here](${dashboardUrl})."
    output "工程链接: Rebuild the SonarDE Jenkins job [here](${BUILD_URL})."

    output_blank_line
    output "------------------------------------------------------------"
    output_blank_line
    
    # 问题统计
    output "问题:"
    output "  阻断问题: $(get_sonar_issue BLOCKER)"
    output "  严重问题: $(get_sonar_issue CRITICAL)"
    output "  主要问题: $(get_sonar_issue MAJOR)"
    output "  次要问题: $(get_sonar_issue MINOR)"
    output "  提示问题: $(get_sonar_issue INFO)"
    output_blank_line
    
    # 大小统计
    output "大小:"
    output "  代码行: ${metric_values[ncloc]:-0}"
    output "  注释行: ${metric_values[comment_lines]:-0}"
    output "  注释率: ${metric_values[comment_lines_density]:-0}%"
    output "  复杂度: ${metric_values[complexity]:-0}"
    local dup_rate=$(get_code_duplications)
    dup_rate=${dup_rate:-0}  # 如果为null则转换为0
    output "  代码重复率: ${dup_rate}%"
    output_blank_line
    
    output "------------------------------------------------------------"
    output_blank_line
    
    # 检查是否有阻断或严重问题
    local blocker_num=$(get_sonar_issue BLOCKER)
    local critical_num=$(get_sonar_issue CRITICAL)
    
    # 检查代码重复率
    local dup_warning=""
    if (( $(echo "$dup_rate > 50" | bc -l) )); then
        dup_warning="警告: 代码重复率(${dup_rate}%)超过50%，建议进行代码重构"
    fi
    
    # 设置 GERRIT_SCORE 全局变量
    if [ "$blocker_num" -gt 0 ] || [ "$critical_num" -gt 0 ]; then
        GERRIT_SCORE="-1"
    else
        GERRIT_SCORE="+1"
    fi
    
    if [ "$blocker_num" -gt 0 ] || [ "$critical_num" -gt 0 ]; then
        output "最终结果: 不通过"
        local fail_reason=""
        if [ "$blocker_num" -gt 0 ]; then
            fail_reason="存在阻断问题: ${blocker_num}个"
        fi
        if [ "$critical_num" -gt 0 ]; then
            if [ -n "$fail_reason" ]; then
                fail_reason="${fail_reason}，"
            fi
            fail_reason="${fail_reason}存在严重问题: ${critical_num}个"
        fi
        output "原因: $fail_reason"
        if [ -n "$dup_warning" ]; then
            output "$dup_warning"
        fi
        set_jenkins_result "FAIL" "$fail_reason"
        return 1
    else
        output "最终结果: 通过"
        output "原因: 未发现阻断或严重问题"
        if [ -n "$dup_warning" ]; then
            output "$dup_warning"
        fi
        set_jenkins_result "PASS" "未发现阻断或严重问题"
        return 0
    fi
}

# 在等待SonarQube扫描完成后调用展示函数
echo "##################waitting for sonar report##################"
# 等待 SonarQube 扫描完成
wait_for_sonar "${ceTaskId}"

# 展示所有指标
get_all_metrics
 
# 循环打分
for singlecommitid in ${GERRIT_PATCHSET_REVISIONX_ALL};do
    # 把分补平
    ssh -p 29418 sonarde@10.0.0.3 gerrit review --code-review=+0 "${singlecommitid}" 
    # 重新打分
    ssh -p 29418 sonarde@10.0.0.3 gerrit review --code-review="${GERRIT_SCORE}" "${singlecommitid}" --message "$(cat ${OUTPUT_FILE})"

done

#!/bin/bash
# HTTP方式调用接口

TIMESTAMP_FILE="/home/<USER>/gerrit_site/hooks/inidir/reviewer-added/timestamp_file"
TIME_THRESHOLD=2 # 2秒，限制次钩子2秒内只能被调用一次
current_time=$(date +%s)
echo $@ > testfile

if [ -f "$TIMESTAMP_FILE" ]; then
    file_time=$(stat -c %Y "$TIMESTAMP_FILE")
    time_diff=$((current_time - file_time))
    if [ $time_diff -lt $TIME_THRESHOLD ]; then
        # "钩子在最近已执行，跳过此次执行。"
        exit 0
    fi
fi

# 定义字典arg_dict
declare -A arg_dict

while (( $# > 1 )); do
    #echo $0 $1 $2 $3
    temkey=$1
    value="$2"
    key="${temkey#--}"
    # 使用普通变量赋值
    arg_dict["$key"]="$value"
    shift 2
done

change_value=${arg_dict[change]}
owner_value=${arg_dict[change-owner-username]}
url_value=${arg_dict[change-url]}
project_value=${arg_dict[project]}
branch_value=${arg_dict[branch]}
changeidtmp=${change_value:(-8)}

curr_time=$(date +%Y-%m-%d-%S)
gerrit_server="http://********:8081"
gerrit_user='gerritadmin'
gerrit_token='YKS8QU0DGMjLPHUiOvpKFcR6UVNfbZAsedrrbCVidw'
api_url="${gerrit_server}/changes/${change_value}/reviewers"
iniPath="/home/<USER>/gerrit_site/hooks/inidir/reviewer-added"
initmp="${iniPath}/temp_${changeidtmp}_${curr_time}.json"
iniaim="${iniPath}/data_${changeidtmp}_${curr_time}.json"
iniuser="${iniPath}/user_${changeidtmp}_${curr_time}.json"

# 调用API
curl --user ${gerrit_user}:${gerrit_token} ${api_url} > ${initmp}

# 去除开头的多余字符 )]}
if [[ $(cat ${initmp} | wc -l) -gt 1 ]] && [[ $(cat ${initmp}  | grep "^)]\}" | wc -l) -eq 1 ]];
then
  sed '1d' ${initmp} > ${iniaim}
fi

all_name=$(cat ${iniaim} | jq '.[] | select((.approvals."Code-Review" == " 0" and .approvals."Verified" == null) or (.approvals."Code-Review" == " 0" and .approvals."Verified" == " 0")).name')

# 白名单，不要带双引号
whitelist=(jenkinsadmin sonarreview lijiajing gerritadmin)
whitelist+=($owner_value)

# 剔除数组的双引号
new_name=()
for element in "${all_name[@]}"; do
    new_name+=(${element//\"/})
done

# 过滤掉白名单
filtered_names=()
for name in "${new_name[@]}"
do
    in_blacklist=false
    for black_name in "${whitelist[@]}"
    do
        if [[ "$black_name" == "$name"  ]];then
            in_blacklist=true
            break
        fi
    done
    if [[ "$in_blacklist" == "false" ]]; then
        filtered_names+=("$name")
    fi
done

# 满足通知的名单
echo "${filtered_names[@]}" > $iniuser


# 群机器人通知
function weixinnotice(){
# 填写自己机器人的KEY
api_key="9b4f8886-50c8-410c-88fe-395f174d15ec"

#发送消息
webhook_url="https://qyapi.weixin.qq.com/cgi-bin/webhook/send"

message="**代码检视提醒**\n
👩‍💻亲爱的同事们(${filtered_names[@]})，以下代码检视任务等待你的审阅：\n
- **提交人**：${owner_value}
- **项目名称**：${project_value}
- **代码分支**：${branch_value}
- **待审阅地址**：${url_value}\n
⏰请尽快审阅，确保项目进度不受影响。感谢你的配合！"


curl "${webhook_url}?key=${api_key}" \
   -H 'Content-Type: application/json' \
   -d "{
        \"msgtype\": \"text\",
        \"text\": {
            \"content\": \"$message\"
        }
   }"
}

if [[ ${#filtered_names[@]} -gt 0 ]];then
  weixinnotice
fi
echo "$curr_time" > $TIMESTAMP_FILE

#!/bin/bash

# Function to perform bubble sort
bubble_sort() {
    local array=("$@")
    local n=${#array[@]}
    local temp

    for ((outer_index = 0; outer_index < n; outer_index++)); do
        for ((inner_index = 0; inner_index < n-i-1; inner_index++)); do
            if (( array[j] > array[j+1] )); then
                temp=${array[inner_index]}
                array[inner_index]=${array[inner_index+1]}
                array[inner_index+1]=$temp
            fi
        done
    done

    echo "${array[@]}"
}

# Example usage
numbers=(64 34 25 12 22 11 90)
sorted_numbers=$(bubble_sort "${numbers[@]}")
echo "Sorted array: ${sorted_numbers}"
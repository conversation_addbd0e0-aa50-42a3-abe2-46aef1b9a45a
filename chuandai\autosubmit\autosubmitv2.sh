#!/bin/bash

SSHUSER="sonarreview"
GERRITEXEC="ssh -p 29418 ${SSHUSER}@10.0.0.3 gerrit"

declare -A patch_dict

function check_all_patches() {
    local change_id=$1
    declare -gA patch_dict
    local all_valid=1

    # 获取所有仓库的patchset
    local revisions=($(${GERRITEXEC} query --current-patch-set --format=JSON "${change_id}" |jq  --arg GERRIT_BRANCH ${GERRIT_BRANCH} 'select(.branch == $GERRIT_BRANCH)'| jq -r '.currentPatchSet.revision | select(.!=null)'))

    # 全局校验锁
    for commit_id in "${revisions[@]}"; do
        #${GERRITEXEC} query commit:"${commit_id}" --current-patch-set --format=JSON > "${commit_id}.json"

        # 严格校验条件
        local submit_status=$(${GERRITEXEC} query commit:"${commit_id}" --current-patch-set --submit-records --format=JSON | jq -r '.submitRecords | select(.!=null) | .[].status' | grep -q 'OK'; echo $?)

        if [ "${submit_status}" -eq 0 ]; then
            patch_dict["${commit_id}"]=1
            echo "${commit_id} current-patch-set打分状态校验完成,该笔Patch满足合入条件。"
        else
            patch_dict["${commit_id}"]=0
            all_valid=0
            echo "${commit_id} current-patch-set打分状态校验完成,该笔Patch不满足合入条件。"
        fi
    done

    [ "${all_valid}" -eq 1 ] || return 1
    return 0
}

function check_dependencies() {
    local commit_id=$1
    ${GERRITEXEC} query commit:"${commit_id}" --dependencies --current-patch-set --submit-records --format=JSON > "${commit_id}_deps.json"
    
    # 这里可能会存在没有dependsOn的场景，遇到的时候在解决
    local depcommit_id=$(jq -r '.dependsOn? // empty | .[].revision? | select(.!=null)' "${commit_id}_deps.json")
    
    # 获取依赖分支
    local depbranch_name=$(jq -r '.branch? // empty | select(.!=null)' "${commit_id}_deps.json")
    
    # 检查deps是否不为空
    if [ -z "${depcommit_id}" ]; then
        return 0
    fi
    # 检查依赖patch的状态
    if ! check_single_dependency "${depcommit_id}"; then
        return 1
    fi

    # 检查依赖分支是否和当前分支一致
    if [ "${GERRIT_BRANCH}" != "${depbranch_name}" ]; then
        echo "依赖分支和当前分支不一致: ${GERRIT_BRANCH} != ${depbranch_name}"
        ${GERRITEXEC} review "${commit_id}" --message "'Warning: DependsOn patchSet branch is not same: ${GERRIT_BRANCH} != ${depbranch_name}!!!'"
        return 1
    fi

    return 0
}

function check_single_dependency() {
    local dep_id=$1
    
    # 获取依赖关系链
    ${GERRITEXEC} query commit:"${dep_id}" --dependencies --submit-records --format=JSON > "${dep_id}_deps.json"
    
    local dep_id_status=$(jq  -r '.status? // empty | select(.!=null)' "${dep_id}_deps.json"  | grep -q 'MERGED';echo $?)
    if [ "${dep_id_status}" -eq 0 ]; then
        echo "${dep_id} 依赖patchset校验通过"
        return 0
    else
        echo "${dep_id} 依赖patchset校验失败" 
        # 查询当前patchset的Code-Review+2用户并撤销评分
        jq -r '.currentPatchSet? //empty | .approvals[] | select(.type == "Code-Review" and .value == "2") | .by.username' "${commit_id}_deps.json" | while read username; do
            if [[ "${username}" != "${SSHUSER}" ]]; then
                ${GERRITEXEC} set-reviewers --remove "${username}" "${commit_id}"
                echo "已撤销用户 ${username} 的Code-Review+2评分"
                ${GERRITEXEC} review "${commit_id}" --message "'Warning: DependsOn patchSet is not merged: ${dep_id},Code-Review vote reset.'"
            fi
        done
        
        return 1
    fi
}

# 主流程
sync_size="FALSE"
change_id="$GERRIT_CHANGE_ID"
if check_all_patches "${change_id}"; then
    # 原子性提交校验
    submit_all=1
    for commit_id in "${!patch_dict[@]}"; do
        if [ "$GERRIT_BRANCH" == *_factory ];then
        	break
        fi
        if ! check_dependencies "${commit_id}"; then
            echo "识别到这一笔Patch存在依赖,且它的依赖patch不满足MERGED状态: ${commit_id}"
            submit_all=0
            exit 1
        fi
    done

    # 统一提交
    if [ "${submit_all}" -eq 1 ]; then
        for commit_id in "${!patch_dict[@]}"; do
            ${GERRITEXEC} review --submit "${commit_id}"
            echo "原子提交完成：${commit_id} with $?"
            # 如果$?为0，则提交成功，否则表示提交失败，并回传信息
            if [ $? -eq 0 ]; then
                #${GERRITEXEC} review "${commit_id}" --message "'NonInteractiveUsers Auto Submit.'"
                echo "Patchset合入成功: ${commit_id}"
                sync_size="TRUE" #为true时，最后要执行同步操作
                
            else
                echo "Patchset合入失败: ${commit_id}"
                ${GERRITEXEC} review "${commit_id}" --message "'自动Submit失败. 检查下是否有冲突，有冲突的话解决冲突后重新提交。'"
                exit 1
            fi
        done
    else
        echo "存在未满足的依赖关系，终止提交"
        exit 1
    fi
else
    echo "存在patchset不满足Ready to submit状态."
    exit 1
fi
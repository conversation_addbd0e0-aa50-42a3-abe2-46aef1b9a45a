#!/bin/bash

echo "=== SonarQube到Grafana监控系统启动脚本 ==="

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "错误: Docker未运行，请先启动Docker"
    exit 1
fi

# 创建必要的目录
echo "创建必要的目录..."
mkdir -p webhook-service/logs
mkdir -p victoria-data
mkdir -p grafana/provisioning/datasources
mkdir -p grafana/provisioning/dashboards

# 启动服务栈
echo "启动监控服务栈..."
docker-compose -f docker-compose/sonar-webhook-service.yml up -d

# 等待服务启动
echo "等待服务启动..."
sleep 30

# 检查服务状态
echo "检查服务状态..."
docker-compose -f docker-compose/sonar-webhook-service.yml ps

# 健康检查
echo "执行健康检查..."

# 检查webhook服务
if curl -f http://localhost:8080/health > /dev/null 2>&1; then
    echo "✓ Webhook服务运行正常"
else
    echo "✗ Webhook服务异常"
fi

# 检查VictoriaMetrics
if curl -f http://localhost:8428/api/v1/label/__name__/values > /dev/null 2>&1; then
    echo "✓ VictoriaMetrics运行正常"
else
    echo "✗ VictoriaMetrics异常"
fi

# 检查Grafana
if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
    echo "✓ Grafana运行正常"
else
    echo "✗ Grafana异常"
fi

echo ""
echo "=== 服务访问地址 ==="
echo "Grafana: http://localhost:3000 (admin/admin123)"
echo "VictoriaMetrics: http://localhost:8428"
echo "Webhook服务: http://localhost:8080"
echo ""
echo "=== 下一步操作 ==="
echo "1. 访问Grafana配置仪表板"
echo "2. 运行webhook配置脚本: python scripts/setup_sonar_webhooks.py"
echo "3. 在SonarQube中触发分析以测试数据流"

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证Grafana仪表盘JSON文件的有效性
"""

import json
import sys
import os

def validate_dashboard_json(file_path):
    """验证仪表盘JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            dashboard = json.load(f)
        
        # 检查必需的字段
        required_fields = ['title', 'panels', 'uid']
        missing_fields = []
        
        for field in required_fields:
            if field not in dashboard:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ 缺少必需字段: {', '.join(missing_fields)}")
            return False
        
        # 检查面板
        panels = dashboard.get('panels', [])
        if not panels:
            print("❌ 没有找到面板")
            return False
        
        print(f"✅ JSON格式正确")
        print(f"📊 仪表盘标题: {dashboard.get('title', 'Unknown')}")
        print(f"🆔 UID: {dashboard.get('uid', 'Unknown')}")
        print(f"📈 面板数量: {len(panels)}")
        
        # 检查每个面板的SQL查询
        sql_errors = []
        for i, panel in enumerate(panels):
            if 'targets' in panel:
                for j, target in enumerate(panel['targets']):
                    if 'rawSql' in target:
                        sql = target['rawSql']
                        # 基本SQL语法检查
                        if 'SELECT' not in sql.upper():
                            sql_errors.append(f"面板 {i+1}, 查询 {j+1}: 缺少SELECT语句")
                        if 'FROM' not in sql.upper():
                            sql_errors.append(f"面板 {i+1}, 查询 {j+1}: 缺少FROM子句")
        
        if sql_errors:
            print("⚠️  SQL查询警告:")
            for error in sql_errors:
                print(f"   - {error}")
        else:
            print("✅ SQL查询格式正确")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON格式错误: {e}")
        return False
    except FileNotFoundError:
        print(f"❌ 文件不存在: {file_path}")
        return False
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    if len(sys.argv) != 2:
        print("使用方法: python validate_dashboard.py <dashboard.json>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        sys.exit(1)
    
    print(f"🔍 验证仪表盘文件: {file_path}")
    print("=" * 50)
    
    if validate_dashboard_json(file_path):
        print("=" * 50)
        print("🎉 仪表盘验证通过，可以导入到Grafana！")
        sys.exit(0)
    else:
        print("=" * 50)
        print("💥 仪表盘验证失败，请修复后重试！")
        sys.exit(1)

if __name__ == '__main__':
    main()
